<template>
  <div class="Echarts">

    <div class="subTitle" v-if="type == 1">
      计划成本占比<el-tooltip class="item" effect="dark" placement="top-start"><i class="el-icon-question"></i>
        <template #content>
          单位为非车方吨{{ titleData.unitNotCorrectCount }}单不参与统计 <br />
          单位为吨土质不设置单位换算{{
            titleData.soilTypeNotCorrectCount
          }}单不参与统计<br />
          未设置装车价/泥尾价/核算价/预设价{{
            titleData.priceNotCorrectCount
          }}单不参与统计<br />
          未设置台班费{{ titleData.ledgerDigPriceNotCorrectCount }}项不参与统计
        </template>
      </el-tooltip>
    </div>
    <div class="subTitle" v-if="type == 2">
      出土构成<el-tooltip class="item" effect="dark" placement="top-start"><i class="el-icon-question"></i>
        <template #content>
          单位为非车方吨{{ titleData.unitNotCorrectCount }}单不参与统计<br />
          单位为吨土质不设置单位换算{{
            titleData.soilTypeNotCorrectCount
          }}单不参与统计<br />
          内运，内转回填运单{{
            titleData.tpModeInnerNotCorrectCount
          }}单不参与统计
        </template>
      </el-tooltip>
      <div class="link" @click="toDetail">
        <el-link type="primary" :disabled="!id">查看详情</el-link>
      </div>
    </div>
    <div class="empty" v-if="isEmpty">
      <el-empty description="暂无数据"></el-empty>
    </div>
    <div ref="piechart" style="width: 100%; flex: 1" v-else></div>
  </div>
</template>

<script>
export default {
  name: "piechart",
  props: {
    isEmpty: {
      type: Boolean,
    },
    titleData: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {},
    },
    echartData: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {}
    },
    type: {
      type: String,
    },
    id: {
      type: String,
    },
  },
  data() {
    return {
      title: "运单共计出土XX方，均车N方",
      tip: "单位为非车方吨N单不参与统计单位为吨土质不设置单位换算N单不参与统计内运，内转回填运单N单不参与统计",
      chart: null,
    };
  },
  watch: {
    isEmpty(val) {
      if (!val) {
        this.$nextTick(() => {
          this.myEcharts();
          window.addEventListener("resize", this.resizeHanlder);
        });
      }
    },
    echartData: {
      handler(val) {
        if (this.chart) {
          let option = this.chart.getOption();
          option.series[0].data =
            this.type == 1
              ? this.echartData.biPhaseCostPieData
              : this.echartData.biPhaseSoilTypePieData;
          this.chart.setOption(option);
          // this.chart.resize()
        }
      },
      deep: true,
    },
  },
  methods: {
    toDetail() {
      if (this.id) {
        this.$router.push({
          path: "/soilChart",
          query: { id: this.id },
        });
      }
    },
    myEcharts() {
      let _this = this
      // 基于准备好的dom，初始化echarts实例
      this.chart = this.$echarts.init(this.$refs.piechart);
      let option = {
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            let str = "";
            // for (let i = 0; i < params.length; i++) {
            str +=
              params.marker +
              params.name +
              '：<span style="float:right;font-weight:600">' +
              params.value +
              `${_this.type == 1 ? '元' : '车'}</span><br/>`;
            // }
            return str;
          },
        },
        legend: {
          orient: "vertical",
          top: "3%",
          left: "3%",
        },
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 40,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data:
              this.type == 1
                ? this.echartData.biPhaseCostPieData
                : this.echartData.biPhaseSoilTypePieData,
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      this.chart.setOption(option);
    },
    resizeHanlder() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  },
  beforeDestroy() {
    // if (!this.chart) {
    //   return;
    // }
    // window.removeEventListener("resize", this.resizeHanlder);
    // this.chart.dispose();
    // this.chart = null;
  },
};
</script>

<style lang="scss" scoped>
.Echarts {
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.subTitle {
  display: flex;
  align-items: center;
  padding: 0 15px;
  padding-top: 15px;
  // text-align: center;
  line-height: 30px;
  color: #464646;
  font-size: 16px;

  .link {
    margin-left: auto;
  }
}

/deep/.el-empty {
  padding: 15px 0 !important;
}
</style>
