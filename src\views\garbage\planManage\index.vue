<template>
  <div class="planManage">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="menu"
                  slot-scope="{row}">
          <el-button size="mini"
                     type="text"
                     icon="el-icon-open"
                     v-if="permissions['chain:planManage:open']&&row.matchOrderCount==0&&row.planStatus==5"
                     @click="open(row)">开启匹配
          </el-button>
          <el-button size="mini"
                     type="text"
                     icon="el-icon-check"
                     v-if="permissions['chain:planManage:confirm']"
                     @click="confirm(row)">{{row.planStatus==5?'查看匹配':'确定匹配'}}
          </el-button>
          <el-button size="mini"
                     type="text"
                     icon="el-icon-edit"
                     v-if="permissions['chain:planManage:edit']&&row.matchOrderCount==0"
                     @click="edit(row)">修改
          </el-button>
          <el-button size="mini"
                     type="text"
                     icon="el-icon-circle-close"
                     v-if="permissions['chain:planManage:cancel']&&row.planStatus==2"
                     @click="cancel(row)">取消
          </el-button>
          <!-- &&row.matchOrderWaybillCount>0 -->
          <el-button size="mini"
                     type="text"
                     icon="el-icon-promotion"
                     v-if="permissions['chain:planManage:schedule']&&row.matchOrderWaybillCount>0"
                     @click="schedule(row)">计划进度
          </el-button>
          <el-button size="mini"
                     type="text"
                     icon="el-icon-add"
                     v-if="permissions['chain:planManage:copyAdd']&&row.matchOrderCount>0&&row.planStatus==5&&row.copyCount==0"
                     @click="add(row)">复制新增
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
    <plan-progress v-if="progressVisible"
                   :info="info"
                   :visible.sync="progressVisible"></plan-progress>
    <edit-plan v-if="editVisible"
                   :info="info"
                   @refreshChange="refreshChange"
                   :visible.sync="editVisible"></edit-plan>
    <confirm-match v-if="confirmVisible"
                   :info="info"
                   :visible.sync="confirmVisible"></confirm-match>
  </div>
</template>

<script>
import { getPage,openMatch,getUnderwayOrderCount,cancelPlan,getMatchGarbagePlanDetailById,copyPlanByPlanId } from "@/api/garbage/planManage";
import { tableOption } from "@/const/crud/garbage/planManage";
import { mapGetters } from "vuex";
import planProgress from "./planProgress.vue";
import editPlan from "./editPlan.vue";
import confirmMatch from "./confirmMatch.vue";

export default {
  name: "planManage",
  components: {
    planProgress,
    editPlan,
    confirmMatch,
  },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      info: {},
      progressVisible: false,
      editVisible: false,
      confirmVisible: false,
    };
  },
  created () { },
  mounted: function () { },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList () {
      return {
        addBtn: this.permissions["chain:planManage:add"]
          ? true
          : false,
        delBtn: this.permissions["chain:planManage:del"]
          ? true
          : false,
        editBtn: this.permissions["chain:planManage:edit"]
          ? true
          : false,
        viewBtn: this.permissions["chain:planManage:get"]
          ? true
          : false,
      };
    },
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange (val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage (page, params = {}) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.recycleSoilCubeStartStr = params.searchDate[0];
          params.recycleSoilCubeEndStr = params.searchDate[1];
          delete params.searchDate;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        ))
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page);
    },
    open (row) {
      this.$confirm('确定开启当前计划的匹配请求?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        openMatch({id:row.id}).then(res=>{
          this.$message.success('操作成功')
          this.getPage(this.page)
        })
      }).catch(() => {});
    },
    confirm (row) {
      this.info = row
      this.confirmVisible = true
    },
    edit (row) {
      getMatchGarbagePlanDetailById({id:row.id}).then(res=>{
        this.info = res.data.data
        this.editVisible = true
      })
    },
    cancel (row) {
      // 根据计划id获取进行中的订单数
      getUnderwayOrderCount({id:row.id}).then(res=>{
        console.log(res)
        this.$confirm(res.data.data>0?`计划取消也会取消撮合订单，当前存在${res.data.data}单进行的订单`:'确定取消当前计划的匹配请求?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          cancelPlan({id:row.id}).then(res=>{
            this.$message.success('操作成功')
            this.getPage(this.page)
          })
        }).catch(() => {});
      })

    },
    schedule (row) {
      this.info = row
      this.progressVisible = true
    },
    add(row){
      this.$confirm('确定复制新增一条?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        copyPlanByPlanId({id:row.id}).then(res=>{
          this.$message.success('操作成功')
          this.getPage(this.page)
        })
      }).catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped></style>
