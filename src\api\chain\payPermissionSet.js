import request from '@/router/axios'

export function getCode(query) {
  return request({
      url: '/chain/uniapp/codeByPay',
      method: 'get',
      params: query
  })
}
export function updatePayPwd(data) {
  return request({
      url: '/chain/companypayauthority/updatePayPwd',
      method: 'post',
      data
  })
}
export function getConfig(params) {
  return request({
      url: '/chain/companypayauthority/getConfig',
      method: 'get',
      params
  })
}
