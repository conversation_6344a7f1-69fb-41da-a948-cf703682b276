export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  searchSpan: 8,
  searchLabelWidth: 100,
  excelBtn: false,
  printBtn: false,
  viewBtn: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  labelWidth:130,
  searchMenuSpan: 6,
  menuWidth:100,
  // calcHeight: 135,
  // height: "auto",
  // defaultSort: {
  //   prop: "createDatetime",
  //   order: "descending",
  // },
  column: [
    {
      label: "施工单位",
      prop: "companyName",
      minWidth:180,
      overHidden:true,
    },
    {
      label: "项目名称",
      prop: "projectName",
      minWidth:180,
      overHidden:true,
    },
    {
      label: "运输任务开始时间",
      prop: "tpDatetime",
      minWidth:150,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "运输任务类型",
      prop: "tpMode",
      // sortable: true,
      type: "select",
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode',
      minWidth:94,
      overHidden:true,
    },
    {
      label: "项目任务状态",
      prop: "status",
      search: true,
      type: "select",
      dicData: [
        {
          label: "待接单",
          value: "1",
        },
        {
          label: "未开始",
          value: "2",
        },
        {
          label: "运输中",
          value: "3",
        },
        {
          label: "任务完成",
          value: "4",
        },
      ],
      minWidth:94,
      overHidden:true,
    },
    {
      label: "任务新增时间",
      prop: "createDatetime",
      minWidth:140,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: '手动创建时间',
      prop: 'manualDatetime',
      span:12,
      addDisplay:false,
      editDisplay:false,
      hide:true,
      showColumn:false,
    },
    {
      label: '撮合成功时间',
      prop: 'matchDatetime',
      span:12,
      addDisplay:false,
      editDisplay:false,
      hide:true,
      showColumn:false,
    },
  ],
};
