<template>
  <div class="totalAbnormalWaybill">
    <el-drawer size="90%" title="异常运单数" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <searchInfoOfAbnormalWaybill :info="paramsSearch"  @searchChange="searchChange" @exOut="exOut">
          <template slot="count">
            <div class="count">异常运单数 <span>{{ count }}个</span></div>
          </template>
        </searchInfoOfAbnormalWaybill>
        <avue-crud ref="crud" :page.sync="page" :data="tableData" :table-loading="tableLoading" :option="tableOption"
          v-model="form" :search.sync="search" @on-load="getPage">
        </avue-crud>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getWaybillUpdateHitTargetList as getPage} from "@/api/chain/board";
import searchInfoOfAbnormalWaybill from './searchInfoOfAbnormalWaybill';
import { exportOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  components: {
    searchInfoOfAbnormalWaybill
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      search: {},
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        // maxHeight:[800],
        // height:'auto',
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: true,
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        header: false,
        column: [
          {
            label: "项目名称",
            prop: "projectName",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "运单号",
            prop: "no",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "申请人",
            prop: "applicationName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "申请时间",
            prop: "applicationTime",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "变更原因",
            prop: "applicationRemark",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "审核状态",
            prop: "applicationStatusName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "运单修改项",
            prop: "itemFieldDesc",
            minWidth: 90,
            overHidden: true,
          },
        ],
      },
      count: 0,
    }
  },
  created () {
    this.paramsSearch = Object.assign({}, this.info)
    this.count = this.info.count
  },
  mounted () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    searchChange (params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    getPage (page, params={}) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          {
            sourceType: 1,
            applicationTimeBegin: this.paramsSearch.startDate,
            applicationTimeEnd: this.paramsSearch.endDate,
            projectInfoId: this.paramsSearch.projectInfoId,
            isHProjectInfoCount: this.paramsSearch.checkDynamic, //是否活跃项目
            itemFieldValue:this.paramsSearch.itemFieldValue,
          }
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
          this.count = response.data.data.total || 0
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    exOut (params, done) {
      let param = {
        sourceType: 1,
        applicationTimeBegin: params.startDate,
        applicationTimeEnd: params.endDate,
        projectInfoId: params.projectInfoId,
        isHProjectInfoCount: params.checkDynamic, //是否活跃项目
        itemFieldValue:this.paramsSearch.itemFieldValue,
        //留个运单修改项位置
      }
      let url = "/chain/companywaybillupdatehittarget/CompanyWaybillUpdateHitTargetExport";
      exportOut(param, url, "异常运单数", 'post').then(res => {
        done()
      }).catch(() => {
        done()
      })
    },
  },

};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

.count {
  margin-bottom: 10px;

  span {
    font-size: 16px;
    font-weight: 700;
  }
}
</style>
