<template>
  <div class="driverTaskDetail">
    <el-dialog width="890px"
               :visible.sync="visible"
               :append-to-body="true"
               title="常用收款人"
               :before-close="cancelModal"
               :close-on-click-modal="false">
      <basic-container>
        <avue-crud ref="selectCommonPayees"
                   :page.sync="page"
                   :data="tableData"
                   :table-loading="tableLoading"
                   :option="tableOption"
                   @selection-change="selectionChange"
                   @sort-change="sortChange"
                   @search-change="searchChange"
                   @on-load="getPage">
        </avue-crud>
      </basic-container>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="cancelModal">取消</el-button>
        <el-button type="primary" :loading="tableLoading" @click="submit">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getPage } from '@/api/chain/companyregularpayee'

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
  },
  components:{
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 10, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableData: [],
      tableOption: {
        header: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        searchMenuSpan:6,
        searchSpan:8,
        searchLabelWidth:86,
        selection:true,
        column: [
          {
            label: "收款人姓名",
            prop: "payeeName",
            search:true,
            minWidth:106,
            overHidden:true,
          },
          {
            label: "手机号",
            prop: "payeeMobile",
            maxlength:11,
            minWidth:100,
            overHidden:true,
          },
          {
            label: "身份证号",
            prop: "idCard",
            minWidth:150,
            overHidden:true,
          },
          {
            label: "收款银行",
            prop: "bindingBankName",
            minWidth:160,
            overHidden:true,
          },
          {
            label: "收款卡号",
            prop: "bindingBankNo",
            minWidth:150,
            overHidden:true,
          },]
      },
      tableLoading: false,
      selectList:[],
    };
  },
  created () {
  },
  mounted () {
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage (page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        isEnable:"1",//生效的
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    selectionChange(val){
      if(val.length > 1){
          const del_row = val.shift()
          this.$refs.selectCommonPayees.toggleRowSelection(del_row,false) //设置这一行取消选中
      }
      console.log(val);
      this.selectList = val
    },
    cancelModal () {
      this.$emit("update:visible", false);
    },
    submit(){
      this.$emit('submit',this.selectList)
      this.cancelModal()
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .avue-crud__tip{
  display: none;
}
/deep/ thead .el-checkbox{
  display: none;
}
</style>
