import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companyprojectwalletapply/page',
        method: 'get',
        params: query
    })
}
export function addObj(obj) {
    return request({
        url: '/chain/companyprojectwalletapply',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companycardresetlog/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companycardresetlog/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companycardresetlog',
        method: 'put',
        data: obj
    })
}
export function getFee(params) {
    return request({
        url: '/chain/companyprojectwalletapply/getFee',
        method: 'get',
        params
    })
}
export function getBalance(id) {
    return request({
        url: '/chain/companyprojectwallet/getBalance/'+ id,
        method: 'get',
    })
}
