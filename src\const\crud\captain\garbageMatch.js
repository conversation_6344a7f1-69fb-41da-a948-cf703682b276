export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true, // 显示搜索字段
  dialogClickModal: false,
  viewBtn: true,
  searchSpan: 8,
  searchMenuSpan: 6,
  menuWidth: 160,
  // searchCustom:2,
  // routerName:"fleetMatch",
  column: [
    {
      label: "搜索",
      prop: "keywords",
      sortable: "custom",
      searchPlaceholder:"请输入计划编号/车队名称",
      search: true,
      hide:true,
      showColumn:false,
    },
    {
      label: "计划编号",
      prop: "planNo",
      sortable: "custom",
      minWidth: 180,
      overHidden: true,
    },
    {
      label: "创建时间",
      prop: "createDatetime",
      sortable: "custom",
      minWidth: 140,
      overHidden: true,
    },
    {
      label: "车队名称",
      prop: "fleetName",
      minWidth: 180,
      overHidden: true,
    },
    {
      label: "土质类型",
      prop: "soils",
      search: true,
      searchMultiple: true,
      type: "select",
      dataType:"string",
      props: {
        label: "itemValue",
        value: "itemValue",
      },
      dicUrl: "/chain/systemdictionaryitem/listDictionaryItem?dictionary=match_soil_type",
      minWidth: 120,
      overHidden: true,
    },
    // {
    //   label: "价格",
    //   prop: "price",
    //   minWidth: 120,
    //   overHidden: true,
    // },
    {
      label: "计划状态",
      prop: "planStatus",
      sortable: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "匹配中",
          value: "1",
        },
        {
          label: "已暂停",
          value: "2",
        },
      ],
      minWidth: 94,
      overHidden: true,
    },
    {
      label: "账期",
      prop: "settleCycle",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "推荐池",
      prop: "matchPoolCount",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "待我确认",
      prop: "waitingConfirmCount",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "待泥尾确认",
      prop: "waitingGarbageConfirmCount",
      minWidth: 90,
      overHidden: true,
    },
    {
      label: "匹配成功",
      prop: "matchSuccessCount",
      minWidth: 80,
      overHidden: true,
    },
  ],
};
