<template>
  <div class="Echarts">
    <div class="subTitle">
      {{ subTitle
      }}<el-tooltip
        class="item"
        effect="dark"
        :content="tip"
        placement="top-start"
        ><i class="el-icon-question"></i
        ><template #content>
          <span style="white-space: pre-wrap">{{ tip }}</span>
        </template></el-tooltip
      >
      <div class="link" @click="toDetail">
        <el-link type="primary" :disabled="!id">查看详情</el-link>
      </div>
    </div>
    <div class="time">
      <el-radio-group v-model="time" size="small">
        <el-radio-button label="day">天</el-radio-button>s
        <el-radio-button label="week">周</el-radio-button>
        <el-radio-button label="month">月</el-radio-button>
      </el-radio-group>
    </div>
    <div class="empty" v-if="isEmpty">
      <el-empty description="暂无数据"></el-empty>
    </div>
    <div ref="barchart" style="width: 100%; flex: 1" v-else></div>
  </div>
</template>

<script>
import {
  transportUnitCost,
  machineCost,
  garbageCost,
} from "@/api/chain/analysis.js";
export default {
  name: "barEcharts",
  props: {
    subTitle: {
      type: String,
      default: "运输成本单价",
    },
    tip: {
      type: String,
      default:
        "单位为非车方吨N单不参与统计,单位为吨土质不设置单位换算N单不参与统计,资源运单N单不参与统计,未设置核算价或预设价的N单不参与统计",
    },
    type: {
      type: String,
    },
    id: {
      type: String,
    },
    isSearch: {
      type: Boolean,
    },
    isEmpty: {
      type: Boolean,
    },
  },
  data() {
    return {
      time: "week",
      chart: null,
    };
  },
  watch: {
    isSearch(val) {
      if (val) {
        if (this.chart) {
          this.initData();
        } else {
          this.$nextTick(() => {
            this.myEcharts();
            window.addEventListener("resize", this.resizeHanlder);
            this.initData();
          });
        }
      }
    },
    time() {
      if (this.id) {
        this.initData();
      }
    },
  },
  methods: {
    toDetail() {
      if (this.id) {
        if (this.type == 1) {
          this.$router.push({
            path: "/transportChart",
            query: { id: this.id },
          });
        } else if (this.type == 2) {
          this.$router.push({
            path: "/machineChart",
            query: { id: this.id },
          });
        } else if (this.type == 3) {
          this.$router.push({
            path: "/garbageChart",
            query: { id: this.id },
          });
        }
      }
    },
    initData() {
      if (this.chart) {
        let option = this.chart.getOption();
        let data = {
          projectInfoBiPhaseId: this.id,
          timeUnit: this.time,
        };
        if (this.type == 1) {
          transportUnitCost(data).then((res) => {
            option.xAxis = {
              type: "category",
              boundaryGap: false,
              data: res.data.data.xAxisData,
            };
            option.legend = {
              data: res.data.data.legendList,
              top: "0",
              left: "center",
            };
            option.series = res.data.data.yAxisData;
            this.chart.setOption(option);
          });
        } else if (this.type == 2) {
          machineCost(data).then((res) => {
            option.xAxis = {
              type: "category",
              boundaryGap: false,
              data: res.data.data.xAxisData,
            };
            option.legend = {
              data: res.data.data.legendList,
              top: "0",
              left: "center",
            };
            option.series = res.data.data.yAxisData;
            this.chart.setOption(option);
            this.$emit("getSummary", res.data.data.projectPhaseCostSummary);
          });
        } else if (this.type == 3) {
          garbageCost(data).then((res) => {
            option.xAxis = {
              type: "category",
              boundaryGap: false,
              data: res.data.data.xAxisData,
            };
            option.series = [
              {
                name: "泥尾成本",
                type: "line",
                data: res.data.data.yAxisData,
              },
            ];
            this.chart.setOption(option);
          });
        }
      }

      // else if(this.type==2){
      //   machineCost(data).then(res=>{
      //     option.xAxis.data = res.data.data.yAxisData
      //     option.series=[ res.data.data.yAxisData]
      //     this.chart.setOption(option);
      //   })
      // }else if(this.type==3){
      //   garbageCost(data).then(res=>{
      //     option.xAxis.data = res.data.data.yAxisData
      //     option.series=[ res.data.data.yAxisData]
      //    c
      //   })
      // }
    },
    myEcharts() {
      // 基于准备好的dom，初始化echarts实例
      this.chart = this.$echarts.init(this.$refs.barchart);
      let _this = this;
      let option = {
        // title: {
        //   text: "Stacked Line",
        // },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            let str = "<span>" + params[0].name + "</span><br/>";
            for (let i = 0; i < params.length; i++) {
              let unit =
                _this.type == 1
                  ? "元/方"
                  : _this.type == 3
                  ? "元/车"
                  : params[i].seriesName == "挖机装车(元/车)"
                  ? "元/车"
                  : "元/时";
              console.info(unit);
              str +=
                params[i].marker +
                params[i].seriesName +
                '：<span style="float:right;font-weight:600">' +
                params[i].value +
                `${unit}</span><br/>`;
            }
            return str;
          },
        },
        // legend: {
        //   data: ["Email", "Union Ads", "Video Ads", "Direct", "Search Engine"],
        //   top: "0",
        //   left: "center",
        // },
        grid: {
          left: "3%",
          right: "8%",
          bottom: "3%",
          containLabel: true,
        },
        // toolbox: {
        //   feature: {
        //     saveAsImage: {},
        //   },
        // },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [],

        },
        yAxis: {
          type: "value",
        },
        series: [],
      };
      // 使用刚指定的配置项和数据显示图表。
      this.chart.setOption(option);
    },
    resizeHanlder() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  },
  mounted() {
    // isEmpty(val) {
    //   if (!val) {
    //     this.$nextTick(() => {
    //       this.myEcharts();
    //       window.addEventListener("resize", this.resizeHanlder);
    //       this.initData();
    //     });
    //   }
    // },
  },
  beforeDestroy() {
    // if (!this.chart) {
    //   return;
    // }
    // window.removeEventListener("resize", this.resizeHanlder);
    // this.chart.dispose();
    // this.chart = null;
  },
};
</script>

<style lang="scss" scoped>
.Echarts {
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  .time {
    z-index: 999;
    position: absolute;
    right: 15px;
    top: 45px;
  }
}
.subTitle {
  padding: 0 15px;
  padding-top: 15px;
  // text-align: center;
  line-height: 30px;
  color: #464646;
  font-size: 16px;
  .link {
    margin-left: auto;
  }
}
.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>
