export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  searchSpan: 8,
  searchLabelWidth: 100,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: true,
  delBtn: false,
  searchMenuSpan: 6,
  menuWidth:120,
  // calcHeight: 135,
  // height: "auto",
  // defaultSort: {
  //   prop: "updateDatetime",
  //   order: "descending",
  // },
  column: [
    {
      label: "车队名称",
      prop: "fleetName",
      // sortable: true,
      span:24,
      minWidth:180,
      overHidden:true,
    },
    {
      label: "车队司机数",
      prop: "memberSize",
      sortable: "custom",
      display:false,
      minWidth:90,
      overHidden:true,
    },
    {
      label: "车队公告",
      prop: "fleetNotic",
      type:'textarea',
      span:24,
      hide:true,
      showColumn:false,
    },
    {
      label: "是否解散",
      prop: "isDel",
      type: "select", // 下拉选择
      search: true,
      minWidth:90,
      overHidden:true,
      dicData: [
        {
          label: "否",
          value: "0",
        },
        {
          label: "是",
          value: "1",
        },
      ],
    },
    {
      label: "更新时间",
      prop: "updateDatetime",
      display:false,
      minWidth:140,
      overHidden:true,
      sortable: "custom",
    },
  ],
};
