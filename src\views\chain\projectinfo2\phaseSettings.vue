<template>
  <div>
    <el-dialog
      title="项目阶段设置"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="80%"
      :before-close="cancelModal"
    >
      <basic-container v-loading="loading" element-loading-text="拼命加载中">
        <avue-form :option="option" v-model="dialogForm" @submit="submit">
          <template slot="tabs">
            <el-tabs
              :value="editableTabsValue + ''"
              type="card"
              :closable="
                dialogForm.phases.length > 1 &&
                !dialogForm.phases.some((v) => v.edit)
              "
              :addable="!dialogForm.phases.some((v) => v.edit)"
              @tab-add="handleTabsAdd"
              @tab-remove="del"
              @click.stop=""
            >
              <el-tab-pane
                :key="index"
                v-for="(item, index) in dialogForm.phases"
                :name="index + ''"
              >
                <template #label>
                  <div class="el-input el-input--small" v-if="item.edit">
                    <input
                      type="text"
                      v-focus
                      class="el-input__inner"
                      v-model="item.projectPhaseName"
                      autofocus
                      @blur="blurTab(item, index)"
                    />
                    <!-- <el-input
                    :ref="index + item.title"
                    v-model="item.name"
                    :autofocus="item.edit"
                    placeholder="请输入内容"
                    @blur="blurTab(item, index)"
                  ></el-input> -->
                  </div>
                  <span @dblclick="editTab(item, index)" v-else>{{
                    item.projectPhaseName
                  }}</span>
                </template>
                <avue-form
                  :option="phaseOption"
                  :ref="`phaseForm${index}`"
                  v-model="dialogForm.phases[index]"
                >
                  <template #projectPhaseTransportCost>
                    <el-input
                      v-model="item.projectPhaseTransportCost"
                      @input="changeProjectPhaseTransportCost($event, item)"
                    ></el-input>
                    <!-- <el-input-number
                      style="width: 100%"
                      v-model="item.projectPhaseTransportCost"
                      :controls="false"
                      @input="changeProjectPhaseTransportCost($event, item)"
                    ></el-input-number> -->
                  </template>
                  <template #projectPhaseSquareCost>
                    <el-input
                      v-model="item.projectPhaseSquareCost"
                      @input="changeProjectPhaseSquareCost($event, item)"
                    ></el-input>
                    <!-- <el-input-number
                      style="width: 100%"
                      v-model="item.projectPhaseSquareCost"
                      :controls="false"
                      @input="changeProjectPhaseSquareCost($event, item)"
                    ></el-input-number> -->
                  </template>

                  <template slot="soilsTable">
                    <div class="group">
                      <span>土质单位</span>
                      <div class="menus">
                        <el-button
                          type="primary"
                          icon="el-icon-download"
                          size="small"
                          :disabled="selectList.length == 0"
                          @click="bacthDelSoil(item)"
                          >批量删除</el-button
                        >
                        <el-button
                          type="primary"
                          icon="el-icon-download"
                          size="small"
                          @click="exIn(item)"
                          >导入</el-button
                        >
                        <el-button
                          type="primary"
                          icon="el-icon-download"
                          size="small"
                          @click="exOut(item)"
                          >导出</el-button
                        >
                      </div>
                    </div>
                    <avue-crud
                      :data="item.soils"
                      :option="tableOption"
                      @selection-change="selectionChange"
                    >
                      <template slot="menu" slot-scope="{ row, index }">
                        <el-button
                          type="text"
                          @click="rowDel(row, index, item)"
                          icon="el-icon-delete-solid"
                          >删除</el-button
                        >
                      </template></avue-crud
                    >
                  </template>
                </avue-form>
              </el-tab-pane>
            </el-tabs>
          </template>
        </avue-form>
      </basic-container>
    </el-dialog>
    <el-dialog
      width="400px"
      title="导入"
      center
      :visible.sync="uploadVisible"
      :close-on-click-modal="false"
    >
      <el-row>
        <el-col :span="5">
          <div style="line-height: 28px">上传文件：</div>
        </el-col>
        <el-col :span="19">
          <el-upload
            class="upload"
            action="#"
            :on-change="handleExcel"
            :file-list="fileList"
            accept=".xlsx,.xls"
            :auto-upload="false"
          >
            <el-button size="mini" type="primary">选择文件</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <el-row>
        备注：只支持xlsx、xls文件
        <span
          @click="openTemp"
          style="
            display: inline-block;
            color: #409eff;
            cursor: pointer;
            margin-top: 20px;
            margin-left: 20px;
          "
          >下载模板</span
        >
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="uploadVisible = false">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="imtExcel"
          >导入</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  phaseOption,
  tableOption,
  option,
} from "@/const/crud/chain/phaseSettings";
import {
  getSetting,
  addSetting,
  delPhase,
  getDefault,
  batchDelete,
  exInSoil,
} from "@/api/chain/projectinfo.js";
import { expotOut } from "@/util/down.js";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      loading:false,
      btnLoading: false,
      uploadVisible: false, //上传显示框展示展示展示展示展示展示展示展示展示展示展示展
      phaseOption,
      tableOption,
      option,
      dialogForm: {
        phases: [],
      },
      editableTabs: [],
      editableTabsValue: 0,
      selectList: [],
      formData: {},
      curItem: {},
      fileList: [],
      isTabEdit: false,
    };
  },
  mounted() {
    // this.editableTabsValue = this.tabIndex + this.editableTabs[0].title;
    this.getSetting();
  },
  methods: {
    formatNum(val) {
      let temp = val.toString();
      temp = temp.replace(/。/g, ".");
      temp = temp.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
      temp = temp.replace(/^\./g, ""); //验证第一个字符是数字
      temp = temp.replace(/\.{2,}/g, ""); //只保留第一个, 清除多余的
      temp = temp.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      temp = temp.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); //只能输入两个小数
      return temp;
    },
    changeProjectPhaseTransportCost(e, item) {
      e = this.formatNum(e);
      item.projectPhaseTransportCost = e;
      console.info(e, item);
      if (e <= 0 || item.projectPhaseSquareCost <= 0) {
        item.projectPhaseSquarePrice = 0;
      } else {
        item.projectPhaseSquarePrice = (
          +e / +item.projectPhaseSquareCost
        ).toFixed(2);
      }
    },
    changeProjectPhaseSquareCost(e, item) {
      e = this.formatNum(e);
      item.projectPhaseSquarePrice = e;
      if (e <= 0 || item.projectPhaseTransportCost <= 0) {
        item.projectPhaseSquarePrice = 0;
      } else {
        item.projectPhaseSquarePrice = (
          +item.projectPhaseTransportCost / +e
        ).toFixed(2);
      }
    },
    exIn(item) {
      this.curItem = item;
      this.formData = new FormData();
      this.formData.append("phaseId", item.id); //id,选择的选项id,选择的选项名称,选择的选项
      this.formData.append("projectInfoId", this.info.id);
      this.uploadVisible = true;
    },
    //导入表格
    handleExcel(file,list) {
      if (list.length > 1 && file.status !== "fail") {
        list.splice(0, 1);
      } else if (file.status === "fail") {
        // errorMsg("上传失败，请重新上传！");
        list.splice(0, 1);
      }
      this.fileList = list
    },
    //上传表格
    imtExcel() {
      console.info(this.fileList)
      if (this.fileList.length > 0) {
        this.formData.append("file", this.fileList[0].raw); //把文件信息放入对象
      }
      console.log(this.formData);
      this.btnLoading = true;
      exInSoil(this.formData)
        .then((res) => {
          res.data.data.map((v) => {
            v.$cellEdit = true;
            return v;
          });
          this.curItem.soils = res.data.data;
          this.btnLoading = false;
          this.uploadVisible = false;
          this.fileList = [];

          this.$message({
            type: "success",
            message: "导入成功!",
          });
        })
        .catch(() => {
          this.btnLoading = false;
          this.formData.delete("file")
        });
      // this.formData = new FormData(); //声明一个FormDate对象
      // this.formData.append("formFile", file.raw);    //把文件信息放入对象中
    },
    openTemp() {
      window.open(
        "https://jyb-app.obs.cn-south-1.myhuaweicloud.com:443/1%2Fappdownload%2F30c14274-2e5b-470f-abee-71bc0a5dc15b.xlsx"
      );
    },
    exOut(item) {
      let url = `/chain/projectinfobiphase/exportSoils`;
      // params.id = this.info.id
      expotOut(
        { projectInfoId: this.info.id, id: item.id },
        url,
        item.projectPhaseName + "土质单位"
      );
    },
    bacthDelSoil(item) {
      let _this = this;
      this.$confirm("是否删除土质?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let soilTyps = _this.selectList.map((v) => v.soilType);
          let allIds = _this.selectList.map((v) => v.id);
          let ids = allIds.filter((v) => v);
          if (ids.length > 0) {
            batchDelete(ids).then((res) => {});
          }
          _this.$message({
            type: "success",
            message: "删除成功!",
          });
          item.soils = item.soils.filter((v) => !soilTyps.includes(v.soilType));
        })
        .catch(() => {});
    },
    rowDel(form, index, item) {
      let _this = this;
      this.$confirm("是否删除该土质?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if (form.id) {
            batchDelete([form.id]).then((res) => {
              _this.$message({
                type: "success",
                message: "删除成功!",
              });
              item.soils.splice(index, 1);
            });
          } else {
            _this.$message({
              type: "success",
              message: "删除成功!",
            });
            item.soils.splice(index, 1);
          }
        })
        .catch(() => {});
    },
    selectionChange(e) {
      this.selectList = e;
    },
    del(item) {
      let _this = this;
      this.$confirm(
        `此操作不可撤销，确认删除项目${
          this.dialogForm.phases[+item].projectPhaseName
        }配置?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(function () {
        let id = _this.dialogForm.phases[item].id;
        if (id) {
          delPhase(id)
            .then((data) => {
              _this.$message({
                showClose: true,
                message: "删除成功",
                type: "success",
              });
              _this.editableTabsValue = item - 1 || 0;
              _this.removeTab(item);
            })
            .catch(function (err) {});
        } else {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          _this.removeTab(item);
        }
      });
    },
    changePhase(e, item) {
      debugger;
    },
    getSetting() {
      this.loading = true
      getSetting(this.info.id).then((res) => {
        this.loading = false
        res.data.data.phases = res.data.data.phases.map((v) => {
          v.projectPhaseDate = [v.projectPhaseStartDate, v.projectPhaseEndDate];
          v.tmpName = v.projectPhaseName;
          v.soils = v.soils.map((v) => {
            v.$cellEdit = true;
            return v;
          });
          return v;
        });

        this.dialogForm = res.data.data;
      });
    },
    async submit(e, done) {
      let valid = true; //验证是否有选择项目活动的选择项目活动的选择项目活动的选择项

      await this.dialogForm.phases.forEach((v, i) => {
        console.info(this.$refs[`phaseForm${i}`][0]); //验证选择项目活动的选择项目活动的选择项目活动的选择项目活动的选
        this.$refs[`phaseForm${i}`][0].validate((val, loading) => {
          console.info(val, loading);
          loading();
          if (!val) {
            valid = false;
          }
        });
      });

      console.info(valid);
      if (valid) {
        console.info(e);
        e.phases = e.phases.map((v) => {
          v.projectPhaseStartDate = v.projectPhaseDate[0];
          v.projectPhaseEndDate = v.projectPhaseDate[1];
          return v;
        });
        addSetting(e)
          .then((res) => {
            this.$message({
              showClose: true,
              message: "提交成功",
              type: "success",
            });
            done();
            this.$emit("update:visible", false);
          })
          .catch(() => {
            done();
          });
      } else {
        done();
      }
      //   this.dialogForm.phases.map((v) => {
      //     console.info(v);
      //   });
    },
    editTab(item, index) {
      this.isTabEdit = true;
      item.edit = true;
    },
    handleTabsAdd(e) {
      getDefault(this.info.id).then((res) => {
        this.isTabEdit = false;
        res.data.data.edit = true;
        res.data.data.projectPhaseName = "";
        res.data.data.soils = res.data.data.soils.map((v) => {
          v.$cellEdit = true;
          return v;
        });
        this.dialogForm.phases.push(res.data.data);
        this.editableTabsValue = this.dialogForm.phases.length - 1;
      });
    },
    removeTab(targetName) {
      let tabs = this.dialogForm.phases;
      let activeName = this.editableTabsValue;
      if (activeName == targetName) {
        tabs.forEach((tab, index) => {
          if (index == targetName) {
            let nextIndex = "";
            if (tabs[index + 1]) {
              nextIndex = index + 1;
            } else {
              nextIndex = index - 1;
            }
            let nextTab = tabs[nextIndex];

            if (nextTab) {
              activeName = nextIndex;
            }
          }
        });
      }
      this.editableTabsValue = activeName;
      this.dialogForm.phases.splice(+targetName, 1);
    },
    blurTab(item, index) {
      item.edit = false;
      if (this.isTabEdit) {
        if (!item.projectPhaseName) {
          item.projectPhaseName = item.tmpName;
        }
      } else {
        if (!item.projectPhaseName) {
          this.removeTab(index);
        } else {
          item.tmpName = item.projectPhaseName;
        }
      }
    },
    tabClick(e) {
      debugger;
    },
    cancelModal() {
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/.el-tabs__new-tab {
  color: #409eff;
  border-color: #409eff;
}
/deep/.avue-crud__pagination {
  padding: 0;
}
.group {
  display: flex;
  align-items: center;
  span {
    padding-left: 8px;
    text-align: left;
    height: 16px;
    line-height: 16px;
    font-size: 16px;
    color: #111528;
    border-left: 4px solid #4688f7;
  }
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  .menus {
    margin-left: auto;
  }
}
/deep/.avue-crud__menu {
  min-height: 0 !important;
}
</style>