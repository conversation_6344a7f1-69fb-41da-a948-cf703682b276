<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="700px"
      title="管理项目"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <avue-crud
          ref="crud"
          :data="tableData"
          :table-loading="tableLoading"
          :option="tableOption"
          @on-load="getPage"
          @refresh-change="refreshChange"
          @search-change="searchChange"
          @selection-change="selectionChange"
        >
          <template slot="platformBranchNsrmcSearch" slot-scope="scope">
            <el-input disabled :value="info.platformBranchNsrmc"></el-input>
          </template>
          <template slot="menuLeft" slot-scope="{ size }">
            <el-radio-group v-model="tabPosition" @change="changeTab">
              <el-radio-button label="2">未添加项目</el-radio-button>
              <el-radio-button label="1">已添加项目</el-radio-button>
            </el-radio-group>
          </template>
          <template slot="menu" slot-scope="scope">
            <el-button
              v-if="tabPosition == 2"
              type="text"
              icon="el-icon-plus"
              size="small"
              plain
              @click="add(scope.row, scope.index)"
            >
              添加
            </el-button>
            <el-button
              type="text"
              v-if="tabPosition == 1"
              icon="el-icon-delete"
              size="small"
              plain
              @click="del(scope.row, scope.index)"
            >
              删除
            </el-button>
          </template>
          <template slot="header" slot-scope="scope">
            <el-button
              style="vertical-align: top; margin-top: 8px; margin-left: 6px"
              type="primary"
              v-if="tabPosition == 2"
              :disabled="selectList.length == 0"
              icon="el-icon-plus"
              size="mini"
              @click="batchAdd(scope.row, scope.index)"
            >
              批量添加项目
            </el-button>
            <el-button
              style="vertical-align: top; margin-top: 8px; margin-left: 6px"
              v-if="tabPosition == 1"
              :disabled="selectList.length == 0"
              type="primary"
              size="mini"
              icon="el-icon-delete"
              @click="batchDel(scope.row, scope.index)"
            >
              批量删除项目
            </el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getDetail,checkStaffOnWork } from "@/api/chain/companystaff";
import { expotOut } from "@/util/down.js";
import { batchUpdatemember} from "@/api/chain/projectinfo";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  data() {
    return {
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: true,
        labelWidth: 150,
        searchLabelWidth: 86,
        searchSpan: 8,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchMenuSpan: 8,
        menu: true,
        selection: true,
        menuWidth: 100,
        page:false,
        column: [
          {
            label: "项目名称",
            prop: "projectName",
            search: true,
            searchSpan: 16,
            overHidden: true,
          },
        ],
      },
      tabPosition: "2",
      selectList: [],
    };
  },
  created() {},
  mounted() {
    this.getPage()
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.getPage(params);
      done();
    },
    getPage(params = {}) {
      console.log(params);
      console.log(this.paramsSearch);
      let data = Object.assign(params,this.paramsSearch)
      data.staffId = this.info.id;
      data.type = this.tabPosition;
      this.tableLoading = true;
      getDetail(data)
        .then((response) => {
          console.log("response", response);
          this.tableData = response.data.data
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange() {
      this.getPage();
    },
    selectionChange(e) {
      this.selectList = e;
    },
    add(row) {
      this.$confirm(`是否添加${row.projectName}？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res=>{
        console.log(row);
        console.log(this);
        this.handleUpdate([row.id],[this.info.id],1)
      }).catch(()=>{})
    },
    del(row) {
      this.checkStaffOnWork([row.id],[this.info.id],2,`是否删除${row.projectName}？`)
    },
    batchAdd() {
    let ids = this.selectList.map(item=>item.id)
    this.$confirm(`确定批量添加项目？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res=>{
        this.handleUpdate(ids,[this.info.id],1)
      }).catch(()=>{})
    },
    batchDel() {
      let ids = this.selectList.map(item=>item.id)
      this.checkStaffOnWork(ids,[this.info.id],2,`确定批量删除项目？`)
    },
    changeTab() {
      this.tableData = [];
      this.getPage();
    },
    checkStaffOnWork(projectInfoIdList,memberIdList,type,text){
      checkStaffOnWork({projectInfoIdList,memberIdList,type}).then(res=>{
        if(res.data.code==789){
          this.$confirm(res.data.msg, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.handleUpdate(projectInfoIdList,memberIdList,type)
          }).catch(() => {});
        }else{
           this.$confirm(text, "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(res=>{
              this.handleUpdate(projectInfoIdList,memberIdList,type)
            }).catch(()=>{})
        }
      })
    },
    handleUpdate(projectInfoIdList,memberIdList,type){
      console.log(projectInfoIdList);
      console.log(memberIdList);
      this.tableLoading = true
      batchUpdatemember(
        {projectInfoIdList,memberIdList,type}
      ).then(res=>{
        this.tableLoading = false
        this.getPage()
      }).catch(()=>{
        this.tableLoading = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .avue-crud__pagination {
  display: none;
}
</style>
