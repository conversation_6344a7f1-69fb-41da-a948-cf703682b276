{"name": "jyb-builders-pc", "version": "2.8.14", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "analyz": "npm run build", "lint": "vue-cli-service lint"}, "dependencies": {"@smallwei/avue": "2.8.4", "animate.css": "^3.7.2", "avue-plugin-print": "^1.0.0", "axios": "^0.19.0", "babel-polyfill": "^6.26.0", "benz-amr-recorder": "^1.0.14", "classlist-polyfill": "^1.2.0", "clipboard": "^2.0.4", "crypto-js": "^3.3.0", "echarts": "^5.4.2", "element-ui": "^2.15.6", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "js-cookie": "^2.2.0", "jspdf": "^3.0.1", "moment": "^2.24.0", "node-sass": "^4.14.0", "nprogress": "^0.2.0", "print-js": "^1.6.0", "script-loader": "^0.7.2", "sockjs-client": "^1.3.0", "stompjs": "^2.3.3", "throttle-debounce": "^5.0.0", "umy-ui": "^1.1.6", "vue": "2.6.10", "vue-amap": "^0.5.10", "vue-axios": "^2.1.4", "vue-clipboard2": "^0.3.0", "vue-count-to": "^1.0.13", "vue-cron": "^1.0.9", "vue-echarts": "^4.0.1", "vue-froala-wysiwyg": "^3.0.6", "vue-json-editor": "^1.2.3", "vue-json-tree-view": "^2.1.4", "vue-pdf": "^4.3.0", "vue-qr": "^2.1.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.1.1", "vue-video-player": "^5.0.2", "vuedraggable": "^2.24.3", "vuex": "^3.1.1", "xss": "^1.0.14"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.21.0", "@vue/cli-plugin-babel": "^3.10.0", "@vue/cli-plugin-eslint": "^3.10.0", "@vue/cli-service": "^3.10.0", "chai": "^4.1.2", "cnpm": "^7.1.0", "compression-webpack-plugin": "^5.0.1", "less": "^3.9.0", "less-loader": "^4.1.0", "mockjs": "^1.0.1-beta3", "sass-loader": "^7.0.1", "svg-sprite-loader": "^4.1.3", "terser-webpack-plugin": "^4.2.3", "vue-template-compiler": "2.6.10", "webpack-bundle-analyzer": "^4.5.0"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}