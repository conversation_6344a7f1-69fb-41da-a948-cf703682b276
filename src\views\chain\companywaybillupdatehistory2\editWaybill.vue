<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%"
               :title="'异常申请运单'+title"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <basic-container>
        <avue-form ref="editForm"
                   v-model="editForm"
                   :option="editOption"
                   @submit="((form,done)=>audit(2,done))">
          <template slot-scope="{disabled,size}"
                    slot="statusList">
            <div>
              <el-tag v-if="statusForm.overtimeStatus"
                      :type="statusForm.isOvertime==1?'danger':''">{{statusForm.overtimeStatus}}</el-tag>
              <el-tag v-if="statusForm.settleStatus"
                      :type="statusForm.isSettle==1?'danger':''"
                      style="margin-left:10px">{{statusForm.settleStatus}}</el-tag>
              <el-tag v-if="statusForm.payStatus"
                      :type="statusForm.isPay==1?'danger':''"
                      style="margin-left:10px">{{statusForm.payStatus}}</el-tag>
            </div>
          </template>
          <template slot-scope="{disabled,size}"
                    slot="flow">
            <div class="approvalInfo">
              <div class="info"
                   style="width:400px"
                   v-if="flowList&&flowList.length>0">
                <div class="approvalFlow">
                  <el-timeline>
                    <el-timeline-item :timestamp="item.companyPositionName"
                                      placement="top"
                                      class="myActive"
                                      color="#409eff"
                                      v-for="(item,index) in flowList"
                                      :key="index">
                      <i :class="item.isShow?'el-icon-arrow-up':'el-icon-arrow-down'"
                        @click="showMore(item)"
                        v-if="item.approveStatus==1"
                        style="cursor: pointer;position: absolute;right: -20px;top: 0px;font-size: 18px;"></i>
                      <div style="display:flex;align-items: center;justify-content: space-between;margin-bottom:10px">
                        <h4>{{item.approveUsername}}</h4><span>{{item.approveDatetime}}</span>
                      </div>
                      <el-input type="textarea"
                                v-if="item.approveRemark"
                                v-model="item.approveRemark"
                                :autosize="{ minRows: 3, maxRows: 8}"
                                disabled></el-input>
                      <div v-if="item.isShow">{{item.positionStaffName}}</div>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </div>
              <el-empty v-else></el-empty>
            </div>
          </template>
          <template slot="menuForm">
            <el-button v-if="title=='审核'"
                       icon="el-icon-close"
                       @click="audit(3)">驳回</el-button>
          </template>
        </avue-form>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  infoList,
  listByDriver,
  querySoilTypeByGarbage,
  auditUpdateHistory,
  listDriverCaptainTaskByProject,
  getProjectNewMatchOrderList,
} from "@/api/chain/companywaybill";
import {
  queryWaybillUpdateStatus,
  getFlowNode
} from "@/api/chain/companywaybillupdatehistory";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
    title: {
      type: String,
      default: "查看",
    },
  },
  data () {
    var validateRemark = (rule, value, callback) => {
      console.log(value);
      if (this.isRequired && (value === '' || !value)) {
        callback(new Error('请输入审批备注'));
      } else {
        callback();
      }
    };
    return {
      editForm: {
        tpModeAf: 2,
      },
      isRequired: false,
      editOption: {
        submitBtn: this.title == '审核',
        labelWidth: 150,
        submitText: "通过",
        position: "left",
        emptyBtn: false,
        cancelBtn: true,
        tabs: true,
        group: [
          {
            label: "挖机签单信息",
            arrow: false,
            prop: "group3",
            column: [
              {
                label: "签单员工变更前",
                prop: "inStaffId",
                cascader: ["machineId"],
                disabled: true,
                type: "select",
                props: {
                  label: "name",
                  value: "id",
                },
                dicData: [],
              },
              {
                label: "签单员工变更后",
                prop: "inStaffIdAf",
                type: "select",
                cascader: ["machineIdAf"],
                className: "red",
                disabled: true,
                props: {
                  label: "name",
                  value: "id",
                },
                dicData: [],
              },
              {
                label: "签单机械型号变更前",
                prop: "machineId",
                type: "select",
                props: {
                  label: "machineCode",
                  value: "id",
                },
                dicUrl:
                  "/chain/projectinfoext/getMachineListByPlatform?staffId={{key}}&projectInfoId=" + this.info.projectInfoId,
                disabled: true,
              },
              {
                label: "签单机械型号变更后",
                prop: "machineIdAf",
                type: "select",
                className: "red",
                disabled: true,
                props: {
                  label: "machineCode",
                  value: "id",
                },
                dicUrl:
                  "/chain/projectinfoext/getMachineListByPlatform?staffId={{key}}&projectInfoId=" + this.info.projectInfoId,
              },
              {
                label: "签单时间变更前",
                prop: "inDatetime",
                disabled: true,
              },
              {
                label: "签单时间变更后",
                prop: "inDatetimeAf",
                type: "datetime",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
                className: "red",
                disabled: true,
              },
              {
                label: "签单土质变更前",
                prop: "inSoilType",
                disabled: true,
              },
              {
                label: "签单土质变更后",
                prop: "inSoilTypeAf",
                className: "red",
                disabled: true,
                type: "select",
                props: {
                  label: "name",
                  value: "name",
                },
                dicData: [],
              },
              {
                label: "挖机班次日期变更前",
                prop: "inShiftTime",
                type: "date",
                valueFormat: "yyyy-MM-dd",
                disabled: true,
              },
              {
                label: "挖机班次日期变更后",
                prop: "inShiftTimeAf",
                type: "date",
                valueFormat: "yyyy-MM-dd",
                className: "red",
                disabled: true,
              },
              {
                label: "签单班次变更前",
                prop: "inShiftType",
                type: "select", // 下拉选择
                // dicUrl:"/chain/projectinfo/getShiftOfProject",
                // dicFormatter: (res) => {
                //   return res.data.map((item)=>{
                //     return {
                //       label:item,
                //       value:item,
                //     }
                //   })
                // },
                disabled: true,
              },
              {
                label: "签单班次变更后",
                prop: "inShiftTypeAf",
                className: "red",
                disabled: true,
                type: "select",
                // dicUrl:"/chain/projectinfo/getShiftOfProject",
                // dicFormatter: (res) => {
                //   return res.data.map((item)=>{
                //     return {
                //       label:item,
                //       value:item,
                //     }
                //   })
                // },
              },
              {
                label: "签单备注变更前",
                prop: "inRemark",
                disabled: true,
              },
              {
                label: "签单备注变更后",
                prop: "inRemarkAf",
                className: "red",
                disabled: true,
              },
              {
                label: "凭证类型变更前",
                prop: "inWeightUnit",
                type: "radio",
                dicData: [
                  {
                    label: "车",
                    value: "车",
                  },
                  {
                    label: "吨",
                    value: "吨",
                  },
                ],
                disabled: true,
              },
              {
                label: "凭证类型变更后",
                prop: "inWeightUnitAf",
                type: "radio",
                className: "red",
                disabled: true,
                dicData: [
                  {
                    label: "车",
                    value: "车",
                  },
                  {
                    label: "吨",
                    value: "吨",
                  },
                ],
                control: (val) => {
                  console.log(val);
                  return {
                    inWeighInstrumentPicture: {
                      //泥尾票土质
                      display: val == "吨" || this.info.inWeightUnit == "吨",
                    },
                    inWeighInstrumentPictureAf: {
                      //泥尾票土质
                      display: val == "吨" || this.info.inWeightUnit == "吨",
                    },
                  };
                },
              },
              {
                label: "签单照片变更前",
                prop: "inPicture",
                type: "upload",
                span: 24,
                listType: "picture-card",
                dataType: "string",
                propsHttp: {
                  url: "link",
                },
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=",
                disabled: true,
                readonly: true,
              },
              {
                label: "签单照片变更后",
                prop: "inPictureAf",
                disabled: true,
                type: "upload",
                span: 24,
                listType: "picture-card",
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=",
                dataType: "string",
                propsHttp: {
                  url: "link",
                },
              },
              {
                label: "称重仪表照片变更前",
                prop: "inWeighInstrumentPicture",
                type: "upload",
                span: 24,
                listType: "picture-card",
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=",
                propsHttp: {
                  url: "link",
                  name: "link",
                },
                dataType: "string",
                disabled: true,
                readonly: true,
              },
              {
                label: "称重仪表照片变更后",
                prop: "inWeighInstrumentPictureAf",
                type: "upload",
                span: 24,
                listType: "picture-card",
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=",
                propsHttp: {
                  url: "link",
                  name: "link",
                },
                dataType: "string",
                disabled: true,
                readonly: true,
              },
              {
                label: "审核备注",
                prop: "auditRemark",
                disabled: this.title != '审核',
                display: this.title == '审核',
                // type: 'textarea',
                span: 24,
                maxlength: 100,
                rules: [
                  {
                    validator: validateRemark,
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "运单状态",
                prop: "statusList",
                span: 24,
              },
            ],
          },
          {
            label: "出场签单信息",
            arrow: false,
            prop: "group4",
            column: [
              {
                label: "司机手机号变更前",
                prop: "mobile",
                disabled: true,
              },
              {
                label: "司机手机号变更后",
                prop: "mobileAf",
                className: "red",
                disabled: true,
                maxlength: 11,
              },
              {
                label: "司机车牌变更前",
                prop: "truckCode",
                disabled: true,
              },
              {
                label: "司机车牌变更后",
                prop: "truckCodeAf",
                className: "red",
                disabled: true,
              },
              {
                label: "状态变更前",
                prop: "status",
                disabled: true,
                type: "select", // 下拉选择
                dicData: [
                  {
                    label: "挖机签单已完成",
                    value: "1",
                  },
                  {
                    label: "出口签单已完成",
                    value: "2",
                  },
                  {
                    label: "司机签单已完成",
                    value: "3",
                  },
                  {
                    label: "司机卸土已完成",
                    value: "4",
                  },
                  {
                    label: "运单已取消",
                    value: "5",
                  },
                ],
              },
              {
                label: "状态变更后",
                prop: "statusAf",
                className: "red",
                disabled: true,
                type: "select", // 下拉选择
                dicData: [
                  {
                    label: "挖机签单已完成",
                    value: "1",
                  },
                  {
                    label: "出口签单已完成",
                    value: "2",
                  },
                  {
                    label: "运单已取消",
                    value: "5",
                  },
                ],
              },
              {
                label: "车队长任务变更前",
                prop: "driverCaptainTaskId",
                disabled: true,
                type: "select", // 下拉选择
                props: {
                  label: "name",
                  value: "task_id",
                },
              },
              {
                label: "车队长任务变更后",
                prop: "driverCaptainTaskIdAf",
                type: "select", // 下拉选择
                props: {
                  label: "name",
                  value: "task_id",
                },
                className: "red",
                disabled: true,
              },
              {
                label: "签单员工变更前",
                prop: "goStaffId",
                disabled: true,
                type: "select",
                props: {
                  label: "name",
                  value: "id",
                },
                dicData: [],
              },
              {
                label: "签单员工变更后",
                prop: "goStaffIdAf",
                className: "red",
                disabled: true,
                type: "select",
                props: {
                  label: "name",
                  value: "id",
                },
                dicData: [],
              },
              {
                label: "签单时间变更前",
                prop: "goDatetime",
                disabled: true,
              },
              {
                label: "签单时间变更后",
                prop: "goDatetimeAf",
                type: "datetime",
                className: "red",
                disabled: true,
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "签单土质变更前",
                prop: "goSoilType",
                disabled: true,
                type: "select",
                props: {
                  label: "name",
                  value: "name",
                },
                dicData: [],
              },
              {
                label: "签单土质变更后",
                prop: "goSoilTypeAf",
                className: "red",
                disabled: true,
                type: "select",
                props: {
                  label: "name",
                  value: "name",
                },
                dicData: [],
              },
              {
                label: "出场班次日期变更前",
                prop: "goShiftTime",
                type: "date",
                valueFormat: "yyyy-MM-dd",
                disabled: true,
              },
              {
                label: "出场班次日期变更后",
                prop: "goShiftTimeAf",
                type: "date",
                valueFormat: "yyyy-MM-dd",
                className: "red",
                disabled: true,
              },
              {
                label: "签单班次变更前",
                prop: "goShiftType",
                type: "select", // 下拉选择
                // dicUrl:"/chain/projectinfo/getShiftOfProject",
                // dicFormatter: (res) => {
                //   return res.data.map((item)=>{
                //     return {
                //       label:item,
                //       value:item,
                //     }
                //   })
                // },
                disabled: true,
              },
              {
                label: "签单班次变更后",
                prop: "goShiftTypeAf",
                className: "red",
                disabled: true,
                type: "select",
                // dicUrl:"/chain/projectinfo/getShiftOfProject",
                // dicFormatter: (res) => {
                //   return res.data.map((item)=>{
                //     return {
                //       label:item,
                //       value:item,
                //     }
                //   })
                // },
              },
              {
                label: "运输类型变更前",
                prop: "tpMode",
                type: "select",
                props: {
                  label: "itemName",
                  value: "itemValue",
                },
                dicUrl:
                  "/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode",
                disabled: true,
              },
              {
                label: "运输类型变更后",
                prop: "tpModeAf",
                className: "red",
                disabled: true,
                type: "select",
                props: {
                  label: "itemName",
                  value: "itemValue",
                },
                dicUrl:
                  "/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode",
              },
              {
                label: "泥尾变更前",
                prop: "garbageId",
                type: "select",
                typeformat (item, label, value) {
                  return item.isMatch == 1 ? `(撮合)${item[label]}` : `${item[label]}`
                },
                props: {
                  label: "name",
                  value: "id",
                },
                dicData: [],
                disabled: true,
              },
              {
                label: "泥尾变更后",
                prop: "garbageIdAf",
                className: "red",
                disabled: true,
                type: "select",
                typeformat (item, label, value) {
                  return item.isMatch == 1 ? `(撮合)${item[label]}` : `${item[label]}`
                },
                props: {
                  label: "name",
                  value: "id",
                },
                dicData: [],
                filterable: true, //是否可以搜索
              },
              // {
              //   label: "撮合订单变更前",
              //   prop: "matchOrderId",
              //   type: "select",
              //   props: {
              //     label: "orderNo",
              //     value: "id",
              //   },
              //   dicData: [],
              //   disabled: true,
              //   display:!!this.info.matchOrderIdAf||!!this.info.matchOrderId,
              // },
              // {
              //   label: "撮合订单变更后",
              //   prop: "matchOrderIdAf",
              //   type: "select",
              //   props: {
              //     label: "orderNo",
              //     value: "id",
              //   },
              //   className: "red",
              //   dicData: [],
              //   disabled: true,
              //   display:!!this.info.matchOrderIdAf||!!this.info.matchOrderId,
              // },
              {
                label: "泥尾票土质变更前",
                prop: "goTicketSoilType",
                type: "select",
                dicData: [],
                disabled: true,
              },
              {
                label: "泥尾票土质变更后",
                prop: "goTicketSoilTypeAf",
                className: "red",
                disabled: true,
                type: "select",
                dicData: [],
              },
              {
                label: "票号变更前",
                prop: "ticketNoText",
                disabled: true,
              },
              {
                label: "票号变更后",
                prop: "ticketNoAf",
                className: "red",
                disabled: true,
              },
              {
                label: "泥尾票单价变更前",
                prop: "ticketPrice",
                disabled: true,
              },
              {
                label: "泥尾票单价变更后",
                prop: "ticketPriceAf",
                className: "red",
                disabled: true,
              },
              {
                label: "资源类型变更前",
                prop: "resourceType",
                type: "select",
                props: {
                  label: "itemName",
                  value: "itemValue",
                },
                dicUrl: `/chain/systemdictionaryitem/listDictionaryItem?dictionary=resource_type`,
                disabled: true,
              },
              {
                label: "资源类型变更后",
                prop: "resourceTypeAf",
                className: "red",
                disabled: true,
                type: "select",
                props: {
                  label: "itemName",
                  value: "itemValue",
                },
                dicUrl: `/chain/systemdictionaryitem/listDictionaryItem?dictionary=resource_type`,
              },
              {
                label: "核销方式变更前",
                prop: "checkMethod",
                type: "select",
                dicData: [
                  {
                    label: "收磅单",
                    value: "1",
                  },
                  {
                    label: "资源地",
                    value: "2",
                  },
                ],
                disabled: true,
              },
              {
                label: "核销方式变更后",
                prop: "checkMethodAf",
                className: "red",
                disabled: true,
                type: "select",
                dicData: [
                  {
                    label: "收磅单",
                    value: "1",
                  },
                  {
                    label: "资源地",
                    value: "2",
                  },
                ],
              },
              {
                label: "资源地变更前",
                prop: "resourceCustomerId",
                type: "select",
                props: {
                  label: "resourceType",
                  value: "id",
                },
                typeformat (item, label, value) {
                  return `${item["customerName"]}-${item["resourceType"]}`;
                },
                dicData: [],
                disabled: true,
              },
              {
                label: "资源地变更后",
                prop: "resourceCustomerIdAf",
                type: "select",
                props: {
                  label: "resourceType",
                  value: "id",
                },
                typeformat (item, label, value) {
                  return `${item["customerName"]}-${item["resourceType"]}`;
                },
                dicData: [],
                className: "red",
                disabled: true,
              },
              {
                label: "单位变更前",
                prop: "weightUnit",
                type: "select",

                dicUrl: `/chain/systemdictionaryitem/listDictionaryItem?dictionary=weight_unit`,
                props: {
                  label: "itemName",
                  value: "itemValue",
                },
                disabled: true,
              },
              {
                label: "单位变更后",
                prop: "weightUnitAf",
                disabled: true,
                type: "select",
                className: "red",
                dicUrl: `/chain/systemdictionaryitem/listDictionaryItem?dictionary=weight_unit`,
                props: {
                  label: "itemName",
                  value: "itemValue",
                },
                // change: (e) => {
                //   if (e.value == "车") {
                //     this.editForm.weightTonsAf = 1;
                //   }
                // },
                // control: (val) => {
                //   console.info(val)
                //   return {
                //     weightTonsAf: {
                //       disabled: val == "车",
                //     },
                //   };
                // },
              },
              {
                label: "数量变更前",
                prop: "weightTons",
                controls: false,
                type: "number",
                precision: 2,
                minRows: 0,
                disabled: true,
              },
              {
                label: "数量变更后",
                prop: "weightTonsAf",
                disabled: true,
                controls: false,
                precision: 2,
                minRows: 0,
                className: "red",
                change: (val) => {
                  // if (this.editForm.unitPriceAf && val.value) {
                  //   this.editForm.payeePriceAf = (
                  //     this.editForm.unitPriceAf * val.value
                  //   ).toFixed(2);
                  // }
                },
              },

              {
                label: "单价变更前",
                prop: "unitPrice",
                disabled: true,
              },
              {
                label: "单价变更后",
                prop: "unitPriceAf",
                disabled: true,
                className: "red",
                change: (val) => {
                  // if (this.editForm.weightTonsAf && val.value) {
                  //   this.editForm.payeePriceAf = (
                  //     this.editForm.weightTonsAf * val.value
                  //   ).toFixed(2);
                  // }
                },
              },
              {
                label: "价格变更前",
                prop: "payeePrice",
                disabled: true,
                // display:this.info.isCubicPrice==1
              },
              {
                label: "价格变更后",
                prop: "payeePriceAf",
                disabled: true,
                className: "red",
                // display:this.info.isCubicPrice==1
              },
              {
                label: "入场重量变更前",
                prop: "inWeight",
                disabled: true,
                // display:this.info.isCubicPrice==1
              },
              {
                label: "入场重量变更后",
                prop: "inWeightAf",
                disabled: true,
                className: "red",
                // display:this.info.isCubicPrice==1
              },
              {
                label: "出场重量变更前",
                prop: "outWeight",
                disabled: true,
                // display:this.info.isCubicPrice==1
              },
              {
                label: "出场重量变更后",
                prop: "outWeightAf",
                disabled: true,
                className: "red",
                // display:this.info.isCubicPrice==1
              },
              {
                label: "结算卡号变更前",
                prop: "settleCardNo",
                disabled: true,
                display: this.info.settleType == 3 || this.info.settleType == 4,
              },
              {
                label: "结算卡号变更后",
                prop: "settleCardNoAf",
                display: this.info.settleType == 3 || this.info.settleType == 4,
                disabled: true,
                className: "red",
              },
              {
                label: "出口签单车型变更前",
                prop: "goVehicleType",
                disabled: true,
                display: this.info.isGoVehicleType == 1,
              },
              {
                label: "出口签单车型变更后",
                prop: "goVehicleTypeAf",
                display: this.info.isGoVehicleType == 1,
                disabled: true,
                className: "red",
              },
              {
                label: "地块变更前",
                prop: "landParcel",
                disabled: true,
              },
              {
                label: "地块变更后",
                prop: "landParcelAf",
                className: "red",
                disabled: true,
              },
              {
                label: "车队名称变更前",
                prop: "captainFleetName",
                disabled: true,
              },
              {
                label: "车队名称变更后",
                prop: "captainFleetNameAf",
                className: "red",
                disabled: true,
              },
              {
                label: "签单备注变更前",
                prop: "goRemark",
                disabled: true,
              },
              {
                label: "签单备注变更后",
                prop: "goRemarkAf",
                className: "red",
                disabled: true,
              },
              {
                label: "签单照片变更前",
                prop: "goPicture",
                type: "upload",
                span: 24,
                listType: "picture-card",
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=",
                dataType: "string",
                propsHttp: {
                  url: "link",
                },
                disabled: true,
                readonly: true,
              },
              {
                label: "签单照片变更后",
                prop: "goPictureAf",
                type: "upload",
                span: 24,
                listType: "picture-card",
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=",
                dataType: "string",
                propsHttp: {
                  url: "link",
                },
                disabled: true,
              },
              {
                label: "磅单票据变更前",
                prop: "poundbillUrl",
                type: "upload",
                span: 24,
                listType: "picture-card",
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=",
                dataType: "string",
                propsHttp: {
                  url: "link",
                },
                disabled: true,
                readonly: true,
              },
              {
                label: "磅单票据变更后",
                prop: "poundbillUrlAf",
                type: "upload",
                span: 24,
                listType: "picture-card",
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=",
                dataType: "string",
                propsHttp: {
                  url: "link",
                },
                disabled: true,
              },
              {
                label: "泥尾票照片变更前",
                prop: "ticketImg",
                type: "upload",
                // span: 24,
                listType: "picture-img",
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=ticketImg/",
                propsHttp: {
                  url: "link",
                },
                disabled: true,
                readonly: true,
              },
              {
                label: "泥尾票照片变更后",
                prop: "ticketImgAf",
                type: "upload",
                // span: 24,
                listType: "picture-img",
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=ticketImg/",
                propsHttp: {
                  url: "link",
                },
                disabled: true,
              },
              {
                label: "拍照票据凭证变更前",
                prop: "imgUrl",
                type: "upload",
                // span: 24,
                listType: "picture-img",
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=imgUrl/",
                propsHttp: {
                  url: "link",
                },
                disabled: true,
                readonly: true,
              },
              {
                label: "拍照票据凭证变更后",
                prop: "imgUrlAf",
                type: "upload",
                // span: 24,
                listType: "picture-img",
                tip: "只能上传jpg/png文件",
                action: "/upms/file/upload?fileType=image&dir=imgUrl/",
                propsHttp: {
                  url: "link",
                },
                disabled: true,
              },
              {
                label: "审核备注",
                prop: "auditRemark",
                disabled: this.title != '审核',
                display: this.title == '审核',
                // type: 'textarea',
                span: 24,
                maxlength: 100,
                rules: [
                  {
                    validator: validateRemark,
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "运单状态",
                prop: "statusList",
                span: 24,
              },
            ],
          },
          {
            label: "变更原因",
            arrow: false,
            prop: "group5",
            column: [
              {
                label: "变更原因",
                prop: "remark",
                // type: 'textarea',
                span: 24,
                className: "red",
                disabled: true,
                maxlength: 50,
              },
              {
                label: "审核备注",
                prop: "auditRemark",
                disabled: this.title != '审核',
                display: this.title == '审核',
                // type: 'textarea',
                span: 24,
                maxlength: 100,
                rules: [
                  {
                    validator: validateRemark,
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "运单状态",
                prop: "statusList",
                span: 24,
              },
            ],
          },
        ],
      },
      entranceForm: {
        label: "入场签单信息",
        arrow: false,
        prop: "group2",
        column: [
          {
            label: "签单员工变更前",
            prop: "entranceStaffId",
            disabled: true,
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
          },
          {
            label: "签单员工变更后",
            prop: "entranceStaffIdAf",
            className: "red",
            disabled: true,
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
          },
          {
            label: "签单时间变更前",
            prop: "entranceDatetime",
            disabled: true,
          },
          {
            label: "签单时间变更后",
            prop: "entranceDatetimeAf",
            type: "datetime",
            className: "red",
            disabled: true,
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "入场班次日期变更前",
            prop: "entranceShiftTime",
            disabled: true,
          },
          {
            label: "入场班次日期变更后",
            prop: "entranceShiftTimeAf",
            type: "datetime",
            className: "red",
            disabled: true,
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "入场班次变更前",
            prop: "entranceShiftType",
            disabled: true,
          },
          {
            label: "入场班次变更后",
            prop: "entranceShiftTypeAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单备注变更前",
            prop: "entranceRemark",
            disabled: true,
          },
          {
            label: "签单备注变更后",
            className: "red",
            disabled: true,
            prop: "entranceRemarkAf",
          },
          {
            label: "签单照片变更前",
            prop: "entrancePicture",
            type: "upload",
            span: 24,
            listType: "picture-card",
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
            disabled: true,
            readonly: true,
          },
          {
            label: "签单照片变更后",
            prop: "entrancePictureAf",
            type: "upload",
            span: 24,
            listType: "picture-card",
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
            disabled: true,
          },
          {
            label: "审核备注",
            prop: "auditRemark",
            disabled: this.title != '审核',
            display: this.title == '审核',
            // type: 'textarea',
            span: 24,
            maxlength: 100,
            rules: [
              {
                validator: validateRemark,
                trigger: "blur",
              },
            ],
          },
          {
            label: "运单状态",
            prop: "statusList",
            span: 24,
          },
        ],
      },
      flowForm: {
        label: "审核流程",
        arrow: false,
        prop: "group6",
        column: [
          {
            label: "",
            prop: "flow",
            labelWidth: 0,
            span: 24,
          },
        ],
      },
      statusForm: {},
      flowList: []
    };
  },
  created () { },
  mounted: function () {
    this.editForm = this.info;
    console.log(this.editForm);
    //配置了入场签单
    if (this.editForm.isEntranceWaybill == 1) {
      this.editOption.group.splice(0, 0, this.entranceForm)
    }
    this.editForm.ticketNoText = this.editForm.ticketNo&&this.editForm.manualSelectTicket=='0'?`${this.editForm.ticketNo}(系统分配)`:this.editForm.ticketNo||''
    //查看增加审核流程
    if (this.title != '审核') {
      this.editOption.group.push(this.flowForm)
    }
    infoList({
      projectInfoId: this.editForm.projectInfoId,
      selectType: 6,
      garbageDelStatus: 2,//有这个参数就包括查询无效泥尾
    }).then((res) => {
      this.$refs.editForm.updateDic("inStaffId", res.data.data.inList);
      this.$refs.editForm.updateDic("inStaffIdAf", res.data.data.inList);
      this.$refs.editForm.updateDic("goStaffId", res.data.data.goList);
      this.$refs.editForm.updateDic("goStaffIdAf", res.data.data.goList);
      this.$refs.editForm.updateDic("inSoilTypeAf", res.data.data.soilList);
      this.$refs.editForm.updateDic("goSoilTypeAf", res.data.data.soilList);
      this.$refs.editForm.updateDic("garbageId", res.data.data.garbageList);
      this.$refs.editForm.updateDic("garbageIdAf", res.data.data.garbageList);
      //配置了入场签单
      if (this.editForm.isEntranceWaybill == 1) {
        this.$refs.editForm.updateDic("entranceStaffId", res.data.data.goList);
        this.$refs.editForm.updateDic(
          "entranceStaffIdAf",
          res.data.data.goList
        );
      }
    });
    listByDriver({ companyAuthId: this.editForm.companyAuthId }).then((res) => {
      this.$refs.editForm.updateDic("resourceCustomerId", res.data.data);
      this.$refs.editForm.updateDic("resourceCustomerIdAf", res.data.data);
    });
    querySoilTypeByGarbage({
      garbageId: this.editForm.garbageId,
      companyAuthId: this.editForm.companyAuthId,
    }).then((res) => {
      this.$refs.editForm.updateDic("goTicketSoilType", res.data.data);
      // this.$refs.editForm.updateDic("goTicketSoilTypeAf",res.data.data)
    });
    //如果有撮合订单，需要先搜索一遍
    if (this.editForm.matchOrderId) {
      getProjectNewMatchOrderList({
        garbageId: this.editForm.garbageId,
        pId: this.editForm.projectInfoId
      }).then(res => {
        this.$refs.editForm.updateDic("matchOrderId", res.data.data);
      })
    }
    if (this.editForm.matchOrderIdAf) {
      getProjectNewMatchOrderList({
        garbageId: this.editForm.garbageIdAf,
        pId: this.editForm.projectInfoId
      }).then(res => {
        this.$refs.editForm.updateDic("matchOrderIdAf", res.data.data);
      })
    }
    queryWaybillUpdateStatus({ companyWaybillId: this.info.companyWaybillId }).then(res => {
      this.statusForm = res.data.data
    })
    listDriverCaptainTaskByProject({ projectInfoId: this.info.projectInfoId }).then(
      (res) => {
        console.log(res);
        this.$refs.editForm.updateDic("driverCaptainTaskId", res.data.data);
        this.$refs.editForm.updateDic("driverCaptainTaskIdAf", res.data.data);
      }
    );
    //获取审批流程信息
    getFlowNode(this.info.id).then(res => {
      this.flowList = res.data.data
    })
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    audit (type, done) {
      if (type == 3) {
        this.isRequired = true
        console.log(this.$refs.editForm);
        this.$refs.editForm.validateField("auditRemark")
        this.isRequired = false
        if (!this.editForm.auditRemark) {
          this.$message.error("请输入审核备注")
          done && done()
          return false
        }
      }
      // type 2审核通过  3驳回
      this.$confirm(`确认${type == 2 ? '通过' : '驳回'}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        auditUpdateHistory({ id: this.editForm.id, status: type, auditRemark: this.editForm.auditRemark }).then(res => {
          loading.close()
          this.$message({
            showClose: true,
            message: "操作成功",
            type: "success",
          });
          this.$emit("update:visible", false);
          this.$emit("searchData");
        }).catch(() => {
          loading.close()
        })
      }).catch(() => { });
      done && done()
    },
    showMore (item) {
      console.log(item);
      this.$set(item, 'isShow', !item.isShow)
      // item.isShow = !item.isShow
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .drawerHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
/deep/ .red {
  .el-form-item__content input {
    color: red !important;
  }
  .is-checked .el-radio__label {
    color: red !important;
  }
  .is-checked .el-radio__inner {
    border-color: red;
    &::after {
      background-color: red;
    }
  }
}
.el-timeline-item__timestamp {
  color: #333;
}
.el-timeline-item__content {
  color: #909399;
}
.myActive .el-timeline-item__tail {
  border-left: 2px solid #409eff;
}
</style>
