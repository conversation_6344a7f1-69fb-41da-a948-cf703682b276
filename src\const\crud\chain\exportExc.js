import moment from "moment"; //导入文件

export const tableOption1 = {
  dialogDrag: false,
  border: true,
  indexLabel: "序号",
  stripe: false,
  align: "center",
  searchShow: false,
  labelWidth: 150,
  // height:'auto',
  excelBtn: false,
  header: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  // index: true,
  menu: false,
  refreshBtn: false,
  column: [
    {
      label:'土石方运输车辆进出登记台账',
      children:[
        {
          label: "项目名称",
          children: [
            {
              label: "序号",
              prop: `index`,
              width:70,
            },
          ],
        },
        {
          label: "",
          prop: "test8",
          children: [
            {
              label: "车牌号",
              prop: "goTruckCode",
              width:80,
              overHidden:true,
            },
            {
              label: "进场时间",
              prop: "test3",
              width:70,
              overHidden:true,
            },
            {
              label: "挖机签单日期",
              prop: "inDate",
              width:96,
              overHidden:true,
            },
          ],
        },
        {
          label: "班次",
          children: [
            {
              label: "挖机签单时间",
              prop: "inTime",
              width:96,
              overHidden:true,
            },
          ],
        },
        {
          label: " ",
          prop: "test9",
          children: [
            {
              label: "出场日期",
              prop: "goDate",
              width:96,
              overHidden:true,
            },
            {
              label: "出场时间",
              prop: "goTime",
              width:96,
              overHidden:true,
            },
          ],
        },
        {
          label: "日期",
          children: [
            {
              label: "司机姓名",
              prop: "goDriverUserName",
              width:80,
              overHidden:true,
            },
          ],
        },
        {
          label: "-",
          prop: "test10",
          children: [
            {
              label: "土石方量(m³)",
              prop: "goCapacity",
              width:110,
              overHidden:true,
            },
            {
              label: "是否超高超载",
              prop: "overload",
              type: 'select',
              dicData:[
                {
                    label:'否',
                    value:'',
                }
              ],
              width:96,
              overHidden:true,
            },
            {
              label: "是否带泥上路",
              prop: "bareRooted",
              type: 'select',
              dicData:[
                {
                    label:'否',
                    value:'',
                }
              ],
              width:96,
              overHidden:true,
            },
            {
              label: "出场土质",
              prop: "goSoilType",
              width:80,
              overHidden:true,
            },
            {
              label: "运输方式",
              prop: "tpMode",
              width:70,
              overHidden:true,
            },
            {
              label: "泥尾",
              prop: "garbageName",
              width:120,
              overHidden:true,
            },
            {
              label: "记录人",
              prop: "goStaffName",
              width:70,
              overHidden:true,
            },
            {
              label: "出场备注",
              prop: "goRemark",
              width:80,
              overHidden:true,
            },
            {
              label: "运单号",
              prop: "no",
              width:160,
              overHidden:true,
            },
          ],
        },
      ],
    }
  ]
};
export const tableOption2 = {
  dialogDrag: false,
  border: true,
  indexLabel: "序号",
  stripe: false,
  align: "center",
  searchShow: false,
  labelWidth: 150,
  // height:'auto',
  excelBtn: false,
  header: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  // index: true,
  menu: false,
  refreshBtn: false,
  column: [
    {
      label:'挖机装车表',
      children:[
        {
          label: "项目名称",
          children:[
            {
                label:'挖机号',
                children: [
                  {
                    label: "序号",
                    prop: `index`,
                    width:70,
                  },
                ],
            }
          ]
        },
        {
          label: "",
          prop: "test1",
          children: [
            {
              label: "",
              prop: "test2",
              children: [
                {
                  label: "挖机签单员姓名",
                  prop: `inStaffName`,
                  minWidth:108,
                  overHidden:true,
                },
                {
                  label: "车牌号",
                  prop: `goTruckCode`,
                  minWidth:90,
                  overHidden:true,
                },
                {
                  label: "装车日期",
                  prop: `goDatetime`,
                  minWidth:120,
                  overHidden:true,
                },
              ],
            },
          ],
        },
        {
          label: "班次",
          children: [
            {
              label: "日期",
              children: [
                {
                  label: "装车时间",
                  prop: "goDatetime",
                  minWidth:140,
                  overHidden:true,
                },
              ],
            },
          ],
        },
        {
          label: " ",
          prop: "test3",
          children: [
            {
              label: "-",
              children: [
                {
                  label: "类别（土质）",
                  prop: "goSoilType",
                  minWidth:100,
                  overHidden:true,
                },
              ],
            },
          ],
        },
      ],
    }
  ]
};
export const tableOption3 = {
  dialogDrag: false,
  border: true,
  indexLabel: "序号",
  stripe: false,
  align: "center",
  searchShow: false,
  labelWidth: 150,
  // height:'auto',
  excelBtn: false,
  header: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  // index: true,
  menu: false,
  refreshBtn: false,
  column: [
    {
      label:'泥尾台账',
      children:[
        {
          label: "项目名称",
          children:[
            {
                label:'车牌号',
                prop: `车牌号`,
                overHidden:true,
            }
          ]
        },
        {
          label: "    ",
          prop: "a",
          children: [
            {
              label: "放飞",
              prop: "放飞",
              overHidden:true,
            },
          ],
        },
        {
          label: "日期",
          children: [
            {
              label: "",
              prop: "e",
              overHidden:true,
            },
          ],
        },
        {
          label: "-",
          prop: "b",
          children: [
            {
              label: "",
              prop: "f",
              overHidden:true,
            },
          ],
        },
        {
          label: "班次",
          children: [
            {
              label: "",
              prop: "g",
              overHidden:true,
            },
          ],
        },
        {
          label: " ",
          prop: "c",
          children: [
            {
              label: "",
              prop: "h",
              overHidden:true,
            },
          ],
        },
        {
          label: "单位(车)",
          children: [
            {
              label: "",
              prop: "i",
              overHidden:true,
            },
          ],
        },
        {
          label: "",
          prop: "d",
          children: [
            {
              label: "",
              prop: "j",
              overHidden:true,
            },
          ],
        },
      ],
    }
  ]
};
export const tableOption4 = {
  dialogDrag: false,
  border: true,
  stripe: false,
  align: "center",
  searchShow: false,
  labelWidth: 150,
  // height:'auto',
  excelBtn: false,
  header: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  // index: true,
  menu: false,
  refreshBtn: false,
  column: [
    {
      label: "泥尾统计运费",
      children:[
        {
          label: "日期",
          children:[
            {
                label:'泥尾',
                prop: `garbageName`,
                overHidden:true,
            }
          ]
        },
        {
          label: "-",
          prop: "a",
          overHidden:true,
          children: [
            {
              label: "单价",
              prop: "price",
              overHidden:true,
            },
            {
              label: "车数",
              prop: "count",
              overHidden:true,
            },
            {
              label: "运费金额",
              prop: "freight",
              overHidden:true,
            },
          ],
        },
        {
          label: "项目",
          children: [
            {
              label: "泥牌",
              prop: "garbagePlate",
              overHidden:true,
            },
          ],
        },
        {
          label: "",
          prop: "b",
          children: [
            {
              label: "单价(元)",
              prop: "unitPrice",
              overHidden:true,
            },
          ],
        },
        {
          label: "班次",
          children: [
            {
              label: "泥牌金额",
              prop: "garbagePlatePrice",
              overHidden:true,
            },
          ],
        },
        {
          label: " ",
          prop: "c",
          children: [
            {
              label: "合计",
              prop: "sum",
              overHidden:true,
            },
          ],
        },

      ],
    }
  ]
};
export const tableOption5 = {
  dialogDrag: false,
  border: true,
  stripe: false,
  align: "center",
  searchShow: false,
  labelWidth: 150,
  // height:'auto',
  excelBtn: false,
  header: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  // index: true,
  menu: false,
  refreshBtn: false,
  column: [
    {
      label: "土票领发统计",
      children:[
        {
          label: "工地名称",
          children:[
            {
                label:'序号',
                prop: `index`,
                overHidden:true,
            }
          ]
        },
        {
          label: "",
          prop: "a",
          children: [
            {
              label: "土票名称(泥尾)",
              prop: "garbageName",
              overHidden:true,
            },
          ],
        },
        {
          label: "筛选日期",
          children: [
            {
              label: "转票人",
              prop: "e",
              overHidden:true,
            },
          ],
        },
        {
          label: "-",
          children: [
            {
              label: "日领土票",
              prop: "dayReceive",
              overHidden:true,
            },
          ],
        },
        {
          label: "班次",
          children: [
            {
              label: "日发土票",
              prop: "count",
              overHidden:true,
            },
          ],
        },
        {
          label: " ",
          children: [
            {
              label: "剩余土票",
              prop: "g",
              overHidden:true,
            },
          ],
        },
        {
          label: "  ",
          prop: "c",
          children: [
            {
              label: "备注",
              prop: "h",
              overHidden:true,
            },
          ],
        },

      ],
    }
  ]
};
export const tableOption6 = {
  dialogDrag: false,
  border: true,
  stripe: false,
  align: "center",
  searchShow: false,
  labelWidth: 150,
  // height:'auto',
  excelBtn: false,
  header: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  // index: true,
  menu: false,
  refreshBtn: false,
  column: [
    {
      label: "机械台班装车数统计",
      children:[
        {
          label: "项目名称",
          children:[
            {
                label:'序号',
                prop: `index`,
                minWidth:106,
                overHidden:true,
            }
          ]
        },
        {
          label: "",
          prop: "a",
          children: [
            {
              label: "机主姓名",
              prop: "garbageName",
              minWidth:80,
              overHidden:true,
            },
          ],
        },
        {
          label: "日期",
          children: [
            {
              label: "型号",
              prop: "e",
              minWidth:100,
              overHidden:true,
            },
          ],
        },
        {
          label: "-",
          children: [
            {
              label: "挖机签单姓名",
              prop: "inName",
              minWidth:96,
              overHidden:true,
            },
            {
              label: "装车数",
              prop: "count",
              width:70,
              overHidden:true,
            },
            {
              label: "作业时间(签单时间)",
              prop: "indateAll",
              minWidth:140,
              overHidden:true,
            },
          ],
        },
        {
          label: "班次",
          children: [
            {
              label: "大车",
              prop: "大车",
              minWidth:70,
              overHidden:true,
            },
          ],
        },
        {
          label: " ",
          children: [
            {
              label: "中车",
              prop: "中车",
              minWidth:70,
              overHidden:true,
            },
          ],
        },
        {
          label: "   ",
          prop: "i",
          children: [
            {
              label: "小车",
              prop: "小车",
              minWidth:70,
              overHidden:true,
            },
          ],
        },
        {
          label: "    ",
          prop: "j",
          children: [
            {
              label: "小时",
              prop: "小时",
              minWidth:70,
              overHidden:true,
            },
          ],
        },
        {
          label: "     ",
          prop: "k",
          children: [
            {
              label: "分钟",
              prop: "分钟",
              minWidth:70,
              overHidden:true,
            },
          ],
        },
        {
          label: "      ",
          prop: "l",
          children: [
            {
              label: "备注",
              prop: "备注",
              minWidth:130,
              overHidden:true,
            },
          ],
        },

      ],
    }
  ]
};
export const tableOption7 = {
  dialogDrag: false,
  border: true,
  stripe: false,
  align: "center",
  searchShow: false,
  labelWidth: 150,
  // height:'auto',
  excelBtn: false,
  header: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  // index: true,
  menu: false,
  refreshBtn: false,
  column: [
    {
      label: "挖机装车台班费用统计",
      children:[
        {
          label: "日期",
          children: [
            {
              label: "机主",
              prop: "owner",
              overHidden:true,
            },
          ],
        },
        {
          label: "-",
          children: [
            {
              label: "挖机签单员",
              prop: "inName",
              overHidden:true,
            },
            {
              label: "装土车数",
              prop: "count",
              overHidden:true,
            },
          ],
        },
        {
          label: "班次",
          children: [
            {
              label: "单价(元)",
              prop: "price",
              overHidden:true,
            },
          ],
        },
        {
          label: "",
          children: [
            {
              label: "金额(元)",
              prop: "money",
              overHidden:true,
            },
            {
              label: "台班(时)",
              prop: "machine",
              overHidden:true,
            },
          ],
        },
        {
          label: "项目名称",
          children:[
            {
                label:'单价(元)',
                prop: "price1",
              overHidden:true,
            }
          ]
        },
        {
          label: " ",
          children: [
            {
              label: "金额(元)",
              prop: "money1",
              overHidden:true,
            },
            {
              label: "合计(元)",
              prop: "合计",
              overHidden:true,
            },
          ],
        },
      ],
    }
  ]
};
export const tableOption8 = {
  dialogDrag: false,
  border: true,
  stripe: false,
  align: "center",
  searchShow: false,
  labelWidth: 150,
  // height:'auto',
  excelBtn: false,
  header: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  // index: true,
  menu: false,
  refreshBtn: false,
  column: [
    {
      label: "机械装车台班表",
      children:[
        {
          label: "项目名称",
          children:[
            {
              label: "日期",
              prop:'createDate',
              width:76,
              overHidden:true,
            },
            {
              label: "挖机机主",
              prop:'ownerName',
              overHidden:true,
            },
            {
              label: "挖机手",
              prop:'inStaffName',
              overHidden:true,
            },
            {
              label: "机械名称",
              prop:'machineCode',
              overHidden:true,
            },
            {
              label: "班次",
              prop:'inShiftTypeName',
              width:50,
              overHidden:true,
            },
            {
              label: "车辆类型",
              children: [
                {
                  label: "小金刚",
                  prop: "smallCarCount",
                  overHidden:true,
                },
                {
                  label: "泥头车",
                  prop: "middleCarCount",
                  overHidden:true,
                },
                {
                  label: "拖头",
                  prop: "bigCarCount",
                  overHidden:true,
                },
                {
                  label: "未定义",
                  prop: "unknownCarCount",
                  overHidden:true,
                },
              ],
            },
            {
              label: "土质",
              prop: "inSoilType",
              overHidden:true,
            },
            {
              label: "费用",
              prop: "cost",
              width:50,
              overHidden:true,
            },
            {
              label: "台班类型",
              prop: "ledgerTypeName",
              overHidden:true,
            },
            {
              label: "起始时间",
              prop: "ledgerStartTime",
              overHidden:true,
            },
            {
              label: "结束时间",
              prop: "ledgerEndTime",
              overHidden:true,
            },
            {
              label: "时长(小时)",
              prop: "workTime",
              overHidden:true,
            },
            {
              label: "完成量(车/方)",
              prop: "scheduleWork",
              overHidden:true,
            },
            {
              label: "费用",
              prop: "cost1",
              width:50,
              overHidden:true,
            },
            {
              label: "台班备注",
              prop: "ledgerRemark",
              overHidden:true,
            },
            {
              label: "备注",
              prop: "remark",
              overHidden:true,
            },

          ],
        }
      ]
    }
  ]
};
