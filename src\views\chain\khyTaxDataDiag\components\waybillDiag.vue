<template>
  <div>
    <my-crud ref="waybillTable" :page.sync="khyHome.page" :data="tableData" :table-loading="tableLoading"
      :option="tableOption" v-model="form" @selection-change="selectionChange" @on-load="getPage"
      @refresh-change="refreshChange" @sort-change="sortChange" @search-change="searchChange">
      <template slot="menuLeft" slot-scope="{ row }">
        <el-button size="small" type="primary" :loading="btnsLoading" @click="exOut">
          导出
        </el-button>
        <el-button size="small" type="primary" :disabled='selectList.length==0' :loading="btnsLoading" @click="creatSettleWaybill">
          生成结算单
        </el-button>
      </template>
      <template slot="driverIsOk" slot-scope="{ row }">
        <i class="el-icon-check checkIcon" v-if="row.driverIsOk == 1"></i>
        <i class="el-icon-close closeIcon" v-else></i>
      </template>
      <template slot="truckIsOk" slot-scope="{ row }">
        <i class="el-icon-check checkIcon" v-if="row.truckIsOk == 1"></i>
        <i class="el-icon-close closeIcon" v-else></i>
      </template>
      <template slot="gpsIsOk" slot-scope="{ row }">
        <i class="el-icon-check checkIcon" v-if="row.gpsIsOk == 1"></i>
        <i class="el-icon-close closeIcon" v-else></i>
      </template>
    </my-crud>
  </div>
</template>

<script>
import {
  mapGetters
} from "vuex";
import {
  batchAddWaybillSettlePre,
} from "@/api/chain/waybillPreset";
export default {
  name: "khyTaxDataDiag",
  inject: ["khyHome"],
  props: {
    tableData: {
      type: Array,
      default () {
        return [];
      },
    },
    btnsLoading: {
      type: Boolean,
      default () {
        return false;
      },
    },
  },
  data () {
    return {
      form: {},
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: true,
        border: true,
        indexLabel: "序号",
        stripe: true,
        menuAlign: "center",
        align: "center",
        menuType: "text",
        searchShow: false,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        searchMenuSpan: 6,
        labelWidth: 120,
        menu: false,
        selection: true,
        useVirtual: true,
        height: "auto",
        calcHeight: 313,
        selectable:(row)=>{
          return !row.companySettleNo
        },
        column: [
          {
            label: "项目名称",
            prop: "projectName",
            overHidden: true,
          },
          {
            label: "运单号",
            prop: "no",
            overHidden: true,
          },
          {
            label: "发货时间",
            prop: "goDatetime",
            formatter: (row) => {
              return row.goDatetime && this.$moment(row.goDatetime).format('YYYY-MM-DD HH:mm:ss');
            },
            width: 140,
            overHidden: true,
          },
          {
            label: "司机诊断",
            prop: "driverIsOk",
            width: 80,
            overHidden: true,
          },
          {
            label: "货车诊断",
            prop: "truckIsOk",
            width: 80,
            overHidden: true,
          },
          {
            label: "运单GPS诊断",
            prop: "gpsIsOk",
            width: 100,
            overHidden: true,
          },
          {
            label: "结算单号",
            prop: "companySettleNo",
            overHidden: true,
          },
        ],
      },
      selectList: [],
      btnLoading: false,
    };
  },
  created () { },
  mounted () {
    window.addEventListener("resize", this.initHeight);
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList () {
      return {
        addBtn: this.permissions["chain:khyTaxDataDiag:add"] ? true : false,
        delBtn: this.permissions["chain:khyTaxDataDiag:del"] ? true : false,
        editBtn: this.permissions["chain:khyTaxDataDiag:edit"] ? true : false,
        viewBtn: this.permissions["chain:khyTaxDataDiag:get"] ? true : false,
        excelBtn: this.permissions["chain:khyTaxDataDiag:excel"] ? true : false,
      };
    },
  },
  methods: {
    initHeight () {
      if(this.tableOption.calcHeight == 182||this.tableOption.calcHeight == 233) return false
      this.tableOption.calcHeight = this.khyHome.searchShow?233:182
    },
    resizeTable(type=true){
      this.tableOption.calcHeight = this.khyHome.searchShow?233:131
      this.$refs.waybillTable.resizeFunc()
    },
    searchChange (params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.khyHome.page.currentPage = 1;
      this.getPage(this.khyHome.page, params);
      done();
    },
    sortChange (val) {
      this.$emit("sortChange", val);
    },
    getPage (page, params) {
      this.$emit("getPage", page);
    },

    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.khyHome.page);
    },
    exOut () {
      this.$emit('exOut', '运单GPS诊断')
    },
    selectionChange (e) {
      this.selectList = e;
    },
    creatSettleWaybill () {
      let data = this.selectList.map((item) => {
        return item.id;
      });
      console.log(data);
      this.btnsLoading = true;
      batchAddWaybillSettlePre(data)
        .then((res) => {
          this.btnsLoading = false;
          if (res.data.msg) {
            this.$message.success(res.data.msg);
          } else {
            this.$message.success("操作成功");
          }
          this.getPage(this.khyHome.page);
        })
        .catch((err) => {
          this.btnsLoading = false;
        });
    },
  },
  destroyed () {
    window.removeEventListener("resize", this.initHeight);
  },
};
</script>

<style lang="scss" scoped>
/deep/ .closeIcon,
.checkIcon {
  font-size: 16px;
  color: red;
}

/deep/ .checkIcon {
  color: #3dcc90;
}
</style>
