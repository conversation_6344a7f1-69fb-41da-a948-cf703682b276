export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  // stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  menuWidth: 160,
  searchSpan: 6,
  searchMenuSpan: 6,
  searchLabelWidth:90,
  selection:true,
  selectable:(row)=>{
    return row.auditAuth
  },
  column: [
    {
      label: "项目名称",
      prop: "projectInfoId",
      search: true,
      type: "select",
      searchOrder: 2,
      display: false,
      hide: true,
      showColumn: false,
      width: 120,
      props: {
        label: "projectName",
        value: "id",
      },
      dicUrl: "/chain/projectinfo/list",
      searchFilterable: true, //是否可以搜索
    },
    {
      label: "项目名称",
      prop: "projectInfoName",
      sortable: "custom",
      minWidth:180,
      overHidden:true,
    },
    {
      label: "运单号",
      prop: "companyWaybillNo",
      sortable: "custom",
      search: true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "申请人",
      prop: "createName",
      sortable: "custom",
      search: true,
      minWidth:86,
      overHidden:true,
    },
    {
      label: "申请时间",
      prop: "createDatetime",
      type: "date",
      sortable: "custom",
      searchRange: true,
      search: true,
      valueFormat: "yyyy-MM-dd",
      minWidth:140,
      overHidden:true,
    },
    {
      label: "变更原因",
      prop: "remark",
      sortable: "custom",
      minWidth:120,
      overHidden:true,
    },
    // {
    //   label: "审核人",
    //   prop: "auditName",
    //   sortable: "custom",
    //   search: true,
    //   minWidth:86,
    //   overHidden:true,
    // },
    {
      label: "审核状态",
      prop: "status",
      sortable: "custom",
      type: "select",
      search: true,
      // props: {
      //   label: "itemName",
      //   value: "itemValue",
      // },
      // dicUrl:"/chain/systemdictionaryitem/listDictionaryItem?dictionary=company_waybill_update_history_audit_status",
      dicData:[
        {
          label:"审核中",
          value:"1",
        },
        {
          label:"已审核",
          value:"2",
        },
        {
          label:"已驳回",
          value:"3",
        },
      ],
      minWidth:100,
      overHidden:true,
    },
    {
      label: "新审核状态",
      prop: "approveStatus",
      sortable: "custom",
      type: "select",
      search: true,
      dicData:[
        {
          label:"待审核",
          value:"1",
        },
        {
          label:"已审核",
          value:"2",
        },
        {
          label:"已驳回",
          value:"3",
        },
      ],
      hide:true,
      showColumn:false,
      minWidth:110,
      overHidden:true,
    },
    // {
    //   label: "审核时间",
    //   prop: "auditDatetime",
    //   type: "date",
    //   sortable: "custom",
    //   searchRange: true,
    //   search: true,
    //   valueFormat: "yyyy-MM-dd",
    //   minWidth:140,
    //   overHidden:true,
    // },
    // {
    //   label: "审核备注",
    //   prop: "auditRemark",
    //   sortable: "custom",
    //   minWidth:120,
    //   overHidden:true,
    // },
  ],
};
