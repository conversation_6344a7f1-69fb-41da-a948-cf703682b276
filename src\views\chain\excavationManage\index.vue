<template>
  <div class="excavationManage">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="menu"
                  slot-scope="{ row, index }">
          <el-button v-if="permissions['chain:excavationManage:setUpExcavationPlan']"
                     @click="handleSet(row, 1)"
                     icon="el-icon-setting"
                     size="small"
                     plain
                     type="text">
            设置出土计划
          </el-button>
          <el-button v-if="permissions['chain:excavationManage:viewPlan']"
                     @click="handleSet(row, 2)"
                     icon="el-icon-view"
                     size="small"
                     plain
                     type="text">
            查看计划
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
      <set-plan :info="info"
                v-if="customVisible"
                :isDetail="isDetail"
                @refreshChange="refreshChange"
                :visible.sync="customVisible"></set-plan>
  </div>
</template>

<script>
import { getPage, } from "@/api/chain/excavationManage";
import { tableOption } from "@/const/crud/chain/excavationManage";
import { mapGetters } from "vuex";
import setPlan from './setPlan.vue'

export default {
  name: "excavationManage",
  components: {
    setPlan
  },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: "id", //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      customVisible: false,
      info: {},
      isDetail:false
    };
  },
  created () { },
  mounted: function () {
    console.log(9999999);
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList () {
      return {
        addBtn: this.permissions["chain:excavationManage:add"]
          ? true
          : false,
        delBtn: this.permissions["chain:excavationManage:del"]
          ? true
          : false,
        editBtn: this.permissions["chain:excavationManage:edit"]
          ? true
          : false,
        viewBtn: this.permissions["chain:excavationManage:get"]
          ? true
          : false,
      };
    },
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange (val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage (page, params) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("finishDate")) {
          params.finishDateStart = params.finishDate[0];
          params.finishDateEnd = params.finishDate[1];
          delete params.finishDate;
        }
        if (params.hasOwnProperty("startDate")) {
          params.startDateStart = params.startDate[0];
          params.startDateEnd = params.startDate[1];
          delete params.startDate;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page);
    },
    handleSet (row,type) {
      this.isDetail = type==2
      this.info = row
      this.customVisible = true
    },
  },
};
</script>

<style lang="scss" scoped>
// /deep/ .excMsg{
//   width: 40;
// }
</style>
