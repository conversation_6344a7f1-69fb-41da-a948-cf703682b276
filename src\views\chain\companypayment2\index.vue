<template>
    <div class="execution">
        <basic-container>
            <avue-crud ref="crud"
                       :page.sync="page"
                       :data="tableData"
                       :permission="permissionList"
                       :table-loading="tableLoading"
                       :option="tableOption"
                       v-model="form"
                       @on-load="getPage"
                       @refresh-change="refreshChange"
                       @row-update="handleUpdate"
                       @row-save="handleSave"
                       @row-del="handleDel"
                       @sort-change="sortChange"
                       @selection-change="changeSelect"
                       @search-change="searchChange">
                       <template slot="menu" slot-scope="scope">
                          <el-button type="text"
                            v-if="permissions['chain:companypayment2:askFor']&&scope.row.invoiceStatus==0&&scope.row.paymentNo"
                            icon="el-icon-document"
                            size="small"
                            plain
                            @click="askForInvoice(scope.row,scope.index)">
                          索票</el-button>
                            <!-- <el-button type="text"
                            v-if="permissions['chain:companypayment2:get']&&scope.row.isInvoice==1"
                            icon="el-icon-view"
                            size="small"
                            plain
                            @click="viewRow(scope.row,scope.index)">
                          查看</el-button> -->
                        </template>
                        <template slot="header" slot-scope="scope">
                          <el-button type="primary"
                                    style="margin-left:6px"
                                    v-if="permissions['chain:companypayment2:askFor']"
                                    icon="el-icon-check"
                                    size="small"
                                    :disabled="multipleSelection.length<1"
                                    @click="batchAskForInvoice">
                            批量索票</el-button>
                        </template>
                       <template slot="projectInfoId" slot-scope="scope">
                          <span>{{scope.row.projectName}}</span>
                        </template>
                         <!-- <template slot="settleCnt" slot-scope="scope">
                          <div style="color:#409eff;cursor:pointer" @click="settleDetail(scope.row)">1</div>
                        </template> -->
                      <template slot="alreadyWaybillCount" slot-scope="scope">
                        <span style="color:#409eff;cursor:pointer" @click="alreadyDetail(scope.row)">{{scope.row.alreadyWaybillCount}}</span>
                      </template>
            </avue-crud>
        </basic-container>
    </div>
</template>

<script>
    import {getInvoicePageList, getObj, addObj, putObj, delObj,askForInvoice,getPaymentOfInvoice,batchAdd} from '@/api/chain/companypayment'
    import {getAllSettle} from '@/api/chain/createPayOrder'
    import {tableOption} from '@/const/crud/chain/companypayment2'
    import {mapGetters} from 'vuex'

    export default {
        name: 'companypayment2',
        data() {
            return {
                form: {},
                tableData: [],
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                    ascs: [],//升序字段
                    descs: []//降序字段
                },
                paramsSearch: {},
                tableLoading: false,
                tableOption: tableOption,
                multipleSelection:[],
            }
        },
        created() {
        },
        mounted: function () {
        },
        computed: {
            ...mapGetters(['permissions']),
            permissionList() {
                return {
                    addBtn: this.permissions['chain:companypayment:add'] ? true : false,
                    delBtn: this.permissions['chain:companypayment:del'] ? true : false,
                    editBtn: this.permissions['chain:companypayment:edit'] ? true : false,
                    viewBtn: this.permissions['chain:companypayment:get'] ? true : false
                };
            }
        },
        methods: {
            searchChange(params,done) {
                params = this.filterForm(params)
                this.paramsSearch = params
                this.page.currentPage = 1
                this.getPage(this.page, params)
                done()
            },
            sortChange(val) {
                let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
                if (val.order == 'ascending') {
                    this.page.descs = []
                    this.page.ascs = prop
                } else if (val.order == 'descending') {
                    this.page.ascs = []
                    this.page.descs = prop
                } else {
                    this.page.ascs = []
                    this.page.descs = []
                }
                this.getPage(this.page)
            },
            getPage(page, params) {
                this.tableLoading = true
                getInvoicePageList(Object.assign({
                    current: page.currentPage,
                    size: page.pageSize,
                    descs: this.page.descs,
                    ascs: this.page.ascs,
                }, params, this.paramsSearch)).then(response => {
                    this.tableData = response.data.data.records
                    this.page.total = response.data.data.total
                    this.page.currentPage = page.currentPage
                    this.page.pageSize = page.pageSize
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            },
            /**
             * @title 数据删除
             * @param row 为当前的数据
             * @param index 为当前删除数据的行数
             *
             **/
            handleDel: function (row, index) {
                let _this = this
                this.$confirm('是否确认删除此数据', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(function () {
                    return delObj(row.id)
                }).then(data => {
                    _this.$message({
                        showClose: true,
                        message: '删除成功',
                        type: 'success'
                    })
                    this.getPage(this.page)
                }).catch(function (err) {
                })
            },
            /**
             * @title 数据更新
             * @param row 为当前的数据
             * @param index 为当前更新数据的行数
             * @param done 为表单关闭函数
             *
             **/
            handleUpdate: function (row, index, done, loading) {
                putObj(row).then(response => {
                    this.$message({
                        showClose: true,
                        message: '修改成功',
                        type: 'success'
                    })
                    done()
                    this.getPage(this.page)
                }).catch(() => {
                    loading()
                })
            },
            /**
             * @title 数据添加
             * @param row 为当前的数据
             * @param done 为表单关闭函数
             *
             **/
            handleSave: function (row, done, loading) {
                addObj(row).then(response => {
                    this.$message({
                        showClose: true,
                        message: '添加成功',
                        type: 'success'
                    })
                    done()
                    this.getPage(this.page)
                }).catch(() => {
                    loading()
                })
            },
            /**
             * 刷新回调
             */
            refreshChange(page) {
                this.getPage(this.page)
            },
            askForInvoice(row){
              this.batchAdd({companySettleId:row.companySettleId})
            },
            //批量索票
            batchAskForInvoice(){
              let companySettleId = this.multipleSelection.map(item => {
                return item.companySettleId
              }).join(",")
              console.log(companySettleId);
              let param = {
                companySettleId
              }
              this.batchAdd(param)
            },
            batchAdd(param){
              this.tableLoading = true
              batchAdd(param).then((res) => {
                this.tableLoading = false
                this.$message.success("索票成功");
                this.getPage(this.page)
              }).catch(() => {
                this.tableLoading = false
              })
            },
            viewRow(row){
               this.$ImagePreview([{thumbUrl:row.invoicePic,url:row.invoicePic}],0,{
                closeOnClickModal: true,
              });
            },
             //查看结算单详情
            // settleDetail(row){
            //   this.tableLoading = true
            //   setTimeout(()=>{
            //     this.tableLoading = false
            //   },3000)
            //   getAllSettle(row.companyPaymentId).then(res=>{
            //     let settleDetailList = res.data.data
            //     this.tableLoading = false
            //     this.$router.push({path:'/waybill/waybillDetail',query:{value:JSON.stringify(settleDetailList)}})
            //     // params:JSON.stringify(this.settleDetailList)
            //     // this.settleDialog = true
            //   })
            // },
             //查看已开票
            alreadyDetail(row){
              this.tableLoading = true
              setTimeout(()=>{
                this.tableLoading = false
              },3000)
                let info = {
                  list:[]
                }
                info.paymentNo = row.paymentNo
                info.amt = row.amt
                info.id = row.companySettleId
              getPaymentOfInvoice(row.companySettleId).then(res=>{
                info.list = res.data.data
                this.tableLoading = false
                this.$router.push({path:'/companypayment/alreadyWaybill',query:{info:JSON.stringify(info)}})
              })
            },
            changeSelect (e) {
              this.multipleSelection = e;
            },
        }
    }
</script>

<style lang="scss" scoped>
</style>
