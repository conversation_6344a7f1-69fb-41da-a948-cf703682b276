<template>
  <div class="receiptInfo">
    <el-drawer size="80%"
               title="回执信息"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <basic-container>
        <!-- <div style="color:red;margin-bottom: 10px;text-align: center;">合计金额:{{ totalAmount}}</div> -->
        <div v-if="receiptList&&receiptList.length>0">
          <div v-for="(item,index) in receiptList" :key="index">
            <avue-crud :data="item.payeeInfoList" :table-loading="tableLoading"
                      :span-method="event=>spanMethod(event,item)"
                      :option="tableOption">
            </avue-crud>
          </div>
        </div>
        <!-- <avue-crud ref="crud"
                   :data="receiptList"
                   :table-loading="tableLoading"
                   :option="tableOption"
                   v-model="form"
                   @on-load="getPage">
        </avue-crud> -->
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { getListGroupBatchNo } from "@/api/chain/companysettle";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {}
  },
  data () {
    return {
      form: {},
      receiptList: [],
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 12,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: true,
        delBtn: false,
        menu: false,
        searchMenuSpan: 6,
        index: false,
        refreshBtn: false,
        header:false,
        column: [
          {
            label: "合计",
            prop: "sumSettleAmount",
            minWidth:80,
            overHidden:true,
          },
          {
            label: "类型",
            prop: "type",
            minWidth:80,
            overHidden:true,
            formatter:(val)=>{
              return val.type==1?'结算单':'回执单'
            }
          },
          {
            label: "初始结算单号/电子卡号",
            prop: "initNo",
            minWidth:160,
            overHidden:true,
          },
          {
            label: "最终结算单单号/回执单号",
            prop: "parentNo",
            minWidth:160,
            overHidden:true,
          },
          {
            label: "收款人",
            prop: "payeeName",
            minWidth:80,
            overHidden:true,
          },
          {
            label: "账号",
            prop: "bindingBankNo",
            minWidth:120,
            overHidden:true,
          },
          {
            label: "开户行",
            prop: "bindingBankName",
            minWidth:120,
            overHidden:true,
          },
          {
            label: "申请结算金额",
            prop: "settleAmount",
            minWidth:100,
            overHidden:true,
          },
          {
            label: "核算运单数",
            prop: "settleCount",
            minWidth:100,
            overHidden:true,
          },
          {
            label: "申请时间",
            prop: "createDatetime",
            minWidth:140,
            overHidden:true,
          },
        ],
      },
    };
  },
  created () { },
  mounted: function () {
    this.getPage()
  },
  computed: {
    // totalAmount(){
    //  return this.receiptList.map((row) => (row.settleAmount)).reduce((acc, cur) => parseFloat(cur) + acc, 0).toFixed(2);
    // }
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    getPage () {
      this.tableLoading = true;
      getListGroupBatchNo({ settleNo: this.info.settleNo })
        .then((response) => {
          this.receiptList = response.data.data;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    spanMethod ({ row, column, rowIndex, columnIndex },item) {
      if (columnIndex === 0) {
        return {
          rowspan: item.payeeInfoList.length,
          colspan: rowIndex>0?0:1
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .drawerHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
</style>
