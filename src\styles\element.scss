//上传图片加号不居中问题
.avue-upload  .el-upload--picture-img .el-icon-plus{
  display: flex;
  align-items: center;
  justify-content: center;
}
//上传图片加号不居中问题
.avue-upload .el-upload--picture-card .el-icon-plus{
  height: 100%;
}
.el-button.is-disabled.is-plain, .el-button.is-disabled.is-plain:focus, .el-button.is-disabled.is-plain:hover{
  border-color: #fff;
}

//暂时性解决dialog 问题 https://github.com/ElemeFE/element/issues/2461
 //  .el-dialog__wrapper{
 //    position: relative;
 //  }
 .el-dialog__wrapper{
  display: flex;
  align-items: center;
  justify-content: center;
}
.el-dialog {
 //  position: absolute !important;
 //  top: 50%;
 //  left: 50%;
 //  -webkit-transform: translate(-50%, -50%);
 //  -webkit-font-smoothing: subpixel-antialiased;
 //  -webkit-transform: translateZ(0) scale(1.0,1.0);
 //  transform: translate(-50%, -50%);
  margin: 0px !important;
  // max-height: calc(100% - 60px);
  // max-width: calc(100% - 30px);
  //  max-height: 90vh;
  // overflow: visible;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .el-dialog__body {
    max-height: 75vh;
   //  max-height:calc(100%-350px);
    // height: 500px;
    overflow-y: auto;
  }
}
.avue-crud__dialog--fullscreen .el-dialog .el-dialog__body {
  max-height: none;
}

//弹框页脚
.el-dialog__footer {
  //  width: 100%;
  padding: 0 !important;
  height: 70px;
  line-height: 70px;
  background-color: #f4f6fa;
  text-align: center;
}

//右侧按钮悬浮出现滚动条拖不动问题
.el-table .el-table__fixed-right {
  height:auto !important;
  bottom: 8px !important;
&::before {
  background-color: transparent;
}
}
//左侧按钮悬浮出现滚动条拖不动问题
.el-table .el-table__fixed {
  height:auto !important;
  bottom: 8px !important;
&::before {
  background-color: transparent;
}
}


.avue-crud__tip{
  display: inline-block;
}

.avue-group__title{
  padding-left: 8px;
  text-align: left;
  height: 16px;
  line-height: 16px;
  font-size: 16px;
  color: #111528;
  border-left: 4px solid #4688f7;
  border-bottom: none;
}
.el-input.is-disabled .el-input__inner{
  color: #303133 !important;
}
//给table添加class隐藏checkbox
.table-column-hidden .el-table-column--selection .el-checkbox__input {
  display: none
}

.el-card__body{
  .avue-crud__pagination{
    padding: 15px 0 0px 20px;
  }
}
//修复目录 1px锯齿问题
.el-menu{
  border-right: none;
}