<template>
  <div class="driverTaskDetail">
    <el-drawer size="90%"
               :data="visible"
               :visible.sync="visible"
               title="查看计划"
               :before-close="cancelModal">
      <basic-container>
        <avue-crud ref="crud"
                   :data="tableData"
                   :table-loading="tableLoading"
                   :option="tableOption">
          <span slot="empty">暂无数据</span>
          <template slot="menu"
                    slot-scope="scope">
            <el-button type="text"
                      v-if="scope.row.companyPaymentPlanNo&&scope.row.auditStatus!=3"
                      icon="el-icon-check"
                      size="small"
                      plain
                      @click="print(scope.row)">
            打印</el-button>
            <el-button type="text"
                      v-if="scope.row.companyPaymentPlanNo"
                      icon="el-icon-view"
                      size="small"
                      plain
                      @click="view(scope.row,scope.index)">
            查看审核</el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
    <printPlan v-if="printVisible" :accountForm="currentForm" :visible.sync="printVisible"></printPlan>
    <flowView v-if="flowVisible" :info="currentForm" :visible.sync="flowVisible"></flowView>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getPlanBySettleId,printCompanyPaymentPlanSettle
} from "@/api/chain/companysettle";
import printPlan from './printPlan.vue';
import flowView from '@/views/chain/companyauthrecharge2/components/flowView';
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  components:{
    printPlan,
    flowView
  },
  data () {
    return {
      tableData: [],
      tableOption: {
        header: false,
        page: false,
        editBtn: false,
        delBtn: false,
        menuWidth: 180,
        column: [
          {
            label: "计划号",
            prop: "companyPaymentPlanNo",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "计划名称",
            prop: "companyPaymentPlanName",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "审核状态",
            prop: "auditStatusName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "计划创建人",
            prop: "createName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "计划创建时间",
            prop: "createDatetime",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "计划付款金额",
            prop: "companyPaymentPlanAmount",
            minWidth: 100,
            overHidden: true,
            formatter:(val)=>{
              return `<span ${val.checkAmount!=val.companyPaymentPlanAmount?'style="color:red"':""}>${val.companyPaymentPlanAmount}</span>`
            },
          },
          {
            label: "核算金额",
            prop: "checkAmount",
            minWidth: 80,
            overHidden: true,
            formatter:(val)=>{
              return `<span ${val.checkAmount!=val.companyPaymentPlanAmount?'style="color:red"':""}>${val.checkAmount}</span>`
            },
          },
          {
            label: "核算驳回金额",
            prop: "rejectPrice",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "已付金额",
            prop: "paidPrice",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "处理中金额",
            prop: "processingPrice",
            minWidth: 96,
            overHidden: true,
          },
          {
            label: "未付金额",
            prop: "notPaidPrice",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "最新支付时间",
            prop: "latestPayTime",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "状态",
            prop: "payStatus",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "计划驳回备注",
            prop: "remark",
            minWidth: 140,
            overHidden: true,
          },
        ]
      },
      tableLoading: false,
      currentForm:{},
      printVisible:false,
      flowVisible:false,
    };
  },
  created () {
    this.getData()
  },
  mounted () {
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    getData () {
      getPlanBySettleId({ companySettleId: this.info.id }).then(res => {
        this.tableData = res.data.data
      })
    },
    cancelModal () {
      this.$emit("update:visible", false);
    },
    //查看核算单
    print (row, index) {
      this.tableLoading = true;
      //获取核算单信息
      this.currentForm = this.info
      this.currentForm.planId = row.id
      let param = {
        settleId: this.info.id,
        planId:row.id
      };
      printCompanyPaymentPlanSettle(param)
        .then((res) => {
          this.currentForm.tableData = res.data.data
          this.printVisible = true;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    view(row){
      this.currentForm = Object.assign({},row)
      this.flowVisible = true
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
