<template>
  <div class="merchantDetail">
    <el-drawer
      size="80%"
      title="打印结算单"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
      :wrapperClosable="false"
      center
    >
      <div ref="accountSheet" style="padding: 0 80px">
        <ul class="info">
          <li>
            <label>结算单号：</label>
            <span>{{ waybillData.header.settleNo }}</span>
          </li>
          <li>
            <label>结算申请人：</label>
            <span>{{ waybillData.header.agentName }}</span>
          </li>
          <li>
            <label>项目名称：</label>
            <span>{{ waybillData.header.projectName }}</span>
          </li>
          <!-- <li>
                  <label>申请结算车数：</label>
                  <span>{{waybillData.header.waybillCnt}}</span>
                </li> -->
          <li>
            <label>运单数：</label>
            <span>{{ waybillData.header.settleCnt }}</span>
          </li>
          <li>
            <label>结算金额：</label>
            <span>{{ waybillData.header.settleAmt }}</span>
          </li>
          <li>
            <label>核算人：</label>
            <span>{{ waybillData.header.settleName }}</span>
          </li>
        </ul>
        <div class="table" v-for="(item, index) in waybillData.body" :key="index">
          <div class="tableTitle">
              <span>承运人:{{ item.carrier_name }}</span
              ><span class="red">共计{{ item.cnt }}车，{{ item.amt }}元</span>
            </div>

          <div class="inner" v-for="(list, k) in item.detail" :key="k+100">
            <div class="tableTitle">
            <span>{{ list.create_datetime }}</span
            ><span class="red">共计{{ list.cnt }}车，{{ list.total_amt }}元</span>
          </div>
            <el-table :data="list.detailDetail" border class="table">
              <el-table-column
                property="truckCode"
                width="100px"
                label="车牌号"
              ></el-table-column>
              <el-table-column
                property="garbageName"
                label="泥尾"
              ></el-table-column>
              <el-table-column
                property="goSoilType"
                label="土质"
              ></el-table-column>
              <el-table-column
                property="settleCnt"
                label="运单数"
              ></el-table-column>
              <el-table-column
                property="weightTons"
                label="总数量"
              ></el-table-column>
              <el-table-column
                property="weightUnit"
                label="单位"
              ></el-table-column>
              <el-table-column
                property="settlePrice"
                label="单价"
              ></el-table-column>
              <el-table-column
                property="totalAmt"
                label="核算价总额"
              ></el-table-column>
            </el-table>
          </div>
        </div>
        <ul class="info" style="margin-top: 30px">
          <li>
            <label>甲方签名：</label>
            <!-- <span>{{accountForm.id}}</span> -->
          </li>
          <li>
            <label>乙方签名：</label>
            <!-- <span>{{accountForm.name}}</span> -->
          </li>
          <li>
            <label>时间：</label>
            <!-- <span>{{accountForm.projectName}}</span> -->
          </li>
          <li>
            <label>时间：</label>
            <!-- <span>{{accountForm.num}}</span> -->
          </li>
        </ul>
      </div>
      <div class="demo-drawer__footer">
        <!-- <el-button @click="downExport" size="small" icon="el-icon-bottom"
          >导出</el-button
        > -->
        <el-button
          type="primary"
          :loading="loading"
          @click="printPDF"
          size="small"
          icon="el-icon-printer"
          >下载PDF打印</el-button
        >
      </div>
    </el-drawer>
    <!-- 弹窗 -->
    <el-dialog
      width="70%"
      title="运单详情"
      :visible="dialogVisible"
      :before-close="closeDialog"
      :close-on-click-modal="false"
    >
      <div class="table" style="margin-top: 20px">
        <el-table :data="tableData" border :loading="loading">
          <el-table-column
            property="no"
            min-width="100px"
            label="运单号"
          ></el-table-column>
          <el-table-column
            property="agentName"
            min-width="100px"
            label="项目合作方"
          ></el-table-column>
          <el-table-column
            property="truckCode"
            min-width="100px"
            label="车牌号"
          ></el-table-column>
          <el-table-column property="tpMode" min-width="100px" label="运输方式">
            <template slot-scope="scope">
              <div>{{ filterTpMode(scope.row.tpMode) }}</div>
            </template>
          </el-table-column>
          <el-table-column
            property="garbageName"
            min-width="100px"
            label="泥尾"
          ></el-table-column>
          <el-table-column
            property="goSoilType"
            min-width="100px"
            label="土质"
          ></el-table-column>
          <el-table-column
            property="captainName"
            label="车队长"
          ></el-table-column>
          <el-table-column property="driverName" label="司机"></el-table-column>
          <el-table-column
            property="inStaffName"
            min-width="100px"
            label="挖机签单员"
          ></el-table-column>
          <el-table-column
            property="goStaffName"
            min-width="100px"
            label="出场签单员"
          ></el-table-column>
          <el-table-column
            property="capacity"
            min-width="100px"
            label="容量"
          ></el-table-column>
          <el-table-column
            property="inDatetime"
            min-width="120px"
            label="挖机签单时间"
          ></el-table-column>
          <el-table-column
            property="goDatetime"
            min-width="120px"
            label="出场签单时间"
          ></el-table-column>
          <el-table-column
            property="completeDatetime"
            min-width="120px"
            label="完成时间"
          ></el-table-column>
          <el-table-column
            property="settlePrice"
            min-width="100px"
            label="结算价"
          ></el-table-column>
          <el-table-column
            property="exAmt"
            min-width="100px"
            label="异常金额"
          ></el-table-column>
          <el-table-column
            property="adjustAmt"
            min-width="100px"
            label="增减值"
          ></el-table-column>
          <el-table-column
            property="goRemark"
            min-width="100px"
            label="备注"
          ></el-table-column>
          <el-table-column
            property="weightTons"
            min-width="100px"
            label="重量(吨)"
          ></el-table-column>
          <el-table-column
            property="isReject"
            min-width="100px"
            label="是否驳回"
          >
            <template slot-scope="scope">
              <div>{{ scope.row.isReject == 1 ? "是" : "否" }}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { clearNoNum } from "@/util/util.js";
import { printWaybillDetail,downloadCompanyPaymentPlanSettlePdf } from "@/api/chain/companysettle";
import $Print from "avue-plugin-print";
import { print } from "@/util/util.js";
import { expotOut } from "@/util/down.js";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    //结算数据
    accountForm: {},
    fileName:{
      type:String,
      default:"资金支付计划结算单"
    },
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      waybillData: this.accountForm.tableData,
      tableData: [],
    };
  },
  mounted() {},
  methods: {
    /* ------------------------------------------------------- 下载PDF打印 ----------------------------------------------------- */
    printPDF(){
      this.loading = true
      let params = {
        settleId: this.accountForm.id,
        planId: this.accountForm.planId,
      };
      downloadCompanyPaymentPlanSettlePdf(params).then(res=>{
        console.log(res);
        const url = window.URL.createObjectURL(new Blob([res.data]));
		    const link = document.createElement('a');
        link.href = url;
        let fileName = `${this.fileName}.pdf` //
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        this.loading = false
      }).catch(err=>{
        this.loading = false
      })
    },
    /* ------------------------------------------------------- 下载exc ----------------------------------------------------- */
    // downExport() {
    //   let params = {
    //     settleId: this.accountForm.id,
    //   };
    //   let url = "/chain/companysettle/exportExcelSettle";
    //   expotOut(params, url, "结算单");
    // },
    /* ------------------------------------------------------- 取消查看核算 ----------------------------------------------------- */
    cancelModal() {
      this.$emit("update:visible", false);
    },
    /* ------------------------------------------------------- 关闭弹窗 ----------------------------------------------------- */
    closeDialog() {
      this.dialogVisible = false;
    },
    /* ------------------------------------------------------- 查看运单 ----------------------------------------------------- */
    toWaybill(row, item) {
      let param = {
        settleId: this.accountForm.id,
        settleDate: item.create_datetime,
        truckId: row.truck_id,
      };
      this.tableData = [];
      this.loading = true;
      printWaybillDetail(param)
        .then((res) => {
          this.loading = false;
          this.dialogVisible = true;
          this.tableData = res.data.data;
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  // border-bottom: 1px solid #ccc;
  padding-top: 20px;
  padding-bottom: 22px;
  li {
    width: 50%;
    line-height: 30px;
    color: #000;
    label {
      display: inline-block;
      width: 116px;
      text-align: right;
    }
    span {
      color: #666;
      // font-size: 14px;
    }
  }
}
.isClick {
  color: #409eff;
  cursor: pointer;
  display: block;
  text-decoration: underline;
}
/deep/ .el-drawer__header {
  margin-bottom: 0px;
  span {
    text-align: center;
    font-size: 18px;
    font-weight: 700;
    color: #000;
  }
}
/deep/ .el-drawer__body {
  padding: 0px;
  // padding-bottom: 70px;
  #accountSheet {
    padding: 0 20px;
  }
  .demo-drawer__footer {
    width: 100%;
    text-align: center;
    background-color: rgba(204, 204, 204, 0.7);
    border-radius: 6px;
    line-height: 60px;
    position: absolute;
    bottom: 10px;
  }
}
.tableTitle {
  border: 1px solid #9a9a9a;
  margin-left: 1px;
  border-bottom: none;
  line-height: 32px;
  padding: 0px 10px;
  // color: #606266;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.red {
  color: red;
}
/deep/ .el-table{
  border-bottom: 1px solid #9a9a9a;
  border-right: 1px solid #9a9a9a;
  tr{
    .el-table__cell {
      border-left: 1px solid #9a9a9a;
      border-top: 1px solid #9a9a9a;
    }
      border-right: 1px solid #9a9a9a;

  }
  .el-table__row{
    td{
      padding: 4px 0px;
    }
  }
  .el-table__header{
    th{
      padding: 4px 0px;
    }
  }
}
</style>
