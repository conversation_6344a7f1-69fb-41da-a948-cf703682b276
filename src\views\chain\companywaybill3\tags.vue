<template>
  <div class="tags">
    <div class="tag" :key="i" v-for="(tag, i) in statements">
      <el-tag closable :disable-transitions="false" @close="handleClose(tag)">
        {{ tag }}
      </el-tag>
    </div>
    <el-input
      class="input-new-tag"
      :autofocus="true"
      style="width:180px;margin-left:0"
      size="small"
      placeholder="输入电子结算卡后请回车"
      v-model.lazy="inputValue"
      ref="saveTagInput"
      @keydown.native="handleKeyUp"
    >
    </el-input>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Array,
      value: [],
    },
  },

  data() {
    return {
      realBarcode: "",
      isScanningGun: false,
      statements: [],
      inputValue: "",
      lock: false,
    };
  },
  watch: {
    statements(val) {
      this.$emit("input", val);
    },
  },
  methods: {
   // 处理keyup事件
   handleKeyUp(e) {
      if (e.key == "Backspace") {
        return;
      }
      var shiftKey = e.shiftKey; //为true则按了shiftKey
      let keyCode = e.code;
      var key = e.key; //其他按键key
      let array = [
        "Q",
        "W",
        "E",
        "R",
        "T",
        "Y",
        "U",
        "I",
        "O",
        "P",
        "A",
        "S",
        "D",
        "F",
        "G",
        "H",
        "J",
        "K",
        "L",
        "Z",
        "X",
        "C",
        "V",
        "B",
        "N",
        "M",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "0",
      ];
      // 这种情况下 基本上是 中文输入法 才有出现
      if (key == "Process") {
        for (const a of array) {
          // 如果匹配到是英文
          if (keyCode == "Key" + a) {
            // 判断大小写
            if (shiftKey) {
              this.realBarcode += a;
              this.isScanningGun = true;
            } else {
              this.realBarcode += a.toLowerCase();
              this.isScanningGun = true;
            }
          }
          // 如果匹配到是数字
          else if (keyCode == "Digit" + a) {
            this.realBarcode += String(a);
            this.isScanningGun = true;
          }
        }
        if (keyCode == "Equal") {
          this.realBarcode += "=";
        }
        if (keyCode == "Enter") {
          this.disScan = true;
          console.info(this.realBarcode)
          console.info(this.inputValue)
          setTimeout(() => {
            this.inputValue = this.getParam(
              this.realBarcode&& this.realBarcode.includes('=') ? this.realBarcode : this.inputValue,
              "id"
            );
            this.realBarcode = "";
            this.handleInputConfirm();
          }, 100);
        }
      }
      // 这是英文状态下 直接判断输入的英文在没在上面大写字母中
      else if (array.includes(key.toUpperCase())) {
        this.realBarcode += key;
      }
      // 这是英文状态下 直接判断输入的小写英文在没在上面大写字母中
      else if (array.includes(key.toUpperCase())) {
        this.realBarcode += key;
      }
      // 这是数字 直接判断输入的数字在没在上面数字中
      else if (array.includes(key)) {
        this.realBarcode += String(key);
      } else if (keyCode == "Equal") {
        this.realBarcode += "=";
      } else if (keyCode =="Enter" || key == "Enter") {
        this.disScan = true;
        // 监听到enter触发了，执行后续事件
        setTimeout(() => {
          this.inputValue = this.getParam(
            this.realBarcode && this.realBarcode.includes('=') ? this.realBarcode : this.inputValue,
            "id"
          );
          this.realBarcode = "";
          this.handleInputConfirm();
        }, 100);
      }
    },
    changeInput(value) {
      if (this.getParam(value, "id")) {
        console.log(value);
        this.inputValue = this.getParam(value, "id");
      }
      //   }
    },
    getParam(path, name) {
      // var reg = new RegExp("(^|\\?|&)" + name + "=([^&]*)(\\s|&|$)", "i");
      console.info(path);
      if (path.indexOf("=") != -1) {
        var n1 = path.length;
        var n2 = path.lastIndexOf("="); //取得=号的位置
        var id = path.substr(n2 + 1, n1 - n2); //从=号后面的内容
        return id;
      }
      return path;
    },
    handleClose(tag) {
      this.statements.splice(this.statements.indexOf(tag), 1);
    },
    handleInputConfirm(e) {
      console.info(e);
      let inputValue = this.inputValue;
      if (inputValue) {
        if (!this.statements.some((v) => v == inputValue)) {
          this.statements.push(inputValue);
        }
        console.info("执行了");
        this.inputValue = "";
      }
    },
  },
};
</script>

<style lang="scss">
.tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .tag {
    margin-right: 10px;
  }
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
