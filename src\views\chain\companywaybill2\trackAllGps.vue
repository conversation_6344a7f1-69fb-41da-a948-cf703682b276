<template>
  <div class="trackAllGps">
    <el-drawer
      size="100%"
      title="轨迹"
      center
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
    <basic-container>
      <div
        class="search"
        style="margin-bottom: 20px"
        v-loading="tableLoading"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
      >
        <div class="gd-maps">
          <div id="mapsAll"></div>
          <div class="panel">
            <div class="routes-container">
              <div class="route-item" v-for="(item,index) in trackList" :key="index">
                <div :style="{color:filterColor(item)}" style="cursor: pointer;" @click="changActive(item)">
                  <div class="bg" :style="{background: filterColor(item)}"></div><h3>{{item.name}}</h3>
                    <!-- <el-color-picker v-model="color1" v-if="item.label=='建运宝轨迹'" @change="event=>changeColor(event,item)"></el-color-picker> -->
                    <!-- <el-color-picker v-model="color2" show-alpha v-if="item.label=='中交兴路轨迹'"></el-color-picker> -->
                  </div>
                <!-- <el-button type="primary" size="mini" @click="startAnimation(item,index)">开始</el-button>
                <el-button type="primary" size="mini" @click="pauseAnimation(index)">暂停</el-button>
                <el-button type="primary" size="mini" @click="resumeAnimation(item,index)">继续</el-button>
                <el-button type="primary" size="mini" @click="stopAnimation(index)">停止</el-button> -->
                <!-- <div class="speedBox">
                  <span>
                    时速:{{item.speed}}km/h
                    </span>
                    <el-slider
                      v-model="item.speed"
                      :min="30"
                      :max="100000"
                    ></el-slider>
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </basic-container>
  </el-drawer>
  </div>
</template>

<script>
export default {
  name: "trackAllGps",
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    trackList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      marker: null,
      map: null,
      pathSimplifierIns: null,
      markers: [],
      tableLoading: false,
      pathNavigs:[],
      color1: '#0055FF',
      color2: '#DD00FF',
    };
  },
  created() {},
  mounted() {
    console.log(11111111111111);
    this.$nextTick(() => {
      this.map = new AMap.Map("mapsAll", {
        resizeEnable: true, //窗口大小调整
        center: [113.986409, 22.547753], //中心 firstArr: [116.478935, 39.997761],
        zoom: 17,
      });
      this.initMap()
    });
  },
  activated() {},
  computed: {},
  methods: {
    cancel(){
      this.$emit('update:visible',false)
    },
    filterColor(item){
      let color = '#f56c6c'
      switch (item.label) {
        case "建运宝轨迹":
          color = this.color1
          break;
        case "中交兴路轨迹":
          color = this.color2
          break;
        case "自定义轨迹":
          color = '#909399'
          break;
        case "标准轨迹":
          color = '#f56c6c'
          break;
        default:
        color = '#f56c6c'
          break;
      }
      return color
    },
    // changeColor(color,item){
    //   console.log(color);
    //   console.log(item);
    //   item.polyLine.setOptions({strokeColor:color})
    // },
    changActive(item){
      this.map.setFitView(item.polyLine); //合适的视口
    },
    clearMarker() {
      this.map.remove(this.markers);
      if (this.pathSimplifierIns) {
        this.pathSimplifierIns.setData([]);
        this.pathSimplifierIns = null;
        this.pathNavigs = []
      }
    },
    //初始化地图
    initMap() {
      this.tableLoading = true
      // 创建一个起点 Icon
      var startIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(25, 34),
        // 图标的取图地址
        image:
          "//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(135, 40),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(-9, -3),
      });
      // 创建一个终点 Icon
      var endIcon = new AMap.Icon({
        size: new AMap.Size(25, 34),
        image:
          "//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
        imageSize: new AMap.Size(135, 40),
        imageOffset: new AMap.Pixel(-95, -3),
      });
      this.trackList.forEach((element) => {
        let startMarker = new AMap.Marker({
          map: this.map,
          position: element.points[0].lnglat,
          icon: startIcon,
          offset: new AMap.Pixel(-13, -30),
          label: {
            // offset: new AMap.Pixel(10, 10), //设置文本标注偏移量
            content: `<div class='markerLabel'><span>${element.label}</span><div class='left-label'><img src='https://s3.bmp.ovh/imgs/2022/04/18/96628058ce87c40a.png' />(起) ${element.points[0].name}</div></div>`, //设置文本标注内容
            direction: "top", //设置文本标注方位
            offset: new AMap.Pixel(-13, -30),
          },
        });
        let endMarker = new AMap.Marker({
          map: this.map,
          position: element.points[element.points.length - 1].lnglat,
          icon: endIcon,
          offset: new AMap.Pixel(-13, -30),
          label: {
            // offset: new AMap.Pixel(10, 10), //设置文本标注偏移量
            content: `<div class='markerLabel'><span>${
              element.label
            }</span><div class='left-label'><img src='https://s3.bmp.ovh/imgs/2022/04/18/96628058ce87c40a.png' />(终) ${
              element.points[element.points.length - 1].name
            }</div></div>`, //设置文本标注内容
            direction: "top", //设置文本标注方位
          },
        });
        this.markers.push(startMarker);
        this.markers.push(endMarker);
        element.polyLine =  new AMap.Polyline({
          map: this.map,
          path: element.lngLatArr,
          showDir: true,
          buildingAnimation: true,
          // lineJoin: 'round',
          // lineCap: 'round',
          strokeColor: this.filterColor(element), //线颜色--蓝色
          strokeWeight: 10, //线宽
        });
      });
      // let that = this
      // AMapUI.load(["ui/misc/PathSimplifier", "lib/$"], (PathSimplifier, $) => {
      //   if (!PathSimplifier.supportCanvas) {
      //     alert("当前环境不支持 Canvas！");
      //     return;
      //   }
      //   this.pathSimplifierIns = new PathSimplifier({
      //     zIndex: 100,
      //     //autoSetFitView:false,
      //     map: this.map, //所属的地图实例
      //     getPath: function (pathData, pathIndex) {
      //       var points = pathData.points,
      //         lnglatList = [];

      //       for (var i = 0, len = points.length; i < len; i++) {
      //         lnglatList.push(points[i].lnglat);
      //       }

      //       return lnglatList;
      //     },
      //     getHoverTitle: function (pathData, pathIndex, pointIndex) {
      //       if (pointIndex >= 0) {
      //         //point
      //         return pathData.name + "，" + pathData.points[pointIndex].name;
      //       }

      //       return "轨迹点数量" + pathData.points.length;
      //     },
      //     renderOptions: {
      //       renderAllPointsIfNumberBelow: 1000000, //绘制路线节点，如不需要可设置为-1
      //       pathLineStyle: {
      //         dirArrowStyle: true,
      //       },
      //       getPathStyle: function (pathItem, zoom) {
      //         console.log(pathItem);
      //         var color = that.filterColor(pathItem.pathData)
      //         return {
      //           pathLineStyle: {
      //             strokeStyle: color,
      //             lineWidth: 6,
      //           },
      //           pathLineHoverStyle: {
      //             strokeStyle: color,
      //             lineWidth: 6,
      //           },
      //           pathLineSelectedStyle: {
      //             lineWidth: 6,
      //           },
      //           pathNavigatorStyle: {
      //             fillStyle: color,
      //           },
      //         };
      //       },
      //     },
      //   });
      //   console.log(this.trackList);
      //   //设置数据
      //   this.pathSimplifierIns.setData(this.trackList);
      //   let that = this;
      //   this.pathNavigs = []
      //   this.trackList.forEach((item,index)=>{
      //     let navg = this.pathSimplifierIns.createPathNavigator(index, {
      //       loop: false, //循环播放
      //       speed: 10000, //巡航速度，单位千米/小时
      //       pathNavigatorStyle: {
      //         width: 69,
      //         height: 39,
      //         initRotateDegree: 270,
      //         autoRotate: true,
      //         pathLinePassedStyle: {
      //           strokeStyle: "#AF5", //线颜色
      //           lineWidth: 6, //线宽
      //         },
      //         content: PathSimplifier.Render.Canvas.getImageContent(
      //           require("../../../static/trucks.png"),
      //           () => {
      //             that.pathSimplifierIns.renderLater();
      //           },
      //           () => {
      //             alert("图片加载失败！");
      //           }
      //         ),
      //       },
      //     });
      //     navg.marker = new AMap.Marker({
      //         offset: new AMap.Pixel(-12, -50),
      //         content: `<div></div>`,
      //         map: that.map
      //     });
      //     navg.on('move', function(e,data) {
      //       navg.marker.setPosition(navg.getPosition());
      //       let idx = data.dataItem.pointIndex
      //       let pathData = item
      //       let time = pathData.points[idx].name
      //       let content = `<div class='markerLabel'><span>${pathData.label}</span><div class='left-label'><img src='https://s3.bmp.ovh/imgs/2022/04/18/96628058ce87c40a.png' />(经)${time}</div></div>`
      //       navg.marker.setContent(content);
      //     });

      //     that.pathNavigs.push(navg)
      //   })
      // console.log(this.pathNavigs);
      // });
      this.map.setFitView(); //合适的视口
      this.tableLoading = false
    },
    startAnimation(item,index) {
      // this.marker.moveAlong(this.lineArr, this.speed);
      let navgtr = this.pathNavigs[index]
      navgtr.setSpeed(item.speed);
      navgtr.start();
    },
    pauseAnimation(index) {
      this.pathNavigs[index].pause();
    },
    resumeAnimation(item,index) {
      this.pathNavigs[index].setSpeed(item.speed);
      this.pathNavigs[index].resume();
    },
    stopAnimation(index) {
      // this.pathNavigs[index].getPathStartIdx();
      this.pathNavigs[index].moveToPoint(this.pathNavigs[index].getPathStartIdx());
      this.pathNavigs[index].stop();
      // this.marker.stopMove();
    },
  },
};
</script>
<style scoped lang="scss">
.gd-maps {
  position: relative;
  #mapsAll {
    height: calc(100vh - 160px);
  }
  .panel {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 999;
    height: calc(100% - 5px);
    overflow: hidden;
    overflow-y: auto;
    // background-color: rgba(0, 0, 0, 0.4);
    .routes-container{
      display: flex;
      .route-item{
      // margin-right: 6px;
      padding:6px 10px 0px;
      background-color: #fff;
      >div{
        display: flex;
      flex-direction: column;
      align-items: center;
      }
        h3{
          // color: #2b89fb;
          margin-bottom: 4px;
          font-size: 16px;
          // &.red{
          //   color: #ff0000;
          // }
        }
        .speedBox{
          display: flex;
          align-items: center;
          font-size: 12px;
          .el-slider{
            flex: 1;
            margin-left: 6px;
          }
        }
      }
    }
  }
}
/deep/ .amap-marker{
  background: rgba(0, 0, 0, 0.4);
  width: 200px;
  border-radius: 4px;

  .markerLabel {
  padding: 6px 0px 4px 6px;
  font-size: 14px;
    color: #ffff;
    span {
      margin-bottom: 4px;
    }
  }
}
/deep/ .amap-marker-label {
  background: rgba(0, 0, 0, 0.4);
  .markerLabel {
    color: #ffff;
    span {
      margin-bottom: 4px;
    }
  }
}
/deep/.left-label,
.right-label {
  padding: 2px;
  display: flex;
  align-items: center;
  color: #ffff;
}

/deep/ .left-label img {
  width: 14px;
  height: 14px;
  margin-right: 10px;
}

/deep/ .right-label img {
  width: 14px;
  height: 14px;
  margin-right: 10px;
}
.bg {
      width: 26px;
      height: 14px;
      border-radius: 2px;
      margin-bottom: 4px;
    }
</style>
