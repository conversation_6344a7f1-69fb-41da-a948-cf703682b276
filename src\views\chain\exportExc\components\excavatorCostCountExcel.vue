<template>
    <div ref="print">
      <avue-crud
        ref="crud"
        :data="tableData"
        :table-loading="tableLoading"
        :option="tableOption"
      >
      </avue-crud>
    </div>
  </template>

  <script>
  import { tableOption7 } from "@/const/crud/chain/exportExc";
  import { mapGetters } from "vuex";
  import { queryByCode } from "@/api/chain/companywaybill.js";
  import { print } from "@/util/util.js";

  export default {
    name: "excavatorCostCountExcel",
    data() {
      return {
        form: {},
        paramsSearch: {},
        tableLoading: false,
        tableOption: tableOption7,
        tableData:[],
      };
    },
    created() {},
    mounted: function () {
    },
    computed: {
      ...mapGetters(["permissions"]),

    },
    methods: {
      queryByCode(form) {
        tableOption7.column[0].children[1].label = '-'
        let params = Object.assign({}, form);
        if (params.goDatetime && params.goDatetime.length > 0) {
          params.goDatetimeStart = params.goDatetime[0];
          params.goDatetimeEnd = params.goDatetime[1];
          tableOption7.column[0].children[1].label = params.goDatetimeStart+' — '+params.goDatetimeEnd
        }
        delete params.goDatetime;
        console.log(form);
        params.code = "excavatorCostCountExcel";
        tableOption7.column[0].children[5].label = form.$projectInfoId
        this.tableOption = JSON.parse(JSON.stringify(tableOption7));
        this.$refs.crud.doLayout();
        this.$refs.crud.refreshTable();
        this.tableLoading = true;
        queryByCode(params)
          .then((res) => {
            this.tableLoading = false;
            this.tableData = res.data.data;
            if(this.tableData&&this.tableData.length>0){
                this.tableData.push({
                    owner:'车数累计:',
                    price:'杂技累计:',
                    machine:'总费用:',
                })
            }
          })
          .catch(() => {
            this.tableLoading = false;
          });
      },
      print(){
        console.log(this.$refs.print);
        print(this.$refs.print)
      },
    },
  };
  </script>

  <style lang="scss" scoped>
  /deep/ .closeIcon,
  .checkIcon {
    font-size: 16px;
    color: red;
  }
  /deep/ .checkIcon {
    color: #3dcc90;
  }
  /deep/ .avue-crud__pagination{
    display: none;
  }
  </style>
