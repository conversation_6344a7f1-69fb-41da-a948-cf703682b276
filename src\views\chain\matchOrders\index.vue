<template>
  <div class="matchOrders">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="menu"
                  slot-scope="scope">
          <el-button size="mini"
                     type="text"
                     icon="el-icon-circle-close"
                     v-if="permissions['chain:matchOrders:cancel']&&scope.row.orderStatus==1"
                     @click="cancel(scope.row)">取消
          </el-button>
          <el-button size="mini"
                     type="text"
                     icon="el-icon-promotion"
                     v-if="permissions['chain:matchOrders:schedule']"
                     @click="schedule(scope.row)">订单进度
          </el-button>
          <el-button size="mini"
                     type="text"
                     icon="el-icon-info"
                     v-if="permissions['chain:matchOrders:waybill']"
                     @click="waybill(scope.row)">运单
          </el-button>
        </template>
      </avue-crud>
      <order-progress v-if="progressVisible"
                      :info="info"
                      :visible.sync="progressVisible"></order-progress>
      <waybill-info v-if="waybillVisible"
                      :info="info"
                      :visible.sync="waybillVisible"></waybill-info>
    </basic-container>
  </div>
</template>

<script>
import { getPage,cancelOrder } from '@/api/chain/matchOrders'
import { tableOption } from '@/const/crud/chain/matchOrders'
import { mapGetters } from 'vuex'
import orderProgress from './orderProgress.vue'
import waybillInfo from './waybillInfo.vue'

export default {
  name: 'matchOrders',
  components: {
    orderProgress,
    waybillInfo,
  },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      info: {},
      progressVisible: false,
      waybillVisible: false,
    }
  },
  created () {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList () {
      return {
        addBtn: this.permissions['chain:matchOrders:add'] ? true : false,
        delBtn: this.permissions['chain:matchOrders:del'] ? true : false,
        editBtn: this.permissions['chain:matchOrders:edit'] ? true : false,
        viewBtn: this.permissions['chain:matchOrders:get'] ? true : false,
        excelBtn: this.permissions['chain:matchOrders:excel'] ? true : false,
      };
    }
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage (page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        type: 3,
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page)
    },
    schedule(row){
      this.info = row
      this.progressVisible = true
    },
    cancel(row){
      this.$confirm('确定取消所选订单？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.tableLoading = true
          cancelOrder(row.id).then(res=>{
            this.tableLoading = false
            this.getPage(this.page)
          }).catch(()=>{
            this.tableLoading = false
          })
        }).catch(() => {});
    },
    waybill(row){
      this.info = row
      this.waybillVisible = true
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
