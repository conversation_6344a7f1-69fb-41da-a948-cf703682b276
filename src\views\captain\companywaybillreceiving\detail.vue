<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%" title="详情" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <avue-crud ref="crud" :data="tableData" :page.sync="page" :table-loading="tableLoading" :option="tableOption" v-model="form"
          @on-load="getPage" >
          <template slot="menuLeft" slot-scope="{ row }">
            <el-button
                size="small"
                icon="el-icon-download"
                type="primary"
                @click="exOut"
              >
                导出
            </el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {getPage2} from '@/api/captain/companywaybillreceiving'
import { expotOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {},
  },
  data() {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      tableData: [],
      paramsSearch: {

      },
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 12,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: true,
        delBtn: false,
        menu: false,
        searchMenuSpan: 6,
        index: false,
        refreshBtn: false,
        column: [
          {
            label: "企业",
            prop: "companyAuthName",
          },
          {
            label: "项目",
            prop: "projectName",
          },
          {
            label: "运单号",
            prop: "waybillNo",
          },
          {
            label: "车队长",
            prop: "fleetName",
          },
          {
            label: "司机",
            prop: "driverName",
          },
          {
            label: "车牌",
            prop: "truckCode",
          },
          {
            label: "出场签单员",
            prop: "goStaffName",
          },
          {
            label: "出场时间",
            prop: "goDateTime",
          },
          {
            label: "土质",
            prop: "soilType",
          },
          {
            label: "泥尾",
            prop: "garbageName",
          },
          {
            label: "运输方式",
            prop: "tpMode",
          },
        ],
      },
    }
  },
  created() {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    getPage(page, params = {}) {
      this.tableLoading = true
      getPage2(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        id:this.info.id
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    exOut(){
      let params = Object.assign({},this.paramsSearch)
      let url = '/chain/deliveryItem/excel'
      params.id = this.info.id
      expotOut( params,url,'收单管理详情');
    },
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .drawerHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  li {
    color: rgba(0, 0, 0, .85);
    font-size: 14px;
    font-weight: 700;
    margin-right: 30px;
  }
}

.demo-block-control {
  border-top: 1px solid #eaeefb;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;
  background-color: #fff;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  text-align: center;
  margin-top: -1px;
  color: #d3dce6;
  cursor: pointer;
  position: relative;

  &:hover {
    color: #409eff;
    background-color: #f9fafc;
  }
}

</style>
