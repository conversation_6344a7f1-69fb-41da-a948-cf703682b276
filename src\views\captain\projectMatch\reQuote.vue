<template>
  <div class="reQuote">
    <el-dialog width="400px" title="重新报价" center :visible.sync="visible" :before-close="cancelModal"
      :close-on-click-modal="false">
      <avue-form ref="form" :option="option" v-model="form"></avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelModal">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitPayee">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { matchplanpoollatestoffer} from "@/api/captain/projectMatch";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  components: {

  },
  data () {
    return {
      form: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 60,
        column: [
          {
            label: "运费",
            prop: "latestOfferPrice",
            type: "number",
            span: 16,
            minRows: 0,
            maxRows: 99999.99,
            precision: 2,
            controls: false,
            rules: [
              {
                required: true,
                message: "请输入 运费",
                trigger: "blur",
              },
              {
                validator: (rule, value, callback) => {
                  console.log(value);
                  if (value === 0) {
                    callback(new Error('运费不能为0哦'));
                  } else {
                    callback();
                  }
                },
                trigger: "blur",
              },
            ],
          },
          {
            label: "",
            prop: "latestOfferPriceUnit",
            span: 8,
            type: "select",
            gutter: 0,
            labelWidth: -20,
            disabled: true,
            placeholder: " ",
            dicData: [
              {
                label: "元/车",
                value: "元/车",
              },
              {
                label: "元/吨",
                value: "元/吨",
              },
              {
                label: "元/方",
                value: "元/方",
              },
            ],
          },
        ]
      },
      loading: false,
    };
  },
  created () { },
  mounted: function () {
    this.form = {
      matchPlanPoolId: this.info.id,
      offerPlanId: this.info.offerPlanId,
      latestOfferPrice: this.info.latestOfferPrice,
      latestOfferPriceUnit: this.info.targetPriceUnit,
    }
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    submitPayee () {
      this.$refs.form.validate((valid,done) => {
        if (valid) {
          console.log(1111);
          this.loading = true
          matchplanpoollatestoffer(this.form).then(res=>{
            this.loading = false
            this.cancelModal()
            this.$emit("refreshChange");
          }).catch(()=>{
            this.loading = false
          })
          done()
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
