<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @sort-change="sortChange"
        @row-save="handleSave"
        @row-del="handleDel"
        @search-change="searchChange"
      >
        <template slot="menuLeft" slot-scope="{ row }">
          <el-button
            icon="el-icon-document-add"
            v-if="permissions['chain:insideGarbageTicketClaim:buy']"
            size="small"
            type="primary"
            @click="add(1)"
          >
            领票
          </el-button>
          <el-button
            icon="el-icon-document-remove"
            v-if="permissions['chain:insideGarbageTicketClaim:refund']"
            size="small"
            type="primary"
            @click="add(2)"
          >
            退票
          </el-button>
          <el-button
            icon="el-icon-download"
            v-if="permissions['chain:insideGarbageTicketClaim:excel']"
            size="small"
            type="primary"
            @click="exOut"
          >
            导出
          </el-button>
        </template>

        <template slot-scope="{ disabled, size }" slot="recipientLabel">
          <span>{{ type == 2 ? "退票人" : "领取人" }}</span>
        </template>
        <template slot-scope="{ disabled, size }" slot="recipientNumLabel">
          <span>{{ type == 2 ? "退泥尾票数" : "领取泥尾票数量(张)" }}</span>
        </template>

        <template slot-scope="{ disabled, size }" slot="recipientDatetimeLabel">
          <span>{{ type == 2 ? "退票时间" : "领票日期" }}</span>
        </template>
        <template slot-scope="{ disabled, size }" slot="lineForm"> - </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {
  getPage,
  addObj,
  delObj,
  refund,
} from "@/api/chain/insideGarbageTicketClaim";
import { tableOption } from "@/const/crud/chain/insideGarbageTicketClaim";
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";
export default {
  name: "insideGarbageTicketClaim",
  data() {
    return {
      projects: [],
      type: "",
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: "recipient_datetime", //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption(this),
    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:insideGarbageTicketClaim:add"]
          ? true
          : false,
        delBtn: this.permissions["chain:insideGarbageTicketClaim:del"]
          ? true
          : false,
        editBtn: this.permissions["chain:insideGarbageTicketClaim:edit"]
          ? true
          : false,
        viewBtn: this.permissions["chain:insideGarbageTicketClaim:get"]
          ? true
          : false,
        excelBtn: this.permissions["chain:insideGarbageTicketClaim:excel"]
          ? true
          : false,
      };
    },
  },
  methods: {
    add(val) {
      this.type = val;
      if (val == 2) {
        let ruleFields = ["startNo", "endNo"];
        let displayTrueFields = ["projectInfoId", "inventory"];
        this.$refs.crud.tableOption.column =
          this.$refs.crud.tableOption.column.map((v, i) => {
            console.info(i + "-------------" + v.prop);
            if (ruleFields.includes(v.prop)) {
              v.rules = [];
            }
            if('projectInfoId' == v.prop){
              v.dicUrl= `/chain/projectinfo/queryStartedProjectByCompanyAuthIdOfTicket?type=${this.type}`
            }
            if ("recipientNum" == v.prop) {
              var validate = (rule, value, callback) => {
                if (value > this.form.inventory) {
                  callback(new Error("退票数不能大于库存数"));
                } else {
                  callback();
                }
              };
              v.rules = [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur",
                },
                {
                  validator: validate,
                  trigger: "blur",
                },
              ];
            }
            if (displayTrueFields.includes(v.prop)) {
              v.display = true;
            }
            return v;
          });

        this.$set(this.tableOption, "addTitle", "退票");
      } else {
        let displayTrueFields = ["projectInfoId"];
        let displayFalseFields = ["inventory"];
        this.$refs.crud.tableOption.column =
          this.$refs.crud.tableOption.column.map((v, i) => {
            if ("recipientNum" == v.prop) {

              v.rules = [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur",
                }
              ];
            }
            if('projectInfoId' == v.prop){
              v.dicUrl= `/chain/projectinfo/queryStartedProjectByCompanyAuthIdOfTicket?type=${this.type}`
            }
            if (displayTrueFields.includes(v.prop)) {
              v.display = true;
            }
            if (displayFalseFields.includes(v.prop)) {
              v.display = false;
            }
            return v;
          });
        this.$set(this.tableOption, "addTitle", "领票");
      }
      this.$refs.crud.rowAdd();
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
        if (params.hasOwnProperty("recipient")) {
          params.keywords = params.recipient;
          delete params.recipient;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      console.log(row, "row");
      row.type = this.type;
      if (row.type == 1) {
        addObj(row)
          .then((response) => {
            this.$message({
              showClose: true,
              message: "领票成功",
              type: "success",
            });
            done();
            this.getPage(this.page);
          })
          .catch(() => {
            loading();
          });
      } else {
        row.agentId = row.recipient;
        row.agentDatetime = row.recipientDatetime;
        refund(row)
          .then((response) => {
            this.$message({
              showClose: true,
              message: "退票成功",
              type: "success",
            });
            done();
            this.getPage(this.page);
          })
          .catch(() => {
            loading();
          });
      }
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    exOut() {
      let params = Object.assign({}, this.paramsSearch);
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
      }
      let url = "/chain/garbagecustomerreceive/exportExcel";
      expotOut(params, url, "内部票领取");
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
