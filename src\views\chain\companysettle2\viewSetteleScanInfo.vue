<template>
  <div class="viewSetteleScanInfo">
    <el-drawer
      size="70%"
      title="查看电子结算卡"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <el-descriptions>
            <el-descriptions-item label="交卡回执单号">{{info.giveCardNo}}</el-descriptions-item>
            <el-descriptions-item label="交卡总数">{{info.cardCount}}</el-descriptions-item>
            <el-descriptions-item label="运单总数">{{info.giveCardCnt}}</el-descriptions-item>
            <el-descriptions-item label="可结算运单数">{{info.settleAbleCount}}</el-descriptions-item>
            <el-descriptions-item label="交卡时间">{{info.giveCardDatetime}}</el-descriptions-item>
            <el-descriptions-item label="交卡人">{{info.giveCardUserId}}</el-descriptions-item>
        </el-descriptions>
        <avue-crud
          ref="cruds"
          :data="tableData"
          :table-loading="tableLoading"
          :option="tableOption"
          v-model="form"
          @on-load="getPage"
        >
          <template slot="menu" slot-scope="{ row }">
            <el-button
              type="text"
              icon="el-icon-view"
              size="small"
              plain
              @click="lookWaybill(row)"
            >
              查看可结算运单</el-button
            >
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
       <waybill
        v-if="waybillVisible"
        :isHistory="true"
        :cardNo="curCardNo"
        :giveCardNo="info.giveCardNo"
        :visible.sync="waybillVisible"
      ></waybill>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {selectWaybillsGiveNoGenerated} from "@/api/chain/companysettle";
import { expotOut } from "@/util/down.js";
import waybill from "./waybill.vue";

export default {
  components: {
    waybill,
  },
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: ()=>{
        return {}
      },
    },
  },
  data() {
    return {
      form: {},
      tableData: [],
      btnLoading: false,
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 12,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchMenuSpan: 6,
        index: false,
        refreshBtn: false,
        menuWidth:140,
        defaultSort: {
          prop: "purchaseDate",
          order: "ascending",
        },
        column: [
          {
            label: "电子结算卡号码",
            prop: "settleCardNo",
          },
          {
            label: "卡类型",
            prop: "cardType",
          },
          // {
          //   label: "项目名称",
          //   prop: "projectName",
          // },
          {
            label: "运单总数",
            prop: "giveCardCnt",
          },
          {
            label: "可结算运单数",
            prop: "settleAbleCount",
          },
        ],
      },
      curCardNo: "",
      waybillVisible:false,
    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },

    getPage(params = {}) {
      this.tableLoading = true;
        selectWaybillsGiveNoGenerated(
          Object.assign(
            {
              giveCardNo: this.info.giveCardNo,
            },
            params,
            this.paramsSearch
          )
        )
          .then((response) => {
            this.tableData = response.data.data;
            this.tableLoading = false;
          })
          .catch(() => {
            this.tableLoading = false;
          });
    },
     lookWaybill(item) {
     this.curCardNo = item.settleCardNo
      this.waybillVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

</style>
