<template>
  <div class="homeDashboard" v-loading="loading">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="margin-right: 20px">数据看板</span>
        <div class="time" style="float: right;">
          <el-radio-group v-model="checkDynamic" size="small" style="margin-right:10px" @change="changeStatus">
            <el-radio-button label="1">活跃项目 {{ projectInfo.dynamicWaybillCount }}</el-radio-button>
            <el-radio-button label="">全部项目 {{ projectInfo.projectCount }}</el-radio-button>
          </el-radio-group>
          <el-radio-group v-model="radio" size="small" @change="changeTime">
            <el-radio-button label="week">最近一周</el-radio-button>
            <el-radio-button label="month">最近一个月</el-radio-button>
            <el-radio-button label="year">最近一年</el-radio-button>
          </el-radio-group>
          <el-date-picker style="margin-left: 8px;margin-right: 8px;" v-model="searchDate" type="daterange" :editable="false"
            :pickerOptions="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            size="small" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false">
          </el-date-picker>
          <el-button type="primary" size="small" @click="search">查询</el-button>
        </div>
      </div>
      <div class="avue-tags-box" v-if="tagList && tagList.length > 0">
        <ul class="flex flex-wrap">
          <li v-for="item in tagList" :key="item.id" :class="activeProjectId == item.id ? 'active' : ''"
            @click="changeProject(item)">{{ item.projectName }}</li>
        </ul>
        <el-select v-model="activeProjectId" size="mini" filterable placeholder="请选择" @change="changeActive"
          style="position: absolute;right: 0;top: 0;width:206px">
          <el-option v-for="item in tagList" :key="item.id" :label="item.projectName" :value="item.id">
          </el-option>
        </el-select>
      </div>
    </el-card>
    <el-row :gutter="10" type="flex">
      <el-col :span="16">
        <el-row :gutter="10" class="countStatistics">
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="lis" @click="detail('totalWaybillVisible')">
              <div class="title flex flex-between">
                <div class="left">出车运单总数</div>
                <div class="right"></div>
              </div>
              <div class="num flex flex-between">
                <div class="left">{{ waybillCountInfo.allWaybillCount }} <span>个</span></div>
                <div class="right tip">
                  <div class="count">{{ waybillCountInfo.withinWaybillCount }}个</div>
                  <div class="label">内运单总数</div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="lis" @click="detail('totalFleetVisible')">
              <div class="title flex flex-between">
                <div class="left">车队长总数</div>
                <div class="right"></div>
              </div>
              <div class="num">
                <div class="left">{{ captainCount }} <span>个</span></div>
                <div class="right">

                </div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="lis" @click="detail('totalGarbageVisible')">
              <div class="title flex flex-between">
                <div class="left">泥尾点总数</div>
                <div class="right"></div>
              </div>
              <div class="num">
                <div class="left">{{ garbageCount }} <span>个</span></div>
                <div class="right">

                </div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="lis" @click="detail('totalExcavatedVisible')" style="position: relative;">
              <div class="title flex flex-between">
                <div class="left"><span style="padding-right:4px;">总出土量</span><el-tooltip class="item" effect="dark"
                    placement="top-start"><i class="el-icon-question"></i><template #content>
                      <div v-show="weightUnit == '方'">
                        <div>总出土方量</div>
                        <div>1.单位为(方)的运单方量总和 X</div>
                        <div>2.单位为(车)的运单数x11.6的方量总和 Y</div>
                        <div>3.内运单方量总和 Z</div>
                        <div>X+Y-Z</div>
                      </div>
                      <div v-show="weightUnit == '吨'">
                        <div>总出土吨量</div>
                        <div>单位为(吨)的运单吨量总和</div>
                      </div>
                    </template></el-tooltip></div>
                <div class="right"></div>
              </div>
              <div class="num flex flex-between">
                <div class="left">{{ weightUnit == '方' ? excavatedNumInfo.partyNumber : excavatedNumInfo.tonsNumber }}
                  <span>{{
                    weightUnit }}</span>
                </div>
                <div class="right">
                  <el-select v-model="weightUnit" size="mini" filterable placeholder="请选择"
                    style="position: absolute;right: 10px;top: 16px;width:66px">
                    <el-option v-for="item in unitList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </div>
              </div>
            </div>
          </el-col>
          <!-- <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="lis" @click="detail('waybillInfoVisible')">
              <div class="title flex flex-between">
                <div class="left" @click.stop="">
                  <el-radio-group size="mini" v-model="wayRadio">
                    <el-radio-button label="1">运单费</el-radio-button>
                    <el-radio-button label="2">运单数</el-radio-button>
                  </el-radio-group>
                </div>
                <div class="right">
                  <div class="count" v-show="wayRadio == 1">{{ boardWayInfo.waitPaymentTotalAmount }}元</div>
                  <div class="count" v-show="wayRadio == 2">{{ boardWayInfo.waitWaybillTotal }}个</div>
                  <div class="label">未付</div>
                </div>
              </div>
              <div class="num flex flex-between">
                <div class="left" v-show="wayRadio == 1">{{ boardWayInfo.totalAmount }} <span>元</span></div>
                <div class="left" v-show="wayRadio == 2">{{ boardWayInfo.totalWaybill }} <span>个</span></div>
                <div class="right">
                  <div class="count" v-show="wayRadio == 1">{{ boardWayInfo.paidTotalAmount }}元</div>
                  <div class="count" v-show="wayRadio == 2">{{ boardWayInfo.paidWaybillTotal }}个</div>
                  <div class="label">已付</div>
                </div>
              </div>
            </div>
          </el-col> -->
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="lis" @click="detail('excavatorInfoVisible')">
              <div class="title flex flex-between">
                <div class="left" @click.stop="">
                  <el-radio-group size="mini" v-model="radio2">
                    <el-radio-button label="挖机装车数"></el-radio-button>
                    <el-radio-button label="台班总时长"></el-radio-button>
                  </el-radio-group>
                </div>
                <div class="right"></div>
              </div>
              <div class="num">
                <div class="left">{{ radio2 == '挖机装车数' ? ledgerDigCountInfo.excavatorLoadingCount :
                  ledgerDigCountInfo.workHour }}
                  <span>{{ radio2 == '挖机装车数' ? '车' : '小时' }}</span>
                </div>
                <div class="right">

                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="8">
        <div class="width30" v-loading="waybillAnalysisLoading" @click="detail('waybillAnalysisVisible')">
          <div class="top" @click.stop="">
            <span class="title">运单分析</span>
            <el-radio-group size="mini" v-model="waybillAnalysisRadio" @change="changWaybillAnalysis">
              <el-radio-button label="1">土质类型</el-radio-button>
              <el-radio-button label="2">结算状态</el-radio-button>
              <el-radio-button label="3">运单类型</el-radio-button>
              <el-radio-button label="4">运费类型</el-radio-button>
              <el-radio-button label="5">异常运单</el-radio-button>
            </el-radio-group>
          </div>
          <div class="bottom">
            <div class="pie">
              <pieChart :option="option1"></pieChart>
            </div>
            <div class="tooltip tooltip2" v-show="waybillAnalysisRadio != '4'">
              <ul v-if="wayTipList && wayTipList.length > 0">
                <li v-for="(item, index) in  wayTipList " :key="index">
                  <div><span class="rotundity" :style="{ 'background-color': colors[index] }"></span><span
                      class="tipLabel">{{ item.name }}</span></div>{{ item.value }}单({{
                        item.percentage }}%)
                </li>
              </ul>
            </div>
            <div class="tooltip tooltip2 tooltip3" v-show="waybillAnalysisRadio == '4'">
              <ul v-if="wayTipList && wayTipList.length > 0">
                <li v-for="(item, index) in  wayTipList " :key="index">
                  <div><span class="rotundity" :style="{ 'background-color': colors[index] }"></span>
                    <!-- <span class="tipLabel">{{ item.name }}</span> -->
                    <avue-text-ellipsis :key="item.name" :text="item.name" :height="17" use-tooltip placement="top">
                      <small slot="more">...</small>
                    </avue-text-ellipsis>
                  </div>{{ item.value }}单({{
                    item.percentage }}%)
                </li>
              </ul>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <div class="chart" v-loading="abnormalWayLoading" @click="detail('totalAbnormalWaybillVisible')">
          <div class="top">
            <span class="title">异常运单诊断</span>
          </div>
          <div class="bottom">
            <div class="pie">
              <pieChart :option="option2"></pieChart>
            </div>
            <div class="tooltip tooltip2">
              <ul>
                <li>
                  <div><span class="rotundity" style="background-color:#409EFF"></span><span
                      class="tipLabel">时间(补单)</span></div>{{ abnormalWayInfo.replenishmentTime }}单({{
                        getPercentWithPrecision(abnormalWayInfoList, 0) }}%)
                </li>
                <li>
                  <div><span class="rotundity" style="background-color:#66b1ff"></span><span class="tipLabel">改土质</span>
                  </div>{{ abnormalWayInfo.goSoilType }}单({{ getPercentWithPrecision(abnormalWayInfoList, 1) }}%)
                </li>
                <li>
                  <div><span class="rotundity" style="background-color:#8cc5ff"></span><span class="tipLabel">换司机</span>
                  </div>{{ abnormalWayInfo.inGoDriverUserId }}单({{ getPercentWithPrecision(abnormalWayInfoList, 2) }}%)
                </li>
                <li>
                  <div><span class="rotundity" style="background-color:#b3d8ff"></span><span
                      class="tipLabel">改车牌(签错车牌)</span></div>{{ abnormalWayInfo.inGoTruckCode }}单({{
                        getPercentWithPrecision(abnormalWayInfoList, 3) }}%)
                </li>
                <li>
                  <div><span class="rotundity" style="background-color:#d9ecff"></span><span
                      class="tipLabel">补单(挖机)</span></div>{{ abnormalWayInfo.inWaybill }}单({{
                        getPercentWithPrecision(abnormalWayInfoList, 4) }}%)
                </li>
                <li>
                  <div><span class="rotundity" style="background-color:#ebf5ff"></span><span class="tipLabel">签错班次</span>
                  </div>{{ abnormalWayInfo.inGoShiftTime }}单({{ getPercentWithPrecision(abnormalWayInfoList, 5) }}%)
                </li>
                <li>
                  <div><span class="rotundity" style="background-color:#50c67f"></span><span class="tipLabel">补照片</span>
                  </div>{{ abnormalWayInfo.inGoInpicture }}单({{ getPercentWithPrecision(abnormalWayInfoList, 6) }}%)
                </li>
                <li>
                  <div><span class="rotundity" style="background-color:#73d199"></span><span class="tipLabel">改备注</span>
                  </div>{{ abnormalWayInfo.inGoRemark }}单({{ getPercentWithPrecision(abnormalWayInfoList, 7) }}%)
                </li>
                <li>
                  <div><span class="rotundity" style="background-color:#96ddb2"></span><span class="tipLabel">改价格</span>
                  </div>{{ abnormalWayInfo.payeePrice }}单({{ getPercentWithPrecision(abnormalWayInfoList, 8) }}%)
                </li>
                <li>
                  <div><span class="rotundity" style="background-color:#b9e8cc"></span><span class="tipLabel">改运输方式</span>
                  </div>{{ abnormalWayInfo.tpModel }}单({{ getPercentWithPrecision(abnormalWayInfoList, 9) }}%)
                </li>
                <li>
                  <div><span class="rotundity" style="background-color:#dcf4e5"></span><span class="tipLabel">签错单位</span>
                  </div>{{ abnormalWayInfo.weightUnit }}单({{ getPercentWithPrecision(abnormalWayInfoList, 10) }}%)
                </li>
              </ul>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart" v-loading="outputAnalysisLoading" @click="detail('totalExcavatedVisible')">
          <!-- <div class="top">
            <span class="title">出土分析</span>
          </div> -->
          <div class="bottom">
            <div class="pie">
              <pieChart :option="option3"></pieChart>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart" v-loading="captainLoading" @click="detail('totalFleetVisible')">
          <div class="bottom">
            <div class="pie">
              <pieChart :option="option4"></pieChart>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <div class="chart" v-loading="garbageLoading" @click="detail('totalGarbageAnalysisVisible')">
          <div class="top" @click.stop="">
            <span class="title">泥尾分析</span>
            <el-radio-group size="mini" v-model="garbageUnitActive" @change="changeGarbage">
              <el-radio-button label="方"></el-radio-button>
              <el-radio-button label="吨"></el-radio-button>
            </el-radio-group>
          </div>
          <div class="bottom">
            <div class="pie">
              <pieChart :option="option5"></pieChart>
            </div>
            <div class="tooltip tooltip2">
              <ul v-if="garbageList && garbageList.length > 0">
                <li v-for="(item, index) in  garbageList " :key="index">
                  <div><span class="rotundity" :style="{ 'background-color': colors[index] }"></span><span
                      class="tipLabel">{{ item.name }}</span></div>{{ item.value }}{{ garbageUnitActive }}({{
                        getGarbagePercentage(index) }}%)
                </li>
              </ul>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="chart" style="position: relative;" v-loading="ledgerDigLoading"
          @click="detail('excavatorInfoVisible','echart')">
          <!-- <div class="top">
            <span class="title">出土分析</span>
          </div> -->
          <div @click.stop="">
            <el-radio-group size="mini" v-model="ledgerDigActive"
              style="position: absolute;right:10px;top:10px;z-index: 1;" @change="changeLedgerDig">
              <el-radio-button label="关联"></el-radio-button>
              <el-radio-button label="未关联"></el-radio-button>
            </el-radio-group>
          </div>
          <div class="bottom">
            <div class="pie">
              <pieChart :option="option6"></pieChart>
            </div>
          </div>
        </div>
      </el-col>
      <!-- <el-col :span="8">
        <div class="chart" style="position: relative;" v-loading="paymentAnalysisLoading"
          >
            <el-radio-group size="mini" v-model="paymentAnalysisRadio"
              style="position: absolute;right:10px;top:10px;z-index: 1;" @change="changePaymentAnalysis">
              <el-radio-button label="运费"></el-radio-button>
              <el-radio-button label="运单"></el-radio-button>
            </el-radio-group>
          <div class="bottom" @click="detail('waybillInfoVisible', 1)">
            <div class="pie">
              <pieChart :option="option7"></pieChart>
            </div>
          </div>
        </div>
      </el-col> -->
    </el-row>
    <!-- 总运单数 -->
    <totalWaybill v-if="totalWaybillVisible" :info="info" :visible.sync="totalWaybillVisible">
    </totalWaybill>
    <!-- 车队长总数 -->
    <totalFleet v-if="totalFleetVisible" :info="info" :visible.sync="totalFleetVisible"></totalFleet>
    <!-- 泥尾点总数 -->
    <totalGarbage v-if="totalGarbageVisible" :info="info" :visible.sync="totalGarbageVisible"></totalGarbage>
    <!-- 总出土量 -->
    <totalExcavated v-if="totalExcavatedVisible" :info="info" :visible.sync="totalExcavatedVisible">
    </totalExcavated>
    <!-- 运单数  运单费 -->
    <waybillInfo v-if="waybillInfoVisible" :info="info" :visible.sync="waybillInfoVisible">
    </waybillInfo>
    <!-- 挖机台班数  台班总时长 -->
    <excavatorInfo v-if="excavatorInfoVisible" :info="info" :visible.sync="excavatorInfoVisible">
    </excavatorInfo>
    <!-- 运单分析 -->
    <waybillAnalysis v-if="waybillAnalysisVisible" :info="info" :visible.sync="waybillAnalysisVisible">
    </waybillAnalysis>
    <!-- 异常运单数 -->
    <totalAbnormalWaybill v-if="totalAbnormalWaybillVisible" :info="info" :visible.sync="totalAbnormalWaybillVisible">
    </totalAbnormalWaybill>
    <!-- 总出土量 -->
    <totalGarbageAnalysis v-if="totalGarbageAnalysisVisible" :info="info" :visible.sync="totalGarbageAnalysisVisible">
    </totalGarbageAnalysis>
  </div>
</template>

<script>
import {
  getProjectList,
  getWaybillAmountCount,
  getCompanyWaybillUpdateHitTargetCount,
  garbageAnalysis,
  outputAnalysis,
  captainIncreaseTrend,
  digAnalysis,
  getExcavatedNumber,
  getWaybillCount,
  getDriverCaptainCount,
  getGarbageCount,
  getExcavatorLoadingAndDigCount,
  getWaybillAnalyzeCount,
  paymentAnalysis,
} from "@/api/chain/board";
import pieChart from "./components/pieChart";
import totalWaybill from "./components/totalWaybill";
import totalFleet from "./components/totalFleet";
import totalGarbage from "./components/totalGarbage";
import totalExcavated from "./components/totalExcavated";
import waybillInfo from "./components/waybillInfo";
import excavatorInfo from "./components/excavatorInfo";
import waybillAnalysis from "./components/waybillAnalysis/index.vue";
import totalAbnormalWaybill from "./components/totalAbnormalWaybill";
import totalGarbageAnalysis from "./components/totalGarbageAnalysis";
import { getPercentWithPrecision } from "@/util/util"
export default {
  name: "databoard",
  components: {
    pieChart,
    totalWaybill,
    totalFleet,
    totalGarbage,
    totalExcavated,
    waybillInfo,
    excavatorInfo,
    waybillAnalysis,
    totalAbnormalWaybill,
    totalGarbageAnalysis,
  },
  data () {
    return {
      info: {},
      monthInfo: {},
      // options: [],
      // time: "",
      searchDate: [this.$moment().subtract(1, 'weeks').format('YYYY-MM-DD'), this.$moment().format('YYYY-MM-DD')],
      checkDynamic: "1",
      radio: "week",
      tagList: [],
      activeProjectId: "",
      active: "1",
      map: null,
      wayRadio: "1",
      waybillAnalysisRadio: "1",
      paymentAnalysisRadio: "运费",
      radio2: "挖机装车数",
      weightUnit: "方",
      unitList: [{
        label: "方",
        value: "方",
      },
      {
        label: "吨",
        value: "吨",
      }],
      // 时间跨度为之前一年
      pickerOptions: {
        disabledDate: () => false,
        onPick: ({ maxDate, minDate }) => {
          if (minDate && !maxDate) {
            const oneYear = 365 * 24 * 60 * 60 * 1000;
            this.pickerOptions.disabledDate = time => {
              return time.getTime() < minDate.getTime() || time.getTime() > minDate.getTime() + oneYear;
            };
          } else {
            this.pickerOptions.disabledDate = () => false;
          }
        },
      },
      option1: {
        color: ['#409EFF', '#66b1ff', "#8cc5ff", "#b3d8ff", "#d9ecff", "#ebf5ff", "#50c67f", "#73d199", "#96ddb2", "#b9e8cc", "#dcf4e5"],
        tooltip: {
          confine: true,
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        graphic: [
          {//2、中心的文字设置
            type: 'text',
            z: 100,
            left: 'center',
            top: "45%",
            style: {
              fill: '#333',
              text: [
                "1335",
              ].join('\n'),
              font: 'bolder 1em "Microsoft YaHei", sans-serif',
              textAlign: 'center',//3、居中显示
            }
          },
          {//2、中心的文字设置
            type: 'text',
            z: 100,
            left: 'center',
            top: '55%',
            style: {
              fill: '#333',
              text: [
                '运单量',
              ].join('\n'),
              font: 'bolder 0.6em "Microsoft YaHei", sans-serif',
              textAlign: 'center',//3、居中显示
            }
          },
        ],
        series: [
          {
            type: 'pie',
            radius: ['45%', '60%'],
            labelLine: {
              show: false
            },
            label: {
              show: false
            },
            data: [
              { value: 1000, name: '普通土运单量', color: "#409EFF" },
              { value: 335, name: '资源土运单量', color: "#B3D8FF" },
            ],
          }
        ]
      },
      option2: {
        color: ['#409EFF', '#66b1ff', "#8cc5ff", "#b3d8ff", "#d9ecff", "#ebf5ff", "#50c67f", "#73d199", "#96ddb2", "#b9e8cc", "#dcf4e5"],
        tooltip: {
          confine: true,
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        // legend: {
        //   type: 'plain',
        //   orient: "vertical",
        //   right: 20,
        //   top: 10,
        //   bottom: 10,
        //   textStyle: {
        //     color: "#474A59",
        //     fontSize: "12",
        //     rich: {
        //       oneText: {
        //         // 设置名称宽度
        //         width: 100,
        //       },
        //       twoText: {
        //         // 设置百分比
        //         textAlign:"right",
        //       },
        //     },
        //   },
        //   icon: 'circle',
        //   itemWidth: 4,
        //   itemHeight: 4,
        //   formatter: function (name) {

        //     let data = [
        //       { value: 1048, name: '时间(补单)' },
        //       { value: 335, name: '改土质' },
        //       { value: 310, name: '换司机' },
        //       { value: 251, name: '改车牌(签错车牌)' },
        //       { value: 234, name: '补单(挖机)' },
        //       { value: 147, name: '签错班次' },
        //       { value: 135, name: '补照片' },
        //       { value: 102, name: '改备注' },
        //       { value: 102, name: '改价格' },
        //       { value: 102, name: '改运输方式' },
        //       { value: 102, name: '签错单位' },
        //     ]
        //     let total = 0
        //     let tarValue
        //     for (let i = 0; i < data.length; i++) {
        //       total += data[i].value
        //       if (data[i].name == name) {
        //         tarValue = data[i].value
        //       }
        //     }
        //     let v = tarValue + '单'
        //     let p = Math.round((tarValue / total) * 100) + '%'
        //     return `{oneText|${name}}  {twoText|${v}(${p})}`
        //   },
        // },
        graphic: [
          {//2、中心的文字设置
            type: 'text',
            z: 100,
            left: 'center',
            top: "45%",
            style: {
              fill: '#333',
              text: [
                "0",
              ].join('\n'),
              font: 'bolder 1em "Microsoft YaHei", sans-serif',
              textAlign: 'center',//3、居中显示
            }
          },
          {//2、中心的文字设置
            type: 'text',
            z: 100,
            left: 'center',
            top: '52%',
            style: {
              fill: '#333',
              text: [
                '运单量',
              ].join('\n'),
              font: 'bolder 0.6em "Microsoft YaHei", sans-serif',
              textAlign: 'center',//3、居中显示
            }
          },
        ],
        series: [
          {
            type: 'pie',
            radius: ['45%', '60%'],
            labelLine: {
              show: false
            },
            label: {
              show: false
            },
            data: [
              { value: 0, name: '时间(补单)' },
              { value: 0, name: '改土质' },
              { value: 0, name: '换司机' },
              { value: 0, name: '改车牌(签错车牌)' },
              { value: 0, name: '补单(挖机)' },
              { value: 0, name: '签错班次' },
              { value: 0, name: '补照片' },
              { value: 0, name: '改备注' },
              { value: 0, name: '改价格' },
              { value: 0, name: '改运输方式' },
              { value: 0, name: '签错单位' },
            ],
          }
        ]
      },
      option3: {
        title: {
          text: '出土分析',
          textStyle: {
            color: "#000",
            fontWeight: 700,
            fontSize: 14,
          },
          top: 10,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              color: '#C4C7CC'
            },
          }
        },
        grid: {
          left: '40px',
          bottom: "40px",
          top: "50px",
          right: "10px",
        },
        legend: {
          // orient: "horizontal",
          // right: 10,
          top: 10,
          textStyle: {
            color: "#474A59",
            fontSize: "12",
          },
          itemWidth: 8,
          itemHeight: 8,
          data: ['方', '吨'],
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 1, //这里是为了突出显示加上的
              },
            },
            // boundaryGap: false, // 不留白，从原点开始
            data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
          }
        ],
        yAxis: [
          {
            type: 'value',
            position: 'left',
            alignTicks: true,
            axisLabel: {
              formatter: "{value}",
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 0, //这里是为了突出显示加上的
              },
            },
            splitLine: {
              //网格线
              lineStyle: {
                type: "dashed", //设置网格线类型 dotted：虚线   solid:实线
                color: "#fff",
                opacity: "0.05",
              },
              show: true, //隐藏或显示
            },
          },
        ],
        series: [
          {
            name: '方',
            type: 'bar',
            color: "#409EFF",
            barMaxWidth: 20,
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
          },
          {
            name: '吨',
            type: 'bar',
            color: "#A3D0FF",
            barMaxWidth: 20,
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
          },
        ]
      },
      option4: {
        title: {
          text: '车队长增长趋势',
          textStyle: {
            color: "#000",
            fontWeight: 700,
            fontSize: 14,
          },
          top: 10,
        },
        tooltip: {
          confine: true,
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              color: '#C4C7CC'
            },
          }
        },
        grid: {
          left: '40px',
          bottom: "40px",
          top: "50px",
          right: "10px",
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 1, //这里是为了突出显示加上的
              },
            },
            // boundaryGap: false, // 不留白，从原点开始
            data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
          }
        ],
        yAxis: [
          {
            type: 'value',
            position: 'left',
            alignTicks: true,
            axisLabel: {
              formatter: "{value}",
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 0, //这里是为了突出显示加上的
              },
            },
            splitLine: {
              //网格线
              lineStyle: {
                type: "dashed", //设置网格线类型 dotted：虚线   solid:实线
                color: "#fff",
                opacity: "0.05",
              },
              show: true, //隐藏或显示
            },
          }
          // {
          //   type: 'value',
          //   name: '吨',
          //   position: 'right',
          //   alignTicks: true,
          //   offset: 80,
          //   axisLine: {
          //     show: true,
          //     lineStyle: {
          //       color: "#91CC75"
          //     }
          //   },
          //   axisLabel: {
          //     formatter: '{value} ml'
          //   },
          //   splitLine: {
          //     //网格线
          //     lineStyle: {
          //       type: "dashed", //设置网格线类型 dotted：虚线   solid:实线
          //       color: "#fff",
          //       opacity: "0.05",
          //     },
          //     show: false, //隐藏或显示
          //   },
          // },
        ],
        series: [
          {
            type: 'bar',
            color: "#409EFF",
            barMaxWidth: 30,
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
          },
        ]
      },
      option5: {
        color: ['#409EFF', '#66b1ff', "#8cc5ff", "#b3d8ff", "#d9ecff", "#ebf5ff", "#50c67f", "#73d199", "#96ddb2", "#b9e8cc", "#dcf4e5"],
        tooltip: {
          confine: true,
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        graphic: [
          {//2、中心的文字设置
            type: 'text',
            z: 100,
            left: 'center',
            top: "45%",
            style: {
              fill: '#333',
              text: [
                "0",
              ].join('\n'),
              font: 'bolder 1em "Microsoft YaHei", sans-serif',
              textAlign: 'center',//3、居中显示
            }
          },
          {//2、中心的文字设置
            type: 'text',
            z: 100,
            left: 'center',
            top: '52%',
            style: {
              fill: '#333',
              text: [
                '方',
              ].join('\n'),
              font: 'bolder 0.6em "Microsoft YaHei", sans-serif',
              textAlign: 'center',//3、居中显示
            }
          },
        ],
        series: [
          {
            type: 'pie',
            radius: ['45%', '60%'],
            labelLine: {
              show: false
            },
            label: {
              show: false
            },
            data: [],
          }
        ]
      },
      option6: {
        title: {
          text: '挖机台班分析',
          textStyle: {
            color: "#000",
            fontWeight: 700,
            fontSize: 14,
          },
          top: 10,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              color: '#C4C7CC'
            },
          }
        },
        grid: {
          left: '40px',
          bottom: "40px",
          top: "60px",
          right: "30px",
        },
        legend: {
          top: 10,
          textStyle: {
            color: "#474A59",
            fontSize: "12",
          },
          itemWidth: 20,
          itemHeight: 8,
          data: [{
            name: "挖机装车数",
            icon: "image://" + require("../../../static/home/<USER>")
          }, {
            name: "台班总时长",
            icon: "image://" + require("../../../static/home/<USER>")
          }]
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 1, //这里是为了突出显示加上的
              },
            },
            // boundaryGap: false, // 不留白，从原点开始
            data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
          }
        ],
        yAxis: [
          {
            type: 'value',
            alignTicks: true,
            axisLabel: {
              formatter: "{value}",
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 0, //这里是为了突出显示加上的
              },
            },
          },
          {
            type: 'value',
            alignTicks: true,
            axisLabel: {
              formatter: "{value}",
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 0, //这里是为了突出显示加上的
              },
            },
          },
        ],
        series: [
          {
            name: '挖机装车数',
            type: 'bar',
            color: "#A3D0FF",
            barMaxWidth: 20,
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
          },
          {
            name: '台班总时长',
            type: 'line',
            yAxisIndex: 1,
            color: "#409EFF",
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
          }
        ]
      },
      option7: {
        title: {
          text: '付款分析',
          textStyle: {
            color: "#000",
            fontWeight: 700,
            fontSize: 14,
          },
          top: 10,
        },
        tooltip: {
          confine: true,
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              color: '#C4C7CC'
            },
          }
        },
        grid: {
          left: '80px',
          bottom: "40px",
          top: "50px",
          right: "10px",
        },
        legend: {
          // orient: "horizontal",
          // right: 10,
          top: 10,
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            color: "#474A59",
            fontSize: "12",
          },
          // data: ['应付', '已付', '未付'],
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 1, //这里是为了突出显示加上的
              },
            },
            // boundaryGap: false, // 不留白，从原点开始
            data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
          }
        ],
        yAxis: [
          {
            type: 'value',
            position: 'left',
            alignTicks: true,
            axisLabel: {
              formatter: "{value}",
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 0, //这里是为了突出显示加上的
              },
            },
            splitLine: {
              //网格线
              lineStyle: {
                type: "dashed", //设置网格线类型 dotted：虚线   solid:实线
                color: "#fff",
                opacity: "0.05",
              },
              show: true, //隐藏或显示
            },
          }
          // {
          //   type: 'value',
          //   name: '吨',
          //   position: 'right',
          //   alignTicks: true,
          //   offset: 80,
          //   axisLine: {
          //     show: true,
          //     lineStyle: {
          //       color: "#91CC75"
          //     }
          //   },
          //   axisLabel: {
          //     formatter: '{value} ml'
          //   },
          //   splitLine: {
          //     //网格线
          //     lineStyle: {
          //       type: "dashed", //设置网格线类型 dotted：虚线   solid:实线
          //       color: "#fff",
          //       opacity: "0.05",
          //     },
          //     show: false, //隐藏或显示
          //   },
          // },
        ],
        series: [
          {
            name: "应付(元)",
            type: 'bar',
            stack: 'Ad',
            color: "#409EFF",
            barMaxWidth: 30,
            data: [120, 132, 101, 134, 90, 230, 210, 120, 132, 101, 134, 90, 230]
          },
          {
            name: "已付(元)",
            type: 'bar',
            stack: 'Ad',
            color: "#A3D0FF",
            barMaxWidth: 30,
            data: [220, 182, 191, 234, 290, 330, 310, 220, 182, 191, 234, 290, 330]
          },
          {
            name: "未付(元)",
            type: 'bar',
            stack: 'Ad',
            color: "#D1E7FF",
            barMaxWidth: 30,
            data: [150, 232, 201, 154, 190, 330, 410, 150, 232, 201, 154, 190, 330]
          },
        ]
      },
      waybillInfoVisible: false,
      waybillAnalysisVisible: false,
      excavatorInfoVisible: false,
      totalWaybillVisible: false,
      totalFleetVisible: false,
      totalGarbageVisible: false,
      totalExcavatedVisible: false,
      totalGarbageAnalysisVisible: false,
      totalAbnormalWaybillVisible: false,
      projectInfo: {
        projectDynamicCount: "",
        projectCount: "",
        projectDynamicList: [],
        projectList: [],
      },
      loading: false,
      excavatedNumInfo: {
        partyNumber: 0,
        tonsNumber: 0,
      },
      boardWayInfo: {
        waitWaybillTotal: 0, // 待支付运单数量
        paidTotalAmount: 0, // 已付总费用
        totalAmount: 0, // 总应付运费
        waitPaymentTotalAmount: 0, // 待付总费用
        totalWaybill: 0, // 总运单数量
        paidWaybillTotal: 0 // 已支付运单数量
      },
      abnormalWayInfo: {
        replenishmentTime: 0,
        goSoilType: 0,
        inGoDriverUserId: 0,
        inGoTruckCode: 0,
        inWaybill: 0,
        inGoShiftTime: 0,
        inGoInpicture: 0,
        inGoRemark: 0,
        payeePrice: 0,
        tpModel: 0,
        weightUnit: 0,
      },
      abnormalWayInfoList: [],
      abnormalWayTotal: 100,
      garbageInfo: {
        cubic: [],
        tons: [],
        totalCubic: 0,
        totalTons: 0,
      },
      garbageUnitActive: '方',
      garbageList: [],
      colors: ['#409EFF', '#66b1ff', "#8cc5ff", "#b3d8ff", "#d9ecff", "#ebf5ff", "#50c67f", "#73d199", "#96ddb2", "#b9e8cc", "#dcf4e5","#E6A23C","#e6a23ccc","#e6a23c99","#e6a23c66","#e6a23c33","#e6a23c19"],
      ledgerDigActive: '关联',
      abnormalWayLoading: false,
      outputAnalysisLoading: false,
      captainLoading: false,
      garbageLoading: false,
      ledgerDigLoading: false,
      paymentAnalysisLoading: false,
      waybillCountInfo: {
        allWaybillCount: 0,
        withinWaybillCount: 0,
      },
      captainCount: 0,
      garbageCount: 0,
      ledgerDigCountInfo: {
        excavatorLoadingCount: 0,
        digCount: 0
      },
      waybillAnalysisLoading: false,
      wayTipList: [
        {
          name: "普通土运单量",
          value: 1000,
          percentage: '75'
        },
        {
          name: "资源土运单量",
          value: 335,
          percentage: '25'
        },
      ],
      waybillAnalysisInfo: {
        tpModeTwoCount: 0, //运输模式-运费数量
        waybillGarbageCount: 0,  //有泥尾点的运单数量
        garbageCount: 0,  //运费运输类型的运单-泥尾总数
        garbageCountList: [],
        resourceSoilCount: 0, //资源土质总数
        waybillUpdateHistoryCount: 0, //异常修改运单数量
        notWaybillUpdateHistoryCount: 0,//未异常修改运单数量
        payCount: 0, //已支付总数
        tpModeOneCount: 0, //运输模式-放飞运单数量
        allWaybillCount: 0, //总运单数
        commonSoilCount: 0, //普通土质总数
        agentStatusCount: 0,  //已结算总数
        notAgentStatusCount: 0 //未结算总数
      },
      ledgerDigInfo: {},
      ledgerDigList: [],
      paymentAnalysisInfo: {
        freightData: {},
        waybillData: {},
      },
      inter:null
    };
  },
  computed: {},
  watch: {},
  created () {
    document.documentElement.style.fontSize = "16px";
    if(!this.inter){
    this.inter = setInterval(() => {
      console.log(11111);
        this.getProjectList()
        this.search()
      }, 1000*60*30);
    }
  },
  mounted () {
    //获取项目列表
    this.getProjectList()
    this.search()
  },
  methods: {
    getPercentWithPrecision,
    search () {
      //获取白板信息

      //白板出车运单总数获取
      this.getWaybillCount()
      //白板车队长总数获取
      this.getDriverCaptainCount()
      //白板泥尾总数获取
      this.getGarbageCount()
      //白板挖机装车数台班数获取
      this.getExcavatorLoadingAndDigCount()
      //运单费运单数获取
      this.getWaybillAmountCount()
      // 白板总出土量获取
      this.getExcavatedNumber()

      //echart图
      //  数据看板运单分析-统计
      this.getWaybillAnalyzeCount()
      //  异常运单
      this.getCompanyWaybillUpdateHitTargetCount()
      //出土分析
      this.outputAnalysis()
      //车队长增长趋势
      this.captainIncreaseTrend()
      //挖机台班分析
      this.digAnalysis()
      //  泥尾
      this.garbageAnalysis()
      //付款分析
      // this.paymentAnalysis()
    },
    getProjectList () {
      getProjectList({ type: 1, checkReturnAll: true, }).then(res => {
        this.projectInfo = res.data.data
        this.tagList = this.checkDynamic==1?this.projectInfo.projectDynamicList:this.projectInfo.projectList
      })
    },
    changeTime (val) {
      let startDate = ""
      let endDate = this.$moment().format('YYYY-MM-DD')
      switch (val) {
        case 'week':
          startDate = this.$moment().subtract(7, 'days').format('YYYY-MM-DD');
          break;
        case 'month':
          startDate = this.$moment().subtract(1, 'months').format('YYYY-MM-DD');
          break;
        case 'year':
          startDate = this.$moment().subtract(1, 'years').format('YYYY-MM-DD');
          break;

        default:
          break;
      }
      this.searchDate = [startDate, endDate]
      console.log(val);
    },
    changeStatus (val) {
      //更换项目列表  有些表格需要变换数据
      this.activeProjectId = ""
      this.tagList = val == 1?this.projectInfo.projectDynamicList:this.projectInfo.projectList
      //更新数据
      this.search()
    },
    //选择项目
    changeProject (item) {
      if (this.activeProjectId == item.id) {
        return false
      }
      this.activeProjectId = item.id
      this.search()
    },
    changeActive(){
      this.search()
    },
    detail (visible, type) { //type只有运单数 运费数的柱状图有传值
      switch (visible) {
        case "totalWaybillVisible"://总运单数量
          this.info = {
            count: this.waybillCountInfo.allWaybillCount,   //数量
            radio: this.radio,  //这个是选中的是年月周
            startDate: this.searchDate[0],  //开始时间
            endDate: this.searchDate[1],  //结束时间
            checkDynamic: this.checkDynamic,  //是否活跃项目
            projectInfoId: this.activeProjectId
          }
          break;
        case "totalFleetVisible"://总车队长数量
          this.info = {
            count: this.captainCount,   //数量
            radio: this.radio,  //这个是选中的是年月周
            startDate: this.searchDate[0],  //开始时间
            endDate: this.searchDate[1],  //结束时间
            checkDynamic: this.checkDynamic,  //是否活跃项目
            projectInfoId: this.activeProjectId
          }
          break;
        case "totalGarbageVisible"://泥尾总数
          this.info = {
            count: this.garbageCount,   //数量
            radio: this.radio,  //这个是选中的是年月周
            startDate: this.searchDate[0],  //开始时间
            endDate: this.searchDate[1],  //结束时间
            checkDynamic: this.checkDynamic,  //是否活跃项目
            projectInfoId: this.activeProjectId
          }
          break;
        case "totalExcavatedVisible": //总出土量
          this.info = {
            count: this.weightUnit == '方' ? this.excavatedNumInfo.partyNumber : this.excavatedNumInfo.tonsNumber,   //数量
            radio: this.radio,  //这个是选中的是年月周
            startDate: this.searchDate[0],  //开始时间
            endDate: this.searchDate[1],  //结束时间
            checkDynamic: this.checkDynamic,  //是否活跃项目
            projectInfoId: this.activeProjectId,
            weightUnit: this.weightUnit,
          }
          console.log(this.info);
          break;
        case "waybillInfoVisible": //运单数 运单费
          this.info = {
            count: this.wayRadio == '1' ? this.boardWayInfo.totalAmount : this.boardWayInfo.totalWaybill,   //数量
            radio: this.radio,  //这个是选中的是年月周
            startDate: this.searchDate[0],  //开始时间
            endDate: this.searchDate[1],  //结束时间
            checkDynamic: this.checkDynamic,  //是否活跃项目
            projectInfoId: this.activeProjectId,
            wayRadio: this.wayRadio,  //1运单费  2运单数
          }
          if (type) {//type只有付款分析运单数 运费数的柱状图有传值
            this.info.wayRadio = this.paymentAnalysisRadio == '运费' ? '1' : '2'
            this.info.count = this.paymentAnalysisRadio == '运费' ? this.boardWayInfo.totalAmount : this.boardWayInfo.totalWaybill
          }
          console.log(this.info);
          break;
        case "excavatorInfoVisible": //挖机装车数
          this.info = {
            count: this.radio2 == '挖机装车数' ? this.ledgerDigCountInfo.excavatorLoadingCount : this.ledgerDigCountInfo.workHour,   //数量
            radio: this.radio,  //这个是选中的是年月周
            startDate: this.searchDate[0],  //开始时间
            endDate: this.searchDate[1],  //结束时间
            checkDynamic: this.checkDynamic,  //是否活跃项目
            projectInfoId: this.activeProjectId,
            radio2: this.radio2,  //挖机装车数  台班总时长
            ledgerDigActive: this.ledgerDigActive,  //1运单费  2运单数
            type
          }
          break;
        case "waybillAnalysisVisible": //运单分析
          this.info = {
            radio: this.radio,  //这个是选中的是年月周
            dateStartStr: this.searchDate[0],  //开始时间
            dateEndStr: this.searchDate[1],  //结束时间
            isHProjectInfoCount: this.checkDynamic,  //是否活跃项目
            projectInfoId: this.activeProjectId,
            waybillAnalyzeType: this.waybillAnalysisRadio,  //
          }
          console.log(11111111111);
          break;
        case "totalGarbageAnalysisVisible": //泥尾点总出土
          this.info = {
            count: this.garbageUnitActive == '方' ? this.garbageInfo.totalCubic : this.garbageInfo.totalTons,   //数量
            radio: this.radio,  //这个是选中的是年月周
            startDate: this.searchDate[0],  //开始时间
            endDate: this.searchDate[1],  //结束时间
            checkDynamic: this.checkDynamic,  //是否活跃项目
            projectInfoId: this.activeProjectId,
            weightUnit: this.garbageUnitActive,
          }
          break;
        case "totalAbnormalWaybillVisible": //异常运单数
          this.info = {
            count: this.abnormalWayTotal,   //数量
            radio: this.radio,  //这个是选中的是年月周
            startDate: this.searchDate[0],  //开始时间
            endDate: this.searchDate[1],  //结束时间
            checkDynamic: this.checkDynamic,  //是否活跃项目
            projectInfoId: this.activeProjectId
          }
          break;

        default:
          break;
      }
      this[visible] = true
    },
    //更新项目需要刷新的
    // upDateProjectRef () {
    //   this.search()
    // },
    getWaybillCount () {
      let param = {
        projectInfoId: this.activeProjectId,
        checkDynamic: this.checkDynamic,
        startDate: this.searchDate[0],
        endDate: this.searchDate[1],
      }
      getWaybillCount(param).then((res) => {
        this.waybillCountInfo = res.data.data
      });
    },
    getDriverCaptainCount () {
      let param = {
        projectInfoId: this.activeProjectId,
        checkDynamic: this.checkDynamic,
        startDate: this.searchDate[0],
        endDate: this.searchDate[1],
      }
      getDriverCaptainCount(param).then((res) => {
        this.captainCount = res.data.data
      });
    },
    getGarbageCount () {
      let param = {
        projectInfoId: this.activeProjectId,
        checkDynamic: this.checkDynamic,
        startDate: this.searchDate[0],
        endDate: this.searchDate[1],
      }
      getGarbageCount(param).then((res) => {
        this.garbageCount = res.data.data
      });
    },
    getExcavatorLoadingAndDigCount () {
      let param = {
        projectInfoId: this.activeProjectId,
        checkDynamic: this.checkDynamic,
        startDate: this.searchDate[0],
        endDate: this.searchDate[1],
      }
      getExcavatorLoadingAndDigCount(param).then((res) => {
        this.ledgerDigCountInfo = res.data.data
      });
    },
    getWaybillAmountCount () {
      let param = {
        projectInfoId: this.activeProjectId,
        isHProjectInfoCount: this.checkDynamic,
        dateStartStr: this.searchDate[0],
        dateEndStr: this.searchDate[1],
      }
      getWaybillAmountCount(param).then((res) => {
        this.boardWayInfo = res.data.data
      });
    },
    getExcavatedNumber () {
      let param = {
        projectInfoId: this.activeProjectId,
        checkDynamic: this.checkDynamic,
        startDate: this.searchDate[0],
        endDate: this.searchDate[1],
        weightUnit: this.weightUnit,
      }
      getExcavatedNumber(param).then((res) => {
        this.excavatedNumInfo = res.data.data
      });
    },
    //运单分析数据获取
    getWaybillAnalyzeCount () {
      let param = {
        projectInfoId: this.activeProjectId,
        isHProjectInfoCount: this.checkDynamic,
        dateStartStr: this.searchDate[0],
        dateEndStr: this.searchDate[1],
        sourceType: "1",
        waybillAnalyzeType:this.waybillAnalysisRadio
      }
      this.waybillAnalysisLoading = true
      getWaybillAnalyzeCount(param).then((res) => {
        this.waybillAnalysisLoading = false
        this.waybillAnalysisInfo = res.data.data
        this.filterWaybillAnalysis(this.waybillAnalysisRadio)
      }).catch(() => {
        this.waybillAnalysisLoading = false
      })
    },
    //选择运单分析
    changWaybillAnalysis () {
      this.getWaybillAnalyzeCount()
    },
    //选择运单分析
    filterWaybillAnalysis (val) {
      let valueArr = []
      switch (val) {
        case '2':
          valueArr = [this.waybillAnalysisInfo.agentStatusCount, this.waybillAnalysisInfo.notAgentStatusCount]
          this.wayTipList = [
            {
              name: "已结算运单量",
              value: this.waybillAnalysisInfo.agentStatusCount || 0,
              percentage: getPercentWithPrecision(valueArr, 0)
            },
            {
              name: "未结算运单量",
              value: this.waybillAnalysisInfo.notAgentStatusCount || 0,
              percentage: getPercentWithPrecision(valueArr, 1)
            },
          ]
          break;
        case '3':
          valueArr = [this.waybillAnalysisInfo.tpModeOneCount, this.waybillAnalysisInfo.tpModeTwoCount]
          this.wayTipList = [
            {
              name: "放飞运单量",
              value: this.waybillAnalysisInfo.tpModeOneCount || 0,
              percentage: getPercentWithPrecision(valueArr, 0)
            },
            {
              name: "运费运单量",
              value: this.waybillAnalysisInfo.tpModeTwoCount || 0,
              percentage: getPercentWithPrecision(valueArr, 1)
            }]
          break;
        case '4':
          let arr = []
          let garbageCountList =  this.waybillAnalysisInfo.garbageCountList
          if(garbageCountList&&garbageCountList.length>0){
            valueArr = this.waybillAnalysisInfo.garbageCountList.map(item => {
              return item.count
            })
            this.waybillAnalysisInfo.garbageCountList.map((item, index) => {
              arr.push({
                name: item.names,
                value: item.count || 0,
                percentage: getPercentWithPrecision(valueArr, index)
              })
            })
          }
          this.wayTipList = arr
          console.log(this.wayTipList,'wayTipList');
          break;
        case '5':
          valueArr = [this.waybillAnalysisInfo.waybillUpdateHistoryCount, this.waybillAnalysisInfo.notWaybillUpdateHistoryCount]
          this.wayTipList = [
            {
              name: "异常运单修改数",
              value: this.waybillAnalysisInfo.waybillUpdateHistoryCount || 0,
              percentage: getPercentWithPrecision(valueArr, 0)
            },
            {
              name: "正常运单数",
              value: this.waybillAnalysisInfo.notWaybillUpdateHistoryCount || 0,
              percentage: getPercentWithPrecision(valueArr, 1)
            }]
          break;
        default:
          let other = this.waybillAnalysisInfo.allWaybillCount - this.waybillAnalysisInfo.commonSoilCount - this.waybillAnalysisInfo.resourceSoilCount
          valueArr = [this.waybillAnalysisInfo.commonSoilCount, this.waybillAnalysisInfo.resourceSoilCount, other]
          this.wayTipList = [
            {
              name: "普通土运单量",
              value: this.waybillAnalysisInfo.commonSoilCount || 0,
              percentage: getPercentWithPrecision(valueArr, 0)
            },
            {
              name: "资源土运单量",
              value: this.waybillAnalysisInfo.resourceSoilCount || 0,
              percentage: getPercentWithPrecision(valueArr, 1)
            },
            {
              name: "其它",
              value: other,
              percentage: getPercentWithPrecision(valueArr, 2)
            },
          ]
          break;
      }

      this.setOption1(val == "4" ? this.waybillAnalysisInfo.waybillGarbageCount : this.waybillAnalysisInfo.allWaybillCount)
    },
    setOption1 (total) {
      this.option1 = {
        color: this.colors,
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)',
          confine: true,
        },
        graphic: [
          {//2、中心的文字设置
            type: 'text',
            z: 100,
            left: 'center',
            top: "45%",
            style: {
              fill: '#333',
              text: [
                total,
              ].join('\n'),
              font: 'bolder 1em "Microsoft YaHei", sans-serif',
              textAlign: 'center',//3、居中显示
            }
          },
          {//2、中心的文字设置
            type: 'text',
            z: 100,
            left: 'center',
            top: '55%',
            style: {
              fill: '#333',
              text: [
                '运单量',
              ].join('\n'),
              font: 'bolder 0.6em "Microsoft YaHei", sans-serif',
              textAlign: 'center',//3、居中显示
            }
          },
        ],
        series: [
          {
            type: 'pie',
            radius: ['45%', '60%'],
            labelLine: {
              show: false
            },
            label: {
              show: false
            },
            data: this.wayTipList
          }
        ]
      }
    },
    //异常运单饼图数据获取
    getCompanyWaybillUpdateHitTargetCount () {
      let param = {
        projectInfoId: this.activeProjectId,
        isHProjectInfoCount: this.checkDynamic,
        auditDatetimeBegin: this.searchDate[0],
        auditDatetimeEnd: this.searchDate[1],
        sourceType: "1",
      }
      this.abnormalWayLoading = true
      getCompanyWaybillUpdateHitTargetCount(param).then((res) => {
        this.abnormalWayLoading = false
        let obj = res.data.data
        this.abnormalWayInfo = res.data.data
        let data = [
          { value: obj.replenishmentTime || 0, name: '时间(补单)' },
          { value: obj.goSoilType || 0, name: '改土质' },
          { value: obj.inGoDriverUserId || 0, name: '换司机' },
          { value: obj.inGoTruckCode || 0, name: '改车牌(签错车牌)' },
          { value: obj.inWaybill || 0, name: '补单(挖机)' },
          { value: obj.inGoShiftTime || 0, name: '签错班次' },
          { value: obj.inGoInpicture || 0, name: '补照片' },
          { value: obj.inGoRemark || 0, name: '改备注' },
          { value: obj.payeePrice || 0, name: '改价格' },
          { value: obj.tpModel || 0, name: '改运输方式' },
          { value: obj.weightUnit || 0, name: '签错单位' },
        ]
        this.abnormalWayInfoList = data.map(item => {
          return item.value
        })
        this.abnormalWayTotal = data.map((row) => (row.value)).reduce((acc, cur) => parseFloat(cur) + acc, 0) || 0
        //     let p = Math.round((tarValue / total) * 100) + '%'
        this.option2 = {
          color: ['#409EFF', '#66b1ff', "#8cc5ff", "#b3d8ff", "#d9ecff", "#ebf5ff", "#50c67f", "#73d199", "#96ddb2", "#b9e8cc", "#dcf4e5"],
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)',
            confine: true,
          },
          graphic: [
            {//2、中心的文字设置
              type: 'text',
              z: 100,
              left: 'center',
              top: "45%",
              style: {
                fill: '#333',
                text: [
                  this.abnormalWayTotal,
                ].join('\n'),
                font: 'bolder 1em "Microsoft YaHei", sans-serif',
                textAlign: 'center',//3、居中显示
              }
            },
            {//2、中心的文字设置
              type: 'text',
              z: 100,
              left: 'center',
              top: '52%',
              style: {
                fill: '#333',
                text: [
                  '运单量',
                ].join('\n'),
                font: 'bolder 0.6em "Microsoft YaHei", sans-serif',
                textAlign: 'center',//3、居中显示
              }
            },
          ],
          series: [
            {
              type: 'pie',
              radius: ['45%', '60%'],
              labelLine: {
                show: false
              },
              label: {
                show: false
              },
              data
            }
          ]
        }
      }).catch(() => {
        this.abnormalWayLoading = false
      })
    },
    getGarbagePercentage (index) {
      let arr = this.garbageList.map(item => {
        return item.value
      })
      return this.getPercentWithPrecision(arr, index)
    },
    //出土分析柱状图
    outputAnalysis () {
      let param = {
        projectInfoId: this.activeProjectId,
        checkDynamic: this.checkDynamic,
        startDate: this.searchDate[0],
        endDate: this.searchDate[1],
      }
      this.outputAnalysisLoading = true
      outputAnalysis(param).then((res) => {
        this.outputAnalysisLoading = false
        this.option3 = {
          title: {
            text: '出土分析',
            textStyle: {
              color: "#000",
              fontWeight: 700,
              fontSize: 14,
            },
            top: 10,
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                color: '#C4C7CC'
              },
            }
          },
          grid: {
            left: '50px',
            bottom: "40px",
            top: "50px",
            right: "10px",
          },
          legend: {
            // orient: "horizontal",
            // right: 10,
            top: 10,
            textStyle: {
              color: "#474A59",
              fontSize: "12",
            },
            itemWidth: 8,
            itemHeight: 8,
            data: ['方', '吨'],
          },
          xAxis: [

            {
              type: "category",
              // interval:0,
              axisLabel: {
                // interval:0,
                textStyle: {
                  color: "#C4C7CC",
                  fontSize: "12",
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#F5F7FA",
                  width: 1, //这里是为了突出显示加上的
                },
              },
              // boundaryGap: false, // 不留白，从原点开始
              data: res.data.data.days,
            }
          ],
          yAxis: [
            {
              type: 'value',
              position: 'left',
              alignTicks: true,
              axisLabel: {
                formatter: function (value, index) {
                  if (value >= 100000000) {
                    return value / 100000000 + "亿";
                  } else if (value >= 10000) {
                    return value / 10000 + "万";
                  } else {
                    return value;
                  }
                },
                textStyle: {
                  color: "#C4C7CC",
                  fontSize: "12",
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#F5F7FA",
                  width: 0, //这里是为了突出显示加上的
                },
              },
              splitLine: {
                //网格线
                lineStyle: {
                  type: "dashed", //设置网格线类型 dotted：虚线   solid:实线
                  color: "#fff",
                  opacity: "0.05",
                },
                show: true, //隐藏或显示
              },
            },
          ],
          series: [
            {
              name: '方',
              type: 'bar',
              color: "#409EFF",
              barMaxWidth: 20,
              data: res.data.data.cubicData
            },
            {
              name: '吨',
              type: 'bar',
              color: "#A3D0FF",
              barMaxWidth: 20,
              data: res.data.data.tonsData
            },
          ]
        }
      }).catch(() => {
        this.outputAnalysisLoading = false
      })
    },
    //车队长增长趋势
    captainIncreaseTrend () {
      let param = {
        projectInfoId: this.activeProjectId,
        checkDynamic: this.checkDynamic,
        startDate: this.searchDate[0],
        endDate: this.searchDate[1],
      }
      this.captainLoading = true
      captainIncreaseTrend(param).then((res) => {
        this.captainLoading = false
        this.option4 = {
          title: {
            text: '车队长增长趋势',
            textStyle: {
              color: "#000",
              fontWeight: 700,
              fontSize: 14,
            },
            top: 10,
          },
          tooltip: {
            confine: true,
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                color: '#C4C7CC'
              },
            },
          },
          grid: {
            left: '40px',
            bottom: "40px",
            top: "50px",
            right: "10px",
          },
          xAxis: [
            {
              type: "category",
              axisLabel: {
                textStyle: {
                  color: "#C4C7CC",
                  fontSize: "12",
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#F5F7FA",
                  width: 1, //这里是为了突出显示加上的
                },
              },
              data: res.data.data.days
            }
          ],
          yAxis: [
            {
              type: 'value',
              position: 'left',
              alignTicks: true,
              axisLabel: {
                formatter: "{value}",
                textStyle: {
                  color: "#C4C7CC",
                  fontSize: "12",
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#F5F7FA",
                  width: 0, //这里是为了突出显示加上的
                },
              },
              splitLine: {
                //网格线
                lineStyle: {
                  type: "dashed", //设置网格线类型 dotted：虚线   solid:实线
                  color: "#fff",
                  opacity: "0.05",
                },
                show: true, //隐藏或显示
              },
            }
          ],
          series: [
            {
              type: 'bar',
              color: "#409EFF",
              barMaxWidth: 30,
              data: res.data.data.data
            },
          ]
        }
      }).catch(() => {
        this.captainLoading = false
      })
    },
    //挖机台班分析
    digAnalysis () {
      let param = {
        projectInfoId: this.activeProjectId,
        checkDynamic: this.checkDynamic,
        startDate: this.searchDate[0],
        endDate: this.searchDate[1],
      }
      this.ledgerDigLoading = true
      digAnalysis(param).then((res) => {
        this.ledgerDigLoading = false
        this.ledgerDigInfo = res.data.data
        this.ledgerDigList = this.ledgerDigActive == '关联' ? this.ledgerDigInfo.relevantData : this.ledgerDigInfo.irrelevantData
        this.setLedgerDigOption()
      }).catch(() => {
        this.ledgerDigLoading = false
      })
    },
    setLedgerDigOption () {
      this.option6 = {
        title: {
          text: '挖机台班分析',
          textStyle: {
            color: "#000",
            fontWeight: 700,
            fontSize: 14,
          },
          top: 10,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              color: '#C4C7CC'
            },
          }
        },
        grid: {
          left: '50px',
          bottom: "40px",
          top: "60px",
          right: "40px",
        },
        legend: {
          top: 10,
          textStyle: {
            color: "#474A59",
            fontSize: "12",
          },
          itemWidth: 20,
          itemHeight: 8,
          data: [{
            name: "挖机装车数",
            icon: "image://" + require("../../../static/home/<USER>")
          }, {
            name: "台班总时长",
            icon: "image://" + require("../../../static/home/<USER>")
          }]
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 1, //这里是为了突出显示加上的
              },
            },
            data: this.ledgerDigInfo.days
          }
        ],
        yAxis: [
          {
            type: 'value',
            alignTicks: true,
            axisLabel: {
              formatter: function (value, index) {
                  if (value >= 100000000) {
                    return value / 100000000 + "亿";
                  } else if (value >= 10000) {
                    return value / 10000 + "万";
                  } else {
                    return value;
                  }
                },
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 0, //这里是为了突出显示加上的
              },
            },
          },
          {
            type: 'value',
            alignTicks: true,
            axisLabel: {
              formatter: "{value}",
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 0, //这里是为了突出显示加上的
              },
            },
          },
        ],
        series: [
          {
            name: '挖机装车数',
            type: 'bar',
            color: "#A3D0FF",
            barMaxWidth: 20,
            data: this.ledgerDigList
          },
          {
            name: '台班总时长',
            type: 'line',
            yAxisIndex: 1,
            color: "#409EFF",
            data: this.ledgerDigInfo.digData
          }
        ]
      }
    },
    //选择台班分析关联不关联
    changeLedgerDig (val) {
      console.log(val);
      this.ledgerDigList = val == '关联' ? this.ledgerDigInfo.relevantData : this.ledgerDigInfo.irrelevantData
      this.setLedgerDigOption()
    },
    //泥尾饼图获取
    garbageAnalysis () {
      let param = {
        projectInfoId: this.activeProjectId,
        checkDynamic: this.checkDynamic,
        startDate: this.searchDate[0],
        endDate: this.searchDate[1],
      }
      this.garbageLoading = true
      garbageAnalysis(param).then((res) => {
        this.garbageLoading = false
        this.garbageInfo = res.data.data
        this.garbageList = this.garbageUnitActive == '方' ?this.garbageInfo.cubic || []:this.garbageInfo.tons
        this.setGarbageOption()
      }).catch(() => {
        this.garbageLoading = false
      })
    },
    setGarbageOption () {
      this.option5 = {
        color: this.colors,
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)',
          confine: true,
        },
        graphic: [
          {//2、中心的文字设置
            type: 'text',
            z: 100,
            left: 'center',
            top: "45%",
            style: {
              fill: '#333',
              text: [
                this.garbageUnitActive == '方' ? this.garbageInfo.totalCubic : this.garbageInfo.totalTons,
              ].join('\n'),
              font: 'bolder 1em "Microsoft YaHei", sans-serif',
              textAlign: 'center',//3、居中显示
            }
          },
          {//2、中心的文字设置
            type: 'text',
            z: 100,
            left: 'center',
            top: '52%',
            style: {
              fill: '#333',
              text: [
                this.garbageUnitActive,
              ].join('\n'),
              font: 'bolder 0.6em "Microsoft YaHei", sans-serif',
              textAlign: 'center',//3、居中显示
            }
          },
        ],
        series: [
          {
            type: 'pie',
            radius: ['45%', '60%'],
            labelLine: {
              show: false
            },
            label: {
              show: false
            },
            data: this.garbageList,
          }
        ]
      }
    },
    //选择泥尾方和吨
    changeGarbage (val) {
      this.garbageList = val == '方' ? this.garbageInfo.cubic : this.garbageInfo.tons
      this.setGarbageOption()
    },
    //付款分析
    paymentAnalysis () {
      let param = {
        projectInfoId: this.activeProjectId,
        checkDynamic: this.checkDynamic,
        startDate: this.searchDate[0],
        endDate: this.searchDate[1],
      }
      this.paymentAnalysisLoading = true
      paymentAnalysis(param).then((res) => {
        this.paymentAnalysisLoading = false
        this.paymentAnalysisInfo = res.data.data
        // this.ledgerDigList = this.ledgerDigInfo.relevantData
        this.setPaymentAnalysisOption()
      }).catch(() => {
        this.paymentAnalysisLoading = false
      })
    },
    setPaymentAnalysisOption () {
      this.option7 = {
        title: {
          text: '付款分析',
          textStyle: {
            color: "#000",
            fontWeight: 700,
            fontSize: 14,
          },
          top: 10,
        },
        tooltip: {
          confine: true,
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              color: '#C4C7CC'
            },
          }
        },
        grid: {
          left: '80px',
          bottom: "40px",
          top: "50px",
          right: "10px",
        },
        legend: {
          top: 10,
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            color: "#474A59",
            fontSize: "12",
          },
        },
        xAxis: [
          {
            type: "category",
            axisLabel: {
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 1, //这里是为了突出显示加上的
              },
            },
            data: this.paymentAnalysisInfo.days
          }
        ],
        yAxis: [
          {
            type: 'value',
            position: 'left',
            alignTicks: true,
            axisLabel: {
              formatter: function (value, index) {
                  if (value >= 100000000) {
                    return value / 100000000 + "亿";
                  } else if (value >= 10000) {
                    return value / 10000 + "万";
                  } else {
                    return value;
                  }
                },
              textStyle: {
                color: "#C4C7CC",
                fontSize: "12",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#F5F7FA",
                width: 0, //这里是为了突出显示加上的
              },
            },
            splitLine: {
              //网格线
              lineStyle: {
                type: "dashed", //设置网格线类型 dotted：虚线   solid:实线
                color: "#fff",
                opacity: "0.05",
              },
              show: true, //隐藏或显示
            },
          }
        ],
        series: [
          {
            name: `应付(${this.paymentAnalysisRadio == '运费' ? '元' : '单'})`,
            type: 'bar',
            stack: 'Ad',
            color: "#409EFF",
            barMaxWidth: 30,
            data: this.paymentAnalysisRadio == '运费' ? this.paymentAnalysisInfo.freightData.prepaid : this.paymentAnalysisInfo.waybillData.prepaid
          },
          {
            name: `已付(${this.paymentAnalysisRadio == '运费' ? '元' : '单'})`,
            type: 'bar',
            stack: 'Ad',
            color: "#A3D0FF",
            barMaxWidth: 30,
            data: this.paymentAnalysisRadio == '运费' ? this.paymentAnalysisInfo.freightData.paid : this.paymentAnalysisInfo.waybillData.paid
          },
          {
            name: `未付(${this.paymentAnalysisRadio == '运费' ? '元' : '单'})`,
            type: 'bar',
            stack: 'Ad',
            color: "#D1E7FF",
            barMaxWidth: 30,
            data: this.paymentAnalysisRadio == '运费' ? this.paymentAnalysisInfo.freightData.unpaid : this.paymentAnalysisInfo.waybillData.unpaid
          },
        ]
      }
    },
    changePaymentAnalysis (val) {
      // paymentAnalysisRadio
      this.setPaymentAnalysisOption()
    },
  },
  beforeDestroy(){
    if(this.inter){
      clearInterval(this.inter)
    }
    this.inter = null
  },
};
</script>

<style lang="scss">
.homeDashboard {
  padding: 0px 10px 30px;

  .active {
    background-color: #409eff;
    color: #fff;
  }

  >.el-card {
    border: none;
    border-radius: 4px 4px 0px 0px;

    >.el-card__body {
      background-color: #fff;
      padding: 0px 10px;
    }
  }

  .el-card__header {
    border-bottom: none;
  }

  .el-radio-button--mini .el-radio-button__inner {
    padding: 4px 8px;
  }

  .avue-tags-box {
    height: 28px;
    overflow: hidden;
    padding-right: 206px;
    box-sizing: border-box;
    position: relative;

    ul {

      // justify-content: space-between;
      li {
        box-sizing: border-box;
        text-align: center;
        padding: 4px 10px;
        font-size: 12px;
        border-right: 1px solid #dcdfe6;
        border-top: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
        max-width: 240px;

        &:first-child {
          border-left: 1px solid #dcdfe6;
        }

        // border-radius: 4px;
        flex-grow: 1;
        margin-bottom: 4px;
        cursor: pointer;
        // min-width: 200px;
      }
    }

    .el-select {
      input {
        border-radius: 0;
      }
    }
  }

  .countStatistics {
    background-color: #fff;
    margin: 0px !important;
    padding: 10px 5px 0px;
    box-sizing: border-box;

    .lis {
      height: 100px;
      padding: 15px 4px 4px 28px;
      border: 1px solid #f3f4f5;
      border-left: 4px solid #66b1ff;
      margin-bottom: 10px;
      box-sizing: border-box;
      margin-left: 4px;
      background: rgba(102, 177, 255, 0.08);
      cursor: pointer;

      .title {
        font-size: 14px;
        padding-right: 10px;
        color: #474A59;

        .right {
          text-align: right;
          font-size: 12px;
          font-weight: 700;
          line-height: 14px;

          .label {
            font-weight: 400;
          }
        }
      }

      .num {
        font-size: 1.5vw;
        font-weight: 700;
        margin-top: 8px;
        padding-right: 10px;

        span {
          font-size: 14px;
          font-weight: 400;
        }

        .right {
          text-align: right;
          font-size: 12px;
          line-height: 14px;
          margin-top: 6px;

          .label {
            font-weight: 400;
          }

          .el-select {
            input {
              border: 1px solid #409eff;
            }
          }
        }
      }
    }
  }

  .width30,
  .chart {
    cursor: pointer;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    height: 100%;
    box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.08),
      0px 1px 2px 0px rgba(25, 15, 15, 0.07),
      0px 2px 4px 0px rgba(0, 0, 0, 0.05);

    .top {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0px 10px;
      width: 100%;
      box-sizing: border-box;

      .title {
        font-size: 14px;
        font-weight: 700;
        color: #000;
      }
    }

    .bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-left: 10px;
      width: 100%;
      flex: 1;
      box-sizing: border-box;

      .pie {
        height: 100%;
        flex: 1;
      }

      .tooltip {
        text-align: left;
        font-size: 12px;
        color: #474a59;
        width: 224px;
        box-sizing: border-box;

        li {
          line-height: 17px;
          margin-bottom: 4px;
          display: flex;
          align-items: center;

          .rotundity {
            display: inline-block;
            width: 4px;
            height: 4px;
            // border-width: 4px;
            border-radius: 50%;
            margin-right: 10px;
            background-color: #409eff;
          }
        }

        &.tooltip2 {
          width: 238px;
          padding-right: 20px;

          li {
            display: flex;
            justify-content: space-between;
          }
        }

        &.tooltip3 {
          max-height: 150px;
          overflow-y: auto;

          li {
            >div {
              display: flex;
              align-items: center;
            }
          }
        }
      }
    }
  }

  .chart {
    margin-top: 10px;
    background-color: #fff;
    height: 305px;
    cursor: pointer;
  }
}
</style>
