import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/drivertruckgps/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/drivertruckgps',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/drivertruckgps/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/drivertruckgps/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/drivertruckgps',
        method: 'put',
        data: obj
    })
}
