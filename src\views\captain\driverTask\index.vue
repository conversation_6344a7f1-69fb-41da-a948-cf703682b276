<template>
    <div class="execution">
        <basic-container>
            <avue-crud ref="crud"
                       :page.sync="page"
                       :data="tableData"
                       :permission="permissionList"
                       :table-loading="tableLoading"
                       :option="tableOption"
                       v-model="form"
                       @on-load="getPage"
                       @refresh-change="refreshChange"
                       @sort-change="sortChange"
                       @search-change="searchChange">
                       <template slot="menu" slot-scope="scope">
                          <el-button
                            type="text"
                            v-if="scope.row.fleetId"
                            icon="el-icon-view"
                            size="small"
                            plain
                            @click="vehicleView(scope.row, scope.index)"
                          > 车辆查看</el-button>
                        </template>
            </avue-crud>
        </basic-container>
        <detail v-if="detailVisible" :info="info" :visible.sync="detailVisible"></detail>
    </div>
</template>

<script>
    import {getPage} from '@/api/captain/driverTask'
    import {tableOption} from '@/const/crud/captain/driverTask'
    import {mapGetters} from 'vuex'
    import detail from './detail.vue'
    export default {
        name: 'driverTask',
        components: {
          detail
        },
        data() {
            return {
                form: {},
                tableData: [],
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                    ascs: [],//升序字段
                    descs: []//降序字段
                },
                paramsSearch: {},
                tableLoading: false,
                tableOption: tableOption,
                detailVisible:false,
                info:{},
            }
        },
        created() {
        },
        mounted: function () {
        },
        computed: {
            ...mapGetters(['permissions']),
            permissionList() {
                return {
                    // addBtn: this.permissions['chain:driverTask:add'] ? true : false,
                    // delBtn: this.permissions['chain:driverTask:del'] ? true : false,
                    // editBtn: this.permissions['chain:driverTask:edit'] ? true : false,
                    // viewBtn: this.permissions['chain:driverTask:get'] ? true : false
                };
            }
        },
        methods: {
            searchChange(params,done) {
                params = this.filterForm(params)
                this.paramsSearch = params
                this.page.currentPage = 1
                this.getPage(this.page, params)
                done()
            },
            sortChange(val) {
                let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
                if (val.order == 'ascending') {
                    this.page.descs = []
                    this.page.ascs = prop
                } else if (val.order == 'descending') {
                    this.page.ascs = []
                    this.page.descs = prop
                } else {
                    this.page.ascs = []
                    this.page.descs = []
                }
                this.getPage(this.page)
            },
            getPage(page, params) {
                this.tableLoading = true
                getPage(Object.assign({
                    current: page.currentPage,
                    size: page.pageSize,
                    descs: this.page.descs,
                    ascs: this.page.ascs,
                }, params, this.paramsSearch)).then(response => {
                    this.tableData = response.data.data.records
                    this.page.total = response.data.data.total
                    this.page.currentPage = page.currentPage
                    this.page.pageSize = page.pageSize
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            },
            /**
             * 刷新回调
             */
            refreshChange(page) {
                this.getPage(this.page)
            },
            vehicleView(row){
              this.info = row
              this.detailVisible = true
            },
        }
    }
</script>

<style lang="scss" scoped>
</style>
