<template>
  <div class="history">
    <el-drawer size="500px"
               title="审核流程"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">

      <div class="info"
           style="width:400px;margin-left: 20px;margin-top: 20px;"
           v-if="flowList&&flowList.length>0">
        <div class="approvalFlow">
          <el-timeline>
            <el-timeline-item :timestamp="item.companyPositionName"
                              placement="top"
                              :class="item.currentAuditFlag==1?'myActive':''"
                              :color="item.currentAuditFlag==1?'#409eff':''"
                              v-for="(item,index) in flowList"
                              :key="index">
                <i :class="item.isShow?'el-icon-arrow-up':'el-icon-arrow-down'"
                @click="showMore(item)"
                   v-if="item.approveStatus==1"
                   style="cursor: pointer;position: absolute;right: -20px;top: 0px;font-size: 18px;"></i>
              <div style="display:flex;align-items: center;justify-content: space-between;margin-bottom:10px">
                <h4>{{item.approveUsername}}</h4><span>{{item.approveDatetime}}</span>
              </div>
              <el-input type="textarea"
                        v-if="item.approveRemark"
                        v-model="item.approveRemark"
                        :autosize="{ minRows: 3, maxRows: 8}"
                        disabled></el-input>
              <div v-if="item.isShow">{{item.positionStaffName}}</div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      <el-empty v-else></el-empty>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getFlowNode} from '@/api/chain/ledgerDig2'

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  data () {
    return {
      flowList: []
    };
  },
  created () { },
  mounted () {
    this.getData()
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    getData () {
      getFlowNode(4,this.info.id).then(res => {
        this.flowList = res.data.data
      })
    },
    showMore(item){
      this.$set(item,'isShow',!item.isShow)
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
/deep/ .myActive .el-timeline-item__tail{
  border-left-color: #409eff;
}
</style>
