<!--u-table二次封装组件  http://www.umyui.com/umycomponent/u-table-api/ -->
<template>
  <div class="myCrud">
    <!-- 搜索组件 -->
    <header-search :search="search"
                   ref="headerSearch">
      <template slot="search"
                slot-scope="scope">
        <slot name="search"
              v-bind="scope"></slot>
      </template>
      <template slot="searchMenu"
                slot-scope="scope">
        <slot name="searchMenu"
              v-bind="scope"></slot>
      </template>
      <template slot-scope="scope"
                v-for="item in searchSlot"
                :slot="item">
        <slot v-bind="scope"
              :name="item"></slot>
      </template>
    </header-search>
    <el-card class="tableCard" shadow="never">
      <slot name="header"></slot>
      <!-- 表格功能列 -->
      <header-menu ref="headerMenu" v-if="vaildData(option.header, true)">
        <template slot="menuLeft" slot-scope="scope">
          <slot name="menuLeft" v-bind="scope"></slot>
        </template>
        <template slot="menuRight" slot-scope="scope">
          <slot name="menuRight" v-bind="scope"></slot>
        </template>
      </header-menu>
      <!-- row-id行数据的 id；在使用虚拟树表格时,该属性是必填的 -->
      <u-table
        v-if="isComplete"
        :key="reload"
        ref="table"
        :row-key="rowKey"
        :row-id="rowKey"
        :current-row-key="rowKey"
        :sum-text="option.sumText || '合计'"
        :data="list"
        :tree-props="treeProps"
        :lazy="vaildData(option.lazy, false)"
        :total-option="
          vaildData(option.totalOption, [{ label: '金额', unit: '元' }])
        "
        :load="treeLoad"
        :expand-row-keys="option.expandRowKeys"
        :default-expand-all="option.defaultExpandAll"
        :highlight-current-row="option.highlightCurrentRow||false"
        :show-summary="option.showSummary"
        :summary-method="tableSummaryMethod"
        :span-method="tableSpanMethod"
        :stripe="option.stripe"
        :show-header="option.showHeader"
        :default-sort="option.defaultSort"
        :row-class-name="rowClassName"
        :cell-class-name="cellClassName"
        :row-style="rowStyle"
        :cell-style="cellStyle||{fontSize:'12px'}"
        :fit="option.fit"
        :header-cell-class-name="headerCellClassName"
        :max-height="isAutoHeight ? tableHeight : option.maxHeight"
        :height="tableHeight"
        :width="setPx(option.width, config.width)"
        :border="option.border"
        :record-table-select="option.recordTableSelect || false"
        v-loading="tableLoading"
        @row-click="rowClick"
        @row-dblclick="rowDblclick"
        @cell-mouse-enter="cellMouseEnter"
        @cell-mouse-leave="cellMouseLeave"
        @cell-click="cellClick"
        @header-click="headerClick"
        @row-contextmenu="rowContextmenu"
        @header-contextmenu="headerContextmenu"
        @cell-dblclick="cellDblclick"
        @filter-change="filterChange"
        @selection-change="selectionChange"
        @table-select-change="tableSelectionChange"
        @select="select"
        @select-all="selectAll"
        @sort-change="sortChange"
        @current-change="currentRowChange"
        @expand-change="expandChange"
        @header-dragend="headerDragend"
        :pagination-show="false"
        :total="page.total"
        :page-size="page.pageSize"
        :current-page="page.currentPage"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        :page-sizes="page.pageSizes || [10, 20, 50, 100]"
        :row-height="option.rowHeight || 53"
        :use-virtual="option.useVirtual || false"
        :big-data-checkbox="option.selection || false"
        :show-header-overflow="option.showHeaderOverflow || true"
        :show-body-overflow="option.showBodyOverflow || false"
        :fixed-columns-roll="option.fixedColumnsRoll || false"
        :beautify-table="option.beautifyTable || false"
        :tree-config="treeConfig"
        :header-cell-style="option.headerCellStyle||{backgroundColor:'#f7f9fd',color:'#333',fontSize:'12px'}"
      >
        <!-- 数据为空显示 -->
        <template slot="empty">
          <div>
            <slot name="empty" v-if="$slots.empty"></slot>
            <el-empty
              v-else
              :image-size="100"
              :description="option.emptyText"
            ></el-empty>
          </div>
        </template>
        <!-- 表格列 -->
        <column :columnOption="columnOption" ref="column">
          <column-default ref="columnDefault" slot="header">
            <template slot-scope="{ row, index }" slot="expand">
              <slot :row="row" :index="index" name="expand"></slot>
            </template>
          </column-default>
          <template v-for="item in mainSlot" slot-scope="scope" :slot="item">
            <slot v-bind="scope" :name="item"></slot>
          </template>
          <column-menu slot="footer">
            <template slot="menuHeader" slot-scope="scope">
              <slot name="menuHeader" v-bind="scope"></slot>
            </template>
            <template slot="menu" slot-scope="scope">
              <slot name="menu" v-bind="scope"></slot>
            </template>
            <template slot="menuBtn" slot-scope="scope">
              <slot name="menuBtn" v-bind="scope"></slot>
            </template>
          </column-menu>
        </column>
      </u-table>
    <slot name="footer"></slot>
    <!-- 分页 -->
    <table-page ref="tablePage"
                  v-if="paginationShow"
                  :page="page">
        <template slot="page">
          <slot name="page"></slot>
        </template>
      </table-page>
    </el-card>
    <!-- 列显隐 -->
    <dialog-column ref="dialogColumn" v-if="isComplete"></dialog-column>
    <!-- 表单 -->
    <dialog-form ref="dialogForm">
      <template slot-scope="scope"
                v-for="item in formSlot"
                :slot="item">
        <slot v-bind="scope"
              :name="item"></slot>
      </template>
      <template slot-scope="scope"
                slot="menuForm">
        <slot name="menuForm"
              v-bind="scope"></slot>
      </template>
    </dialog-form>
  </div>
</template>

<script>
import headerSearch from "./header/header-search";
import headerMenu from "./header/header-menu";
import { validatenull } from "@/util/validate";
import config from "./config.js";
import column from "./column/column";
import columnDefault from "./column/column-default";
import columnMenu from "./column/column-menu";
import dialogColumn from "./dialog/dialog-column";
import dialogForm from "./dialog/dialog-form";
import tablePage from "./table-page";
import { deepClone } from "@/util/util";
import { sendDic, loadDic, loadCascaderDic, loadLocalDic } from './dic';
import init from "./init.js";
import {getByRoute,saveOrUpdate} from '@/api/chain/companycustompage'

export default {
  mixins: [init()],
  props: {
    spanMethod: Function,
    summaryMethod: Function,
    rowStyle: Function,
    cellStyle: Function,
    beforeClose: Function,
    beforeOpen: Function,
    rowClassName: Function,
    cellClassName: Function,
    headerCellClassName: Function,
    option: {
      type: Object,
      default: () => {
        return {};
      },
    },
    defaults: {
      type: Object,
      default() {
        return {};
      },
    },
    value: {
      type: Object,
      default: () => {
        return {};
      }
    },
    tableLoading: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    page: {
      type: Object,
      default: () => {
        return {
          pageSizes: [10, 20, 50, 100],
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 20, // 每页显示多少条
          ascs: [], //升序字段
          descs: [], //降序字段
        };
      },
    },
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
    search: {
      type: Object,
      default() {
        return {};
      },
    },
    permission: {
      type: [Function, Object],
      default: () => {
        return {};
      },
    },
  },
  components: {
    headerSearch, //搜索
    headerMenu, //菜单头部
    column,
    columnDefault, //其它列,
    columnMenu, //操作栏，
    dialogColumn, //列显隐
    dialogForm, //弹窗表单
    tablePage, //页码
  },
  data() {
    return {
      tableHeight: undefined,
      reload: Math.random(),
      tableOption: {},
      tableSelect: [],
      list: [],
      tableForm: {},
      config,
      sumsList: [],
      objectOption: {},
      btnDisabled: false,
      listError: {},
      DIC:{},
      cascaderDIC:{},
      tableIndex: -1,
      defaultSort:{},//记录下defaultSort
      dialogColumnVisible:false,
      // defaultOption:{},//最初一份option
    };
  },
  provide() {
    return {
      crud: this,
    };
  },
  computed: {
    treeConfig() {
      return this.option.treeConfig || {};
    },
    rowKey() {
      return this.option.rowKey || "id";
    },
    searchSlot() {
      return this.getSlotList(["Search"], this.$scopedSlots, this.propOption);
    },
    mainSlot() {
      let result = [];
      this.propOption.forEach((item) => {
        if (this.$scopedSlots[item.prop]) result.push(item.prop);
      });
      return this.getSlotList(
        ["Header", "Form"],
        this.$scopedSlots,
        this.propOption
      ).concat(result);
    },
    propOption() {
      let result = [];
      function findProp(list = []) {
        if (!Array.isArray(list)) return;
        list.forEach((ele) => {
          if (Array.isArray(ele.children)) findProp(ele.children);
          else result.push(ele);
        });
      }
      findProp(this.columnOption);
      result = this.calcCascader(result);
      let obj = {};
        result.forEach((ele) => {
          obj[ele.prop] = ele;
        });
        this.$set(this, "objectOption", obj);
      return result;
    },
    formRules () {
        let result = {};
        this.propOption.forEach(ele => {
          if (ele.rules && ele.display !== false)
            result[ele.prop] = ele.rules
        });
        return result;
      },
    columnOption() {
      let column = this.option.column || [];
      return column;
    },
    childrenKey() {
      return this.treeProps.children || "children";
    },
    hasChildrenKey() {
      return this.treeProps.hasChildren || "hasChildren";
    },
    treeProps() {
      return this.option.treeProps || {};
    },
    isAutoHeight() {
      return this.option.height === "auto";
    },
    formSlot () {
      return this.getSlotList(['Error', 'Label', 'Type', 'Form'], this.$scopedSlots, this.propOption)
    },
    paginationShow() {
      console.log(this.vaildData(this.option.page,true));
      return this.vaildData(this.option.page,true);
    },
    isMediumSize() {
      return this.option.size || "small";
    },
    selectLen() {
      return this.tableSelect ? this.tableSelect.length : 0;
    },
    isCard () {
      return this.option.card ? 'always' : 'never'
    },
    resultOption () {
      return Object.assign(this.deepClone(this.option), {
        column: this.propOption
      })
    },
    routerName(){
      return this.option.routerName||""
    },
  },
  watch: {
    value: {
      handler() {
        this.tableForm = this.value;
      },
      immediate: true,
      deep: true,
    },
    data: {
      handler() {
        this.list = this.data;
      },
      immediate: true,
      deep: true,
    },
    defaults: {
      handler(val) {
        this.objectOption = val;
      },
      deep: true,
    },
    objectOption: {
      handler(val) {
        this.$emit("update:defaults", val);
      },
      deep: true,
    },
    isComplete:{
      handler(val) {
        this.getTableHeight();
      },
      deep: true,
    }
  },
  created() {
    //如果要列表自定义，就必须有路由
    // if(this.routerName){
    //   //先获取看看有没有保存
    //   // let obj = localStorage.getItem(this.routerName)
    //    getByRoute({route:this.routerName}).then(res=>{
    //     console.log(res);
    //     if(res.data.data){
    //       let pageTableOption =  JSON.parse(res.data.data.pageTableOption)
    //       console.log(pageTableOption);
    //       for (const key in this.objectOption) {
    //         if(key in pageTableOption){
    //             this.$set(this.objectOption[key],'hide',pageTableOption[key].hide)
    //             // this.objectOption[key].hide = pageTableOption[key].hide
    //           if(pageTableOption[key].width){
    //             this.objectOption[key].width = pageTableOption[key].width
    //           }
    //           if(pageTableOption[key].order){
    //             this.objectOption[key].order = pageTableOption[key].order
    //           }
    //         }
    //       }
    //     }
    //     console.log(this.objectOption);
    //   //如果自定义列还需要重新排序
    //     this.refreshTable()
    //     // localStorage.setItem(this.routerName,JSON.stringify(this.objectOption))
    //     saveOrUpdate({pageRoute:this.routerName,pageTableOption:JSON.stringify(this.objectOption)}).then(res=>{
    //       console.log(res);
    //     })
    //   })
    // }
    // this.defaultOption = this.deepClone(this.option)
    this.initDic();
  },
  mounted() {

  },
  methods: {
    deepClone,
    initDic(type) {
      this.tableOption = this.option;
      this.defaultSort = this.option.defaultSort||{}
      // this.getIsMobile();
      this.handleLocalDic();
      this.handleLoadDic()
    },
    updateDic (prop, list) {
      let column = this.findObject(this.propOption, prop);
      if (this.validatenull(list) && this.validatenull(prop)) {
        this.handleLoadDic();
      } else if (this.validatenull(list) && !this.validatenull(column.dicUrl)) {
        sendDic({
          column: column
        }).then(list => {
          this.$set(this.DIC, prop, list);
        });
      } else {
        this.$set(this.DIC, prop, list);
      }
    },
    handleSetDic (list, res = {}) {
      Object.keys(res).forEach(ele => {
        this.$set(list, ele, res[ele])
      });
    },
    //本地字典
    handleLocalDic () {
      this.handleSetDic(this.DIC, loadLocalDic(this.resultOption));
    },
    // 网络字典加载
    handleLoadDic () {
      loadDic(this.resultOption).then(res => this.handleSetDic(this.DIC, res))
    },
    //级联字典加载
    handleLoadCascaderDic () {
      loadCascaderDic(this.propOption, this.data).then(res => this.handleSetDic(this.cascaderDIC, res));
    },
    getTableHeight() {
      if (this.isAutoHeight) {
        this.$nextTick(() => {
          console.log(this.$refs.table.$el.offsetTop);
          let tableHeight = this.$refs.table.$el.offsetTop;
          console.log('offsetTop',tableHeight);
          console.log(this.option.calcHeight);
          this.tableHeight = window.innerHeight - tableHeight - this.option.calcHeight || 137;
          console.log(this.tableHeight);
          const fn = this.throttle1(this.resizeFunc,1000)
          window.addEventListener("resize", fn);
          this.$on("hook:beforeDestroy", () => {
            console.log("组件销毁啦！");
            window.removeEventListener("resize", fn);
          });
          setTimeout(()=>{
            this.doLayout()
          },100)
        });
      } else {
        this.tableHeight = this.option.height;
        this.doLayout()
      }
    },
    resizeFunc() {
      console.log(this.$refs.table);
      let tableHeight = this.$refs.table.$el.offsetTop;
      console.log('offsetTop',tableHeight);
      console.log(this.option.calcHeight);
      this.tableHeight = window.innerHeight - tableHeight - this.option.calcHeight || 137;
      console.log(this.tableHeight);
      this.doLayout();
    },
    doLayout(){
      this.$nextTick(() => {
        this.$refs.table.doLayout && this.$refs.table.doLayout();
      });
      // this.$refs.table.doLayout();
    },
    handleValidate(prop, valid, msg) {
      if (!this.listError[prop])
        this.$set(this.listError, prop, { valid: false, msg: "" });
      this.listError[prop].valid = !valid;
      this.listError[prop].msg = msg;
    },
    getPermission(key, row, index) {
      if (typeof this.permission === "function") {
        return this.permission(key, row, index);
      } else if (!this.validatenull(this.permission[key])) {
        return this.permission[key];
      } else {
        return true;
      }
    },
    getBtnIcon(value) {
      const name = value + "Icon";
      return this.option[name] || config[name];
    },
    calcCascader(list = []) {
      list.forEach((ele) => {
        let cascader = ele.cascader;
        if (!validatenull(cascader)) {
          let parentProp = ele.prop;
          cascader.forEach((citem) => {
            let column = this.findObject(list, citem);
            if (column) column.parentProp = parentProp;
          });
        }
      });
      return list;
    },
    getSlotName(item = {}, type = "D", slot) {
      let result = {
        F: "Form",
        H: "Header",
        E: "Error",
        L: "Label",
        S: "Search",
        T: "Type",
        D: "",
      };
      let name = item.prop + result[type];
      if (slot) return slot[name];
      return name;
    },
    getSlotList(list = [], slot, propList) {
      propList = propList.map((ele) => ele.prop);
      return Object.keys(slot).filter((ele) => {
        let result = false;
        if (!propList.includes(ele)) {
          list.forEach((name) => {
            if (ele.includes(name)) result = true;
          });
        }
        return result;
      });
    },
    // handlePageSize({ page, size }) {
    //   console.log(121);
    //   let pages = Object.assign({}, this.page)
    //   if (size != this.page.pageSize) {
    //     pages.currentPage = 1;
    //   } else {
    //     pages.currentPage = page;
    //   }
    //   pages.pageSize = size;
    //   this.$emit('on-load', pages)
    //   this.$emit('update:page', pages)
    //   this.$emit("handle-page-size",pages)
    // },
    refreshTable(callback) {
      this.reload = Math.random();
      this.$nextTick(() => {
        callback && callback();
      });
    },
    //树懒加载
    treeLoad(tree, treeNode, resolve) {
      this.$emit("tree-load", tree, treeNode, (data) => {
        tree.children = data;
        resolve(data);
      });
    },
    //展开或则关闭
    expandChange(row, expand) {
      this.$emit("expand-change", row, expand);
    },
    //设置单选
    currentRowChange(currentRow, oldCurrentRow) {
      this.$emit("current-row-change", currentRow, oldCurrentRow);
    },
    //拖动表头事件 必须有border
    headerDragend(newWidth, oldWidth, column, event) {
      let obj = this.objectOption[column.property];
      if (obj) this.$set(this.objectOption[column.property], "width", newWidth);
      this.$emit("header-dragend", newWidth, oldWidth, column, event);
    },
    //合并行
    tableSpanMethod(param) {
      if (typeof this.spanMethod === "function") return this.spanMethod(param);
    },
    //合集统计逻辑
    tableSummaryMethod(param) {
      let sumsList = {};
      //如果自己写逻辑则调用summaryMethod方法
      if (typeof this.summaryMethod === "function")
        return this.summaryMethod(param);
      const { columns, data } = param;
      let sums = [];
      if (columns.length > 0) {
        columns.forEach((column, index) => {
          let currItem = this.sumColumnList.find(
            (item) => item.name === column.property
          );
          if (currItem) {
            let decimals = currItem.decimals || 2;
            let label = currItem.label || "";
            switch (currItem.type) {
              case "count":
                sums[index] = label + data.length;
                break;
              case "avg":
                let avgValues = data.map((item) =>
                  Number(item[column.property])
                );
                let nowindex = 1;
                sums[index] = avgValues.reduce((perv, curr) => {
                  let value = Number(curr);
                  if (!isNaN(value)) {
                    return (perv * (nowindex - 1) + curr) / nowindex++;
                  } else {
                    return perv;
                  }
                }, 0);
                sums[index] = label + sums[index].toFixed(decimals);
                break;
              case "sum":
                let values = data.map((item) => Number(item[column.property]));
                sums[index] = values.reduce((perv, curr) => {
                  let value = Number(curr);
                  if (!isNaN(value)) {
                    return perv + curr;
                  } else {
                    return perv;
                  }
                }, 0);
                sums[index] = label + sums[index].toFixed(decimals);
                break;
            }
            sumsList[column.property] = sums[index];
          } else {
            sums[index] = "";
          }
        });
      }
      this.sumsList = sumsList;
      return sums;
    },
    // 选择回调
    selectionChange(val) {
      this.tableSelect = val;
      this.$emit("selection-change", this.tableSelect);
    },
    // 选择回调
    tableSelectionChange(tableSelectData) {
      console.log(tableSelectData);
      this.$emit("table-selection-change", tableSelectData);
    },
    clearSelection() {
      this.$refs.table.clearSelection();
    },
    // 单个选择回调
    select(selection, row) {
      this.$emit("select", selection, row);
    },
    // 点击勾选全选 Checkbox
    selectAll(selection) {
      this.$emit("select-all", selection);
    },
    //筛选回调用
    filterChange(filters) {
      this.$emit("filter-change", filters);
    },
    // 排序回调
    sortChange(val) {
      console.log(val);
      console.log(this.defaultSort);
      //设置默认第一次会触发排序更改，每次调整排序，筛选也会触发，判断跟之前一样不触发
      console.log(validatenull(this.defaultSort));
      if((!validatenull(this.defaultSort))&&val.order==this.defaultSort.order&&val.prop==this.defaultSort.prop){
        console.log(111);
        return false
      }
      this.defaultSort = {
        order:val.order,
        prop:val.prop
      }
      this.$emit("sort-change", val);
    },
    // 行双击
    rowDblclick(row, event) {
      this.$emit("row-dblclick", row, event);
    },
    // 行单机
    rowClick(row, event, column) {
      this.$emit("row-click", row, event, column);
    },
    //当单元格 hover 进入时会触发该事件
    cellMouseEnter(row, column, cell, event) {
      this.$emit("cell-mouse-enter", row, column, cell, event);
    },
    //当单元格 hover 退出时会触发该事件
    cellMouseLeave(row, column, cell, event) {
      this.$emit("cell-mouse-leave", row, column, cell, event);
    },
    //当某个单元格被点击时会触发该事件
    cellClick(row, column, cell, event) {
      this.$emit("cell-click", row, column, cell, event);
    },
    //	当某一列的表头被点击时会触发该事件
    headerClick(column, event) {
      this.$emit("header-click", column, event);
    },
    //当某一行被鼠标右键点击时会触发该事件
    rowContextmenu(row, column, event) {
      this.$emit("row-contextmenu", row, column, event);
    },
    //当某一列的表头被鼠标右键点击时触发该事件
    headerContextmenu(column, event) {
      this.$emit("header-contextmenu", column, event);
    },
    //当某个单元格被双击击时会触发该事件
    cellDblclick(row, column, cell, event) {
      this.$emit("cell-dblclick", row, column, cell, event);
    },
    //新增事件
    rowAdd() {
      this.$refs.dialogForm.show("add");
    },
    // 编辑
    rowEdit (row, index) {
      this.tableForm = this.rowClone(row);
      this.tableIndex = index;
      this.$emit("input", this.tableForm);
      this.$refs.dialogForm.show("edit");
    },
    //复制
    rowCopy (row) {
      this.tableForm = this.rowClone(row);
      delete this.tableForm[this.rowKey]
      this.tableIndex = -1;
      this.$emit("input", this.tableForm);
      this.$refs.dialogForm.show("add");
    },
    //查看
    rowView (row, index) {
      this.tableForm = this.rowClone(row);
      this.tableIndex = index;
      this.$emit("input", this.tableForm);
      this.$refs.dialogForm.show("view");
    },
    // 删除
    rowDel (row, index) {
      this.$emit("row-del", row, index, () => {
        let { parentList, index } = this.findData(row[this.rowKey])
        if (parentList) parentList.splice(index, 1);
      });
    },
    //对象克隆
    rowClone (row) {
      let rowData = {};
      Object.keys(row).forEach(ele => {
        if (!["_parent", "children"].includes(ele)) {
          rowData[ele] = row[ele];
        }
      });
      return rowData;
    },
    findData (id) {
      let result = {}
      const callback = (parentList, parent) => {
        parentList.forEach((ele, index) => {
          if (ele[this.rowKey] == id) {
            result = {
              item: ele,
              index: index,
              parentList: parentList,
              parent: parent
            }
          }
          if (ele[this.childrenKey]) {
            callback(ele[this.childrenKey], ele)
          }
        })
      }
      callback(this.list)
      return result;
    },
    //刷新事件
    refreshChange() {
      this.$emit("refresh-change");
    },
    //搜索条件重置
    initSearchForm(filters) {
      this.$refs.headerSearch.initSearchForm();
    },
    selectClear () {
      this.$refs.table.clearSelection();
    },
    toggleRowSelection (row, selected) {
      this.$refs.table.toggleRowSelection([{row,selected}]);
    },
    partRowSelections (data, state) {
      console.log(data);
      console.log(state);
      console.info(this.$refs.table.partRowSelections);
      this.$refs.table.partRowSelections(data,state);
      console.log(this.$refs.table.getCheckboxRecords());

    },
    reloadData(data){
      this.$refs.table.reloadData(data);
    }
  },

};
</script>
<style lang="scss">
.myCrud {
  .plTableBox {
  .el-table__fixed-right::before {
    height: 0;
  }
  .el-table__fixed::before {
    height: 0;
  }
  .el-table {
    .el-table__body {
      .cell {
        width: 100% !important;
      }
    }
  }
}
.my_crud__search{
  overflow: visible;
  border: none;
  >.el-card__body{
    padding: 0;
  }
}
}
.tableCard{
  border: none;
  >.el-card__body{
    padding: 0;
  }
}
</style>
