export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  index: true,
  searchMenuSpan: 6,
  labelWidth: 110,
  menuWidth: 210,
  searchLabelWidth: 100,
  column: [
    {
      label: "项目名称",
      prop: "projectName",
      sortable: true,
      search: true,
      minWidth: 200,
      overHidden: true,
    },
    {
      label: "项目地址",
      prop: "projectAddress",
      sortable: false,
      width: 140,
      overHidden: true,
    },
    {
      label: "范围(米)",
      prop: "electronicRange",
      sortable: false,
      span: 24,
      width: 90,
      overHidden: true,
    },
    // {
    //   label: "签单方式",
    //   prop: "isGo",
    //   order: 4,
    //   hide: true,
    //   display: false,
    //   width: 80,
    //   overHidden: true,
    // },
    {
      label: "负责人姓名",
      prop: "leadingNames",
      sortable: true,
      search: true,
      minWidth: 140,
      overHidden: true,
    },
    // {
    //   label: "成员姓名",
    //   prop: "memberIds",
    //   order: 3,
    //   hide: true,
    //   sortable: true,
    //   display: false,
    //   type: "select",
    //   props: {
    //     label: "name",
    //     value: "id",
    //   },
    //   multiple: true,
    //   filterable: true,
    //   dicUrl: "/chain/companystaff/list2",
    //   minWidth: 140,
    //   overHidden: true,
    // },
    // {
    //   label: "土质",
    //   prop: "listSoilType",
    //   order: 5,
    //   hide: true,
    //   display: false,
    //   sortable: true,
    //   type: "cascader",
    //   filterable: true,
    //   props: {
    //     label: "itemName",
    //     value: "itemName",
    //   },
    //   multiple: true,
    //   separator: ",",
    //   dicUrl:
    //     "/chain/systemdictionaryitem/listDictionaryItem?dictionary=soil_type",
    //   width: 80,
    //   overHidden: true,
    // },
    // {
    //   label: "泥尾",
    //   prop: "garbageIds",
    //   order: 6,
    //   hide: true,
    //   sortable: true,
    //   display: false,
    //   type: "select",
    //   filterable: true,
    //   props: {
    //     label: "names",
    //     value: "id",
    //   },
    //   multiple: true,
    //   dicUrl: "/chain/garbage/listByCompanyAuth",
    //   width: 140,
    //   overHidden: true,
    // },
    // {
    //   label: "称重方式",
    //   order: 10,
    //   hide: true,
    //   display: false,
    //   prop: "weightType",
    //   width: 90,
    //   overHidden: true,
    // },
    // {
    //   label: "需泥尾票管控",
    //   prop: "ticketEnable",
    //   order: 13,
    //   type: "switch",
    //   display: false,
    //   dicData: [
    //     {
    //       label: "否",
    //       value: "0",
    //     },
    //     {
    //       label: "是",
    //       value: "1",
    //     },
    //   ],
    //   hide: true,
    //   minWidth: 140,
    //   overHidden: true,
    // },
    {
      label: "运单结算类型",
      prop: "settleType",
      type: "select",
      search: true,
      searchOrder:1,
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl:
        "/chain/systemdictionaryitem/listDictionaryItem?dictionary=settle_type",
      width: 110,
      overHidden: true,
    },
    {
      label: "项目状态",
      prop: "projectStatus",
      sortable: true,
      search: true,
      type: "select",
      dicData: [
        {
          label: "进行中",
          value: "1",
        },
        {
          label: "已完成",
          value: "9",
        },
      ],
      width: 94,
      overHidden: true,
    },
    {
      label: "启动日期",
      prop: "startDate",
      type: "datetime",
      sortable: true,
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      searchRange: true,
      search: true,
      width: 140,
      overHidden: true,
    },
    {
      label: "完成日期",
      prop: "finishDate",
      type: "datetime",
      searchRange: true,
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      sortable: true,
      search: true,
      width: 140,
      overHidden: true,
    },
  ],
};
