export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  searchMenuSpan: 6,
  menuWidth:140,
  searchLabelWidth:80,
  column: [
    {
      label: "卡号",
      prop: "cardNo",
      sortable: true,
      search:true,
      minWidth:130,
      overHidden:true,
    },
    {
      label: "激活时间",
      prop: "bindingDate",
      sortable: true,
      type:"datetime",
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:140,
      overHidden:true,
    },
    {
      label: "激活时间",
      prop: "searchDate",
      sortable: true,
      type:"date",
      searchRange:true,
      search: true,
      hide:true,
      showColumn:false,
      valueFormat: 'yyyy-MM-dd',
    },
    {
      label: "状态",
      prop: "status",
      search:true,
      sortable: true,
      type:"select",
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=card_status',
      minWidth:80,
      overHidden:true,
    },
    {
      label: "B卡卡号",
      prop: "parentCardNo",
      search:true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "车队长",
      prop: "captainName",
      search:true,
      minWidth:100,
      overHidden:true,
    },
    {
      label: "车队长电话",
      prop: "mobile",
      searchLabelWidth:100,
      search:true,
      minWidth:110,
      overHidden:true,
    },
    {
      label: "运单总数",
      prop: "waybillCount",
      sortable: true,
      minWidth:100,
      overHidden:true,
    },
    {
      label: "已交运单数",
      prop: "giveCardCount",
      minWidth:100,
      overHidden:true,
    },
    {
      label: "待结算金额",
      prop: "unsettledAmount",
      sortable: true,
      minWidth:116,
      overHidden:true,
    },
    {
      label: "已结算金额",
      prop: "settledAmount",
      sortable: true,
      minWidth:116,
      overHidden:true,
    },
    {
      label: "已付款金额",
      prop: "paidAmount",
      sortable: true,
      minWidth:116,
      overHidden:true,
    },
    {
      label: "冻结操作人",
      prop: "frozenName",
      minWidth:90,
      overHidden:true,
    },
    {
      label: "冻结操作时间",
      prop: "frozenUpdateDatetime",
      sortable: true,
      minWidth:140,
      overHidden:true,
    },
    {
      label: "解禁操作人",
      prop: "thawName",
      minWidth:90,
      overHidden:true,
    },
    {
      label: "解禁操作时间",
      prop: "thawUpdateDatetime",
      sortable: true,
      minWidth:140,
      overHidden:true,
    },
  ],
};
