import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/platformservicecharge/getPage',
        method: 'get',
        params: query
    })
}
export function getListByCompanyNameAndPresentDate(query) {
    return request({
        url: '/chain/platformservicecharge/getListByCompanyNameAndPresentDate',
        method: 'get',
        params: query
    })
}
export function confirmReconciliation(data) {
    return request({
        url: '/chain/platformservicecharge/confirmReconciliation',
        method: 'post',
        data
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/platformservicecharge',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/platformservicecharge/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/platformservicecharge/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/platformservicecharge',
        method: 'put',
        data: obj
    })
}
//付款
export function confirmPay(obj) {
    return request({
        url: '/chain/platformservicecharge/confirmPay',
        method: 'post',
        data: obj
    })
}
//服务费税洼地查询：
export function getBranchWalletList(obj) {
    return request({
        url: '/chain/companynsrsbhwallet/getBranchWalletList',
        method: 'post',
        data: obj
    })
}
