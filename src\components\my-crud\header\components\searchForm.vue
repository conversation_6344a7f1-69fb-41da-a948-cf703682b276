<!--表格组件 -->
<template>
  <div style="margin-top: 10px" class="searchForm">
    <avue-form
      :option="option"
      ref="form"
      @submit="searchChange"
      v-model="searchForm"
    >
      <template slot="menuForm">
        <slot name="menuForm"></slot>
      </template>
    </avue-form>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import { arraySort } from "@/util/util";

export default {
  props: {
    searchOption: {
      type: Object,
      default: () => {
        return {};
      },
    },
    noSplice: {
      type: Boolean,
      default: () => {
        return false
      },
    }
  },
  data() {
    return {
      searchForm: {}, //筛选值
    };
  },
  inject: ["crud"],
  computed: {
    option(){
      let tempOption = this.deepClone(this.searchOption)
      console.log(tempOption,'tempOption');
      if(this.noSplice){
        return tempOption
      }else{
        tempOption.column= tempOption.column.splice(0,this.crud.option.searchIndex||3)
        return tempOption
      }
    },
  },
  components: {},
  watch: {},
  mounted() {},
  methods: {
    //点击查询
    searchChange(form, done) {
      this.$emit("searchChange", form, done);
    },
  },
  created() {},
};
</script>
<style lang="scss"></style>
