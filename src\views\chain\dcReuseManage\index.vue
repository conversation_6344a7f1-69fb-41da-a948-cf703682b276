<template>
  <div class="execution">
      <basic-container>
          <avue-crud ref="crud"
                     :page.sync="page"
                     :data="tableData"
                     :permission="permissionList"
                     :table-loading="tableLoading"
                     :option="tableOption"
                     v-model="form"
                     @on-load="getPage"
                     @refresh-change="refreshChange"
                     @row-update="handleUpdate"
                     @row-del="handleDel"
                     @sort-change="sortChange"
                     @search-change="searchChange">
                     <template slot="menuLeft"  slot-scope="scope">
                        <el-button style="margin-left:14px" v-if="permissions['chain:dcReuseManage:reuse']"
                        icon="el-icon-document"
                        size="small"
                        type="primary"
                        @click="handleReuse(scope.row,scope.index)">
                        电子卡批量扫描重用
                      </el-button>
                    </template>
          </avue-crud>
        <reuse v-if="reuseVisible" v-on:refreshChange="refreshChange" :info="info" :visible.sync="reuseVisible"></reuse>
      </basic-container>
  </div>
</template>

<script>
  import {getPage, putObj, delObj} from '@/api/chain/dcReuseManage'
  import {tableOption} from '@/const/crud/chain/dcReuseManage'
  import {mapGetters} from 'vuex'
  import reuse from './reuse.vue'

  export default {
      name: 'dcReuseManage',
      components: {
        reuse,
      },
      data() {
          return {
              form: {},
              tableData: [],
              page: {
                  total: 0, // 总页数
                  currentPage: 1, // 当前页数
                  pageSize: 20, // 每页显示多少条
                  ascs: [],//升序字段
                  descs: 'create_datetime'//降序字段
              },
              paramsSearch: {},
              tableLoading: false,
              tableOption: tableOption,
              reuseVisible:false,
              info:{},
          }
      },
      created() {
      },
      mounted: function () {
      },
      computed: {
          ...mapGetters(['permissions']),
          permissionList() {
              return {
                  addBtn: this.permissions['chain:dcReuseManage:add'] ? true : false,
                  delBtn: this.permissions['chain:dcReuseManage:del'] ? true : false,
                  editBtn: this.permissions['chain:dcReuseManage:edit'] ? true : false,
                  viewBtn: this.permissions['chain:dcReuseManage:get'] ? true : false
              };
          }
      },
      methods: {
          searchChange(params,done) {
              params = this.filterForm(params)
              this.paramsSearch = params
              this.page.currentPage = 1
              this.getPage(this.page, params)
              done()
          },
          sortChange(val) {
              let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
              if (val.order == 'ascending') {
                  this.page.descs = []
                  this.page.ascs = prop
              } else if (val.order == 'descending') {
                  this.page.ascs = []
                  this.page.descs = prop
              } else {
                  this.page.ascs = []
                  this.page.descs = []
              }
              this.getPage(this.page)
          },
          getPage(page, params) {
              this.tableLoading = true
              if (params) {
                if (params.hasOwnProperty("searchDate")) {
                  params.latestSettleDatetimeStart = params.searchDate[0];
                  params.latestSettleDatetimeEnd = params.searchDate[1];
                  delete params.searchDate;
                }
                if (params.hasOwnProperty("searchDate2")) {
                  params.resetDatetimeStart = params.searchDate2[0];
                  params.resetDatetimeEnd = params.searchDate2[1];
                  delete params.searchDate2;
                }
              }
              getPage(Object.assign({
                  current: page.currentPage,
                  size: page.pageSize,
                  descs: this.page.descs,
                  ascs: this.page.ascs,
              }, params, this.paramsSearch)).then(response => {
                  this.tableData = response.data.data.records
                  this.page.total = response.data.data.total
                  this.page.currentPage = page.currentPage
                  this.page.pageSize = page.pageSize
                  this.tableLoading = false
              }).catch(() => {
                  this.tableLoading = false
              })
          },
          /**
           * @title 数据删除
           * @param row 为当前的数据
           * @param index 为当前删除数据的行数
           *
           **/
          handleDel: function (row, index) {
              let _this = this
              this.$confirm('是否确认删除此数据', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
              }).then(function () {
                  return delObj(row.id)
              }).then(data => {
                  _this.$message({
                      showClose: true,
                      message: '删除成功',
                      type: 'success'
                  })
                  this.getPage(this.page)
              }).catch(function (err) {
              })
          },
          /**
           * @title 数据更新
           * @param row 为当前的数据
           * @param index 为当前更新数据的行数
           * @param done 为表单关闭函数
           *
           **/
          handleUpdate: function (row, index, done, loading) {
              putObj(row).then(response => {
                  this.$message({
                      showClose: true,
                      message: '修改成功',
                      type: 'success'
                  })
                  done()
                  this.getPage(this.page)
              }).catch(() => {
                  loading()
              })
          },
          /**
           * 刷新回调
           */
          refreshChange(page) {
              this.getPage(this.page)
          },
          handleReuse(row){
            this.info = row
            this.reuseVisible = true
          }
      }
  }
</script>

<style lang="scss" scoped>
</style>
