export const tableOption = (value)=>{
  let that = value
  console.log(that);
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    index:true,
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    dialogClickModal:false,
    dialogWidth:500,
    excelBtn: false,
    printBtn: false,
    addBtn: false,
    viewBtn: false,
    searchSpan:8,
    searchMenuSpan: 6,
    labelWidth:100,
    menu:false,
    // height:'auto',
    selection:false,
    column: [
      {
        label: "项目",
        prop: "projectInfoIds",
        search: true,
        type:'select',
        hide:true,
        showColumn:false,
        dataType:'string',
        searchMultiple:true,
        props: {
          label: 'projectName',
          value: 'id'
        },
        dicUrl: '/chain/projectinfo/listTicket',
      },
      {
        label: "项目",
        prop: "projectName",
        overHidden:true,
      },
      {
        label: "泥尾",
        prop: "garbageIds",
        search: true,
        hide:true,
        type:'select',
        dataType:'string',
        searchMultiple:true,
        showColumn:false,
        props: {
          label: "names",
          value: "id",
        },
        dicUrl: "/chain/garbage/listByCompanyAuth",
      },
      {
        label: "泥尾",
        prop: "garbageName",
        overHidden:true,
      },
      {
        label: "土质",
        prop: "soilTypes",
        type:'select',
        search: true,
        dataType:'string',
        searchMultiple:true,
        showColumn:false,
        hide:true,
        props: {
          label: "soilType",
          value: "soilType",
        },
        dicUrl:"/chain/companyticketpurchase/getCompanyTicketPurchaseSoilType",
      },
      {
        label: "土质",
        prop: "soilType",
        overHidden:true,
      },
      {
        label: "前期余票",
        prop: "earlyRemainingTickets",
        overHidden:true,
      },
      {
        label: "本期领票",
        prop: "collectTickets",
        overHidden:true,
      },
      {
        label: "本期用票",
        prop: "useTickets",
        overHidden:true,
      },
      {
        label: "本期退票",
        prop: "refundTickets",
        overHidden:true,
      },
      {
        label: "本期余票",
        prop: "inRemainingTickets",
        overHidden:true,
      },
      {
        label: "日期",
        prop: "searchDate",
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange:true,
        search:true,
        hide:true,
        showColumn:false,
        pickerOptions: {
          // disabledDate(time) {
          //   return time.getTime() < Date.now();
          // },
          shortcuts: [{
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近六个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
              picker.$emit('pick', [start, end]);
            }
          },]
        }
      },
    ],
  };
}

