import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/garbagecustomerreceive/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/garbagecustomerreceive',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/garbagecustomerreceive/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/garbagecustomerreceive/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/garbagecustomerreceive',
        method: 'put',
        data: obj
    })
}

export function refund(obj) {
    return request({
        url: '/chain/garbagecustomerreceive/refund',
        method: 'post',
        data: obj
    })
}

