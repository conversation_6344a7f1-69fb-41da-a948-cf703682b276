import request from '@/router/axios'

export function getPage(params) {
    return request({
        url: '/chain/ledgerDig/page',
        method: 'get',
        params
    })
}
//核算员批量通过
export function passObj(data) {
    return request({
        url: '/chain/ledgerDig/batchConfirmByCheckStaff',
        method: 'post',
        data
    })
}
//核算员批量驳回
export function rejectObj(data) {
    return request({
        url: '/chain/ledgerDig/batchRejectByCheckStaff',
        method: 'post',
        data
    })
}
//核算员批量删除
export function delObj(data) {
    return request({
        url: '/chain/ledgerDig/batchDeleteByCheckStaff',
        method: 'post',
        data
    })
}
//获取权限
export function getSettingAll(params) {
    return request({
        url: 'chain/companyledgersetting/getSettingAll',
        method: 'get',
        params
    })
}
//获取权限
export function editPcLedgerRemark(data) {
    return request({
        url: 'chain/ledgerDig/editPcLedgerRemark',
        method: 'post',
        data
    })
}
//获取权限
export function getHistoryByLedgerDigId(params) {
    return request({
        url: '/chain/companyledgerdighistory/getHistoryByLedgerDigId',
        method: 'get',
        params
    })
}

export function batchEditPrice(data) {
    return request({
        url: 'chain/ledgerDig/batchEditPrice',
        method: 'post',
        data
    })
}
export function getWorkTimeStatistic(data) {
    return request({
        url: '/chain/ledgerDig/getWorkTimeStatistic',
        method: 'post',
        data
    })
}
export function batchConfirmByCheckStaff(data) {
    return request({
        url: '/chain/ledgerDig/v2/batchConfirmByCheckStaff',
        method: 'post',
        data
    })
}
export function batchRejectByCheckStaff(data) {
    return request({
        url: '/chain/ledgerDig/v2/batchRejectByCheckStaff',
        method: 'post',
        data
    })
}
export function queryExcavatorStageCrewPage(data) {
    return request({
        url: '/chain/ledgerDig/queryExcavatorStageCrewPage',
        method: 'post',
        data
    })
}
export function batchAuditByBusinessStaff(data) {
    return request({
        url: '/chain/ledgerDig/batchAuditByBusinessStaff',
        method: 'post',
        data
    })
}
