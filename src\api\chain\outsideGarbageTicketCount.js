import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companyticketpurchase/companyTicketPurchaseStatistical',
        method: 'get',
        params: query
    })
}
export function getCollectTickets(query) { //本期领票
    return request({
        url:'/chain/companyticketpurchase/getCollectTickets',
        method: 'get',
        params: query
    })
}
export function getUseTickets(query) { //本期用票按月
    return request({
        url:'/chain/companyticketpurchase/getMonthUseTicketsStatistical',
        method: 'get',
        params: query
    })
}
export function getDayUseTicketsStatistical(query) { //本期用票按日
    return request({
        url:'/chain/companyticketpurchase/getDayUseTicketsStatistical',
        method: 'get',
        params: query
    })
}
export function getRefundTickets(query) { //本期退票
    return request({
        url:'/chain/companyticketpurchase/getRefundTickets',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companyticketprojectinventory',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companyticketprojectinventory/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companyticketprojectinventory/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companyticketprojectinventory',
        method: 'put',
        data: obj
    })
}
export function calculateTicketMonthCostPrice(params) {
    return request({
        url: '/chain/companyticketpurchase/calculateTicketMonthCostPrice',
        method: 'get',
        params
    })
}
