import { isMobileNumber } from "@/util/validate";
export const tableOption = {
  dialogDrag: true,
  border: true,
  dialogWidth:400,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  addTitle:"新增收款人",
  addBtnText:"新增收款人",
  searchLabelWidth:100,
  labelWidth:100,
  searchMenuSpan: 6,
  menuWidth:100,
  editBtn:false,
  routerName:"companyregularpayee",
  column: [
    {
      label: "收款人姓名",
      prop: "payeeName",
      search:true,
      span:24,
      rules: [
        {
          required: true,
          message: "请输入 收款人姓名",
          trigger: "blur",
        },
      ],
      minWidth:106,
      overHidden:true,
    },
    {
      label: "手机号",
      prop: "payeeMobile",
      search:true,
      maxlength:11,
      span:24,
      rules: [
        {
          required: true,
          message: "请输入 手机号",
          trigger: "blur",
        },
        {
          validator: isMobileNumber,
          trigger: "blur",
        },
      ],
      minWidth:100,
      overHidden:true,
    },
    {
      label: "是否生效",
      prop: "isEnable",
      search:true,
      // sortable: true,
      type: "select",
      display:false,
      dicData: [
        {
          label: '否',
          value: '0'
        },
        {
          label: '是',
          value: '1'
        },
      ],
      minWidth:96,
      overHidden:true,
    },
    {
      label: "是否实名",
      prop: "isAuthentication",
      // sortable: true,
      type: "select",
      display:false,
      dicData: [
        {
          label: '否',
          value: '0'
        },
        {
          label: '是',
          value: '1'
        },
      ],
      minWidth:96,
      overHidden:true,
    },
    {
      label: "身份证号",
      prop: "idCard",
      minWidth:150,
      span:24,
      overHidden:true,
    },
    {
      label: "收款银行",
      prop: "bindingBankName",
      minWidth:160,
      span:24,
      overHidden:true,
    },
    {
      label: "收款卡号",
      prop: "bindingBankNo",
      // sortable: true,
      span:24,
      minWidth:150,
      overHidden:true,
    },
    {
      label: "身份证国徽面",
      prop: "idCardNegativeUrl",
      type:"upload",
      listType: "picture-img",
      action: "/upms/file/upload?fileType=image&dir=companyregularpayee",
      propsHttp: {
        url: "link",
      },
      loadText: "图上上传中，请稍等",
      hide:true,
      showColumn:false,
      span:12,
    },
    {
      label: "身份证人像面",
      prop: "idCardPositiveUrl",
      type:"upload",
      listType: "picture-img",
      action: "/upms/file/upload?fileType=image&dir=companyregularpayee",
      propsHttp: {
        url: "link",
      },
      loadText: "图上上传中，请稍等",
      hide:true,
      showColumn:false,
      span:12,
    },
    {
      label: "合同是否签约",
      prop: "isContract",
      // sortable: true,
      display:false,
      minWidth:110,
      overHidden:true,
    },
    {
      label: "创建时间",
      prop: "createDatetime",
      // sortable: true,
      display:false,
      minWidth:140,
      overHidden:true,
    },
  ],
};
