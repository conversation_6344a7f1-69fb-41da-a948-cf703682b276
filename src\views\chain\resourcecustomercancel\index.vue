<template>
  <div class="execution">
      <basic-container>
          <avue-crud ref="crud"
                     :page.sync="page"
                     :data="tableData"
                     :permission="permissionList"
                     :table-loading="tableLoading"
                     :option="tableOption"
                     v-model="form"
                     @on-load="getPage"
                     @refresh-change="refreshChange"
                     @row-update="handleUpdate"
                     @sort-change="sortChange"
                     @search-change="searchChange">
                     <template slot="menuLeft" slot-scope="{ row }">
                        <el-button
                            icon="el-icon-download"
                            v-if="permissions['chain:resourcecustomercancel:excel']"
                            size="small"
                            type="primary"
                            @click="exOut"
                          >
                            导出
                        </el-button>
                      </template>
                      <template slot-scope="{ row,index }" slot="menu">
                        <!-- 资源地才要上传磅单 -->
                        <el-button
                            type="text"
                            v-if="permissions['chain:resourcecustomercancel:upload']&&row.type!=2"
                            icon="el-icon-view"
                            size="small"
                            plain
                            @click="editRow(row,row.status)"
                            >{{row.status==1?'修改磅单':'上传磅单'}}</el-button>
                      </template>
                      <template slot-scope="{disabled,size}" slot="imgUrlLabel">
                        <span>拍照磅单</span>
                      </template>
          </avue-crud>
      </basic-container>
  </div>
</template>

<script>
  import {getPage,putObj} from '@/api/chain/resourcecustomercancel'
  import {tableOption} from '@/const/crud/chain/resourcecustomercancel'
  import {mapGetters} from 'vuex'
  import { expotOut } from "@/util/down.js";

  export default {
      name: 'resourcecustomercancel',
      data() {
          return {
              form: {},
              tableData: [],
              page: {
                  total: 0, // 总页数
                  currentPage: 1, // 当前页数
                  pageSize: 20, // 每页显示多少条
                  ascs: [],//升序字段
                  descs: []//降序字段
              },
              paramsSearch: {},
              tableLoading: false,
              tableOption: tableOption(this),
              detailVisible:false,
          }
      },
      created() {
      },
      mounted: function () {
      },
      computed: {
          ...mapGetters(['permissions']),
          permissionList() {
              return {
                  addBtn: this.permissions['chain:resourcecustomercancel:add'] ? true : false,
                  delBtn: this.permissions['chain:resourcecustomercancel:del'] ? true : false,
                  editBtn: this.permissions['chain:resourcecustomercancel:edit'] ? true : false,
                  viewBtn: this.permissions['chain:resourcecustomercancel:get'] ? true : false,
              };
          }
      },
      methods: {
          searchChange(params,done) {
              params = this.filterForm(params)
              this.paramsSearch = params
              this.page.currentPage = 1
              this.getPage(this.page, params)
              done()
          },
          sortChange(val) {
              let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
              if (val.order == 'ascending') {
                  this.page.descs = []
                  this.page.ascs = prop
              } else if (val.order == 'descending') {
                  this.page.ascs = []
                  this.page.descs = prop
              } else {
                  this.page.ascs = []
                  this.page.descs = []
              }
              this.getPage(this.page)
          },
          getPage(page, params) {
              this.tableLoading = true
              if (params) {
                if (params.hasOwnProperty("searchDate")) {
                  params.goDatetimeStart = params.searchDate[0];
                  params.goDatetimeEnd = params.searchDate[1];
                  delete params.searchDate;
                }
                if (params.hasOwnProperty("searchDate2")) {
                  params.uploadDatetimeStart = params.searchDate2[0];
                  params.uploadDatetimeEnd = params.searchDate2[1];
                  delete params.searchDate2;
                }
              }
              getPage(Object.assign({
                  current: page.currentPage,
                  size: page.pageSize,
                  descs: this.page.descs,
                  ascs: this.page.ascs,
              }, params, this.paramsSearch)).then(response => {
                  this.tableData = response.data.data.records
                  this.page.total = response.data.data.total
                  this.page.currentPage = page.currentPage
                  this.page.pageSize = page.pageSize
                  this.tableLoading = false
              }).catch(() => {
                  this.tableLoading = false
              })
          },
          /**
           * @title 数据更新
           * @param row 为当前的数据
           * @param index 为当前更新数据的行数
           * @param done 为表单关闭函数
           *
           **/
            handleUpdate: function (row, index, done, loading) {
              putObj(row).then(response => {
                  this.$message({
                      showClose: true,
                      message: '修改成功',
                      type: 'success'
                  })
                  done()
                  this.getPage(this.page)
              }).catch(() => {
                  loading()
              })
          },
          /**
           * 刷新回调
           */
          refreshChange(page) {
              this.getPage(this.page)
          },
          exOut(){
            let params = Object.assign({},this.paramsSearch)
            if (params) {
              if (params.hasOwnProperty("searchDate")) {
                params.goDatetimeStart = params.searchDate[0];
                params.goDatetimeEnd = params.searchDate[1];
                delete params.searchDate;
              }
              if (params.hasOwnProperty("searchDate2")) {
                params.uploadDatetimeStart = params.searchDate2[0];
                params.uploadDatetimeEnd = params.searchDate2[1];
                delete params.searchDate2;
              }
            }
            let url = '/chain/resourcecustomerwaybill/exportExcel'
            expotOut( params,url,'资源票核销');
          },
          editRow(row,type){
            //type  1修改磅单  上传磅单
            this.$set(this.tableOption,'editTitle',type==1?'修改磅单':'上传磅单')
            this.$refs.crud.rowEdit(row)
          }
      }
  }
</script>

<style lang="scss" scoped>
</style>
