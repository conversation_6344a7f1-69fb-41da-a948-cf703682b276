<template>
  <div class="chooseCompany" v-loading="loading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading">
    <div class="header" @click="$router.go(-1)"><span>返回上一步</span></div>
    <div class="title">我的企业({{list.length}})</div>
    <ul v-if="list && list.length > 0">
      <li v-for="item in list" :key="item.companyAuthId">
        <div class="top flex flex-items-center">
          <div class="photo flex flex-items-center">
            <img src="../../static/login/jyb.png" alt="" />
            <span class="overflow_1">{{ item.companyAuthName }}</span>
          </div>
          <el-tag v-if="item.currentCompanyAuth==1">当前企业</el-tag>
        </div>
        <div class="accessIdentity flex flex-items-center flex-between">
          <div>访问身份:{{ item.positionName }}</div>
          <!-- <div class="remark">当前企业</div> -->
          </div>
        <div class="btns">
          <el-button type="primary"  @click="handle(item)">进入企业</el-button>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import {switchCompanyAuthCode} from "@/api/login.js"
import { mapGetters } from "vuex";
import { getAuditPassCompanyAuthList } from "@/api/login.js";

export default {
  data() {
    return {
      list: [],
      loading:false,
    };
  },
  mounted() {
    this.getList()
  },
  computed: {
    ...mapGetters(["tagWel","roleType"]),
  },
  methods: {
    getList(){
      this.loading = true
      getAuditPassCompanyAuthList().then(res=>{
        sessionStorage.setItem('companyUser',JSON.stringify(res.data.data))
        this.loading = false
        this.list = res.data.data
      }).catch(()=>{
        this.loading = false
      })
    },
    handle(item){
      switchCompanyAuthCode().then(res=>{
        let data = res.data.data
        let loginForm ={
          phone:item.phone,
          code:data,
          roleType:this.roleType,
          company_auth_id:item.companyAuthId,
        }
        this.$store.dispatch("LoginByPhone", loginForm).then(() => {
          console.log(loginForm);
          this.$store.commit("SET_TOP_MENU_INDEX", 0);
          this.$store.dispatch("SetRoleType",loginForm.roleType)
          this.$store.commit("SET_ROLES",[])
          this.$store.commit("DEL_ALL_TAG");
          this.$router.push({ path: this.tagWel.value });
        });
      })
    }
  },
};
</script>

<style scoped lang="less">
.chooseCompany {
  height: 100%;
  background-color: #f3f3f3;
  overflow-y: auto;
  .header{
    // background-color: #fff;
    text-align: center;
    color: #1876e9;
    line-height: 56px;

    width: 100%;
    span{
      text-decoration: underline;
    }
  }
  .title {
    font-size: 20px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
    line-height: 28px;
    padding: 0 30px;
    width: 1100px;
    margin: 10px auto 0;
  }
}
ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  width: 1190px;
  margin: 24px auto 0;
  li {
    width: 380px;
    height: 214px;
    background: #fdfdfd;
    box-shadow: 0px 10px 15px 0px rgba(102, 102, 102, 0.1);
    border-radius: 8px;
    border: 1px solid #eeeeee;
    margin-right: 16px;
    box-sizing: border-box;
    padding: 28px 25px 24px;
    margin-bottom: 10px;
    .top {
      justify-content: space-between;
      img {
        width: 32px;
        height: 32px;
        margin-right: 9px;
      }
      .photo {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #666666;
        line-height: 20px;
        text-shadow: 0px 10px 15px rgba(102, 102, 102, 0.1);
      }
      // .remark {
      //   width: 52px;
      //   margin-left: 10px;
      //   font-size: 12px;
      //   font-family: PingFangSC-Regular, PingFang SC;
      //   font-weight: 400;
      //   color: #bbbbbb;
      //   line-height: 17px;
      //   text-shadow: 0px 10px 15px rgba(102, 102, 102, 0.1);
      // }
    }
    .accessIdentity {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 20px;
      text-shadow: 0px 10px 15px rgba(102, 102, 102, 0.1);
      margin-top: 26px;
      margin-bottom: 40px;
    }
    .btns {
      // text-align: center;
      .el-button{
        width: 100%;
      }
    }
  }
}
</style>
