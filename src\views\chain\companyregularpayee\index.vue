<template>
  <div class="execution">
    <basic-container>
      <my-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-save="handleSave"
                 @row-update="handleUpdate"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="menuLeft"
                  slot-scope="{ row, index }">
          <!-- <el-button type="primary"
                     v-if="permissions['chain:companyregularpayee:sendSms']"
                     size="small"
                     :loading="tableLoading"
                     icon="el-icon-s-comment"
                     @click="batchSendSms">批量发送短信</el-button> -->
          <el-button type="primary"
                     v-if="permissions['chain:companyregularpayee:add']"
                     size="small"
                     icon="el-icon-plus"
                     @click="add">新增收款人</el-button>
          <el-button type="primary"
                     v-if="permissions['chain:companyregularpayee:upload']"
                     size="small"
                     icon="el-icon-upload2"
                     @click="uploadVisible=true">导入</el-button>
          <el-button type="primary"
                     v-if="permissions['chain:companyregularpayee:excel']"
                     size="small"
                     :loading="tableLoading"
                     icon="el-icon-download"
                     @click="exOut">导出</el-button>
        </template>
        <template slot="menu"
                  slot-scope="{ row, index }">
          <el-button type="text"
                     plain
                     v-if="permissions['chain:companyregularpayee:edit']"
                     size="small"
                     :loading="tableLoading"
                     :disabled="row.isEnable=='是'"
                     icon="el-icon-edit"
                     @click="beforeOpen(row,index)">编辑</el-button>
          <!-- <el-button type="text"
                     plain
                     v-if="permissions['chain:companyregularpayee:sendSms']&&row.isEnable!='是'"
                     size="small"
                     :loading="tableLoading"
                     icon="el-icon-s-comment"
                     :disabled="row.isSendSms==1"
                     @click="sendSms(row)">{{row.isSendSms==1?'已发送':'短信提醒'}}</el-button> -->
        </template>
      </my-crud>
    </basic-container>
    <uploadImport ref="uploadImport" v-if="uploadVisible" :visible.sync="uploadVisible" @imtExcel="imtExcel" @openTempUrl="openTempUrl"></uploadImport>
  </div>
</template>

<script>
import { getPage, getObj, addObj,putObj,importExcel,sendRegularPayeeSms,batchSendRegularPayeeSms,downloadRegularPayeeTemplate } from '@/api/chain/companyregularpayee'
import { tableOption } from '@/const/crud/chain/companyregularpayee'
import { mapGetters } from 'vuex'
import uploadImport from "@/components/uploadImport"
import { exportOut } from "@/util/down.js";
export default {
  name: 'companyregularpayee',
  components:{
    uploadImport
  },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      uploadVisible:false,
    }
  },
  created () {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList () {
      return {
        addBtn: this.permissions['chain:companyregularpayee:add'] ? true : false,
        delBtn: this.permissions['chain:companyregularpayee:del'] ? true : false,
        viewBtn: this.permissions['chain:companyregularpayee:get'] ? true : false
      };
    }
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage (page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    add(){
      this.$refs.crud.tableOption.column.forEach(element => {
        if (element.prop == 'idCard'||element.prop == 'idCardNegativeUrl'||element.prop=="idCardPositiveUrl") {
           this.$set(element,'disabled',false)
        }
        if (element.prop == 'payeeMobile') {
           this.$set(element,'disabled',false)
        }
      });
      this.$refs.crud.rowAdd()
    },
    beforeOpen(row,index) {
      this.$refs.crud.tableOption.column.forEach(element => {
        if (element.prop == 'idCard'||element.prop == 'idCardNegativeUrl'||element.prop=="idCardPositiveUrl") {
           this.$set(element,'disabled',row.cardStatus==2||row.cardStatus==3)
        }
        if (element.prop == 'payeeMobile') {
           this.$set(element,'disabled',true)
        }
      });
      // this.$refs.crud.doLayout()
      console.log(this.$refs.crud.option);
      this.$refs.crud.rowEdit(row,index)
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
      handleUpdate: function (row, index, done, loading) {
        putObj(row).then(response => {
            this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
            })
            done()
            this.getPage(this.page)
        }).catch(() => {
            loading()
        })
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page)
    },
    openTempUrl(){
      let params ={}
      let url = '/chain/companyregularpayee/downloadRegularPayeeTemplate'
      this.tableLoading = true
      exportOut(params,url,'导入模板').then(res=>{
        this.tableLoading = false
      }).catch(err=>{
        this.$message.error('导出失败')
        this.tableLoading = false
      })
    },
    //上传表格
    imtExcel(formData) {
      importExcel(formData).then(res=>{
        this.$message.success("导入成功")
        this.uploadVisible = false
        this.$refs.uploadImport.init()
        this.getPage(this.page)
      }).catch((err)=>{
        this.$refs.uploadImport.btnLoading = false
        this.$message.error(err.data.msg||"导入失败")
      })
    },
    sendSms(row){
      this.$confirm('是否发送短信通知收款人进行完善资料?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.tableLoading = true
          sendRegularPayeeSms([row.id]).then(res=>{
            this.tableLoading = false
            this.$message.success("发送成功")
            this.getPage(this.page)
          }).catch(()=>{
            this.tableLoading = false
          })
        }).catch(() => {});
    },
    batchSendSms(){
      this.$confirm('是否发送短信通知所有未生效的收款人进行完善资料?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.tableLoading = true
          batchSendRegularPayeeSms().then(res=>{
            this.tableLoading = false
            this.$message.success(res.data.msg)
            this.getPage(this.page)
          }).catch(()=>{
            this.tableLoading = false
          })
        }).catch(() => {});
    },
    exOut(){
      let params = Object.assign({}, this.paramsSearch);
      let url = '/chain/excelExport/postCreateByCode'
      params.code="CompanyRegularPayeeExcelExport"
      this.tableLoading = true
      exportOut(params,url,'常用收款人',"post").then(res=>{
        this.tableLoading = false
      }).catch(err=>{
        this.$message.error('导出失败')
        this.tableLoading = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
