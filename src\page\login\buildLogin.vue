<template>
  <div class="login-containers pull-height">
    <img
      :class="imageClass"
      src="https://jyb-test.obs.cn-south-1.myhuaweicloud.com:443/null%2FauthPic%2F80c395d8-15e0-48f5-9298-59a299c6dd3e.png"
      alt=""
    />
      <buildLoginCom />
    <div class="footer">
      <div>深圳益路供应链有限公司</div>
      <div>
        <span>copyright © 2022 jyb.szyl8.cn 版权所有</span>
        <span style="padding: 0 0.06rem">|</span>
        <a
          target="_blank"
          href="https://beian.miit.gov.cn/#/Integrated/recordQuery"
          >粤ICP备20006643号</a
        >
      </div>
    </div>
  </div>
</template>
<script>
import buildLoginCom from "./buildLoginCom";
import { mapGetters } from "vuex";
import { getStore, setStore } from "@/util/store";
import { dateFormat } from "@/util/date";
import { validatenull } from "@/util/validate";
// import topColor from "@/page/index/top/top-color";
export default {
  name: "login",
  components: {
    buildLoginCom,
  },
  data() {
    return {
      time: "",
      activeName: this.$route.meta.activeName,
      thirdPartyForm: {},
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
    };
  },
  watch: {
    $route: {
      handler() {
        const params = this.$route.query;
        if (validatenull(params.state) && validatenull(params.code)) return;

        this.thirdPartyForm.state = params.state || "";
        this.thirdPartyForm.code = params.code || "";
        this.$store
          .dispatch("LoginByThirdParty", this.thirdPartyForm)
          .then(() => {
            this.$store.commit("SET_TOP_MENU_INDEX", 0);
            this.$router.push({ path: this.tagWel.value });
          })
          .catch(() => {});
      },
      immediate: true,
    },
  },
  created() {
    // this.getTime();
    // setInterval(() => {
    //   this.getTime();
    // }, 1000);
  },
  mounted() {
    this.fnResize();
    window.addEventListener("resize", this.fnResize);
  },
  computed: {
    ...mapGetters(["website", "tagWel"]),
    imageClass() {
      if (this.screenWidth > this.screenHeight) {
        return "landscape-image";
      } else {
        return "portrait-image";
      }
    },
  },
  props: [],
  methods: {
    getTime() {
      this.time = dateFormat(new Date());
      console.log(this.time);
    },
    fnResize() {
      this.screenWidth = window.innerWidth;
      this.screenHeight = window.innerHeight;
      var deviceWidth =
        document.documentElement.clientWidth || window.innerWidth;
      if (deviceWidth >= 1920) {
        deviceWidth = 1920;
      }
      if (deviceWidth <= 1080) {
        deviceWidth = 1080;
      }
      document.documentElement.style.fontSize = deviceWidth / 19.2 + "px";
    },
  },
  beforeDestroy() {
    document.documentElement.style.fontSize = "16px";
    window.removeEventListener("resize", this.fnResize);
  },
  // beforeRouteLeave(to, from, next) {
  //   document.documentElement.style.fontSize = '16px';
  //    next() // 下一步
  // }
};
</script>

<style lang="scss" scoped>
.login-containers {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;

  .landscape-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
  .portrait-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
  }

  .right {
    width: 30%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
   
  }
}

.footer {
  position: absolute;
  bottom: 0.15rem;
  text-align: center;
  width: 100%;
  color: #999aaa;
  font-size: 0.14rem;

  a {
    color: blue;
  }
}
</style>
