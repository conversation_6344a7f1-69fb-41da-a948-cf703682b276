<template>
  <div class="voucher">
    <el-drawer
      size="700px"
      custom-class="detailDialog"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <span slot="title" class="drawer-title">
        <!-- <el-button type="primary" size="small" @click="print">打印</el-button> -->
        <el-button
          type="primary"
          size="small"
          :loading="btnLoading"
          @click="printPDF(1)"
          >下载支付凭证</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="btnLoading2"
          @click="printPDF(2)"
          >下载银行回单</el-button
        >
      </span>
      <div
        ref="print"
        class="print"
        id="print"
        v-if="detailList && detailList.length > 0"
      >
        <div>
          <div v-for="(item, index) in detailList" :key="index">
            <div class="voucher">
              <img
                :src="item.img"
                alt=""
                width="400"
                height="250"
                style="position: absolute; left: 40px; top: 0px"
              />
              <div class="lists">
                <div class="title"><span></span>支付凭证</div>
                <ul>
                  <li>
                    <el-row>
                      <el-col :span="12"> 交易类型：支付 </el-col>
                      <el-col :span="12">
                        支付单号：{{ item.paymentNo }}
                      </el-col>
                    </el-row>
                  </li>
                  <li>
                    <el-row>
                      <el-col :span="12">
                        项目名称：{{ item.projectInfoName }}
                      </el-col>
                      <el-col :span="12">
                        付款运单总数：{{ item.waybillCnt }}车
                      </el-col>
                    </el-row>
                  </li>
                  <li>
                    <el-row>
                      <el-col :span="12">
                        交易金额：{{ item.tranAmount }}元
                      </el-col>
                      <el-col :span="12">
                        收款卡号：{{ item.bindingBankNo }}
                      </el-col>
                    </el-row>
                  </li>
                  <li>
                    <el-row>
                      <el-col :span="12">
                        收款人姓名：{{ item.payeeName }}
                      </el-col>
                      <el-col :span="12">
                        收款银行：{{ item.bindingBankName }}
                      </el-col>
                    </el-row>
                  </li>
                  <li>
                    <el-row>
                      <el-col :span="24">
                        付款公司：{{ item.companyName }}
                      </el-col>
                    </el-row>
                  </li>
                  <li>
                    <el-row>
                      <el-col :span="12">
                        创建时间：{{ item.createDatetime }}
                      </el-col>
                      <el-col :span="12">
                        付款时间：{{
                          $moment(item.payTime).format("YYYY-MM-DD HH:mm:ss")
                        }}
                      </el-col>
                    </el-row>
                  </li>
                </ul>
              </div>
            </div>
            <img width="100%" :src="item.voucherPic" alt="" />
          </div>
        </div>
      </div>
      <el-empty v-else></el-empty>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getEvidenceWherePdf } from "@/api/chain/companyauthrecharge";
import { print, downloadPDF } from "@/util/util.js";
import { expotOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    detailList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    paymentIdList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    sourceId: {
      type: [String, Boolean],
      default: () => {
        return false;
      },
    },
  },
  data() {
    return {
      btnLoading: false,
      btnLoading2: false,
    };
  },
  created() {},
  mounted: function () {
    console.log("12" + this.paymentIdList);
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
      this.$emit("refreshChange");
    },
    print() {
      print(this.$refs.print);
    },
    printPDF(i) {
      if (i == 1) {
        this.btnLoading = true;
      } else {
        this.btnLoading2 = true;
      }
      console.log(this.sourceId);
      let param = {
        paymentIdList: this.paymentIdList,
        printFlag: i,
      };
      console.log("this.sourceId=" + this.sourceId);
      //钱包查询凭证
      if (this.sourceId) {
        param = {
          sourceId: this.sourceId,
          printFlag: i,
        };
      }
      getEvidenceWherePdf(param)
        .then((res) => {
          console.log(res);
          const url = window.URL.createObjectURL(new Blob([res.data]));
          const link = document.createElement("a");
          link.href = url;
          let fileName = i == 1 ? "支付凭证.pdf" : "银行回单.pdf"; //
          link.setAttribute("download", fileName);
          document.body.appendChild(link);
          link.click();
          if (i == 1) {
            this.btnLoading = false;
          } else {
            this.btnLoading2 = false;
          }
        })
        .catch((err) => {
          if (i == 1) {
            this.btnLoading = false;
          } else {
            this.btnLoading2 = false;
          }
        });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .detailDialog {
  .avue-crud__menu {
    display: none;
  }
  .lists {
    border: 1px solid #9a9a9a;
    padding: 10px 0px;
    font-size: 12px;

    .title {
      padding-left: 8px;
      text-align: left;
      height: 20px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #111528;
      // border-left: 4px solid #4688f7;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #9a9a9a;
      span {
        display: inline-block;
        height: 16px;
        width: 4px;
        background-color: #4688f7;
        margin-right: 4px;
      }
    }
    ul {
      padding: 0px 30px;
      li {
        line-height: 30px;
        color: #303133;
      }
    }
  }
  .el-drawer__header {
    margin-bottom: 0;
  }
  .el-drawer__body {
    padding: 0px 20px;
    .voucher {
      position: relative;
      padding-bottom: 55.8px;
      &:nth-of-type(3n) {
        margin-bottom: 20px;
      }
    }
  }
  // .detailContent{
  .print {
    padding: 30px 30px 0px;
    position: relative;
    left: 0;
    top: 0;
  }

  // }
}
</style>
