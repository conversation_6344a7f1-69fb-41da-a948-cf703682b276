import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/driverfleetmember/captainList',
        method: 'get',
        params: query
    })
}


export function putObj(obj) {
    return request({
        url: '/chain/systemuser',
        method: 'put',
        data: obj
    })
}
export function findCaptain(phone) {
    return request({
        url: '/chain/systemuser/findCaptain/'+phone,
        method: 'get',
    })
}
export function getAuthorizationProjectList(captainId) {
    return request({
        url: '/chain/projectinfo/getAuthorizationProjectList/'+captainId,
        method: 'get',
    })
}
