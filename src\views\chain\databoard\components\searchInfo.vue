<template>
  <div class="searchInfo">
    <slot name="count"></slot>
    <div class="search flex flex-between">
      <el-radio-group v-model="form.checkDynamic" size="small" style="margin-right:10px;min-width:160px"
        @change="changeStatus">
        <el-radio-button label="">
          全部项目
        </el-radio-button>
        <el-radio-button label="1">
          活跃项目
        </el-radio-button>
      </el-radio-group>
      <slot name="searchLeft"></slot>
      <div class="searchContent">
        <slot name="searchRight" class="item"></slot>
        <el-radio-group v-model="form.weightUnit" @change="changeUnit" size="small" style="margin-right:10px;"
          v-if="source == '4'" class="item">
          <el-radio-button label="方">
          </el-radio-button>
          <el-radio-button label="吨">
          </el-radio-button>
        </el-radio-group>
        <el-radio-group v-model="radio" size="small" @change="changeTime" class="item">
          <el-radio-button label="week">最近一周</el-radio-button>
          <el-radio-button label="month">最近一个月</el-radio-button>
          <el-radio-button label="year">最近一年</el-radio-button>
        </el-radio-group>
        <div class="item">
          <span style="font-size:14px;margin-left:8px">{{ filterLabel }}：</span>
          <el-date-picker style="margin-right: 8px;width:300px" v-model="form.searchTime" type="daterange" :editable="false"
          :pickerOptions="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" :clearable="false">
          </el-date-picker>
        </div>
        <div class="item">
          <el-button type="primary" size="small" @click="search" :loading="btnLoading">搜索</el-button>
          <el-button type="primary" size="small" @click="exOut" :loading="btnLoading">导出</el-button>
        </div>
      </div>
    </div>
    <slot name="center"></slot>
    <!-- //异常运单数searchTimeLabel才会传入申请时间 -->
    <projectList v-if="projectList && projectList.length > 0" :text="source == '4' ? ' ' : '运单数'"
      @changeProject="changeProject" :active="form.projectInfoId" :projectList="projectList" :defaultProp="defaultProp">
    </projectList>
  </div>
</template>

<script>
import projectList from './projectList';
import { getProjectList, getProjectDynamicInfoByExcavatedNumber, activityProjectGarbage } from "@/api/chain/board";

export default {
  props: {
    //传进来的数据
    info: {},
    isShowProject: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    //来源 1出车运单总数 2车队长总数  3 泥尾点总数  4总出土量/泥尾点总出土量  5运单费
    source: {
      type: String,
      default: () => {
        return "1"
      }
    },
    type: {
      type: Number,
      default: () => {
        return 1
      }
    },
    defaultProp: {
      label: "projectName", value: "projectInfoId", cnt: "waybillCnt"
    }
  },
  components: {
    projectList
  },
  data () {
    return {
      projectList: [],
      form: {},
      projectInfo: {},
      radio: "",
      btnLoading: false,
      // 时间跨度为之前一年
      pickerOptions: {
        disabledDate: () => false,
        onPick: ({ maxDate, minDate }) => {
          if (minDate && !maxDate) {
            const oneYear = 365 * 24 * 60 * 60 * 1000;
            this.pickerOptions.disabledDate = time => {
              return time.getTime() < minDate.getTime() || time.getTime() > minDate.getTime() + oneYear;
            };
          } else {
            this.pickerOptions.disabledDate = () => false;
          }
        },
      },
    }
  },
  created () {
    this.form = {
      checkDynamic: this.info.checkDynamic,
      projectInfoId: this.info.projectInfoId || '',
      searchTime:[],
    }
    this.radio = this.info.radio
    if (this.source == '4') {
      this.form = {
        checkDynamic: this.info.checkDynamic,
        projectInfoId: this.info.projectInfoId || '',
        weightUnit: this.info.weightUnit
      }
      console.log(this.form.weightUnit);
    }
    if (this.info.startDate) {
      this.form.searchTime = [this.info.startDate, this.info.endDate]
    }
    if (this.isShowProject) {
      this.getProjectList()
    }
  },
  mounted () {
  },
  computed: {
    filterLabel () {
      switch (this.source) {
        case "1":
          return "运单创建的时间"
          break;
        case "2":
          return "车队长创建时间"
          break;
        case "3":
          return "泥尾创建时间"
          break;
        case "4":
          return "出场签单时间"
          break;
        case "5":
          return "支付单生成时间"
          break;
        case "6":
          return "支付单创建时间"
          break;
        default:
          break;
      }
    }
  },
  methods: {
    getProjectList () {
      let param = {
        startDate: this.form.searchTime[0],  //开始时间
        endDate: this.form.searchTime[1],  //结束时间
        type: this.type,
        checkReturnAll: true,
      }
      this.getList(param)
    },
    getList (param) {
      if (this.source == '3') {//总泥尾数
        let data = {
          startDate: this.form.searchTime[0],  //开始时间
          endDate: this.form.searchTime[1],  //结束时间
        }
        activityProjectGarbage(data).then(res => {
          this.projectInfo = res.data.data
          this.projectList = this.form.checkDynamic == 1 ? this.projectInfo.projectDynamicList : this.projectInfo.projectList
          console.log(this.form.projectInfoId);
        })
      } else {
        getProjectList(param).then(res => {
          this.projectInfo = res.data.data
          this.projectList = this.form.checkDynamic == 1 ? this.projectInfo.projectDynamicList : this.projectInfo.projectList
          console.log(this.form.projectInfoId);
        })
      }
    },
    changeStatus (val) {
      //更换项目列表  有些表格需要变换数据
      if (this.isShowProject) {
        if (val == 1) {
          this.projectList = this.projectInfo.projectDynamicList
          this.form.projectInfoId = this.projectList[0].id
        } else {
          this.projectList = this.projectInfo.projectList
          this.form.projectInfoId = this.projectList[0].id
        }
        this.searchData()
      }
      this.$emit("changeStatus", val)
    },
    changeUnit (val) {
      //更换单位，表格需要变换数据
      this.$emit("changeUnit", val)
    },
    changeProject (val) {
      this.form.projectInfoId = val
      console.log(this.form.projectInfoId);
      this.searchData()
    },
    changeTime (val) {
      let startDate = ""
      let endDate = this.$moment().format('YYYY-MM-DD')
      switch (val) {
        case 'week':
          startDate = this.$moment().subtract(7, 'days').format('YYYY-MM-DD');
          break;
        case 'month':
          startDate = this.$moment().subtract(1, 'months').format('YYYY-MM-DD');
          break;
        case 'year':
          startDate = this.$moment().subtract(1, 'years').format('YYYY-MM-DD');
          break;

        default:
          break;
      }
      this.form.searchTime = [startDate, endDate]
      console.log(val);
    },
    search () {
      this.searchData()
      if (this.isShowProject) {
        this.getProjectList()
      }
    },
    searchData () {
      let param = {
        startDate: this.form.searchTime[0],  //开始时间
        endDate: this.form.searchTime[1],  //结束时间
        checkDynamic: this.form.checkDynamic,  //是否活跃项目
        projectInfoId: this.form.projectInfoId,
      }
      if (this.source == 4) {
        param.weightUnit = this.form.weightUnit
      }
      this.btnLoading = true
      this.$emit('searchChange', param, this.stopLoading)
    },
    stopLoading () {
      this.btnLoading = false
    },
    exOut () {
      let param = {
        startDate: this.form.searchTime[0],  //开始时间
        endDate: this.form.searchTime[1],  //结束时间
        checkDynamic: this.form.checkDynamic,  //是否活跃项目
        projectInfoId: this.form.projectInfoId
      }
      if (this.source == 4) {
        param.weightUnit = this.form.weightUnit
      }
      this.$emit("exOut", param, this.stopLoading)
    },
  },

};
</script>

<style lang="scss" scoped>
.search {
  .searchContent {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .item {
      flex-grow: 1;
      margin-bottom: 10px;
    }
  }
}
</style>
