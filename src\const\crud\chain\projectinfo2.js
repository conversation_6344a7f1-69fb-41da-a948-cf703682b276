export const tableOption = (value) => {
  let that = value;
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    // stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    searchBtn: true,
    excelBtn: false,
    printBtn: false,
    dialogWidth: "66%",
    dialogFullscreen:true,
    delBtn: false,
    viewBtn: false,
    editBtn: false,
    dialogClickModal: false,
    searchMenuSpan: 6,
    labelWidth: 130,
    searchLabelWidth: 100,
    menuWidth: 240,
    customClass: "customProjectDialog",
    // searchIcon:true,
    // searchIndex:3,
    searchCustom:2,
    routerName:"projectinfo2",
    column: [
      //     {
      //     label: '企业认证ID',
      //     prop: 'companyAuthId',
      //     sortable: true,
      //     rules: [
      //             {
      //                 required: true,
      //                 message: '请输入企业认证ID',
      //                 trigger: 'blur'
      //             },
      //                             {
      //                 max: 36,
      //                 message: '长度在不能超过36个字符'
      //             },
      //         ]
      // },
      {
        label: "项目名称",
        prop: "projectName",
        sortable: true,
        placeholder: "项目简称",
        search: true,
        display: false,
        maxlength: 10,
        order: 1,
        rules: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
          {
            max: 16,
            message: "长度在不能超过10个字符",
          },
        ],
        minWidth:200,
        overHidden:true,
      },
      {
        label: "项目地址",
        prop: "projectAddress",
        disabled: true,
        display: false,
        placeholder: "请在地图选择定位",
        sortable: false,
        order: 12,
        rules: [
          {
            required: true,
            message: "请选择项目地址",
            trigger: "change",
          },
        ],
        width:140,
        overHidden:true,
      },
      {
        label: "范围(米)",
        prop: "electronicRange",
        sortable: false,
        display: false,
        order: 16,
        span: 24,
        hide: false,
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        width:90,
        overHidden:true,
      },
      //     {
      //     label: '项目负责人IDS',
      //     prop: 'leadingIds',
      //     sortable: true,
      //     rules: [
      //                             {
      //                 max: 1000,
      //                 message: '长度在不能超过1000个字符'
      //             },
      //         ]
      // },

      {
        label: "签单方式",
        prop: "isGo",
        order: 4,
        hide: true,
        display: false,
        width:80,
        overHidden:true,
      },
      {
        label: "负责人姓名",
        prop: "leadingIds",
        order: 2,
        // searchSpan:15,
        sortable: true,
        display: false,
        type: "select",
        props: {
          label: "name",
          value: "id",
        },
        multiple: true,
        dicUrl: "/chain/companystaff/list",
        search: true,
        formslot: true, // 自定义表单
        filterable: true,
        searchFilterable: true,
        rules: [
          {
            required: true,
            message: "请选择负责人",
            trigger: "change",
          },
        ],
        minWidth:140,
        overHidden:true,
      },
      // /chain/companystaff/list
      //     {
      //     label: '项目成员IDS',
      //     prop: 'memberIds',
      //     sortable: true,
      //     rules: [
      //                             {
      //                 max: 1000,
      //                 message: '长度在不能超过1000个字符'
      //             },
      //         ]
      // },
      {
        label: "成员姓名",
        prop: "memberIds",
        order: 3,
        hide: true,
        sortable: true,
        display: false,
        type: "select",
        props: {
          label: "name",
          value: "id",
        },
        multiple: true,
        filterable: true,
        dicUrl: "/chain/companystaff/list2",
        minWidth:140,
        overHidden:true,
      },
      {
        label: "土质",
        prop: "listSoilType",
        order: 5,
        hide: true,
        display: false,
        sortable: true,
        type: "cascader",
        filterable: true,
        props: {
          label: "itemName",
          value: "itemName",
        },
        multiple: true,
        separator: ",",
        dicUrl:
          "/chain/systemdictionaryitem/listDictionaryItem?dictionary=soil_type",
        width:80,
        overHidden:true,
      },
      {
        label: "泥尾",
        prop: "garbageIds",
        order: 6,
        hide: true,
        sortable: true,
        display: false,
        type: "select",
        filterable: true,
        props: {
          label: "names",
          value: "id",
        },
        multiple: true,
        dicUrl: "/chain/garbage/listByCompanyAuth",
        width:140,
        overHidden:true,
        // rules: [
        //   {
        //     required: true,
        //     message: "请选择泥尾",
        //     trigger: "change",
        //   },
        // ],
      },
      // {
      //   order: 7,
      //   prop: "soilTypeTip",
      //   formslot:true,
      //   hide:true,
      //   row:true,
      //   className:'txt'
      // },
      //  {
      //   order: 8,
      //   hide:true,
      //   prop: "muTailTip",
      //   formslot:true,
      //   className:'txt'
      // },
      {
        label: "称重方式",
        order: 10,
        hide: true,
        display: false,
        prop: "weightType",
        width:90,
        overHidden:true,
      },
      // {
      //   label: "称重车辆",
      //   order: 11,
      //   hide: true,
      //   prop: "weightTruckTypeIds",
      //   formslot: true,
      //   type: 'checkbox',
      //   display: false,
      //   props: {
      //     label: "itemName",
      //     value: "itemValue",
      //   },
      //   dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=weight_truck_type',
      //   rules: [
      //     {
      //       required: true,
      //       message: "请选择称重车辆",
      //       trigger: "change",
      //     },
      //   ],
      // },
      {
        label: "需泥尾票管控",
        prop: "ticketEnable",
        order: 13,
        type: "switch",
        display: false,
        dicData: [
          {
            label: "否",
            value: "0",
          },
          {
            label: "是",
            value: "1",
          },
        ],
        hide: true,
        minWidth:140,
        overHidden:true,
      },
      // {
      //   label: "挖机是否选择土质",
      //   prop: "isInSoilType",
      //   order: 14,
      //   type: "switch",
      //   dicData:[
      //     {
      //     label: '否',
      //     value: '0'
      //   },
      //     {
      //     label: '是',
      //     value: '1'
      //   },
      // ],
      //   hide:true,
      // },
      {
        label: "运单结算类型",
        prop: "settleType",
        type: "select",
        search: true,
        display: false,
        order: 15,
        value: "3",
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl:
          "/chain/systemdictionaryitem/listDictionaryItem?dictionary=settle_type",
        rules: [
          {
            required: true,
            message: "请选择运单结算类型",
            trigger: "change",
          },
        ],
        width:110,
        overHidden:true,
      },
      {
        label: "需泥尾票库存",
        prop: "isTicketInventory",
        order: 15,
        type: "switch",
        dicData: [
          {
            label: "否",
            value: "0",
          },
          {
            label: "是",
            value: "1",
          },
        ],
        hide: true,
        display: false,
        width:120,
        overHidden:true,
      },
      {
        label: "智能IOT",
        prop: "isIotWaybill",
        type: "switch",
        order: 15,
        addDisplay: false,
        display: false,
        disabled: true,
        dicData: [
          {
            label: "否",
            value: "0",
          },
          {
            label: "是",
            value: "1",
          },
        ],
        width:90,
        overHidden:true,
      },
      //     {
      //     label: '合作单位IDS',
      //     prop: 'agentInfoIds',
      //     sortable: true,
      //     rules: [
      //                             {
      //                 max: 1000,
      //                 message: '长度在不能超过1000个字符'
      //             },
      //         ]
      // },
      {
        label: "合作单位",
        prop: "agentInfoNames",
        sortable: false,
        addDisplay: false,
        editDisplay: false,
        minWidth:120,
        overHidden:true,
      },
      {
        label: "项目状态",
        prop: "projectStatus",
        sortable: true,
        search: true,
        addDisplay: false,
        editDisplay: false,
        type: "select",
        dicData: [
          {
            label: "进行中",
            value: "1",
          },
          {
            label: "已完成",
            value: "9",
          },
        ],
        rules: [
          {
            required: true,
            message: "请选择项目状态",
            trigger: "blur",
          },
        ],
        width:94,
        overHidden:true,
      },
      //     {
      //     label: '电子围栏',
      //     prop: 'electronicFence',
      //     sortable: true,
      //     rules: [
      //                         ]
      // },
      //     {
      //     label: '电子围栏覆盖范围',
      //     prop: 'electronicRange',
      //     sortable: true,
      //     rules: [
      //                         ]
      // },
      {
        label: "启动日期",
        prop: "startDate",
        type: "datetime",
        sortable: true,

        valueFormat: "yyyy-MM-dd HH:mm:ss",
        searchRange: true,
        addDisplay: false,
        editDisplay: false,
        search: true,
        width:140,
        overHidden:true,
      },
      {
        label: "完成日期",
        prop: "finishDate",
        type: "datetime",
        searchRange: true,
        addDisplay: false,
        valueFormat: "yyyy-MM-dd HH:mm:ss",
        editDisplay: false,
        display: false,
        sortable: true,
        search: true,
        width:140,
        overHidden:true,
      },
      //     {
      //     label: '是否删除',
      //     prop: 'isDel',
      //     sortable: true,
      //     formatter:(val)=>{
      //         if (val.isDel == "0") {
      //             return '<span>未删除</span>'
      //         }
      //         if (val.isDel == "1") {
      //             return '<span>已删除</span>'
      //         }
      //       },
      //     rules: [
      //             {
      //                 required: true,
      //                 message: '请输入是否删除：1=已删除、0=未删除',
      //                 trigger: 'blur'
      //             },
      //                             {
      //                 max: 1,
      //                 message: '长度在不能超过1个字符'
      //             },
      //         ]
      // },
      //     {
      //     label: '创建日期时间',
      //     prop: 'createDatetime',
      //     sortable: true,
      //     rules: [
      //             {
      //                 required: true,
      //                 message: '请输入创建日期时间',
      //                 trigger: 'blur'
      //             },
      //                         ]
      // },
      //     {
      //     label: '创建用户',
      //     prop: 'createId',
      //     sortable: true,
      //     rules: [
      //             {
      //                 required: true,
      //                 message: '请输入创建用户',
      //                 trigger: 'blur'
      //             },
      //                             {
      //                 max: 36,
      //                 message: '长度在不能超过36个字符'
      //             },
      //         ]
      // },
      //     {
      //     label: '修改日期时间',
      //     prop: 'updateDatetime',
      //     sortable: true,
      //     rules: [
      //             {
      //                 required: true,
      //                 message: '请输入修改日期时间',
      //                 trigger: 'blur'
      //             },
      //                         ]
      // },
      //     {
      //     label: '修改用户',
      //     prop: 'updateId',
      //     sortable: true,
      //     rules: [
      //             {
      //                 required: true,
      //                 message: '请输入修改用户',
      //                 trigger: 'blur'
      //             },
      //                             {
      //                 max: 36,
      //                 message: '长度在不能超过36个字符'
      //             },
      //         ]
      // },
    ],
    group: [
      //弹窗区分
      {
        label: "项目信息",
        arrow: false,
        prop: "group1",
        column: [
          {
            label: "项目名称",
            prop: "projectName",
            placeholder: "项目简称",
            maxlength: 15,
            order: 1,
            rules: [
              {
                required: true,
                message: "请输入项目名称",
                trigger: "blur",
              },
              {
                max: 16,
                message: "长度在不能超过15个字符",
              },
            ],
          },
          {
            label: "项目负责人",
            prop: "leadingIds",
            order: 2,
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            multiple: true,
            dicUrl: "/chain/companystaff/list",
            // search: true,
            formslot: true, // 自定义表单
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择负责人",
                trigger: "change",
              },
            ],
          },
          {
            label: "项目成员",
            prop: "memberIds",
            order: 3,
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            multiple: true,
            filterable: true,
            dicUrl: "/chain/companystaff/list2",
          },
          {
            label: "运单结算类型",
            prop: "settleType",
            type: "select",
            order: 4,
            value: "1",
            props: {
              label: "itemName",
              value: "itemValue",
            },
            dicUrl:
              "/chain/systemdictionaryitem/listDictionaryItem?dictionary=settle_type",
            change: ({ value }) => {
              if (value) {
                let group = that.tableOption.group.find(
                  (item) => item.prop == "group3"
                );
                group.display = value == 4;
              }
            },
            rules: [
              {
                required: true,
                message: "请选择运单结算类型",
                trigger: "change",
              },
            ],
          },
          {
            label: "土质",
            prop: "listSoilType",
            order: 5,
            sortable: true,
            type: "select",
            filterable: true,
            props: {
              label: "itemName",
              value: "itemName",
            },
            multiple: true,
            separator: ",",
            dicUrl:
              "/chain/systemdictionaryitem/listDictionaryItem?dictionary=soil_type",
          },
          {
            label: "泥尾",
            prop: "garbageIds",
            order: 6,
            type: "select",
            filterable: true,
            props: {
              label: "names",
              value: "id",
            },
            multiple: true,
            dicUrl: "/chain/garbage/listByCompanyAuth",
          },
        ],
      },
      {
        label: "电子卡签单及APP直付设置",
        arrow: false,
        prop: "group3",
        className: "group3",
        display: false,
        class: "group3",
        column: [
          {
            label: "",
            labelWidth: 0,
            span: 24,
            prop: "annotation",
          },
          {
            label: "项目钱包余额停止直付(元)",
            labelWidth: 200,
            prop: "stopPayBalance",
            type: "number",
            controls: false,
            minRows: 0,
            maxRows: *********.99,
            precision: 2,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
            span: 16,
          },
          {
            label: "项目钱包余额短信通知(元)",
            labelWidth: 200,
            prop: "smsBalance",
            type: "number",
            controls: false,
            minRows: 0,
            maxRows: *********.99,
            precision: 2,
            // row:true,
            span: 16,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "司机运单卸土之后提现限制(小时)",
            labelWidth: 240,
            row: true,
            span: 16,
            prop: "companyWithdrawPeriod",
            type: "number",
            controls: false,
            minRows: 0,
            maxRows: *********,
            precision: 0,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "直付通道",
            prop: "directChannel",
            value: "1",
            type: "radio",
            dicData: [
              {
                label: "快钱支付",
                value: "1",
              },
              {
                label: "平安银行支付",
                value: "2",
              },
              {
                label: "台州银行支付",
                value: "5",
              },
            ],
            span: 24,
          },
          {
            label: "",
            span: 24,
            prop: "smsNoticeMobile",
            labelWidth: 8,
          },
          {
            label: "出场签单价格为直付价",
            prop: "isGoPayDirect",
            labelWidth: 160,
            value: "0",
            change: ({ value }) => {
              if (value) {
                console.log(that.tableOption);
                let maxDirectPay = that.tableOption.group.find(
                  (item) => item.prop == "group3"
                ).column.find(item2=>item2.prop=='maxDirectPay')
                maxDirectPay.display = value == 1;
                // that.form.weightType = value == 1?"3":"1"
                // that.form.isCubicPrice = value == 1?"1":"0"
              }
            },
            type: "switch",
            dicData: [
              {
                label: "否",
                value: "0",
              },
              {
                label: "是",
                value: "1",
              },
            ],
            span: 24,
          },
          {
            label: "最高限额价(元)",
            prop: "maxDirectPay",
            value: 1500,
            type:'number',
            controls:false,
            display:false,
            precision:2,
            minRows:0,
            maxRows:99999,
            span: 12,
          },
          {
            label: "直付触发点",
            prop: "directTriggerPoint",
            value: "1",
            type: "radio",
            dicData: [
              {
                label: "司机卸土",
                value: "0",
              },
              {
                label: "司机出场",
                value: "1",
              },
            ],
            span: 24,
          },
        ],
        span: 12,
      },
      {
        label: "签单方式",
        arrow: false,
        prop: "group2",
        span: 12,
        column: [
          {
            label: "入场签单",
            prop: "isEntranceWaybill",
            order: 1,
          },
          {
            label: "出口签单",
            prop: "isGo",
            order: 3,
            span: 24,
          },
          
          {
            label: "出场根据历史运单搜索车牌(天)",
            prop: "truckCodePeriod",
            labelWidth: 264,
            placeholder: "请输入",
            type: "number",
            controls: false,
            row: true,
            span: 13,
            width: 280,
            order: 5,
            precision: 0,
            minRows: 0,
            hide: true,
            showCloumn: false,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "出场签单间隔时长(分钟)",
            prop: "goInterval",
            labelWidth: 264,
            placeholder: "请输入",
            type: "number",
            controls: false,
            row: true,
            span: 13,
            width: 280,
            order: 5,
            precision: 0,
            minRows: 0,
            hide: true,
            showCloumn: false,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "挖机签单间隔时长(分钟)",
            prop: "inInterval",
            labelWidth: 264,
            placeholder: "请输入",
            type: "number",
            controls: false,
            row: true,
            span: 13,
            width: 280,
            order: 5,
            precision: 0,
            minRows: 0,
            hide: true,
            showCloumn: false,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          // {
          //   label: "强制下班时长(小时)",
          //   prop: "test",
          //   labelWidth: 264,
          //   placeholder: "请输入",
          //   type: "number",
          //   controls: false,
          //   row: true,
          //   span: 13,
          //   width: 280,
          //   order: 5,
          //   precision: 0,
          //   minRows: 0,
          //   hide: true,
          //   showCloumn: false,
          //   rules: [
          //     {
          //       required: true,
          //       message: "请输入",
          //       trigger: "blur",
          //     },
          //   ],
          // },
          {
            label: "出场图片离线上传",
            labelWidth: 180,
            prop: "isGoPictureUploadOffline",
            type: "switch",
            value: "0",
            dicData: [
              {
                label: "否",
                value: "0",
              },
              {
                label: "是",
                value: "1",
              },
            ],
            order: 6,
            span: 24,
          },
          {
            label: "出口签单车型",
            labelWidth: 180,
            prop: "isGoVehicleType",
            type: "switch",
            value: "0",
            dicData: [
              {
                label: "否",
                value: "0",
              },
              {
                label: "是",
                value: "1",
              },
            ],
            order: 6,
            span: 24,
          },
          {
            label: "提示核对信息",
            labelWidth: 180,
            prop: "goWaybillTipCheck",
            type: "switch",
            value: "0",
            dicData: [
              {
                label: "否",
                value: "0",
              },
              {
                label: "是",
                value: "1",
              },
            ],
            order: 6,
            span: 24,
          },
        ],
      },
      {
        label: "挖机设置",
        arrow: false,
        prop: "group6",
        column: [
          {
            label: "挖机图片离线上传",
            prop: "isInPictureUploadOffline",
            value: "0",
            labelWidth: 180,
            type: "switch",
            dicData: [
              {
                label: "否",
                value: "0",
              },
              {
                label: "是",
                value: "1",
              },
            ],
            order: 2,
            span: 24,
          },
          {
            label: "作业类型",
            prop: "manHourType",
            order: 2,
            span: 24,
          },
          {
            label: "挖机签单",
            prop: "isIn",
            order: 1,
            span: 24,
          },
          {
            label: "作业地点",
            prop: "inJobAddress",
            order: 2,
            span: 24,
          },
          {
            label: "工时单位",
            prop: "inUnitWork",
            order: 2,
            span: 24,
          },
          // {
          //   label: "挖机型号模式",
          //   prop: "inModelType",
          //   hide:true,
          //   showCloumn:false,
          //   value: ["1"],
          //   // labelWidth: 180,
          //   type: "checkbox",
          //   dicData: [
          //     {
          //       label: "可输入",
          //       value: "1",
          //     },
          //     {
          //       label: "可选择",
          //       value: "2",
          //     },
          //   ],
          //   rules: [
          //     {
          //       required: true,
          //       message: "请选择",
          //       trigger: "change",
          //     },
          //   ],
          //   order: 3,
          //   span: 24,
          // },
          // {
          //   label: "挖机型号",
          //   prop: "inModel",
          //   order: 2,
          //   span: 24,
          // },
          {
            label: "挖机运单保留时间(分钟)",
            prop: "inWaybillSaveTime",
            labelWidth: 176,
            placeholder: "请输入",
            type: "number",
            controls: false,
            span: 13,
            width: 280,
            order: 5,
            precision: 0,
            minRows: 15,
            maxRows: 99999,
            hide: true,
            showCloumn: false,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "",
            prop: "remark1",
            labelWidth: 0,
            detail:true,
            placeholder: " ",
            className:"red",
            value:"备注：99999分钟代表不限制，最小15分钟",
            span: 11,
            width: 0,
            hide: true,
            showCloumn: false,
          },
        ],
      },
      {
        label: "班次设置",
        arrow: false,
        prop: "group4",
        column: [
          {
            label: "",
            prop: "projectShifts",
            order: 1,
            labelWidth:70,
            span:24
          },
        ],
      },
      {
        label: "泥尾票管理",
        arrow: false,
        prop: "group5",
        column: [
          {
            label: "泥尾票",
            prop: "isTicket",
            order: 1,
          },
        ],
      },
      {
        label: "项目地址",
        arrow: false,
        prop: "group7",
        column: [
          {
            label: "范围(米)",
            prop: "electronicRange",
            order: 1,
          },
          {
            label: "项目地址",
            prop: "projectAddress",
            disabled: true,
            placeholder: "请在地图选择定位",
            sortable: false,
            order: 12,
            rules: [
              {
                required: true,
                message: "请选择项目地址",
                trigger: "change",
              },
            ],
          },
        ],
      },
    ],
  };
};
