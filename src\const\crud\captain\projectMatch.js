export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true, // 显示搜索字段
  dialogClickModal: false,
  viewBtn: true,
  searchSpan: 8,
  searchMenuSpan: 6,
  menuWidth: 160,
  // searchCustom:2,
  // routerName:"fleetMatch",
  column: [
    {
      label: "搜索",
      prop: "keywords",
      searchPlaceholder:"请输入计划编号/车队名称",
      search: true,
      hide:true,
      showColumn:false,
    },
    {
      label: "车型",
      prop: "vehicleTypes",
      search: true,
      type: "select",
      searchMultiple:true,
      dataType:"string",
      dicData: [
        {
          label: "泥头车",
          value: "泥头车",
        },
        {
          label: "小金刚",
          value: "小金刚",
        },
        {
          label: "拖头",
          value: "拖头",
        },
      ],
      hide:true,
      showColumn:false,
    },
    {
      label: "计划编号",
      prop: "planNo",
      sortable: "custom",
      minWidth: 170,
      overHidden: true,
    },
    {
      label: "创建时间",
      prop: "createDatetime",
      sortable: "custom",
      minWidth: 140,
      overHidden: true,
    },
    {
      label: "车队名称",
      prop: "fleetName",
      minWidth: 180,
      overHidden: true,
    },
    {
      label: "运费",
      prop: "freight",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "计划状态",
      prop: "planStatus",
      sortable: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "匹配中",
          value: "1",
        },
        {
          label: "已暂停",
          value: "2",
        },
      ],
      minWidth: 94,
      overHidden: true,
    },
    {
      label: "账期",
      prop: "settleCycle",
      minWidth: 70,
      overHidden: true,
    },
    {
      label: "推荐池",
      prop: "matchPoolCount",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "待我确认",
      prop: "waitingConfirmCount",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "待项目确认",
      prop: "waitingProjectConfirmCount",
      minWidth: 90,
      overHidden: true,
    },
    {
      label: "匹配成功",
      prop: "matchSuccessCount",
      minWidth: 80,
      overHidden: true,
    },
  ],
};
