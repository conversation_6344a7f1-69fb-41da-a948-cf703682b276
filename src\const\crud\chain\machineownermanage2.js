import { isMobileNumber } from "@/util/validate";

export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  index: true,
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  dialogClickModal: false,
  searchLabelWidth: 100,
  excelBtn: false,
  printBtn: false,
  addBtn: true,
  viewBtn: false,
  searchSpan: 8,
  searchMenuSpan: 6,
  labelWidth: 110,
  menu:false,
  defaultSort:{
    prop:'updateDatetime',
    order:'descending'
  },
  column: [
    //     {
    //     label: 'ID',
    //     prop: 'id',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
    //     {
    //     label: '企业认证ID',
    //     prop: 'companyAuthId',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入企业认证ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
    //     {
    //     label: '项目ID',
    //     prop: 'projectInfoId',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入项目ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
    {
      label: "挖机车主",
      prop: "ownerName",
      search: true,
      sortable: true,
      maxlength: 10,
      rules: [
        {
          required: true,
          message: "请输入姓名",
          trigger: "blur",
        },
        {
          max: 50,
          message: "长度在不能超过50个字符",
        },
      ],
      minWidth:96,
      overHidden:true,
    },
    {
      label: "挖机车主手机",
      prop: "ownerMobile",
      search: true,
      maxlength: 11,
      sortable: true,
      rules: [
        {
          required: true,
          message: "请输入手机号码",
          trigger: "blur",
        },
        {
          validator: isMobileNumber,
          trigger: "blur",
        },
      ],
      minWidth:120,
      overHidden:true,
    },

    {
      label: "机械型号",
      prop: "machineCode",
      search: true,
      sortable: true,
      maxlength: 15,
      rules: [
        {
          required: true,
          message: "请输入机械类型型号",
          trigger: "blur",
        },
        {
          max: 15,
          message: "长度在不能超过15个字符",
        },
      ],
      minWidth:96,
      overHidden:true,
    },
    {
      label: "挖机司机",
      prop: "inStaffName",
      search: true,
      display: false,
      sortable: true,
      minWidth:96,
      overHidden:true,
    },
    {
      label: "挖机司机",
      prop: "inStaffId",
      showColumn: false,
      hide: true,
      rules: [
        {
          required: true,
          message: "请选择挖机司机",
          trigger: "change",
        },
      ],
    },
    {
      label: "挖机司机手机号",
      prop: "inMobile",
      minWidth:120,
      overHidden:true,
    },
    //     {
    //     label: '是否删除：1=已删除、0=未删除',
    //     prop: 'isDel',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入是否删除：1=已删除、0=未删除',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 1,
    //                 message: '长度在不能超过1个字符'
    //             },
    //         ]
    // },
    {
      label: "出车状态",
      prop: "status",
      sortable: true,
      value: "0",
      display: false,
      type: "select",
      dicData: [
        {
          label: "未出车",
          value: "0",
        },
        {
          label: "已出车",
          value: "1",
        },
      ],
      minWidth:96,
      overHidden:true,
    },
    {
      label: "新增时间",
      prop: "createDatetime",
      sortable: true,
      display: false,
      width:140,
      overHidden:true,
    },
    //     {
    //     label: '创建ID',
    //     prop: 'createId',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入创建ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
    {
      label: "更新时间",
      prop: "updateDatetime",
      sortable: true,
      display: false,
      width:140,
      overHidden:true,
    },
    //     {
    //     label: '修改ID',
    //     prop: 'updateId',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入修改ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
  ],
};
