import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companymachineowner/queryMachineStatPage',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companymachineowner',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companymachineowner/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companymachineowner/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companymachineowner',
        method: 'put',
        data: obj
    })
}
