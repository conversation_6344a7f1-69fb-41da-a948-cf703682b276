<template>
  <div class="totalWaybill">
    <el-drawer size="90%" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <el-tabs v-model="activeName" @tab-click="changTab">
          <el-tab-pane label="土质类型" name="1">
            <soil :info="info" v-if="isFirst1"></soil>
          </el-tab-pane>
          <el-tab-pane label="结算状态" name="2">
            <settle :info="info" v-if="isFirst2"></settle>
          </el-tab-pane>
          <el-tab-pane label="运输类型" name="3">
            <tpMode :info="info" v-if="isFirst3"></tpMode>
          </el-tab-pane>
          <el-tab-pane label="运费类型" name="4">
            <freight :info="info" v-if="isFirst4"></freight>
          </el-tab-pane>
          <el-tab-pane label="异常运单" name="5">
            <abnormalWaybill :info="info" v-if="isFirst5"></abnormalWaybill>
          </el-tab-pane>
        </el-tabs>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import soil from "./soil";
import settle from "./settle";
import tpMode from "./tpMode";
import freight from "./freight";
import abnormalWaybill from "./abnormalWaybill";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  components: {
    soil,
    settle,
    tpMode,
    freight,
    abnormalWaybill,
  },
  data () {
    return {
      activeName:"1",
      isFirst1:false,
      isFirst2:false,
      isFirst3:false,
      isFirst4:false,
      isFirst5:false,
    }
  },
  created () {
    this.activeName = this.info.waybillAnalyzeType
    console.log(this.activeName);
    this.changTab()
  },
  mounted () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    changTab(){
      if(this.activeName=='1'&&!this.isFirst1){
        this.isFirst1 = true
      }
      if(this.activeName=='2'&&!this.isFirst2){
        this.isFirst2 = true
      }
      if(this.activeName=='3'&&!this.isFirst3){
        this.isFirst3 = true
      }
      if(this.activeName=='4'&&!this.isFirst4){
        this.isFirst4 = true
      }
      if(this.activeName=='5'&&!this.isFirst5){
        this.isFirst5 = true
      }
    }
  },

};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
</style>
