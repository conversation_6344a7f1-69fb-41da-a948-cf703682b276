<template>
  <div class="selectPersonnel">
    <el-dialog
      :visible.sync="visible"
      title="选择人员"
      :close-on-click-modal="false"
      width="700px"
      :before-close="oncancel"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <span>选择：</span>
        </el-col>
        <el-col :span="12">
          <span>已选：</span>
        </el-col>
      </el-row>
      <div class="userSearch" >
        <el-row :gutter="16" style="height: 100%">
          <el-col :span="12" style="height: 100%">
            <div class="left" v-loading.lock="spinning" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading">
              <el-input
                placeholder="搜索"
                prefix-icon="el-icon-search"
                v-model="searchValue"
                @change="onSearch"
                style="margin-bottom: 10px"
              >
              </el-input>
              <div>
                <span class="blue" @click="clickCrumb(false)">通讯录</span>
                <span v-if="crumbList && crumbList.length > 0">
                  <span v-for="(item, index) in crumbList" :key="item.id">
                    <span :class="index !== crumbList.length - 1 ? 'blue' : ''">
                      >
                    </span>
                    <span
                      :class="index !== crumbList.length - 1 ? 'blue' : ''"
                      @click="clickCrumb(item, index)"
                      >{{ item.deptName }}</span
                    >
                  </span>
                </span>
              </div>
              <el-checkbox
                v-model="checkAll"
                @change="onCheckAllChange"
                style="margin-top: 10px; margin-bottom: 6px"
                v-show="userList.length>0"
              >
                全选
              </el-checkbox>
              <el-row
                v-for="(item, index) in organizationList"
                :key="item.id"
                style="padding-left: 20px;display:flex;align-items: center;"
                class="myRow"
                @click.native="clickOrganization(item, index)"
              >
                <el-col :span="19" style="line-height:30px">
                  <i class="el-icon-user"></i>
                  {{ item.deptName }}
                </el-col>
                <el-col
                  :span="5"
                  v-if="item.children && item.children.length > 0"
                  style="color: #409eff"
                >
                  <i class="el-icon-sort-down" />
                  下级
                </el-col>
              </el-row>
              <el-row
                v-for="item in userList"
                :key="item.id"
                style="margin-top: 6px"
              >
                <el-col :span="24">
                  <el-checkbox
                    v-model="item.checked"
                    @change="changeUser($event, item)"
                    >{{ item.name+'('+item.positionName+')' }}</el-checkbox
                  >
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col :span="12" style="height: 100%">
            <div class="right">
              <el-row v-for="item in checkUserList" :key="item.value">
                <el-col :span="24">
                  <div class="listItem">
                    {{ item.name }}
                    <i
                      @click="changeCheckUser(item)"
                      style="
                        float: right;
                        font-size: 16px;
                        cursor: pointer;
                        color: #409eff;
                      "
                      class="el-icon-close"
                    >
                    </i>
                  </div>
                  <!-- <el-checkbox
                    :checked="item.checked"
                    @change="changeCheckUser($event, item)"
                    ></el-checkbox
                  > -->
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="oncancel">取 消</el-button>
        <el-button type="primary" @click="handleUser">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { officeList,staffList} from "@/services/communication";
import { officeList, staffList } from "@/api/chain/projectinfo";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    checkList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      checkAll: false, //选择人员的全选
      crumbList: [], //面包屑list
      organizationList: [], //组织架构
      organList: [],
      usList: [],
      userList: [],
      checkUserList: [
        // {
        //   id: "3",
        //   name: "张三",
        //   checked: true,
        // },
      ], //已选人员列表
      spinning: false,
      searchValue: "",
    };
  },
  async mounted() {
    this.officeList();
    //获取部门职员列表
    await this.staffList();
    this.checkUserList = JSON.parse(JSON.stringify(this.checkList));
    console.log(this.checkUserList);
    let num = 0;
    this.userList.forEach((item2) => {
      this.checkUserList.forEach((item3) => {
        if (item3.id == item2.id) {
          //  item2.checked = true
          this.$set(item2, "checked", true);
          num++;
        }
      });
    });
    this.checkAll = this.userList.length > 0 && this.userList.length == num;
  },
  methods: {
    officeList() {
      //获取公司部门列表
      officeList().then((res) => {
        console.log(res);
        let data = res.data.data;
        this.organizationList = data;
        this.organList = data;
        console.log(this.organizationList);
      });
    },
    //选择人员全选按钮
    onCheckAllChange(e) {
      console.log(e);
      if (e) {
        //点击选中
        this.checkAll = true;
        this.userList.forEach((item) => {
          this.$set(item, "checked", true);
          console.log(item);
          let flag = this.checkUserList.some((item2) => {
            return item2.id == item.id;
          });
          if (!flag) {
            this.checkUserList.push(item);
          }
        });
      } else {
        this.checkAll = false;
        this.userList.forEach((item) => {
          this.$set(item, "checked", false);
          this.checkUserList.forEach((item2, index) => {
            if (item2.id == item.id) {
              this.checkUserList.splice(index, index + 1);
            }
          });
        });
      }
    },
    //选择人员单选按钮
    changeUser(e, item) {
      console.log(e);
      item.checked = e;
      if (e) {
        //点击选中
        this.$set(item, "checked", true);
        let flag = this.checkUserList.some((item2) => {
          return item2.id == item.id;
        });
        if (!flag) {
          this.checkUserList.push(item);
        }
      } else {
        this.$set(item, "checked", false);
        this.checkUserList.forEach((item2, index) => {
          if (item2.id == item.id) {
            this.checkUserList.splice(index, 1);
          }
        });
      }
      let num = 0;
      this.userList.forEach((item) => {
        if (item.checked) num++;
      });
      this.checkAll = this.userList.length > 0 && this.userList.length == num;
    },
    //已选人员删除
    changeCheckUser(item) {
      this.checkUserList.forEach((item2, index) => {
        if (item2.id == item.id) {
          this.checkUserList.splice(index, 1);
        }
      });
      let num = 0;
      this.userList.forEach((item3) => {
        if (item3.id == item.id) {
          item3.checked = false;
        }
        if (item3.checked) num++;
      });
      this.checkAll = this.userList.length == num;
    },
    //选择人员弹框搜索
    onSearch(e) {
      if (this.searchValue) {
        this.userList = this.userList.filter((item) => {
          return item.name.indexOf(this.searchValue) != -1;
        });
      } else {
        this.userList = this.usList;
      }
    },
    //选择人员弹框点击面包屑
    async clickCrumb(item, index) {
      if (item === false) {
        //点击的通讯录
        this.crumbList = [];
        this.organizationList = this.organList;
        //获取部门职员列表
        let params = {};
        await this.staffList(params);
      } else {
        this.crumbList = this.crumbList.splice(0, index + 1);
        this.organizationList = item.children;
        //获取部门职员列表
        let params = {
          companyDeptId: item.id,
        };
        await this.staffList(params);
      }
      let num = 0;
      this.userList.forEach((item2) => {
        this.checkUserList.forEach((item3) => {
          if (item3.id == item2.id) {
            //  item2.checked = true
            this.$set(item2, "checked", true);
            num++;
          }
        });
      });
      this.checkAll = this.userList.length > 0 && this.userList.length == num;
    },
    //选择人员弹框点击组织架构
    async clickOrganization(item, index) {
      this.crumbList.push(JSON.parse(JSON.stringify(item)));
      if (!item.children) {
        this.organizationList = [];
      } else {
        this.organizationList = item.children;
      }
      //获取部门职员列表
      let params = {
        companyDeptId: item.id,
      };
      await this.staffList(params);
      let num = 0;
      this.userList.forEach((item2) => {
        this.checkUserList.forEach((item3) => {
          if (item3.id == item2.id) {
            //  item2.checked = true
            this.$set(item2, "checked", true);
            num++;
          }
        });
      });
      this.checkAll = this.userList.length > 0 && this.userList.length == num;
    },
    // 获取人员列表
    async staffList(params = {}) {
      this.spinning = true;
      params.size = 9999999;
      await staffList(params)
        .then((res) => {
          this.spinning = false;
          let data = res.data;
          this.userList = data.data.records;
          this.usList = data.data.records;
          console.log(this.userList);
        })
        .catch((err) => {
          this.spinning = false;
          this.$message.error("获取人员失败");
        });
    },
    //选择人员弹框确定
    handleUser() {
      if (this.checkUserList && this.checkUserList.length > 0) {
        let str = JSON.stringify(this.checkUserList);
        this.$emit("changeUserList", str);
      }
      this.$emit("update:visible", false);
    },
    oncancel() {
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style scoped lang="less">
.userSearch {
  border: 1px solid #d9d9d9;
  margin-top: 12px;
  height: 440px;
  .left,
  .right {
    background-color: #f2f5f7;
    padding: 10px;
    overflow: auto;
    height: 100%;
    &.right {
      padding: 10px 0px;
    }
  }
  .right {
    .listItem {
      color: #545656;
      line-height: 36px;
      font-size: 16px;
      padding: 0px 10px;
      &:hover {
        background-color: #e7eaef;
      }
      i {
        padding-top: 10px;
        padding-right: 10px;
      }
    }
  }
  .myRow {
    cursor: pointer;
    &:hover {
      background-color: #e7eaef;
    }
    .ant-col-4 {
      color: #3e9cfa;
    }
  }
  .blue {
    color: #3e9cfa;
    cursor: pointer;
  }
}
</style>
