export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: false,
  excelBtn: true,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  delBtn: false,
  searchMenuSpan: 6,
  labelWidth: 120,
  menu:false,
  column: [
    {
      label: "是否合格",
      prop: "contractTime2",
      hide:true,
    },
    {
      label: "运单编号",
      prop: "no",
      sortable: true,
    },
    {
      label: "施工企业",
      prop: "companyAuthId",
      type: "select", // 下拉选择
      search: true,
      props: {
        label: "companyName",
        value: "id",
      },
      dicUrl: "/chain/companyauth/list",
    },
    {
      label: "所属项目",
      prop: "projectInfoId",
      type: "select", // 下拉选择
      search: true,
      props: {
        label: "projectName",
        value: "id",
      },
      dicUrl: "/chain/projectinfo/list",
      filterable: true, //是否可以搜索
    },
    {
      label: "车牌号",
      prop: "goTruckCode",
      // search: true,
    },
    {
      label: "大车队长",
      prop: "driverFleetName",
      // search: true,
    },
    {
      label: "大队长手机号",
      prop: "captainMobile",
    },
    {
      label: "绑定车辆",
      prop: "truckCode",
    },
    {
      label: "小车队长",
      prop: "driverFleetName2",
      // search: true,
    },
    {
      label: "结算金额",
      prop: "contractId",
    },

    {
      label: "自有车辆",
      prop: "contractType",
      search: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "否",
          value: "0",
        },
        {
          label: "是",
          value: "1",
        },
      ],
    },
    {
      label: "结算状态",
      prop: "status",
      sortable: true,
      type: "select", // 下拉选择
      search: true,
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=settle_status',
    },
    {
      label: "支付状态",
      prop: "status",
      search:true,
      type:'select',
      dicData: [
        {
          label: '待支付',
          value: '1'
        },
        {
          label: '已审批',
          value: '2'
        },
        {
          label: '已驳回',
          value: '3'
        },
        {
          label: '已支付',
          value: '4'
        },
      ],
    },
    {
      label: "签约分公司",
      prop: "agreementUrl",
    },
    {
      label: "项目合同配置",
      prop: "test",
    },
    {
      label: "合同信息",
      prop: "test1",
    },
    {
      label: "司机证照",
      prop: "test2",
    },
    {
      label: "车辆证照",
      prop: "test3",
    },
    {
      label: "GPS轨迹",
      prop: "test4",
    },
    {
      label: "支付信息",
      prop: "test5",
    },
  ],
};
