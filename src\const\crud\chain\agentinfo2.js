﻿import {isMobileNumber} from '@/util/validate'

export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  defaultSort:{
    prop:'createDatetime',
    order:'descending'
  },
  viewBtn: false,
  searchMenuSpan: 6,
  routerName:"agentinfo2",
  column: [
    {
      label: "单位名称",
      prop: "agentName",
      sortable: true,
      search:true,
      maxlength:10,
      labelWidth: "30%",
      rules: [
        {
          required: true,
          message: "请输入单位名称",
          trigger: "blur",
        },
        {
          max: 10,
          message: "长度在不能超过10个字符",
        },
      ],
      minWidth:120,
      overHidden:true,
    },

    {
      label: "联系人",
      prop: "linkman",
      sortable: true,
      labelWidth: "30%",
      rules: [
        {
          required: true,
          message: "请输入联系人",
          trigger: "blur",
        },
        {
          max: 5,
          message: "长度在不能超过5个字符",
        },
      ],
      minWidth:84,
      overHidden:true,
    },
    {
      label: "联系方式",
      prop: "contact",
      search:true,
      maxlength: 11,
      number: true,
      labelWidth: "30%",
      sortable: true,
      rules: [
        {
          required: true,
          message: "请输入联系方式",
          trigger: "blur",
        },

        {
          validator: isMobileNumber,
          trigger: "blur",
        },
      ],
      minWidth:100,
      overHidden:true,
    },

    {
      label: "添加时间",
      prop: "createDatetime",
      labelWidth: "30%",
      addDisplay: false,
      editDisplay: false,
      sortable: true,
      rules: [
        {
          required: true,
          message: "请输入创建日期时间",
          trigger: "blur",
        },
      ],
      minWidth:170,
      overHidden:true,
    },
  ],
};
