<template>
  <div class="execution">
    <basic-container>
      <el-container v-if="permissions['chain:exportExc:list']">
        <el-aside width="180px">
          <el-tabs tab-position="left" v-model="activeName">
            <el-tab-pane
              :label="item.label"
              v-for="item in tabs"
              :key="item.id"
              :name="item.value"
            >
            </el-tab-pane>
          </el-tabs>
        </el-aside>
        <el-main>
          <avue-form
            :option="option"
            v-model="form"
            @submit="submit"
            @empty="empty"
          >
            <template slot="menuForm">
              <el-button icon="el-icon-user" v-if="permissions['chain:exportExc:print']" type="primary" @click="print"
                >打 印</el-button
              >
              <el-button icon="el-icon-download" v-if="permissions['chain:exportExc:excel']" @click="exOut" :loading="btnLoading">下 载</el-button>
            </template>
          </avue-form>
          <component v-bind:is="activeName" ref="myComponents"></component>
        </el-main>
      </el-container>
    </basic-container>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";
import { queryByCode } from "@/api/chain/companywaybill.js";
import { tableOption1 } from "@/const/crud/chain/exportExc.js";
import truckVisit from "./components/truckVisit.vue";

export default {
  name: "exportExc",
  components: {
    truckVisit, //车辆进出台账
    truckSoil: () => import("./components/truckSoil.vue"), //挖机装车台账
    garbageExcel: () => import("./components/garbageExcel.vue"), //泥尾台账
    garbageCountExcel: () => import("./components/garbageCountExcel.vue"), //泥尾统计运费
    landStampCountExcel: () => import("./components/landStampCountExcel.vue"), //土票领发统计
    mechanicalLoadingCountExcel: () => import("./components/mechanicalLoadingCountExcel.vue"), //机械台班装车数统计
    excavatorCostCountExcel: () => import("./components/excavatorCostCountExcel.vue"), //挖机装车台班费用统计
    machineOwnerAndLegerExcel: () => import("./components/machineOwnerAndLegerExcel.vue"), //挖机装车和台班
  },
  data() {
    return {
      tabs: [
        {
          label: "车辆进出台账",
          value: "truckVisit",
          id: 1,
        },
        {
          label: "挖机装车台账",
          value: "truckSoil",
          id: 2,
        },
        {
          label: "泥尾台账",
          value: "garbageExcel",
          id: 3,
        },
        {
          label: "泥尾统计运费",
          value: "garbageCountExcel",
          id: 4,
        },
        {
          label: "土票领发统计",
          value: "landStampCountExcel",
          id: 5,
        },
        {
          label: "机械台班装车数统计",
          value: "mechanicalLoadingCountExcel",
          id: 6,
        },
        {
          label: "挖机装车台班费用统计",
          value: "excavatorCostCountExcel",
          id: 7,
        },
        {
          label: "挖机装车和台班",
          value: "machineOwnerAndLegerExcel",
          id: 8,
        },
      ],
      form: {},
      option: {
        labelWidth: 100,
        border: true,
        align: "center",
        menuAlign: "center",
        submitText: "查询",
        emptyText: "重置",
        column: [
          {
            label: "项目名称",
            prop: "projectInfoId",
            type: "select",
            props: {
              label: "projectName",
              value: "id",
            },
            dicUrl: "/chain/projectinfo/list",
            filterable: true, //是否可以搜索
          },
          {
            label: "签单时间",
            prop: "goDatetime",
            type: "datetimerange",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
        ],
      },
      activeName: "truckVisit",
      tableLoading: false,
      tableOption: tableOption1,
      btnLoading:false,
    };
  },
  created() {},
  mounted() {},
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        // addBtn: this.permissions['chain:companywaybill:add'] ? true : false,
        // delBtn: this.permissions['chain:companywaybill:del'] ? true : false,
        // editBtn: this.permissions['chain:companywaybill:edit'] ? true : false,
        viewBtn: this.permissions["chain:companywaybill:get"] ? true : false,
      };
    },
  },
  methods: {
    expotOut,
    submit(form, done) {
      if (form.projectInfoId == "") {
        this.$message.error("请选择项目名称");
        done();
        return false;
      }
      // this.queryByCode(this.form);
      this.$refs.myComponents.queryByCode(this.form);
      console.log(this.$refs.myComponents);
      setTimeout(() => {
        done();
      }, 3000);
    },
    empty() {
      console.log("qingkong");
    },
    exOut() {
      if (this.form.projectInfoId == "") {
        this.$message.error("请选择项目名称");
        return false;
      }
      this.btnLoading = true
      let params = Object.assign({}, this.form);
      if (params.goDatetime && params.goDatetime.length > 0) {
        params.goDatetimeStart = params.goDatetime[0];
        params.goDatetimeEnd = params.goDatetime[1];
      }
      delete params.goDatetime;
      let url = "/chain/excelExport/createByCode";
      let name = "车辆进出台账";
      params.code = this.activeName;
      switch (this.activeName) {
        case "truckSoil":
          url = "/chain/excelExport/createByCode";
          name = "挖机装车台账";
          break;
        case "garbageExcel":
          url = "/chain/excelExport/createByCode";
          name = "泥尾台账";
          break;
        case "garbageCountExcel":
          url = "/chain/excelExport/createByCode";
          name = "泥尾统计运费";
          break;
        case "landStampCountExcel":
          url = "/chain/excelExport/createByCode";
          name = "土票领发统计";
          break;
        case "mechanicalLoadingCountExcel":
          url = "/chain/excelExport/createByCode";
          name = "机械台班装车数统计";
          break;
        case "excavatorCostCountExcel":
          url = "/chain/excelExport/createByCode";
          name = "挖机装车台班费用统计";
          break;
        case "machineOwnerAndLegerExcel":
          url = "/chain/excelExport/createByCode";
          name = "挖机装车和台班";
          break;
      }

      this.expotOut(params, url, name).then((res) => {
          this.btnLoading = false;
        })
        .catch((err) => {
          this.btnLoading = false;
        });
    },
    print() {
      this.$refs.myComponents.print();
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-table{
  border-bottom: 1px solid #9a9a9a;
  border-right: 1px solid #9a9a9a;
  tr{
    .el-table__cell {
      border-left: 1px solid #9a9a9a;
      border-top: 1px solid #9a9a9a;
    }
      border-right: 1px solid #9a9a9a;

  }
  .el-table__row{
    td{
      padding: 4px 0px;
    }
  }
  .el-table__header{
    th{
      padding: 4px 0px;
    }
  }
  .el-table__empty-block{
    border-top: 1px solid #9a9a9a;
    border-left: 1px solid #9a9a9a;
  }
}
/deep/ .el-card__body{
  padding:20px 0;
}
</style>
