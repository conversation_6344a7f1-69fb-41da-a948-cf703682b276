<template>
  <div class="Echarts" id="echarts">
    <div class="subTitle">
      <div style="font-weight: bold">
        计划完成度
        <el-tooltip class="item" effect="dark" placement="top-start"
          ><i class="el-icon-question"></i>
          <template #content>
            单位为非车方吨{{ titleData.unitNotCorrectCount }}单不参与统计 <br />
            单位为吨土质不设置单位换算{{
              titleData.soilTypeNotCorrectCount
            }}单不参与统计<br />
            内运，内转回填运单{{
              titleData.tpModeInnerNotCorrectCount
            }}单不参与统计
          </template>
        </el-tooltip>
      </div>
      计划出土方量：{{ planCompletes.projectPhaseSquareCost }}方 <br />
      勘测出土方量：{{ planCompletes.projectPhaseRealSquareCost }}方<br />
      运单出土方量：{{ planCompletes.waybillPhaseSquareCost }}方<br />
      外运运输成本：{{ planCompletes.waybillPhaseTransportCost }}元<br />
      内运运输成本：{{ planCompletes.companyWaybillInternalCost }}元<br />
      运单平均单位成本：{{
        planCompletes.waybillPhaseAvgTransportCost
      }}元/方<br />
      预期运输成本：{{ planCompletes.projectPhaseTransportCost }}元<br />
      预期单位成本：{{ planCompletes.projectPhaseSquarePrice }}元/方
    </div>

    <div ref="soilChart" class="echart"></div>
  </div>
</template>
  
  <script>
import {
  getPlanAchievementStatistic,
  getTruckTpModeTitle,
} from "@/api/chain/analysis.js";
const colors = ["#5470C6", "#91CC75", "#EE6666"];
export default {
  name: "soilChart",
  data() {
    return {
      planCompletes: {},
      chart: null,
      id: "",
      titleData: {
        truckTotalCount: 0, // 共出车N单
        truckCorrectCount: 0, // 运输方式-N单参与统计
        squareCorrectCount: 0, // 出土情况-N单参与统计
        unitNotCorrectCount: 0, // 单位为非车方吨N单不参与统计
        soilTypeNotCorrectCount: 0, // 单位为吨土质不设置单位换算N单不参与统计
        tpModeInnerNotCorrectCount: 0, // 回填，内转回填运单N单不参与统计
        tpModeResourceNotCorrectCount: 0, // 资源运单N单不参与统计
        priceNotCorrectCount: 0, // 未设置装车价/泥尾价/核算价/预设价N单不参与统计
        ledgerDigPriceNotCorrectCount: 0, // 未设置台班费N项不参与统计
      },
    };
  },
  activated() {
    var myEvent = new Event("resize");
    window.dispatchEvent(myEvent);
  },
  methods: {
    myEcharts(data) {
      // 基于准备好的dom，初始化echarts实例
      this.chart = this.$echarts.init(this.$refs.soilChart);
      let option = {
        title: {
          text: "日均出土趋势图",
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            let str = "<span>" + params[0].name + "</span><br/>";
            for (let i = 0; i < params.length; i++) {
              str +=
                params[i].marker +
                params[i].seriesName +
                '：<span style="float:right;font-weight:600">' +
                params[i].value +
                `方</span><br/>`;
            }
            return str;
          },
        },
        // legend: {
        //   data: data.legendList,
        // },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: data.xAxis,
          axisLabel: {
            showMaxLabel: true,
          },
        },
        yAxis: {
          type: "value",
        },
        series: [],
      };

      option.series = data.series.map((v) => {
        v.type = "line";
        return v;
      });

      option.legend = {
        data: data.series.map((v) => v.name),
      };
      // for (const key in data.yAxisData) {
      //   option.series.push({
      //     name: key,
      //     type: "line",
      //     data: data.yAxisData[key],
      //   });
      // }

      // 使用刚指定的配置项和数据显示图表。
      this.chart.setOption(option);
    },
    resizeHanlder() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  },
  mounted() {
    this.id = this.$route.query.id;
    getTruckTpModeTitle({
      projectInfoBiPhaseId: this.id,
    })
      .then((res) => {
        this.titleData = res.data.data;
      })
      .catch(() => {});
    getPlanAchievementStatistic(this.id).then((res) => {
      this.planCompletes = res.data.data;
      this.myEcharts(res.data.data);
      window.addEventListener("resize", this.resizeHanlder);
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    window.removeEventListener("resize", this.resizeHanlder);
    this.chart.dispose();
    this.chart = null;
  },
  watch: {},
};
</script>
  
  <style lang="scss" scoped>
.Echarts {
  display: flex;
  border-radius: 8px;
  margin: 0 15px;
  width: auto;
  background-color: #fff;
  //   overflow: hidden;
  height: 85vh;
  //   display: flex;
  //   flex-direction: column;
  //   justify-content: flex-start;
  .echart {
    padding: 15px 30px;
    width: 100%;
    height: 95%;
  }
}
.subTitle {
  padding: 0 15px;
  padding-top: 15px;
  line-height: 30px;
  white-space: nowrap;
  color: #464646;
  font-size: 16px;
  .link {
    margin-left: auto;
  }
}
</style>
  