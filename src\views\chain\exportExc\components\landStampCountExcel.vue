<template>
    <div ref="print">
      <avue-crud
        ref="crud"
        :data="tableData"
        :table-loading="tableLoading"
        :option="tableOption"
      >
      <template slot="index" slot-scope="{ row }">
        <span v-if="row.index==''||row.index">{{row.index }}</span>
        <span v-else>{{ row.$index + 1 }}</span>
      </template>
      </avue-crud>
    </div>
  </template>

  <script>
  import { tableOption5 } from "@/const/crud/chain/exportExc";
  import { mapGetters } from "vuex";
  import { queryByCode } from "@/api/chain/companywaybill.js";
  import { print } from "@/util/util.js";

  export default {
    name: "landStampCountExcel",
    data() {
      return {
        form: {},
        paramsSearch: {},
        tableLoading: false,
        tableOption: tableOption5,
        tableData:[],
      };
    },
    created() {},
    mounted: function () {
    },
    computed: {
      ...mapGetters(["permissions"]),

    },
    methods: {
      queryByCode(form) {
        tableOption5.column[0].children[3].label = '-'
        let params = Object.assign({}, form);
        if (params.goDatetime && params.goDatetime.length > 0) {
          params.goDatetimeStart = params.goDatetime[0];
          params.goDatetimeEnd = params.goDatetime[1];
          tableOption5.column[0].children[3].label = params.goDatetimeStart+' — '+params.goDatetimeEnd
        }
        delete params.goDatetime;
        console.log(form);
        params.code = "landStampCountExcel";
        tableOption5.column[0].children[1].label = form.$projectInfoId
        this.tableOption = JSON.parse(JSON.stringify(tableOption5));
        this.$refs.crud.doLayout();
        this.$refs.crud.refreshTable();
        this.tableLoading = true;
        queryByCode(params)
          .then((res) => {
            this.tableLoading = false;
            this.tableData = res.data.data;
            if(this.tableData&&this.tableData.length>0){
                this.tableData.push({
                    index:'签字:',
                    dayReceive:'剩余土票余交人签名:',
                })
            }
          })
          .catch(() => {
            this.tableLoading = false;
          });
      },
      print(){
        console.log(this.$refs.print);
        print(this.$refs.print)
      },
    },
  };
  </script>

  <style lang="scss" scoped>
  /deep/ .closeIcon,
  .checkIcon {
    font-size: 16px;
    color: red;
  }
  /deep/ .checkIcon {
    color: #3dcc90;
  }
  /deep/ .avue-crud__pagination{
    display: none;
  }
  </style>
