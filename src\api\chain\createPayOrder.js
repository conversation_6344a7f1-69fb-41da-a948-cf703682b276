import request from '@/router/axios'
//待生成支付单列表
export function getPage(query) {
    return request({
        url: '/chain/companysettle/getSettleList',
        method: 'get',
        params: query
    })
}
//已生成支付单列表
export function getPages(query) {
    return request({
        url: '/chain/companypayment/getPaymentList',
        method: 'get',
        params: query
    })
}

//生成支付单
export function genPayment(obj) {
    return request({
        url: '/chain/companypayment/genPayment',
        method: 'post',
        data: obj
    })
}
//支付单详情
export function paymentInfo(id) {
    return request({
        url: '/chain/companypayment/paymentInfo/'+id,
        method: 'get',
    })
}
//支付单审批提交
export function flowDeal(obj) {
    return request({
        url: '/chain/companypayment/flowDeal',
        method: 'post',
        data: obj
    })
}
//支付单取消
export function cannelPayment(obj) {
    return request({
        url: '/chain/companypayment/cannel',
        method: 'post',
        data: obj
    })
}
//支付单查看结算单详情
export function getAllSettle(id) {
    return request({
        url: '/chain/companypayment/printSettleByPaymentId/'+id,
        method: 'get',
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companysettle',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companysettle/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companysettle/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companysettle',
        method: 'put',
        data: obj
    })
}

