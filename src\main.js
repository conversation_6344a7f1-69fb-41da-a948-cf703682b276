import Vue from "vue";
import axios from "./router/axios";
import VueAxios from "vue-axios";
import App from "./App";
import router from "./router/router";
import store from "./store";
import Element from "element-ui";
import Avue from "@smallwei/avue";
import "@smallwei/avue/lib/index.css";
import "babel-polyfill";
import "classlist-polyfill";
import "./permission"; // 权限
import "./error"; // 日志
import "element-ui/lib/theme-chalk/index.css";
import { loadStyle, filterForm,vaildData,setPx,throttle,throttle1,debounce } from "./util/util";
import * as urls from "@/config/env";
import { iconfontUrl, iconfontVersion } from "@/config/env";
import * as filters from "./filters"; // 全局filter
import "./styles/common.scss";
import "./styles/loading.css";
import basicContainer from "./components/basic-container/main";
import dialogDrag from './util/dialog-drag';
Vue.directive('dialogDrag',dialogDrag)
// 插件 json 展示
// import vueJsonTreeView from "vue-json-tree-view";
// import VueQr from "vue-qr";
// import VideoPlayer from "vue-video-player";
import './icons' // icon
//时间插件
import moment from "moment"; //导入文件
import * as echarts from 'echarts';
Vue.prototype.$echarts = echarts //引入组件
//引入 Froala Editor js file.
// require("froala-editor/js/froala_editor.pkgd.min.js");
//引入中文语言包
// require("froala-editor/js/languages/zh_cn");
//引入 Froala Editor css files.
// require("froala-editor/css/froala_editor.pkgd.min.css");
// require("froala-editor/css/froala_style.min.css");
// require("font-awesome/css/font-awesome.css");
// Import and use Vue Froala lib.
// import VueFroala from "vue-froala-wysiwyg";

import animate from "animate.css";
import myxss from './util/xss';
Vue.prototype.$xss = (val) => {
    return myxss.process(val);
}
Vue.use(Element,{
  size: 'medium', // set element-ui default size
});
Vue.use(router);
Vue.use(VueAxios, axios);
Vue.use(animate);
// Vue.use(VueQr);
// Vue.use(VideoPlayer);
Vue.prototype.$moment = moment; //赋值使用
// Vue.use(vueJsonTreeView);
// 注册全局容器
Vue.component("basicContainer", basicContainer);
//引入my-crud
import myCards from '@/components/table/components/cards';
Vue.component('myCards',myCards);
//引入my-crud
import myCrud from '@/components/my-crud/index'
Vue.component('myCrud',myCrud);
//二次封装avue-crud
import VTable from '@/components/table/index'
Vue.component('VTable',VTable);
import { UTable, UTableColumn } from 'umy-ui';
import 'umy-ui/lib/theme-chalk/index.css';// 引入样式
Vue.component(UTable.name, UTable);
Vue.component(UTableColumn.name, UTableColumn);
// 加载相关url地址
Object.keys(urls).forEach((key) => {
  Vue.prototype[key] = urls[key];
});
//空值过滤器
Vue.prototype.filterForm = filterForm;
Vue.prototype.vaildData = vaildData;
Vue.prototype.setPx = setPx;
Vue.prototype.debounce = debounce;
Vue.prototype.throttle = throttle;
Vue.prototype.throttle1 = throttle1;
Vue.prototype.filterTpMode = filters.filterTpMode;

//加载过滤器
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});
// 动态加载阿里云字体库
iconfontVersion.forEach((ele) => {
  loadStyle(iconfontUrl.replace("$key", ele));
});

import VueAMap from "vue-amap";
// key: "c96380818f7646901533fac4132aea18",
Vue.use(VueAMap);
VueAMap.initAMapApiLoader({
  key: "24495dcbb0c4bc5afc0a234a4d376aa8",
  plugin: [
    "AMap.Autocomplete",
    "AMap.PlaceSearch",
    "AMap.Scale",
    "AMap.OverView",
    "AMap.ToolBar",
    "AMap.MapType",
    "AMap.PolyEditor",
    "AMap.CircleEditor",
    "AMap.Geolocation",
    "AMap.GraspRoad",
  ],
  v: "1.4.4",
  uiVersion: '1.0.11'
});

Vue.config.productionTip = false;
new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app");
Vue.use(Avue);
Vue.directive('focus', {
  inserted (el, binding, vnode) {
    // 聚焦元素
    el.focus()
  }
})
// Vue.use(VueFroala);
