<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="70%"
      title="批量充值"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <el-descriptions>
          <el-descriptions-item label="转入银行">{{
            info.header.bankName
          }}</el-descriptions-item>
          <el-descriptions-item label="转入户名">{{
            info.header.accountName
          }}</el-descriptions-item>
          <el-descriptions-item label="转入账户">{{
            info.header.bankAccount
          }}</el-descriptions-item>
        </el-descriptions>
        <el-collapse v-if="tableData && tableData.length > 0" :value="['1']">
          <el-collapse-item name="1">
            <avue-crud
              ref="myCrud"
              class="myCrud"
              :data="tableData"
              :table-loading="tableLoading"
              :option="tableOption"
            >
            </avue-crud>
          </el-collapse-item>
        </el-collapse>
        <avue-form ref="form" v-model="form" :option="option" @submit="submit">
        </avue-form>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
 batchCharge
} from "@/api/chain/companyauthrecharge";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      //支付信息
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {},
  data() {
    return {
      form: {
        checkbox:[1,2],
        remark:"",
        pic:"",
      },
      tableData: [],
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        index: true,
        indexLabel: "序号",
        stripe: false,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        excelBtn: true,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        refreshBtn: false,
        header:false,
        column: [
          {
            label: "支付单号",
            prop: "paymentNo",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "待充值运费",
            prop: "prepaidPrice",
            overHidden: true,
          },
          {
            label: "待充值税费",
            prop: "prepaidTaxFee",
            overHidden: true,
          },
          {
            label: "已充值运费",
            prop: "chargedPrice",
            overHidden: true,
          },
          {
            label: "已支付税费",
            prop: "chargedTaxFee",
            overHidden: true,
          },
          {
            label: "总费用",
            prop: "realPayPrice",
            overHidden: true,
          },
          {
            label: "运费",
            prop: "freight",
            overHidden: true,
          },
          {
            label: "税费",
            prop: "taxFee",
            overHidden: true,
          },
          {
            label: "税点",
            prop: "taxRate",
            formatter: (val) => {
              return val.taxRate && val.taxRate + "%";
            },
            overHidden: true,
          },
        ],
      },
      option: {
        labelWidth: 70,
        submitBtn: true,
        position: "left",
        emptyBtn: false,
        cancelBtn: false,
        labelWidth:140,
        column: [
          {
            label: "选择批量充值费用",
            prop: "checkbox",
            type: "checkbox",
            dicData: [
              {
                label: "总待充值运费：" + this.info.totalPrepaidPrice,
                value: 1,
              },
              {
                label: "总待充值税费：" + this.info.totalPrepaidTaxFee,
                value: 2,
              },
            ],
            span: 24,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 3,
            maxRows: 5,
            span: 24,
          },
          {
            label: "银行凭证",
            prop: "pic",
            type: "upload",
            listType: "picture-card",
            action: "/upms/file/upload?fileType=image&dir=batchRecharge/",
            dataType: 'string',
            propsHttp: {
              url: "link",
            },
            loadText: "附件上传中，请稍等",
            span: 24,
            tip: "只能上传jpg/png文件，且不超过500kb",
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
        ],
      },
    };
  },
  created() {},
  mounted() {
    console.log(this.info);
    this.tableData = this.info.body || [];
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.form = {
        checkbox:[1,2],
        remark:"",
        pic:"",
      },
      this.$emit("update:visible", false);
    },
    submit(form, done) {
      if(this.tableData.length==0){
        this.$message.error("没有可支付运单")
        done()
        return false
      }
      let param = Object.assign({},form)
      let ids = this.tableData.map(item=>item.id)
      param.paymentIdList = ids
      param.checkFreight = form.checkbox.includes(1)
      param.checkTax = form.checkbox.includes(2)
      delete param.checkbox
      delete param.$checkbox
      batchCharge(param).then(res=>{
        done()
        this.$message.success("操作成功")
        this.cancelModal()
        this.$emit("refreshChange")
      }).catch(()=>{
        done()
      })
    },
  },
  destroyed() {},
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
/deep/ .avue-crud__pagination{
  display: none;
}
</style>
