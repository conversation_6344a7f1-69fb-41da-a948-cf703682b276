<template>
  <div class="rechargeLog">
    <el-dialog title="充值记录" width="70%" :visible.sync="visible" :before-close="cancelModal">
      <avue-crud
        ref="crud2"
        :page="page"
        :data="tableData"
        :table-loading="tableLoading"
        :option="tableOption"
        @sort-change="sortChange"
        @on-load="getPage"
        v-model="form"
      >
      </avue-crud>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {getRechargePage} from "@/api/chain/companyauthrecharge";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    platformBranchId:{
      type:String,
      default:()=>{
        return ""
      }
    },
  },
  components: {
  },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        border: true,
        stripe: true,
        columnBtn: false,
        // showHeader:false,
        index: this.showIndex,
        size: "mini",
        selection: false,
        addBtn: false,
        refreshBtn: false,
        menu: false,
        // page:this.showPage,
        align: "center",
        menuAlign: "center",
        // menuType:this.menuType,
        // menuBtnTitle:'自定义名称',
        defaultSort:{
          prop:'paymentDatetime',
          order:'descending'
        },
        header:false,
        column: [
          {
            label: "充值时间",
            prop: "paymentDatetime",
            sortable: 'custom',
            minWidth:140,
            overHidden:true,
          },
          {
            label: "收款方银行名称",
            prop: "nsrmc",
            sortable: 'custom',
            minWidth:140,
            overHidden:true,
          },
          {
            label: "充值金额",
            prop: "moneyTotalAmount",
            minWidth:100,
            overHidden:true,
          },
          {
            label: "充值备注",
            prop: "remark",
            sortable: 'custom',
            minWidth:96,
            overHidden:true,
          },
          {
            label: "上传截图",
            prop: "pic",
            sortable: 'custom',
            type:'upload',
            minWidth:96,
            overHidden:true,
          },
          {
            label: "充值状态",
            prop: "status",
            type: "select",
            sortable: 'custom',
            dicData: [
              {
                label: "确认中",
                value: "1",
              },
              {
                label: "已到账",
                value: "2",
              },
              {
                label: "已拒绝",
                value: "3",
              },
            ],
            minWidth:96,
            overHidden:true,
          },
        ],
      },
    };
  },
  created () {
  },
  mounted () {
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true;
      getRechargePage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: page.descs,
            ascs: page.ascs,
            platformBranchId:this.platformBranchId,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    cancelModal () {
      this.$emit("update:visible", false);
    },

  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body{
  padding-top: 0;
}
</style>
