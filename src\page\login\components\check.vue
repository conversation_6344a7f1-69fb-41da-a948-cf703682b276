<template>
  <div>
    <div class="title">法人信息</div>

    <div class="cards">
      <el-row :gutter="50">
        <el-col :span="JSON.stringify(peopleInfo) !=='{}'?12:24">
          <el-descriptions
            title="企业信息"
            direction="vertical"
            :column="2"
            border
          >
            <el-descriptions-item label="企业名称" :span="1"
              >{{companyInfo.name}}</el-descriptions-item
            >
            <el-descriptions-item label="法定代表人" :span="1"
              >{{companyInfo.legalRepresentative}}</el-descriptions-item
            >
            <el-descriptions-item label="企业类型" :span="4"
              >{{companyInfo.type}}</el-descriptions-item
            >

            <el-descriptions-item label="成立日期" :span="4"
              >{{companyInfo.foundDate}}</el-descriptions-item
            >
          </el-descriptions>
        </el-col>
        <el-col :span="12" v-if="JSON.stringify(peopleInfo) !=='{}'">
          <el-descriptions
            title="法人信息"
            direction="vertical"
            :column="4"
            border
          > 
            <el-descriptions-item label="姓名" :span="2"
              >{{peopleInfo.name}}</el-descriptions-item
            >
            <el-descriptions-item label="性别" :span="1"
              >{{peopleInfo.sex}}</el-descriptions-item
            >
            <el-descriptions-item label="民族" :span="1"
              >{{peopleInfo.ethnicity}}</el-descriptions-item
            >

            <el-descriptions-item label="身份证号" :span="4"
              >{{peopleInfo.number}}</el-descriptions-item
            >

            <el-descriptions-item label="有效期限"
              >{{peopleInfo.validFrom}}至{{peopleInfo.validTo}}</el-descriptions-item
            >
          </el-descriptions>
        </el-col>
      </el-row>
    </div>
    <div class="btns">
      <el-row :gutter="50">
        <el-col :span="12">
          <el-button type="primary" style="width: 100%" @click="$emit('reWrite')"
            >信息有误，重新填写</el-button
          >
        </el-col>
        <el-col :span="12">
          <el-button type="primary" style="width: 100%" @click="$emit('check')"
            >信息无误，提交审核</el-button
          >
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    companyInfo: {
      type: Object,
      default: function () {
        return {};
      },
    },
    peopleInfo: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  methods: {
 
  },
};
</script>

<style lang="scss" scoped>
.title {
  margin: 20px 0;
  font-size: 20px;
  font-weight: 400;
  span {
    color: #ff531a;
    margin-left: 10px;
  }
}

.cards {
  padding: 0 120px;
}

.btns {
  margin-top: 50px;
  display: flex;
  justify-content: center;
}
</style>