import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/deliveryItem/acceptDetailList',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/delivery/add',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companywaybilldelivery/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companywaybilldelivery/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companywaybilldelivery',
        method: 'put',
        data: obj
    })
}
