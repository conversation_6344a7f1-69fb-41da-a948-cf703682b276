import request from "@/router/axios";

export function batchAddWaybillCardSettle(obj) {
  return request({
    url: "/chain/companysettle/V2.12/batchAddWaybillCardSettle",
    method: "post",
    data: obj,
  });
}

export function getCardReceipt(obj) {
  return request({
    url: "/chain/companysettle/getCardReceipt",
    method: "post",
    data: obj,
  });
}

export function generateCardReceipt(obj) {
  return request({
    url: "/chain/companysettle/generateCardReceipt",
    method: "post",
    data: obj,
  });
}

export function getCardHistoryReceipt(query) {
  return request({
    url: "/chain/companysettle/getCardHistoryReceipt",
    method: "get",
    params: query,
  });
}

export function getPage(query) {
  return request({
    url: "/chain/companysettle/page",
    method: "get",
    params: query,
  });
}
export function statisticsPage(query) {
  return request({
    url: "/chain/companysettle/statisticsPage",
    method: "get",
    params: query,
  });
}

export function giveCardUserEdit(obj) {
  return request({
    url: "/chain/companysettle/giveCardUserEdit",
    method: "post",
    data: obj,
  });
}

export function addObj(obj) {
  return request({
    url: "/chain/companysettle",
    method: "post",
    data: obj,
  });
}

export function getObj(id) {
  return request({
    url: "/chain/companysettle/" + id,
    method: "get",
  });
}

export function delObj(id) {
  return request({
    url: "/chain/companysettle/" + id,
    method: "delete",
  });
}

export function putObj(obj) {
  return request({
    url: "/chain/companysettle",
    method: "put",
    data: obj,
  });
}
//获取结算单的运单信息
export function getSettleWaybill(obj) {
  return request({
    url: "/chain/companysettlewaybill/getSettleWaybill",
    method: "post",
    data: obj,
  });
}
//提交核算明细
// export function submitSettleWaybill(obj) {
//     return request({
//         url: '/chain/companysettlewaybill/submitSettleWaybill',
//         method: 'post',
//         data: obj
//     })
// }
//核算完毕提交
export function submitSettleWaybill(obj) {
  return request({
    url: "/chain/companysettlewaybill/submitSettleWaybill4V11",
    method: "get",
    params: obj,
  });
}
//批量修改价格
export function batchUpdatePrice(query,batchEditType=1) {
  return request({
    url: `/chain/companysettlewaybill/batchUpdatePrice?batchEditType=${batchEditType}`,
    method: "post",
    data: query,
  });
}
//批量结算
export function batchSettle(query) {
  return request({
    url: "/chain/companysettlewaybill/batchSettle",
    method: "post",
    data: query,
  });
}
//批量撤回核算
export function batchCancelSettle(query) {
  return request({
    url: "/chain/companysettlewaybill/batchCancelSettle",
    method: "post",
    data: query,
  });
}
//批量驳回
export function batchRejectPrice(query) {
  return request({
    url: "/chain/companysettlewaybill/batchRejectPrice",
    method: "post",
    data: query,
  });
}
//批量撤回驳回
export function batchCancelReject(query) {
  return request({
    url: "/chain/companysettlewaybill/batchCancelReject",
    method: "post",
    data: query,
  });
}
//驳回申请
export function rejectSettle(query) {
  return request({
    url: "/chain/companysettle/rejectSettle",
    method: "get",
    params: query,
  });
}
//获取泥尾类型
export function getGarbageList(query) {
  ///companysettle/getGarbage 后面写了一个接口，暂时不用
  return request({
    url: "/chain/companysettlewaybill/getGarbage",
    method: "get",
    params: query,
  });
}
//获取土质
export function getGoSoilType(query) {
  return request({
    url: "/chain/companysettle/getGoSoilType",
    method: "get",
    params: query,
  });
}
//查看核算单
export function printSettle(query) {
  return request({
    url: "/chain/companysettle/printSettle",
    method: "get",
    params: query,
  });
}
//查看结算凭证
export function printSettleDoc(query) {
  return request({
    url: "/chain/companysettle/printSettleDoc",
    method: "get",
    params: query,
  });
}
//结算单的运单详情
export function printWaybillDetail(query) {
  return request({
    url: "/chain/companysettle/printWaybillDetail",
    method: "get",
    params: query,
  });
}
//结算单的运单详情
export function cancelCompanyWayBill(data) {
  return request({
    url: "/chain/companysettlewaybill/cancelCompanyWayBill",
    method: "post",
    data,
  });
}
//电子结算卡号码查询运单
export function scanningSettle(params) {
  return request({
    url: "/chain/companysettle/scanningSettle",
    method: "get",
    params,
  });
}

//电子结算卡号码查询运单
export function postScanningSettle(data) {
  return request({
    url: "/chain/companysettle/postScanningSettle",
    method: "post",
    data,
  });
}
//生成结算单
export function batchAddWaybillSettle(data) {
  return request({
    url: "/chain/companysettle/batchAddWaybillCardSettle",
    method: "post",
    data,
  });
}

//运单详情
export function getWaybill(query) {
  return request({
    url: "/chain/companysettle/selectWaybillsByCardNo",
    method: "get",
    params: query,
  });
}

//运单详情
export function selectWaybillsCardGenerated(query) {
  return request({
    url: "/chain/companysettle/selectWaybillsCardGenerated",
    method: "get",
    params: query,
  });
}

//导出
export function exportWaybillsByCardNoExcel(data) {
  return request({
    url: "/chain/companysettle/exportWaybillsByCardNoExcel",
    method: "post",
    data,
  });
}

export function updateCarrierName(obj) {
  return request({
    url: "/chain/companywaybill/updateCarrierName",
    method: "post",
    data: obj,
  });
}
export function selectWaybillsGiveNoGenerated(obj) {
  return request({
    url: "/chain/companysettle/selectWaybillsGiveNoGenerated",
    method: "get",
    params: obj,
  });
}
// 五证是否齐全
export function checkPayment(params) {
  return request({
    url: "/chain/companypayment/checkSettleExistDocIncomplete",
    method: "get",
    params,
  });
}
// 合并结算单
export function mergeSettlement(data) {
  return request({
    url: "/chain/companysettle/mergeSettlement",
    method: "post",
    data,
  });
}
// 下载结算单pdf
export function downloadSettlePdf(params) {
  return request({
    url: "/chain/companysettle/downloadSettlePdf",
    method: "get",
    params,
    responseType: "blob",
  });
}
// 下载结算凭证pdf
export function downloadSettleDocPdf(params) {
  return request({
    url: "/chain/companysettle/downloadSettleDocPdf",
    method: "get",
    params,
    responseType: "blob",
  });
}
// 下载交卡回执单pdf
export function downloadReceiptPdf(data) {
  return request({
    url: "/chain/companysettle/downloadReceiptPdf",
    method: "post",
    data,
    responseType: "blob",
  });
}

export function getDocCompleteRateByCompanySettleIdList(data) {
  return request({
    url: "/chain/companysettle/getDocCompleteRateByCompanySettleIdList",
    method: "post",
    data,
  });
}
// 检查是否开启结算单回执
export function checkOpen(data) {
  return request({
    url: "/chain/companysettlewaybillpayeeinfo/checkOpen",
    method: "post",
    data,
  });
}
// 根据结算单号获取回执列表
export function getList(data) {
  return request({
    url: "/chain/companysettlewaybillpayeeinfo/getList",
    method: "post",
    data,
  });
}
// 根据结算单号获取回执列表
export function getListGroupBatchNo(data) {
  return request({
    url: "/chain/companysettlewaybillpayeeinfo/getListGroupBatchNo",
    method: "post",
    data,
  });
}
// 预览资金支付计划
export function previewPaymentPlan(data) {
  return request({
    url: "/chain/companypaymentplansettle/previewPaymentPlan",
    method: "post",
    data,
  });
}
// 预览资金支付计划
export function createPaymentPlan(data) {
  return request({
    url: "/chain/companypaymentplansettle/createPaymentPlan",
    method: "post",
    data,
  });
}
// 查看资金支付计划
export function getPlanBySettleId(params) {
  return request({
    url: "/chain/companypaymentplansettle/getPlanBySettleId",
    method: "get",
    params,
  });
}
// 打印资金支付计划
export function printCompanyPaymentPlanSettle(params) {
  return request({
    url: "/chain/companysettle/printCompanyPaymentPlanSettle",
    method: "get",
    params,
  });
}
// 打印资金支付计划
export function downloadCompanyPaymentPlanSettlePdf(params) {
  return request({
    url: "/chain/companysettle/downloadCompanyPaymentPlanSettlePdf",
    method: "get",
    params,
    responseType: "blob",
  });
}

// 结算单拆单
export function spiltCompanySettle(data) {
  return request({
    url: "/chain/companysettle/spiltCompanySettle",
    method: "post",
    data,
  });
}
// 获取项目名称
export function getProjectList(params) {
  return request({
    url: "/chain/projectinfo/list",
    method: "get",
    params
  });
}
// 打印小票查询
export function receiptSettlement(data) {
  return request({
    url: "/chain/companywaybillreceipt/receiptSettlement",
    method: "post",
    data
  });
  
}
// 批量生成结算运单
export function receiptBatchAddWaybillSettlePre(data) {
  return request({
    url: "/chain/companysettle/receiptBatchAddWaybillSettlePre",
    method: "post",
    data,
  }); 
}
// 数据字典查询
export function listDictionaryItem(params) {
  return request({
      url: '/chain/systemdictionaryitem/platformDictionary',
      method: 'get',
      params
  })
}
export function cardGiveCheck(obj) {
  return request({
      url: '/chain/companycardgive/cardGiveCheck',
      method: 'post',
      data: obj
  })
}

