.theme-yerrow {
  .avue-logo{
    background: #c46420;
    box-shadow: none;
    color:#fff;
    text-align: center;
    // .avue-logo_title{
      // div{
      //   border-top-left-radius: 5px;
      //   border-top-right-radius: 5px;
      //   border-bottom-left-radius: 3px;
      //   border-bottom-right-radius: 3px;
      //   font-size: 22px;
      //   color:#fff;
      //   font-weight: 500;
      //   margin:  10px auto;
      //   width: 180px;
      //   height: 45px;
      //   background-color: #D0990B;
      // }
    // }
  }
  .avue-tags{
    padding:  3px 5px 5px 0;
    background: #f0f0f0;
    box-shadow: inset 0 0 3px 2px hsla(0,0%,39.2%,.1);
    .is-active{
      &:before{
        background: #409EFF !important;
      }
    }
    .el-tabs__item{
      padding: 0 15px !important;
      position: relative;
      height: 32px !important;
      line-height:32px !important;
      border: 1px solid #e8eaec!important;
      color: #515a6e!important;
      background: #fff!important;
      border-radius: 3px;
      &:before{
        content:'';
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right:10px;
        border-radius: 50%;
        background: #e8eaec;
      }
    }
    }
    .avue-sidebar{
      box-shadow: 2px 0 6px rgba(0, 21, 41, 0.15);
      background-color:#c46420 !important;
      .el-menu-item{
        i,span{
          color:#fff ;
        }
        background-color:#de7622 !important;
      }
      .el-submenu__title{
        i,span{
          color:#fff ;
        }
        background-color:#c46420 !important;
      }
      .el-menu-item,.el-submenu__title{
        &:hover{
          background-color: #c08435 !important;
          i,span{
            color:#fff ;
          }
        }
      }
      .el-menu-item.is-active,.el-submenu__title.is-active{
        background-color:#fc8c23 !important;
        i,span{
          color:#fff ;
        }
        &::before{
          display: none;
        }
        &:hover{
          background-color: #fc8c23 !important;
          i,span{
            color:#fff ;
          }
        }
      }
    }
  }

//   .avue-sidebar{
//     background: #c46420;
//     .el-menu-item{
//       &.is-active {
//           background-color:  #b0690a;
//           &:before {
//             display: none;
//           }
//           i,span{
//             color:#b0690a;
//         }
//       }
//     }
//      .el-submenu{
//         .el-menu-item{
//           &.is-active {
//             background-color:  #b0690a;
//             &:before {
//               display: none;
//             }
//             i,span{
//               color:#fff;
//           }
//         }
//       }
//     }
//   }
// }
