export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  index:true,
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  dialogClickModal:false,
  excelBtn: false,
  printBtn: false,
  addBtn: false,
  viewBtn: false,
  searchSpan:8,
  searchMenuSpan: 6,
  menuWidth:140,
  column: [
    // {
      // label: "id",
      // prop: "id",
      // disabled:true,
      // display:false,
      // sortable: true,
      // rules: [
      //         {
      //             required: true,
      //             message: '请输入主键id',
      //             trigger: 'blur'
      //         },
      //                         {
      //             max: 36,
      //             message: '长度在不能超过36个字符'
      //         },
      //     ]
    // },
    // {
    //   label: "企业名称",
    //   prop: "companyName",
    //   sortable: true,
    //   search: true,
    //   disabled:true,
    // },
    {
      label: "项目名称",
      prop: "projectName",
      sortable: true,
      search: true,
      disabled:true,
      minWidth:140,
      overHidden:true,
    },
    {
      label: "泥尾",
      prop: "garbageName",
      sortable: true,
      search: true,
      disabled:true,
      minWidth:140,
      overHidden:true,
    },
    {
      label: "记录类型",
      prop: "actType",
      sortable: true,
      search: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "领用",
          value: "1",
        },
        {
          label: "剩余",
          value: "2",
        },
      ],
      rules: [
        {
          required: true,
          message: "请选择记录类型",
          trigger: "change",
        },
      ],
      minWidth:96,
      overHidden:true,
    },
    {
      label: "记录数量",
      prop: "actNum",
      sortable: true,
      controls:false,
      type:'number',
      precision:0,
      minRows:0,
      rules: [
        {
          required: true,
          message: "请输入本次记录的数量",
          trigger: "blur",
        },
        {
          min: 0,
          type: "number",
          message: "值不能小于0",
          trigger: "blur",
        },
      ],
      minWidth:96,
      overHidden:true,
    },

    //     {
    //     label: '是否删除：0=否、1=是',
    //     prop: 'isDel',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入是否删除：0=否、1=是',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 1,
    //                 message: '长度在不能超过1个字符'
    //             },
    //         ]
    // },
    // {
    //   label: "备注",
    //   prop: "ticketRemark",
    //   sortable: true,
    //   rules: [],
    // },
    {
      label: "记录人",
      prop: "staffName",
      sortable: true,
      disabled:true,
      display:false,
      rules: [
        {
          required: true,
          message: "请输入创建人",
          trigger: "blur",
        },
        {
          max: 36,
          message: "长度在不能超过36个字符",
        },
      ],
      minWidth:84,
      overHidden:true,
    },
    {
      label: "创建时间",
      prop: "createDatetime",
      sortable: true,
      disabled:true,
      display:false,
      rules: [
        {
          required: true,
          message: "请输入创建时间",
          trigger: "blur",
        },
      ],
      minWidth:140,
      overHidden:true,
    },
    {
      label: "日期",
      prop: "searchDate",
      type:'datetime',
      disabled:true,
      display:false,
      // searchSpan:12,
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      searchRange:true,
      // sortable: true,
      search:true,
      showColumn:false,
      hide:true,
      display:false,
    },
    // {
    //   label: "修改人",
    //   prop: "updateId",
    //   sortable: true,
    //   rules: [
    //     {
    //       max: 36,
    //       message: "长度在不能超过36个字符",
    //     },
    //   ],
    // },
    // {
    //   label: "修改时间",
    //   prop: "updateDatetime",
    //   sortable: true,
    //   rules: [],
    // },
  ],
};
