import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/garbagecustomersales/page',
        method: 'get',
        params: query
    })
}
export function getCustomerList(query) {
    return request({
        url: '/chain/garbagecustomer/list',
        method: 'get',
        params: query
    })
}
export function getProjectInfoList(query) {
    return request({
        url: '/chain/garbagecustomersales/getProjectInfoList',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/garbagecustomersales',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/garbagecustomersales/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/garbagecustomersales/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/garbagecustomersales',
        method: 'put',
        data: obj
    })
}
