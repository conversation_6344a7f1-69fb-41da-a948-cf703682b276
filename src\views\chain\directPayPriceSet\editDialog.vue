<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="50%"
      :title="title"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <avue-form
          :option="option"
          ref="dialogForm"
          v-model="dialogForm"
          @submit="submit"
        >
          <template slot="menuForm">
            <el-button
              :loading="btnLoading"
              icon="el-icon-close"
              @click="resetChange"
              >审核不通过</el-button
            >
          </template>
        </avue-form>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { audit } from "@/api/chain/directPayPriceSet";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
    title: {
      type: String,
      default: "审核",
    },
  },
  data() {
    return {
      dialogForm: {},
      btnLoading: false,
      option: {
        labelWidth: 110,
        submitText: "审核通过",
        position: "left",
        cancelBtn: true,
        disabled: true,
        submitBtn: true,
        emptyBtn: false,
        column: [
          {
            label: "项目名称",
            prop: "projectName",
            disabled: true,
            span: 24,
          },
          {
            label: "运输方式",
            prop: "tpMode",
            type: "select", // 下拉选择
            span: 24,
            props: {
              label: "itemName",
              value: "itemValue",
            },
            dicUrl:
              "/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode",
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "土质",
            prop: "soilType",
            type: "select", // 下拉选择
            span: 24,
            props: {
              label: "itemName",
              value: "itemName",
            },
            dicUrl:"/chain/projectinfo/getSoilTypePlatform?projectInfoId="+this.info.projectInfoId,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "泥尾",
            prop: "garbageId",
            type: "select", // 下拉选择
            // search:true,
            span: 24,
            props: {
              label: "names",
              value: "id",
            },
            dicUrl: "/chain/projectinfo/queryProjectGarbageAll?projectInfoId="+this.info.projectInfoId,
            filterable: true, //是否可以搜索
          },
          {
            label: "直付价",
            prop: "directPayPrice",
            type: "number",
            controls: false,
            minRows: 0,
            precision: 2,
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "change",
              },
            ],
          },
          {
            label: "车队长和司机",
            prop: "captainAndDrivers",
            span: 24,
            type: "textarea",
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "change",
              },
            ],
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            span: 24,
            dicData: [
              {
                value: "1",
                label: "待审核",
              },
              {
                value: "2",
                label: "已审核",
              },
              {
                value: "3",
                label: "已失效",
              },
              {
                value: "4",
                label: "已驳回",
              },
            ],
          },
          {
            label: "申请人",
            prop: "applyName",
            sortable: "custom",
            span: 24,
          },
          {
            label: "申请时间",
            prop: "applyDatetime",
            span: 24,
            type: "datetime",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
        ],
      },
    };
  },
  created() {},
  mounted() {
    this.dialogForm = this.info;
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    submit(form, done) {
      //提现审核通过
      this.$confirm("确定审核通过？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            id: form.id,
            auditType: 1,
          };
          audit(params)
            .then((res) => {
              this.cancelModal();
              this.$emit("refreshChange");
              done();
            })
            .catch(() => {
              done();
            });
        })
        .catch(function (err) {
          done();
        });
    },
    resetChange() {
      //提现审核不通过
      this.$confirm("确定审核不通过？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            id: this.dialogForm.id,
            auditType: 0,
          };
          this.btnLoading = true;
          audit(params)
            .then((res) => {
              this.btnLoading = false;
              this.cancelModal();
              this.$emit("refreshChange");
            })
            .catch(() => {
              this.btnLoading = false;
            });
        })
        .catch(function (err) {});
    },
  },
};
</script>

<style lang="scss" scoped></style>
