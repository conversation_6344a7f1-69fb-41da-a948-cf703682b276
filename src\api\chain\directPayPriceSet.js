import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companyprojectdirectpayprice/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companyprojectdirectpayprice',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companyprojectdirectpayprice/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companyprojectdirectpayprice/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companyprojectdirectpayprice',
        method: 'put',
        data: obj
    })
}
export function failure(params) {
    return request({
        url: '/chain/companyprojectdirectpayprice/failure',
        method: 'get',
        params
    })
}
export function getSoilType(params) {
    return request({
        url: '/chain/projectinfo/getSoilTypePlatform',
        method: 'get',
        params
    })
}
export function queryProjectGarbage(params) {
    return request({
        url: '/chain/projectinfo/queryProjectGarbageAll',
        method: 'get',
        params
    })
}
export function getCaptain(params) {
    return request({
        url: '/chain/companyprojectdirectpayprice/getCaptainForPlatform',
        method: 'get',
        params
    })
}
//审核
export function audit(query) {
  return request({
      url: '/chain/companyprojectdirectpayprice/audit',
      method: 'get',
      params: query
  })
}
//搜索土质
export function searchSoilType(params) {
  return request({
      url: '/chain/companyprojectdirectpayprice/getSoilType',
      method: 'get',
      params
  })
}
//搜索泥尾
export function searchGarbage(params) {
  return request({
      url: '/chain/companyprojectdirectpayprice/getGarbage',
      method: 'get',
      params
  })
}
