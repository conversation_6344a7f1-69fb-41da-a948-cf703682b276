<template>
  <div class="driverDetail">
    <el-drawer
      size="50%"
      title="设置车队名称"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <div style="display:flex;align-items:center;margin-bottom:16px;">
          <label for="plots" style="width:80px;text-align:right;">模式：</label>
          <el-checkbox-group v-model="checkList">
            <el-checkbox label="2">选择模式</el-checkbox>
            <el-checkbox label="1">输入模式</el-checkbox>
          </el-checkbox-group>
        </div>
        <div style="display:flex;align-items:center">
          <label for="plots" style="width:80px;text-align:right;">车队名称：</label>
          <el-input id="plots" v-model.trim="plotsName" style="width:300px" maxlength="15" size="small" placeholder="请输入车队名称" @keyup.enter.native="addTag">

            <el-button slot="suffix" @click="addTag" size="small" type="text" >新增</el-button>
          </el-input>
        </div>
        <div class="tags" style="margin:20px 0px">
          <el-tag  v-for="(tag,index) in tags" :key="index" closable @close="handleClose(index)" style="margin-right:6px;margin-bottom:10px">
            {{ tag }}
          </el-tag>
        </div>
        <div class="btns" style="text-align: center; margin-top: 20px">
          <el-button @click="cancelModal" size="small" style="margin-left: 20px"
            >取 消</el-button>
          <el-button type="primary" size="small" :loading="btnLoading" @click="handleUpdate"
            >确 定</el-button>
        </div>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {updProFleetNames,getProjectInfoExt} from "@/api/chain/projectinfo";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  data() {
    return {
      btnLoading: false,
      plotsName:'',
      tags:[],
      checkList:['1']
    };
  },
  created() {},
  mounted() {
    this.getProjectInfoExt()
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    getProjectInfoExt(){
      getProjectInfoExt(this.info.id).then(res=>{
        this.tags = res.data.data.fleetNames&&res.data.data.fleetNames.split(",")||[]
        this.checkList = res.data.data.fleetNameModelType=="0"?["1","2"]:res.data.data.fleetNameModelType=="2"?["2"]:["1"]
      })
    },
    cancelModal() {
      this.$emit("update:visible", false);
    },
    addTag(){
      if(!this.plotsName) return false
      if(this.tags.includes(this.plotsName)){
        this.$message.error("车队名称已存在")
      }else{
        this.tags.push(this.plotsName)
      }
    },
    handleClose(index){
      this.tags.splice(index,1)
    },
    handleUpdate() {
      if(this.checkList.length==0){
        this.$message.error("请选择模式")
        return false
      }
      this.btnLoading = true;
      updProFleetNames({
        projectInfoId: this.info.id,
        fleetNameModelType:this.checkList.length==2?0:this.checkList.includes("1")?1:this.checkList.includes("2")?2:"",
        fleetNames: this.tags.join(","),
      })
        .then((res) => {
          this.btnLoading = false;
          this.$emit("update:visible", false);
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

</style>
