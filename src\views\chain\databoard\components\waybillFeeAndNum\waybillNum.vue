<template>
  <div class="waybillFee">
    <searchInfo :info="paramsSearch" :tab="tab" :type="2" :payType="active"  source="6" unit="单" @searchChange="searchChange" @exOut="exOut">
      <template v-slot:center="slotProps">
        <div style="margin-bottom: 10px">
          <el-radio-group size="small" v-model="active">
            <el-radio-button label="1">
              <div class="label">全部应付(单)</div>
              <div class="value" style="margin-top: 10px;">
                {{ slotProps.total.totalWaybill }}
              </div>
            </el-radio-button>
            <el-radio-button label="2">
              <div class="label">待付(单)</div>
              <div class="value" style="margin-top: 10px;">
                {{ slotProps.total.waitWaybillTotal }}
              </div></el-radio-button
            >
            <el-radio-button label="3">
              <div class="label">已付(单)</div>
              <div class="value" style="margin-top: 10px;">
                {{ slotProps.total.paidWaybillTotal }}
              </div>
            </el-radio-button>
          </el-radio-group>
        </div>
      </template>
    </searchInfo>
    <avue-crud ref="crud" :page.sync="page" :data="tableData" :table-loading="tableLoading" :option="tableOption"
      v-model="form" :search.sync="search" @on-load="getPage">
    </avue-crud>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getCompanyPaymentProjectList as getPage } from "@/api/chain/board";
import searchInfo from './searchInfo';
import { exportOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    tab: {
      type: String,
      default: ""
    },
  },
  components: {
    searchInfo
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      search: {},
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        // maxHeight:[800],
        // height:'auto',
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: true,
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        header: false,
        column: [
          {
            label: "所属项目",
            prop: "projectName",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "支付单号",
            prop: "paymentNo",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "结算单号",
            prop: "settleNos",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "结算申请人",
            prop: "applyNames",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "承运人",
            prop: "payeeNames",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "运单总数",
            prop: "waybillTotal",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "已支付运单",
            prop: "bdcWaybillCnt",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "待支付运单",
            prop: "waitWaybillTotal",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "支付单创建时间",
            prop: "createDatetime",
            minWidth: 160,
            overHidden: true,
          },

        ],
      },
      active: "1",
    }
  },
  created () {
    this.paramsSearch = Object.assign({}, this.info)
  },
  mounted () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    searchChange (params, done) {
      // params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    getPage (page, params = {}) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          {
            dateStartStr: this.info.startDate,
            dateEndStr: this.info.endDate,
            isHProjectInfoCount: this.info.checkDynamic,
            projectInfoId: this.info.projectInfoId,
            paymentMethodType:this.active
          },
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    exOut (params, done) {
      let url = "/chain/companynsrsbhwallet/companyPaymentProjectListExport";
      params.exportType=2
      exportOut(params, url, "总运单数", 'post').then(res => {
        done()
      }).catch(() => {
        done()
      })
    },
  },

};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

.count {
  margin-bottom: 10px;

  span {
    font-size: 16px;
    font-weight: 700;
  }
}
</style>
