<template>
  <div class="history">
    <el-drawer size="70%" title="项目名称产值记录详情" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <avue-form :option="option" v-model="form">
        <template slot="totalOutputValue">
          <div class="totalOutputValue">
            <div class="total">总计：¥:{{info.totalPrice}}</div>
            <div class="title flex flex-between">
              <span>车辆运输费用统计表</span>
              <span>合计：¥:{{info.truckTotalPrice}}</span>
            </div>
            <avue-crud :option="option1" :data="info.truckList"></avue-crud>
            <div class="title flex flex-between">
              <span>机械费用统计表</span>
            </div>
            <avue-crud :option="option2" :data="info.machineList"></avue-crud>
            <div class="title flex flex-between">
              <span>入场签单【回填】统计表</span>
              <span>合计：¥:{{info.entranceTotalPrice}}</span>
            </div>
            <avue-crud :option="option3" :data="info.entranceList"></avue-crud>
          </div>
        </template>
        <template slot="flow">
          <div class="info" style="width:400px;margin-left: 20px;margin-top: 20px;"
            v-if="flowList && flowList.length > 0">
            <div class="approvalFlow">
              <el-timeline>
                <el-timeline-item :timestamp="item.companyPositionName" placement="top"
                  :class="item.currentAuditFlag == 1 ? 'myActive' : ''"
                  :color="item.currentAuditFlag == 1 ? '#409eff' : ''" v-for="(item, index) in flowList" :key="index">
                  <i :class="item.isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" @click="showMore(item)"
                    v-if="item.approveStatus == 1"
                    style="cursor: pointer;position: absolute;right: -20px;top: 0px;font-size: 18px;"></i>
                  <div style="display:flex;align-items: center;justify-content: space-between;margin-bottom:10px">
                    <h4>{{ item.approveUsername }}</h4><span>{{ item.approveDatetime }}</span>
                  </div>
                  <el-input type="textarea" v-if="item.approveRemark" v-model="item.approveRemark"
                    :autosize="{ minRows: 3, maxRows: 8 }" disabled></el-input>
                  <div v-if="item.isShow">{{ item.positionStaffName }}</div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </template>
      </avue-form>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getFlowNode } from '@/api/chain/ledgerDig2'
// import poPerson from "@/views/chain/waybillProcessSettings/poPerson"
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {},
  data () {
    return {
      flowList: [],
      activeName: "1",
      form: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 120,
        detail: true,
        column: [],
        tabs: true,
        group: [
          {
            label: "基础信息",
            prop: "group1",
            column: [
              {
                label: "项目名称",
                prop: "projectName",
              },
              {
                label: "产值合计(元)",
                prop: "totalPrice",
              },
              {
                label: "班次信息",
                prop: "shiftTime",
              },
              {
                label: "班次类型",
                prop: "shiftType",
              },
            ]
          },
          {
            label: "产值合计",
            prop: "group2",
            column: [
              {
                label: "",
                prop: "totalOutputValue",
                labelWidth: 0,
                span: 24,

              },
            ]
          },
          {
            label: "审核流程",
            prop: "group3",
            column: [
              {
                label: "",
                prop: "flow",
                labelWidth: 0,
                span: 24,
              },
            ]
          },
        ]
      },
      option1: {
        header: false,
        menu: false,
        column: [
          {
            label: "泥头泥尾",
            prop: "garbageName",
          },
          {
            label: "车队长",
            prop: "captainName",
          },
          {
            label: "单价(元)",
            prop: "unitPrice",
          },
          {
            label: "单位",
            prop: "weightUnit",
            formatter:(val)=>{
              return val.weightTons+val.weightUnit
            },
          },
          {
            label: "总价(元)",
            prop: "totalPrice",
          },
        ]
      },
      option2: {
        header: false,
        menu: false,
        column: [
          {
            label: "机主",
            prop: "ownerName",
          },
          {
            label: "机械型号",
            prop: "machineCode",
          },
          {
            label: "单价(元)",
            prop: "unitPrice",
            formatter:()=>{
              return "-"
            }
          },
          {
            label: "单位",
            prop: "weightUnit",
            formatter:(val)=>{
              return val.weightTons+val.weightUnit
            },
          },
          {
            label: "总价(元)",
            prop: "totalPrice",
            formatter:()=>{
              return "-"
            }
          },
        ]
      },
      option3: {
        header: false,
        menu: false,
        column: [
        {
            label: "泥头泥尾",
            prop: "garbageName",
          },
          {
            label: "车队长",
            prop: "captainName",
          },
          {
            label: "单价(元)",
            prop: "unitPrice",
          },
          {
            label: "单位",
            prop: "weightUnit",
            formatter:(val)=>{
              return val.weightTons+val.weightUnit
            },
          },
          {
            label: "总价(元)",
            prop: "totalPrice",
          },
        ]
      },
    };
  },
  created () { },
  mounted () {
    this.form = Object.assign({}, this.info)
    this.getData()
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    getData () {
      getFlowNode(5, this.info.id).then(res => {
        this.flowList = res.data.data
      })
    },
    showMore (item) {
      console.log(item);
      this.$set(item, 'isShow', !item.isShow)
      // item.isShow = !item.isShow
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .myActive .el-timeline-item__tail {
  border-left-color: #409eff;
}

/deep/ .avue--detail .el-textarea.is-disabled .el-textarea__inner {
  background-color: #F5F7FA;
  border-color: #E4E7ED;
  color: #C0C4CC;
  cursor: not-allowed;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
}
</style>
