<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%" title="司机未实名或钱包未开通" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <avue-crud ref="crud" :data="tableData"  :table-loading="tableLoading" :option="tableOption">
        </avue-crud>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: ()=>{
        return []
      }
    }
  },
  data() {
    return {
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: false,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        excelBtn: true,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: false,
        menu:false,
        refreshBtn:false,
        column: [
          {
            label: "司机手机号码",
            prop: "driverMobile",
            overHidden:true,
          },
          {
            label: "司机姓名",
            prop: "driverName",
            overHidden:true,
          },
          {
            label: "车队长",
            prop: "captainName",
            overHidden:true,
          },
          {
            label: "车队长手机号码",
            prop: "captainMobile",
            overHidden:true,
          },
          {
            label: "账号",
            prop: "kuaiqianOpenId",
            overHidden:true,
          },
          {
            label: "开户时间",
            prop: "kuaiqianCreateDatetime",
            overHidden:true,
          },
          {
            label: "原因",
            prop: "reason",
            formatter: (val) => {
              return `<span style="color:red">${val.reason}</span>`
            },
            overHidden:true,
          },
        ],
      },
    }
  },
  created() {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
  }
};
</script>

<style lang="scss" scoped>
/deep/ #el-drawer__title span{
  color: red;
}
</style>
