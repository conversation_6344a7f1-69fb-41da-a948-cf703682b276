<template>
  <div class="selectPersonnel">
    <el-dialog
      :visible.sync="visible"
      title="选择人员"
      :close-on-click-modal="false"
      width="700px"
      :before-close="oncancel"
    >
      <div class="tranferbox">
        <div class="conbox">
          <p class="transfer-panel__header">
            <label> 可选员工</label>
          </p>
          <div class="titbox" style="padding: 0 10px">
            <el-input
              size="small"
              placeholder="搜索成员、部门"
              prefix-icon="el-icon-search"
              v-model="filterText"
              style="border-ra"
            ></el-input>
          </div>
          <div class="wordbox">
            <el-tree
              @check="getData"
              show-checkbox
              class="filter-tree"
              node-key="id"
              :data="data"
              :props="defaultProps"
              :default-expand-all="true"
              :filter-node-method="filterNode"
              ref="tree"
            ></el-tree>
          </div>
        </div>
        <i class="el-icon-arrow-right"></i>
        <div class="conbox">
          <p class="transfer-panel__header">
            <label> 已选员工({{ keyarr.length }})</label>
          </p>
          <div
            class="wordbox"
            style="padding-left: 15px; height: calc(100% - 50px)"
          >
            <ul>
              <li v-for="(item, index) in keyarr" :key="index">
                <div class="inli">
                  <i class="el-icon-s-custom"></i>
                  <span>{{ item.deptName }}</span>
                  <i class="el-icon-close" @click="removeData(item)"></i>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="oncancel">取 消</el-button>
        <el-button type="primary" @click="handleUser">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { selectTreeStaff } from "@/api/chain/projectinfo";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      checkList: [],
      keyarr: [],
      filterText: "",
      data: [],
      defaultProps: {
        children: "children",
        label: "deptName",
        isLeaf: (data, node) => {
          if (!data.children && data.deptType != 3) {
            node.isLeaf = false;
            node.checked = false;
            node.visible = false;
          }
        },
      },
      tempList: [],
      num: 0,
      checkTempList:[]
    };
  },
  async mounted() {
    // this.checkTempList = JSON.parse(JSON.stringify(this.list))
    //获取部门职员列表
    await this.selectTreeStaff();
    // this.getData()
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    selectTreeStaff() {
      selectTreeStaff().then((res) => {
        this.tempList = res.data.data;
        this.data = this.filterData(this.tempList);
        setTimeout(()=>{
          this.keyarr = JSON.parse(JSON.stringify(this.list))
          this.setCheckedNodes(this.keyarr)
        },100)
      });
    },
    filterData(data) {
      this.num = 0;
      data.forEach((item, index, arr) => {
        if (item.children && item.children.length > 0) {
          this.parentList = data;
          this.filterData(item.children);
        } else {
          if (item.deptType != 3) {
            console.log(item.deptName);
            arr.splice(index, 1);
            this.num++;
          }
          // if(this.checkTempList&&this.checkTempList.length>0){
          //  this.checkTempList = this.checkTempList.map(item2=>{
          //     if(item2.id ==item.id ){
          //     console.log(item.deptName);
          //       item2 = item
          //     }
          //     return item2
          //   })
          // }
        }
      });
      if (this.num > 0) {
        this.num = 0;
        this.filterData(this.tempList);
      }
      return data;
    },
    //关键词搜索
    filterNode(value, data, node) {
      // if(!data.children&&data.deptType!=3){
      //     return false
      //   }
      if (!value) {
        return true;
      }
      return data.deptName.indexOf(value) !== -1;
    },
    getData() {
      this.keyarr = [];
      this.checkList = this.$refs.tree.getCheckedNodes();
      console.log(this.checkList);
      if (this.checkList.length != 0) {
        for (var i = 0; i < this.checkList.length; i++) {
          if (!this.checkList[i].children && this.checkList[i].deptType == 3) {
            this.keyarr.push(this.checkList[i]);
            console.log(this.checkList[i]);
          }
        }
        console.log(this.data);
      } else {
        this.keyarr = [];
      }
    },
    setCheckedNodes(arr) {
    console.log(arr);
      this.$refs.tree.setCheckedNodes(arr);
    },
    removeData(data) {
      let checklist = this.keyarr;
      console.log(this.keyarr);
      for (var i = 0; i < checklist.length; i++) {
        if (checklist[i].deptName === data.deptName) {
          checklist.splice(i, 1);
        }
      }
      console.log(this.keyarr);
      this.keyarr = checklist;
      this.setCheckedNodes(this.keyarr);
    },
    //选择人员弹框确定
    handleUser() {
      if (this.keyarr && this.keyarr.length > 0) {
        let str = JSON.stringify(this.keyarr);
        this.$emit("changeUserList", str);
      }
      this.$emit("update:visible", false);
    },
    oncancel() {
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style scoped lang="less">
.tranferbox {
  display: flex;
  justify-content: center;
  align-items: center;
  .transfer-panel__header{
      height: 40px;
    line-height: 40px;
    background: #F5F7FA;
    margin: 0;
    padding-left: 15px;
    border-bottom: 1px solid #EBEEF5;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #000;
    margin-bottom: 10px;
  }
  // width: 830px;
  /deep/ .el-icon-arrow-right{
    display: inline-block;
    width: 182px;
    box-sizing: border-box;
    text-align: center;
    } 
  /deep/ .conbox {
    height: 286px;
    border: 1px solid #EBEEF5;
    border-radius: 2px;
    background: #fdfdfd;
    width: 198px;
    .titbox {
      height: 29px;
      line-height: 29px;
      h2 {
        font-size: 14px;
      }
      .el-input__inner{
        border-radius: 16px;
        border-color: #C0C4CC;
      }
    }
    
    .wordbox {
      font-size: 12px;
      height: 197px;
      margin-top: 10px;
      overflow-y: auto;
      .el-tree-node__label {
        font-size: 12px;
      }
      ul {
        li {
          .inli {
            display: flex;
            align-items: baseline;
          }
          span {
            font-size: 12px;
            display: block;
            // width: 70px;
            padding:3px 8px;
          }
          .el-icon-s-custom {
            color: #93a9d3;
          }
          .el-icon-close {
            color: #808080;
            cursor: pointer;
          }
        }
      }
    }
  }
}

</style>
