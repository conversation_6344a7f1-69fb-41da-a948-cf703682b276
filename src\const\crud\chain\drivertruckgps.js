export const tableOption = {
    dialogDrag: true,
    border: true,
    indexLabel: '序号',
    stripe: true,
    menuAlign: 'center',
    align: 'center',
    menuType: 'text',
    searchShow: false,
    excelBtn: true,
    printBtn: true,
    viewBtn: true,
    searchMenuSpan: 6,
    column: [
            {
            label: 'ID',
            prop: 'id',
            sortable: true,
            rules: [
                    {
                        required: true,
                        message: '请输入ID',
                        trigger: 'blur'
                    },
                                    {
                        max: 36,
                        message: '长度在不能超过36个字符'
                    },
                ]
        },
            {
            label: '车辆ID',
            prop: 'driverTruckInfoId',
            sortable: true,
            rules: [
                    {
                        required: true,
                        message: '请输入车辆ID',
                        trigger: 'blur'
                    },
                                    {
                        max: 36,
                        message: '长度在不能超过36个字符'
                    },
                ]
        },
            {
            label: '车辆号码',
            prop: 'truckCode',
            sortable: true,
            rules: [
                    {
                        required: true,
                        message: '请输入车辆号码',
                        trigger: 'blur'
                    },
                                    {
                        max: 50,
                        message: '长度在不能超过50个字符'
                    },
                ]
        },
            {
            label: '司机ID,对应system_user.id',
            prop: 'driverId',
            sortable: true,
            rules: [
                    {
                        required: true,
                        message: '请输入司机ID,对应system_user.id',
                        trigger: 'blur'
                    },
                                    {
                        max: 36,
                        message: '长度在不能超过36个字符'
                    },
                ]
        },
            {
            label: '司机姓名',
            prop: 'driverName',
            sortable: true,
            rules: [
                    {
                        required: true,
                        message: '请输入司机姓名',
                        trigger: 'blur'
                    },
                                    {
                        max: 50,
                        message: '长度在不能超过50个字符'
                    },
                ]
        },
            {
            label: '运单ID',
            prop: 'companyWaybillId',
            sortable: true,
            rules: [
                                    {
                        max: 36,
                        message: '长度在不能超过36个字符'
                    },
                ]
        },
            {
            label: '运单号',
            prop: 'companyWaybillNo',
            sortable: true,
            rules: [
                                    {
                        max: 100,
                        message: '长度在不能超过100个字符'
                    },
                ]
        },
            {
            label: 'GPS坐标（经度、纬度），用英文逗号隔开。（高德地图火星坐标GCJ-02）',
            prop: 'gps',
            sortable: true,
            rules: [
                    {
                        required: true,
                        message: '请输入GPS坐标（经度、纬度），用英文逗号隔开。（高德地图火星坐标GCJ-02）',
                        trigger: 'blur'
                    },
                                    {
                        max: 300,
                        message: '长度在不能超过300个字符'
                    },
                ]
        },
            {
            label: '创建日期时间',
            prop: 'createDatetime',
            sortable: true,
            rules: [
                    {
                        required: true,
                        message: '请输入创建日期时间',
                        trigger: 'blur'
                    },
                                ]
        },
            {
            label: '类型：1=出车、2=空车、3=运单确认、4=运单运输、5=卸土确认、6=收车、7=未知',
            prop: 'type',
            sortable: true,
            rules: [
                    {
                        required: true,
                        message: '请输入类型：1=出车、2=空车、3=运单确认、4=运单运输、5=卸土确认、6=收车、7=未知',
                        trigger: 'blur'
                    },
                                    {
                        max: 1,
                        message: '长度在不能超过1个字符'
                    },
                ]
        },
            ]
}
