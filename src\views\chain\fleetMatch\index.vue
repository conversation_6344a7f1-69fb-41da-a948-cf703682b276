<template>
  <div class="fleetMatch">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="menu"
                  slot-scope="{ row }">
          <el-button type="text"
                     :icon="row.planStatus==1?'el-icon-video-pause':'el-icon-video-play'"
                     v-if="permissions['chain:fleetMatch:match']"
                     size="small"
                     :loading="tableLoading"
                     plain
                     @click="start(row)">{{row.planStatus==1?'暂停匹配':'恢复匹配'}}</el-button>
          <el-button type="text"
                     icon="el-icon-info"
                     v-if="permissions['chain:fleetMatch:detail']"
                     size="small"
                     plain
                     @click="detail(row)">详情</el-button>
        </template>
        <template slot="matchPoolCount"
                  slot-scope="{ row }">
          <span class="blue"
                @click="viewDialog(row,'1')">{{row.matchPoolCount}}</span>
        </template>
        <template slot="waitingConfirmCount"
                  slot-scope="{ row }">
          <span class="blue"
                @click="viewDialog(row,'2')">{{row.waitingConfirmCount}}</span>
        </template>
        <template slot="waitingFleetConfirmCount"
                  slot-scope="{ row }">
          <span class="blue"
                @click="viewDialog(row,'3')">{{row.waitingFleetConfirmCount}}</span>
        </template>
        <template slot="matchSuccessCount"
                  slot-scope="{ row }">
          <span class="blue"
                @click="viewDialog(row,'4')">{{row.matchSuccessCount}}</span>
        </template>
      </avue-crud>
    </basic-container>
    <matching-list v-if="listDialog"
                   @refreshChange="refreshChange"
                   :info="info"
                   :visible.sync="listDialog"></matching-list>
    <planDetail v-if="detailVisible"
                :detailForm="detailForm"
                :option="detailOption"
                size="50%"
                :visible.sync="detailVisible"
                title="车队撮合计划详情"></planDetail>
  </div>
</template>

<script>
import { getPage,pause,renew } from "@/api/chain/fleetMatch";
import { tableOption } from "@/const/crud/chain/fleetMatch";
import { mapGetters } from "vuex";
import matchingList from './matchingList'
import planDetail from '@/components/formDetail/index.vue';

export default {
  name: "fleetMatch",
  components: { matchingList, planDetail },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      listDialog: false,
      info: {},
      detailVisible: false, //详情弹窗
      detailForm: {},
      detailOption: {
        detail: true,
        labelWidth: 100,
        column: [
          {
            label: "计划编号",
            prop: "planNo",
            span: 24,
            placeholder: " ",
          },
          {
            label: "项目名称",
            prop: "projectName",
            span: 24,
            placeholder: " ",
          },
          {
            label: "项目地址",
            prop: "gpsAddress",
            span: 24,
            placeholder: " ",
          },
          {
            label: "联系人",
            prop: "contactPerson",
            span: 24,
            placeholder: " ",
          },
          {
            label: "联系方式",
            prop: "contactPhone",
            span: 24,
            placeholder: " ",
          },
          {
            label: "项目启动日期",
            prop: "startDate",
            span: 24,
            placeholder: " ",
          },
          {
            label: "运费",
            prop: "freight",
            span: 24,
            placeholder: " ",
          },
          {
            label: "运输方式",
            prop: "tpMode",
            span: 24,
            placeholder: " ",
          },
          {
            label: "账期",
            prop: "settleCycle",
            span: 24,
            placeholder: " ",
          },
          {
            label: "车型",
            prop: "vehicleTypes",
            span: 24,
            placeholder: " ",
          },
          {
            label: "车数/天",
            prop: "carsNumber",
            span: 24,
            placeholder: " ",
          },
        ],
      },
    };
  },
  created () { },
  mounted: function () { },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList () {
      return {
        addBtn: this.permissions["chain:fleetMatch:add"] ? true : false,
        delBtn: this.permissions["chain:fleetMatch:del"] ? true : false,
        editBtn: this.permissions["chain:fleetMatch:edit"] ? true : false,
        viewBtn: this.permissions["chain:fleetMatch:get"] ? true : false,
      };
    },
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange (val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage (page, params) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page);
    },
    viewDialog (row, type) {
      this.info = row
      this.info.type = type
      this.listDialog = true
    },
    start (row) {
      this.$confirm(row.planStatus == 1 ? "暂停后，该计划将不在平台所有车队中参与匹配显示" : "恢复匹配后，将在平台所有车队中重新参与匹配显示", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          row.planStatus == 1 ? this.pause(row.id) : this.renew(row.id)
        })
        .catch((err) => { });
    },
    pause (id) {
      this.tableLoading = true
      pause(id).then(res => {
        this.getPage(this.page)
        this.tableLoading = false
      }).catch(err => {
        this.tableLoading = false
      })
    },
    renew (id) {
      this.tableLoading = true
      renew(id).then(res => {
        this.getPage(this.page)
        this.tableLoading = false
      }).catch(err => {
        this.tableLoading = false
      })
    },
    detail (row) {
      this.detailForm = {}
      this.detailForm = Object.assign({}, row)
      this.detailVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.blue {
  color: #409eff;
  cursor: pointer;
}
</style>
