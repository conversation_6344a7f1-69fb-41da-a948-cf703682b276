<template>
  <div class="execution">
    <basic-container>
      <el-row type="flex">
          <el-tabs tab-position="left" style="width:210px" v-model="activeName">
            <el-tab-pane :label="item" v-for="(item, index) in tabs" :key="index" :name="index+''">
            </el-tab-pane>
          </el-tabs>
        <el-col :span="16">
          <avue-form :option="option" :value="form" @submit="submit" @empty="empty"> </avue-form>
        </el-col>
      </el-row>
    </basic-container>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";

export default {
  name: "garbage",
  data() {
    return {
      tabs: [
        "车辆进出台账",
        "挖机装车台账",
        "泥尾台账",
        "泥尾统计运费",
        "土票领发统计",
        "机械台班装车数统计",
        "挖机装车台班费用统计",
        "挖机装车和台班",
      ],
      form: {},
      option: {
        labelWidth: 100,
        border: true,
        align: "center",
        menuAlign: "center",
        submitText: "查询并下载",
        emptyText: "重置",
        column: [
          {
            label: "项目名称",
            prop: "projectInfoId",
            type: "select",
            props: {
              label: "projectName",
              value: "id",
            },
            // rules: [
            //   {
            //     required: true,
            //     message: '请选择项目名称',
            //     trigger: 'change'
            //   },
            // ],
            dicUrl: "/chain/projectinfo/list",
            filterable: true, //是否可以搜索
          },
          {
            label: "签单时间",
            prop: "goDatetime",
            type: "datetimerange",
            format:'yyyy-MM-dd HH:mm:ss',
            valueFormat:'yyyy-MM-dd HH:mm:ss',
          },
        ],
      },
      activeName:'0',
    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        // addBtn: this.permissions['chain:companywaybill:add'] ? true : false,
        // delBtn: this.permissions['chain:companywaybill:del'] ? true : false,
        // editBtn: this.permissions['chain:companywaybill:edit'] ? true : false,
        viewBtn: this.permissions["chain:companywaybill:get"] ? true : false,
      };
    },
  },
  methods: {
    expotOut,
    submit(form,done){
      if(form.projectInfoId==''){
        this.$message.error('请选择项目名称')
        done()
        return false
      }
      this.exOut(Number(this.activeName)+1,form)
      setTimeout(()=>{
        done()
      },3000)
    },
    empty(){
      console.log('qingkong');
    },
    exOut(value,form) {
      console.log(value);
      let params = Object.assign({}, form);
      if (params.goDatetime && params.goDatetime.length > 0) {
        params.goDatetimeStart = params.goDatetime[0];
        params.goDatetimeEnd = params.goDatetime[1];
      }
      delete params.goDatetime;
      let url = '/chain/excelExport/createByCode'
      let name = '车辆进出台账'
      params.code = 'truckVisit'
      switch(value){
        case 2:
          params.code = 'truckSoil'
          url = '/chain/excelExport/createByCode'
          name = '挖机装车台账'
          break;
        case 3:
          params.code = 'garbageExcel'
          url = '/chain/excelExport/createByCode'
          name = '泥尾台账'
          break;
        case 4:
          params.code = 'garbageCountExcel'
          url = '/chain/excelExport/createByCode'
          name = '泥尾统计运费'
          break;
        case 5:
          params.code = 'landStampCountExcel'
          url = '/chain/excelExport/createByCode'
          name = '土票领发统计'
          break;
        case 6:
          params.code = 'mechanicalLoadingCountExcel'
          url = '/chain/excelExport/createByCode'
          name = '机械台班装车数统计'
          break;
        case 7:
          params.code = 'excavatorCostCountExcel'
          url = '/chain/excelExport/createByCode'
          name = '挖机装车台班费用统计'
          break;
        case 8:
          params.code = 'machineOwnerAndLegerExcel'
          url = '/chain/excelExport/createByCode'
          name = '挖机装车和台班'
          break;
      }

      this.expotOut( params,url,name);
    },
  },
};
</script>

<style lang="scss" scoped></style>
