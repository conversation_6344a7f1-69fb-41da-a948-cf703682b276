export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: false,
  searchLabelWidth:100,
  searchSpan:8,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  addBtn:false,
  editBtn:false,
  delBtn:false,
  searchMenuSpan: 6,
  column: [
    {
      label: "支付单号",
      prop: "paymentNo",
      minWidth:160,
      overHidden:true,
    },
    {
      label: "结算申请人",
      prop: "agentInfoId",
      type: "select", // 下拉选择
      props: {
        label: "agentName",
        value: "id",
      },
      dicUrl: "/chain/companypayment/getAgentList",
      minWidth:96,
      overHidden:true,
    },
    {
      label: "承运人",
      prop: "payeeName",
      minWidth:80,
      overHidden:true,
    },
    {
      label: "所属项目",
      prop: "projectInfoId",
      type: "select", // 下拉选择
      props: {
        label: "projectName",
        value: "id",
      },
      dicUrl: "/chain/companypayment/getProjectList",
      minWidth:150,
      overHidden:true,
    },

    {
      label: "结算单数量",
      prop: "settleCnt",
      minWidth:96,
      overHidden:true,
    },
    {
      label: "结算运单数量",
      prop: "waybillCnt",
      minWidth:120,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "结算合计金额(元)",
      prop: "settleAmt",
      minWidth:120,
      overHidden:true,
    },
    {
      label: "状态",
      prop: "statusName",
      minWidth:80,
      overHidden:true,
      sortable:"custom",
      // type:'select',
      // dicData: [
      //   {
      //     label: '待支付',
      //     value: '1'
      //   },
      //   {
      //     label: '已审批',
      //     value: '2'
      //   },
      //   {
      //     label: '已驳回',
      //     value: '3'
      //   },
      //   {
      //     label: '已支付',
      //     value: '4'
      //   },
      // ],
    },
  ],
};
