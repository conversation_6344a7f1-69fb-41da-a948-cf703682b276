<template>
  <div class="block">
    <div class="infoBox">
      <h3>用户登录</h3>
      <div class="stand">
        <div
          :class="[standNum == index ? 'stand_item active' : 'stand_item']"
          v-for="(item, index) in standing"
          :key="index"
          @click="standSkip(item, index)"
        >
          {{ item.title }}
        </div>
      </div>
      <el-form
        :model="formData"
        status-icon
        :rules="formRules"
        ref="form"
        label-width="100px"
        class="demo-formData"
      >
        <el-form-item prop="phone">
          <el-input
            v-model="formData.phone"
            :maxlength="11"
            placeholder="请输入手机号码"
          >
            <img
              class="iconImg"
              slot="prefix"
              src="../../static/login/phone.png"
              alt=""
            />
            <template slot="suffix">
              <div class="but" @click="skipPopup">
                <span class="codeBtn" style="font-size: 0.16rem">{{
                  msgText
                }}</span>
              </div>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code">
          <el-input
            v-model="formData.code"
            placeholder="请输入验证码"
            @click.native.prevent="handleDone"
          >
            <img
              class="iconImg"
              slot="prefix"
              src="../../static/login/code.png"
              alt=""
          /></el-input>
        </el-form-item>
        <div class="register">
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 0.16rem"
            @click="$router.push({ path: '/companyAuth' })"
            >立即注册企业</el-link
          >
          <div
            class="line"
            style="color: #5663fa; font-size: 0.16rem; margin: 0 0.05rem"
          >
            |
          </div>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 0.16rem"
            @click="$router.push({ path: '/garbageRegister' })"
            >快速注册泥尾</el-link
          >
        </div>
        <div class="check" style="margin-top: 0.1rem">
          <el-checkbox v-model="checked" checked></el-checkbox>
          <span style="margin-left: 10px"
            >我已阅读并同意<a
              href="https://jyb.szyl8.cn/h5/#/pages/privacyAgreement/privacyAgreement"
              target="_blank"
              >《隐私协议》</a
            >和<a
              href="https://jyb.szyl8.cn/h5/#/pages/customerService/customerService"
              target="_blank"
              >《软件使用服务协议》</a
            ></span
          >
        </div>
        <el-form-item>
          <el-button type="primary" @click.native.prevent="handleDone"
            >提交</el-button
          >
        </el-form-item>
      </el-form>

      <!-- 图形验证码弹框 -->
      <div class="popup" v-show="popupShow">
        <div class="popup_title">请输入验证码并确认</div>
        <div class="iptBox">
          <span class="iptDesc">验证码：</span>
          <el-input
            v-model="imgCode"
            placeholder="请输入验证码，不区分大小写"
            clearable
          ></el-input>
        </div>

        <div id="v_container" @click="btncode"></div>
        <div class="instructions">看不清？点击图片换一张</div>
        <div class="btnBox">
          <el-button @click="popupShow = false">取消</el-button>
          <el-button type="primary" @click="next">确认</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { validatePhone } from "@/util/validate";
import { sendCode } from "@/api/upms/phone";
import { GVerify } from "@//util/GVerify";
import { encryptDes, decryptDes } from "@/util/des.js";
const MSGINIT = "发送验证码",
  MSGSCUCCESS = "${time}秒",
  MSGTIME = 60;
export default {
  name: "codePhoneDiv",
  props: {
    //1、登录；2、绑定；3、解绑
    type: {
      type: String,
    },
    phone: {
      type: String,
    },
  },
  data() {
    var validatorPhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入手机号码"));
      } else {
        callback();
      }
    };
    var validatorCode = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    return {
      msgText: MSGINIT,
      msgTime: MSGTIME,
      msgKey: false,
      standNum: 0,
      standing: [
        {
          title: "施工单位员工",
          id: "2",
        },
        {
          title: "车队长",
          id: "1",
        },
        {
          title: "挖机车主",
          id: "6",
        },
        {
          title: "泥尾端",
          id: "7",
        },
      ],
      formData: {
        phone: "",
        code: "",
        roleType: 2,
      },
      formRules: {
        phone: [{ required: true, trigger: "blur", validator: validatorPhone }],
        code: [{ required: true, trigger: "blur", validator: validatorCode }],
      },
      checked: false,
      verifyCode: "",
      imgData: "", //图片验证码
      imgCode: "",
      popupShow: false,
    };
  },
  created() {},
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    standSkip(item, index) {
      this.standNum = index;
      this.formData.roleType = item.id; 
    },

    skipPopup() {
      if (this.msgKey) return;
      if (validatePhone(this.formData.phone)[0]) {
        this.$message.error("请输入正确的手机号");
        return;
      }
      this.handleSend();
      // 图片验证码
      // this.verifyCode = new GVerify("v_container");
      // this.imgData = this.verifyCode.options.code;
      //  this.popupShow = true;
    },
    // 切换图片验证码
    btncode() {
      this.imgData = this.verifyCode.options.code.substring(
        this.verifyCode.options.code.length - 4
      );
    },
    next() {
      var imgCode = this.toLower(this.imgCode);
      var imgData = this.toLower(this.imgData);
      console.log(imgCode, imgData);
      if (imgCode != imgData) {
        this.$message.error("图形验证码错误,请重新输入");
        return;
      }
      this.popupShow = false;
      this.imgCode = "";
      this.handleSend();
    },
    // 设置cookie
    setCookie(c_name, c_pwd, c_state, exdays) {
      const exdate = new Date();
      exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays); // 保存的天数
      window.document.cookie =
        "username" + "=" + c_name + ";path=/;expires=" + exdate.toGMTString();
      window.document.cookie =
        "password" + "=" + c_pwd + ";path=/;expires=" + exdate.toGMTString();
      window.document.cookie =
        "state" + "=" + c_state + ";path=/;expires=" + exdate.toGMTString();
    },
    // 读取cookie
    getCookie() {
      if (document.cookie.length > 0) {
        const arr = document.cookie.split("; ");
        for (let i = 0; i < arr.length; i++) {
          const arr2 = arr[i].split("=");
          console.log(arr[2]);
          if (arr2[0] === "username") {
            this.username = arr2[1];
          } else if (arr2[0] === "password") {
            this.password = arr2[1];
          } else if (arr2[0] === "state") {
            this.checked = Boolean(arr2[1]);
          }
        }
      }
    },
    // 清除cookie
    clearCookie: function () {
      this.setCookie("", "", false, -1);
    },
    handleSend() {
      if (this.msgKey) return;
      if (validatePhone(this.formData.phone)[0]) {
        this.$message.error("请输入正确的手机号");
        return;
      }
      this.msgKey = true;
      var key = Math.floor(Date.now() / 3000000) + 168;
      var mobile = encryptDes(this.formData.phone, key);
      var deviceNewNo = encryptDes("AB288758-C1D0-1089-B513-YILU98DB5215", key);
      console.log(this.formData.roleType)
      sendCode({
        mobile: mobile,
        type: this.type,
        roleType: this.formData.roleType,
        deviceNewNo: deviceNewNo,
      })
        .then((response) => {
          this.msgKey = false;
          if (response.data.code == "0") {
            this.$message.success(response.data.msg || "验证码发送成功");
            this.msgText = MSGSCUCCESS.replace("${time}", this.msgTime);
            this.msgKey = true;
            const time = setInterval(() => {
              this.msgTime--;
              this.msgText = MSGSCUCCESS.replace("${time}", this.msgTime);
              if (this.msgTime == 0) {
                this.msgTime = MSGTIME;
                this.msgText = MSGINIT;
                this.msgKey = false;
                clearInterval(time);
              }
            }, 1000);
          } else {
            this.$message.error(response.data.msg);
          }
        })
        .catch(() => {
          this.msgKey = false;
        });
    },
    handleDone() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.checked) {
            this.$message({
              showClose: true,
              message: "请先勾选协议",
              type: "success",
            });
            return;
          }
          this.$emit("handleDone", this.formData, this.type);
        }
      });
    },
    // 字母转小写
    toLower(str) {
      var arr = "";
      for (var i = 0; i < str.length; i++) {
        arr = arr + str[i].toLowerCase();
      }
      return arr;
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
.el-form-item {
  margin-bottom: 0.25rem;
}
/deep/.el-form-item__content {
  margin-left: 0 !important;
}
.el-button {
  width: 100%;
  height: 0.42rem;
  font-size: 0.14rem;
}
.el-link.el-link--primary {
  color: #5663fa;
}
.el-button--primary {
  color: #fff;
  background-color: #5663fa;
  border-color: #5663fa;
}

.el-form-item__error {
  font-size: 0.12rem;
}
.block {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.infoBox {
  width: 4.5rem;
  height: 5.3rem;
  background: #fff;
  font-weight: 400;
  font-size: 18px;
  color: #636363;
  padding: 0.3rem 0.2rem;
  border-radius: 0.2rem;
  position: absolute;
  top: 13%;
  right: 10%;

  h3 {
    font-weight: 700;
    font-size: 0.32rem;
    color: #2f2f2f;
    text-align: center;
    margin-bottom: 0.1rem;
  }

  .stand {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.2rem;
    .stand_item {
      width: 25%;
      text-align: center;
      font-weight: 400;
      font-size: 0.16rem;
      color: #636363;
      line-height: 0.35rem;
      cursor: pointer;
    }

    .active {
      color: #5663fa;
    }

    .active::after {
      content: "";
      width: 40%;
      display: block;
      margin: 0 auto;
      border-bottom: 0.02rem solid #5663fa;
      border-radius: 0.02rem;
    }
  }
  .iconImg {
    width: 0.22rem;
    height: 0.22rem;
    margin: auto 0;
  }
  .register {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 0.18rem;
  }
  .check {
    color: #353347;
    font-size: 0.16rem;
    margin-top: 0.3rem;
    margin-bottom: 0.18rem;
    a {
      color: #5663fa;
    }
  }
  .but {
    width: 1rem;
    padding: 0 0.1rem;
    cursor: pointer;
    position: relative;
    // border-left: 0.01rem solid #e2e2e2;
  }
  .but::before {
    content: "";
    height: 0.22rem;
    display: block;
    margin: 0 auto;
    border-left: 0.01rem solid #eaeaea;
    border-radius: 0.02rem;
    position: absolute;
    top: 50%;
    left: 0;
    margin-top: -0.11rem;
  }
  .codeBtn {
    font-weight: 400;
    font-size: 16px;
    color: #5663fa;
  }

  .popup {
    width: 100%;
    height: 100%;
    background: #fff;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
    font-size: 0.16rem;
    color: #353347;
    padding: 0 .3rem;
    #v_container{
      display: flex;
      justify-content: center;
      margin: .2rem 0;
    }
    .instructions{
      text-align: center;
    margin-bottom: .3rem;

    }
    .popup_title {
      width: 100%;
      font-weight: 600;
      line-height: 1rem;
      text-align: center;
    }

    .iptBox {
      display: flex;
      justify-content: left;
      align-items: center;

      .iptDesc {
        width: 1.2rem;
      }
    }
    .btnBox{
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
