<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="50%"
      :title="title"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <avue-form
        :option="option"
        ref="dialogForm"
        v-model="dialogForm"
        @submit="submit"
      >
      </avue-form>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import {
  getFee,getBalance,addObj
} from "@/api/chain/directWalletRecharge";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
    title:{
      type: String,
      default: "提现",
    }
  },
  data() {
    return {
      dialogForm: {},
      option: {
        labelWidth: 100,
        submitText: "确认提现",
        position: "left",
        cancelBtn: true,
        submitBtn:true,
        emptyBtn: false,
        disabled: this.title=='查看',
        submitBtn:this.title=='提现',
        column: [
        {
            label: "类型",
            prop: "applyType",
            type: "select", // 下拉选择
            disabled: true,
            display:this.title=='查看',
            order:1,
            dicData: [
              {
                value: "1",
                label: "充值",
              },
              {
                value: "2",
                label: "提现",
              },
            ],
            span: 24,
          },
          {
            label: "项目名称",
            prop: "projectInfoId",
            span: 24,
            row: true,
            order:1,
            type: 'select',   // 下拉选择
            props: {
              label: 'projectName',
              value: 'id'
            },
            dicUrl: 'chain/projectinfo/getDirectPayProjectList',
            change:({value})=>{
              if(this.title=='提现'){
                this.dialogForm.amount = undefined
              }
              if(value&&this.title=='提现'){
                  let form = this.$refs.dialogForm;
                  let obj = form.DIC.projectInfoId.find((item) => item.id == value)
                  this.dialogForm.platformBranchBankAccountName = obj.withdrawBankAccountName
                  this.dialogForm.platformBranchBankAccount = obj.withdrawBankAccount
                  this.dialogForm.platformBranchId = obj.platformBranchId
                  this.dialogForm.platformBranchNsrmc = obj.platformBranchNsrmc
                  this.dialogForm.companyName = obj.companyAuthName
                  this.dialogForm.availableBalance = obj.balance
              }
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
           {
            label: "提现企业",
            prop: "platformBranchNsrmc",
            span: 24,
            row: true,
            order:3,
            disabled: true,
            placeholder:" ",
          },
          {
            label: "提现银行",
            prop: "platformBranchBankAccountName",
            span: 24,
            order:3,
            row: true,
            disabled: true,
            placeholder:" ",
          },
          {
            label: "提现账户",
            prop: "platformBranchBankAccount",
            span: 24,
            row: true,
            order:3,
            disabled: true,
            placeholder:" ",
          },
          {
            label: "项目钱包金额",
            prop: "availableBalance",
            span: 24,
            row: true,
            order:3,
            display:this.title=='提现',
            disabled: true,
            placeholder:" ",
          },
          {
            label: "收款企业",
            prop: "companyName",
            span: 24,
            row: true,
            order:this.title=="查看"?2:3,
            disabled: true,
            placeholder:" ",
          },
          {
            label: "提现金额",
            prop: "amount",
            span: 24,
            type: 'number',
            order:4,
            controls: false,
            minRows: 0,
            maxRows: *********.99,
            precision: 2,
            row: true,
            blur:({value})=>{
              if(value&&!!this.dialogForm.projectInfoId&&this.title=='提现'){
                //type 1充值  2提现
                getFee({projectInfoId:this.dialogForm.projectInfoId,amount:value,type:2}).then(res=>{
                  this.dialogForm.platformFee = res.data.data.fee
                  this.dialogForm.actualAmount = res.data.data.actualAmount
                })
              }
            },
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "手续费",
            prop: "platformFee",
            span: 24,
            order:4,
            row: true,
            disabled:true,
            placeholder:" ",
          },
          {
            label: "实际到账金额",
            prop: "actualAmount",
            order:4,
            span: 24,
            row: true,
            disabled:true,
            placeholder:" ",
          },
          {
            label: "收款银行",
            prop: "withdrawBankAccountName",
            span: 24,
            order:this.title=="查看"?2:5,
            row: true,
            // rules: [
            //   {
            //     required: true,
            //     message: "请输入",
            //     trigger: "blur",
            //   },
            // ],
          },
          {
            label: "收款账户",
            prop: "withdrawBankAccount",
            order:this.title=="查看"?2:5,
            span: 24,
            row: true,
            // rules: [
            //   {
            //     required: true,
            //     message: "请输入",
            //     trigger: "blur",
            //   },
            // ],
          },
           {
            label: "银行流水号",
            prop: "frontLogNo",
            span: 24,
            display:this.title=='查看',
            disabled:true,
            placeholder:" ",
          },
          {
            label: "备注",
            prop: "remark",
            span: 24,
            placeholder:this.title=='查看'?" ":'',
          },
          {
            label: "审核备注",
            prop: "auditRemark",
            type:'textarea',
            minRows:3,
            maxRows:5,
            span: 24,
            disabled:true,
            placeholder:" ",
            display:this.title =="查看"
          },
          {
            label: "银行凭证",
            prop: "frontLogPic",
            span: 24,
            type: "upload",
            listType: "picture-img",
            disabled:true,
            action: "/upms/file/upload?fileType=image&dir=directWalletRecharge/",
            propsHttp: {
              url: "link",
            },
            loadText: "图上上传中，请稍等",
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
            placeholder:" ",
            display:this.title=='查看'
            // listType: "picture-img",
          },
          {
            label: "提现时间",
            prop: "applyDatetime",
            type: "datetime",
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            span: 24,
            placeholder:" ",
            display:this.title=='查看'

          },
          {
            label: "提现人",
            prop: "applyName",
            span: 24,
            placeholder:" ",
            display:this.title=='查看'
          },
          {
            label: "确认时间",
            prop: "auditDatetime",
            type: "datetime",
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            span: 24,
            placeholder:" ",
            display:this.title=='查看'
          },
          {
            label: "确认人",
            prop: "auditName",
            span: 24,
            placeholder:" ",
            display:this.title=='查看'
          },
        ],
      },
    };
  },
  created() {},
  mounted () {
    this.dialogForm = this.info
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    submit(form, done) {
      let param = Object.assign({},form)
      delete param.frontLogPic
      param.applyType = 2 //提现
      addObj(param).then(res=>{
        this.cancelModal()
        this.$emit("refreshChange");
        done()
      }).catch(()=>{
        done()
      })
    }
  },
};
</script>

<style lang="scss" scoped></style>
