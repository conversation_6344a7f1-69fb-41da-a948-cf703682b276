<template>
  <div class="companyposition2">
    <basic-container>
      <div class="min">
        <el-row type="flex" :gutter="10">
          <el-col style="min-Width:190px;max-width:220px">
            <el-card class="box-card" shadow="never">
              <div style="padding-left:14px">
                <el-button type="primary" size="small" @click="addPos">
                  新 增</el-button>
              </div>
              <div class="flex flex-between flex-item-center topName">
                <div class="name">职位名称</div>
                <div class="tools">操作</div>
              </div>
              <div class="position-list">
                <div
                  class="position-item"
                  v-for="(item, index) in listsData"
                  :key="index"
                  :class="{ active: item.id == selectId }"
                  @click="changePosition(item.id, item.childMenuIds, index)"
                >
                  <div class="name">{{ item.positionName }}</div>
                  <div class="tools del" @click="delPosition(item)">删除</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col class="user__main">
            <div style="padding: 10px 0; font-weight: 500">权限详情</div>
            <el-card shadow="never">
              <!-- :renderContent="renderContent" -->
              <!-- :default-expand-all="true" -->
              <el-tree
                ref="tree"
                :data="menuData"
                show-checkbox
                node-key="id"
                :default-checked-keys="selectIds"
                :props="{
                  children: 'children',
                  label: 'name',
                }"
              >
              </el-tree>

              <!-- <div class="lines" v-for="(item, index) in menuData" :key="index">
                <div style="height: 10px"></div>
                <el-checkbox
                  :indeterminate="item.indeterminate"
                  v-model="item.checked"
                  @change="handleCheckAllChange($event, index)"
                >{{ item.name }}</el-checkbox
                >

                <div style="margin: 20px">
                  <el-checkbox
                    v-for="(it, ind) in item.children"
                    :key="it.id"
                    @change="handleChange($event, index, ind)"
                    v-model="it.checked"
                  >{{ it.name }}</el-checkbox
                  >
                </div>
              </div> -->

              <div class="right">
                <el-button
                  type="primary"
                  size="medium"
                  @click="updatePs"
                  v-show="menuData && menuData.length > 0"
                >
                  保 存</el-button
                >
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </basic-container>
  </div>
</template>

<script>
import {
  getPage,
  getObj,
  addObj,
  putObj,
  delObj,
  systemmenu,
  getList,
} from "@/api/chain/companyposition";
import { tableOption } from "@/const/crud/chain/companyposition2";
import { mapGetters } from "vuex";

export default {
  name: "companyposition2",
  data() {
    return {
      selectIndex: 0,
      selectId: "",
      selectIds: [],
      form: {},
      noMenuData: [],
      menuData: [],
      listsData: [],
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
    };
  },
  created() {
    this.getList();
  },
  updated() {
    // this.changeTreeClass();
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:companyposition:add"] ? true : false,
        delBtn: this.permissions["chain:companyposition:del"] ? true : false,
        editBtn: this.permissions["chain:companyposition:edit"] ? true : false,
        viewBtn: this.permissions["chain:companyposition:get"] ? true : false,
      };
    },
  },
  methods: {
    // renderContent(h, { node, data }) {
    //   let className = ""; // perms这个是后台数据区分普通tree节点和横向tree节点的标志  各位要根据实际情况做相对应的修改
    //   console.info(node.level);
    //   if (node.level == 1) {
    //     className = "level1";
    //   }
    //   if ( !data.children && !node.parent.childNodes.some(v=>v.data.children)) {
    //     className = "especially";
    //   }
    //   if (node.level != 1 && data.children) {
    //     className = "level2";
    //   }

    //   return (
    //     // 在需要做横向排列的模块做标记

    //     <div class={className}> {node.label} </div>
    //   );
    // }, // 改变tree节点样式

    // changeTreeClass() {
    //   // 找到之前做标记的class

    //   var classDomList = document.getElementsByClassName("especially"); // 改变这几个样式
    //   var classLevelDomList = document.getElementsByClassName("level1"); // 改变这几个样式
    //   var classLevel2DomList = document.getElementsByClassName("level2");

    //   for (var i = 0; i < classDomList.length; i++) {
    //     classDomList[i].parentNode.style.cssText = "float: left";

    //     classDomList[i].parentNode.className =
    //       "el-tree-node__content option-wrapper";

    //     let parentN = classDomList[i].parentNode.parentNode;
    //     parentN.style.marginLeft = "60px";
    //   }
    //   for (var i = 0; i < classLevelDomList.length; i++) {
    //     classLevelDomList[i].parentNode.parentNode.style.borderBottom =
    //       "1px solid #EBEEF5";
    //     classLevelDomList[i].parentNode.parentNode.style.padding = "10px 0";
    //   }
    //   for (var i = 0; i < classLevel2DomList.length; i++) {
    //     if(i!=classLevel2DomList.length-1){
    //       classLevel2DomList[i].parentNode.parentNode.style.padding = "5px 0";
    //     }

    //   }
    // },
    updatePs() {
      // let pri = [];
      // this.menuData.map((item, index) => {
      //   if (item.checked || item.indeterminate) {
      //     pri.push(item.id);
      //     item.children.map((its) => {
      //       if (its.checked) {
      //         pri.push(its.id);
      //       }
      //     });
      //   }
      // });
      // console.info(this.selectId);
      let checkIds = this.$refs.tree.getCheckedKeys(true);
      console.log(checkIds);
      let data = {
        id: this.selectId,
        systemMenuIds: checkIds.join(","),
      };
      putObj(data)
        .then((res) => {
          this.$message({
            type: "success",
            message: "修改成功",
          });
          this.getList();
        })
        .catch((err) => {
          this.$message({
            type: "info",
            message: "修改失败",
          });
        });
    },
    changePosition(id, ids, index) {
      this.selectId = id;
      this.selectIndex = index;
      this.selectIds = ids ? ids.split(",") : [];
      this.$refs.tree.setCheckedNodes(this.selectIds);
      console.info(this.selectIds);
      // this.setPerm();
    },

    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.cities.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.cities.length;
    },
    delPosition(item) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(item.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          if (this.listsData && this.listsData.length == 1) {
            this.menuData = [];
          }
          this.getList();
        })
        .catch(function (err) {});
    },
    addPos() {
      this.$prompt("请输入职位名称", "新增职位", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /^.{3}/,
        inputErrorMessage: "请输入3个字以上的职位名称",
      })
        .then(({ value }) => {
          let data = {
            positionName: value,
          };
          addObj(data).then((res) => {
            this.$message({
              type: "success",
              message: "新增成功",
            });
            this.getList();
          });
          // this.$message({
          //   type: 'success',
          //   message: '你的邮箱是: ' + value
          // });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消输入",
          });
        });
    },

    getChildren(item) {
      if (item.children) {
        if (item.children.length > 0) {
          item.children.map((itss) => {
            itss.deptName = `${itss.deptName}(${itss.count})`;
            this.getChildren(itss);
          });
        }
      }
    },
    getsystemmenu() {
      systemmenu().then((res) => {
        // res.data.data.map((item, index) => {
        //   let arr = {
        //     name: item.name,
        //     checked: false,
        //     indeterminate: false,
        //     id: item.id,
        //     children: [],
        //   };
        //   if (item.children) {
        //     item.children.forEach((it) => {
        //       arr.children.push({
        //         name: it.name,
        //         checked: false,
        //         id: it.id,
        //       });
        //     });
        //   }
        //   this.menuData.push(arr);
        // });
        this.menuData = res.data.data;
        // this.noMenuData = JSON.stringify(this.menuData);
        // this.setPerm();
      });
    },
    setPerm() {
      if (!this.listsData[this.selectIndex].systemMenuIds) {
        this.menuData = JSON.parse(this.noMenuData);
      } else {
        let systemMenuIds =
          this.listsData[this.selectIndex].systemMenuIds.split(",");
        console.log(systemMenuIds);
        let arrs = [];
        this.menuData.map((item, index) => {
          let arr = [];
          let count = 0;
          if (item.children) {
            item.children.map((it) => {
              let select = systemMenuIds.includes(it.id);
              if (select) {
                count++;
              }
              arr.push({
                name: it.name,
                checked: select,
                id: it.id,
              });
            });
          }
          let indeterminate = false;
          if (count > 0 && count < arr.length) {
            indeterminate = true;
          }
          arrs.push({
            name: item.name,
            checked: count == arr.length,
            indeterminate,
            id: item.id,
            children: arr,
          });
        });
        this.menuData = arrs;
      }
    },
    getList() {
      getList().then((res) => {
        if (res.data.data) {
          this.listsData = res.data.data;
          if (!this.selectId && this.listsData && this.listsData.length > 0) {
            this.selectIndex = 0;
            this.selectId = this.listsData[0].id;
            this.selectIds = this.listsData[0].childMenuIds
              ? this.listsData[0].childMenuIds.split(",")
              : [];
            console.info(this.selectIds);
          }
          if (
            this.menuData.length == 0 &&
            this.listsData &&
            this.listsData.length > 0
          ) {
            this.getsystemmenu();
          }
        }
      });
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/.option-wrapper {
  padding: 0 !important;
}
.custom-tree {
  display: flex;
}
.right {
  padding-top: 20px;
  text-align: right;
}
.topName{
  padding: 4px 10px;
  border-radius: 4px;
  margin-top: 10px;
}
.position-item {
  display: flex;
  align-content: center;
  justify-content: space-between;
  padding: 6px 10px;
  border-radius: 2px;
  margin-top: 4px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  &:hover{
    background-color: #e8f4ff;
    .name{
      color: #1890ff;
    }
  }
}
.del {
  color: #1890ff;
  cursor: pointer;
}

.box-card {
  min-height: calc(100vh - 200px);
}
/deep/ .min {
  min-height: calc(100vh - 200px);
  .el-card__body{
    padding-left: 6px;
    padding-right: 6px;
    padding-top: 10px;
  }
}
.lines {
  border-bottom: 1px solid #ccc;
  padding-bottom: 20px;
}
.active {
  background: #e8f4ff;
  .name{
    color: #1890ff;
  }
}
</style>
