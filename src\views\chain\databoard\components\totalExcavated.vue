<template>
  <div class="totalExcavated">
    <el-drawer size="80%" title="总出土量" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <searchInfo :info="paramsSearch" :isShowProject="false" :type="2" source="4" @searchChange="searchChange"
          @changeStatus="changeStatus" @changeUnit="changeUnit" @exOut="exOut">
          <template slot="count">
            <div class="count">总出土量 <span>{{ count }}{{paramsSearch.weightUnit}}</span></div>
          </template>
        </searchInfo>
        <projectList v-if="projectList && projectList.length > 0" text="" @changeProject="changeProject"
          :active="paramsSearch.projectInfoId" :projectList="projectList" :defaultProp="defaultProp"
          :unit="paramsSearch.weightUnit">
        </projectList>
        <avue-crud ref="crud" :page.sync="page" :data="tableData" :table-loading="tableLoading" :option="tableOption"
          v-model="form" :search.sync="search" @on-load="getPage">
          <template slot="weightTonsHeader" slot-scope="{column}">
            <span>{{ (column || {}).label }}({{paramsSearch.weightUnit}})</span>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getExcavatedNumberPage as getPage, getProjectDynamicInfoByExcavatedNumber } from "@/api/chain/board";
import searchInfo from './searchInfo';
import { exportOut } from "@/util/down.js";
import projectList from './projectList';

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  components: {
    searchInfo,
    projectList,
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      search: {},
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        // maxHeight:[800],
        // height:'auto',
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: true,
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        header: false,
        column: [
          {
            label: "项目名称",
            prop: "projectInfoName",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "运单号",
            prop: "no",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "运输方式",
            prop: "tpMode",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "出土量",
            prop: 'weightTons',
            headerslot:true,
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "出场签单时间",
            prop: "goDatetime",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "泥尾名称",
            prop: "garbageName",
            minWidth: 120,
            overHidden: true,
          },
        ],
      },
      count: 0,
      projectInfo: {},
      projectList: [],
      defaultProp: {
        label: "projectName", value: "id", cnt: "weightTons"
      },
    }
  },
  created () {
    this.paramsSearch = Object.assign({}, this.info)
    console.log(this.paramsSearch);
    this.count = this.info.count
    this.getProject()
  },
  mounted () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    searchChange (params, done) {
      // params = this.filterForm(params);
      params.projectInfoId = this.paramsSearch.projectInfoId
      this.paramsSearch = Object.assign({},params);
      this.page.currentPage = 1;
      this.getPage(this.page);
      this.getProject()
      console.log(this.paramsSearch);
      done();
    },
    getPage (page) {
      this.tableLoading = true;
      getPage(
      Object.assign(
      {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    getProject () {
      let param = {
        startDate: this.paramsSearch.startDate,  //开始时间
        endDate: this.paramsSearch.endDate,  //结束时间
        weightUnit: this.paramsSearch.weightUnit,
        checkReturnAll: true,
      }
      getProjectDynamicInfoByExcavatedNumber(param).then(res => {
        this.projectInfo = res.data.data
        this.projectList = this.paramsSearch.checkDynamic == 1 ? this.projectInfo.projectDynamicList : this.projectInfo.projectList
        this.count = this.projectList[0].weightTons || 0
      })
    },
    closeLoading () {
      this.tableLoading = false
    },
    changeStatus (val) {
      //更换项目列表  有些表格需要变换数据
      this.paramsSearch.checkDynamic = val
      this.paramsSearch.projectInfoId = ""
      this.projectList = val == 1?this.projectInfo.projectDynamicList:this.projectInfo.projectList
      this.page.currentPage = 1;
      this.getPage(this.page);
    },
    changeUnit (val) {
      //更换项目列表  有些表格需要变换数据
      this.paramsSearch.weightUnit = val
      this.tableLoading = true
      this.searchChange(this.paramsSearch, this.closeLoading)
    },
    changeProject (val) {
      console.log(val);
      this.paramsSearch.projectInfoId = val || ''
      this.page.currentPage = 1;
      this.getPage(this.page);
    },
    exOut (params, done) {
      let url = "/chain/statisticsboard/exportExcavatedNumberExcel";
      params.projectInfoId = this.paramsSearch.projectInfoId
      params.garbageExportType = 1
      exportOut(params, url, "总出土量", 'get').then(res => {
        done()
      }).catch(() => {
        done()
      })
    },
  },

};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

.count {
  margin-bottom: 10px;

  span {
    font-size: 16px;
    font-weight: 700;
  }
}
</style>
