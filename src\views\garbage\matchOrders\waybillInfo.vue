<template>
  <div class="driverTaskDetail">
    <el-drawer size="80%"
               title="运单信息"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <basic-container>
        <avue-crud ref="crud"
                   :data="tableData"
                   :page.sync="page"
                   @sort-change="sortChange"
                   :table-loading="tableLoading"
                   :option="tableOption"
                   v-model="form"
                   @on-load="getPage">
        </avue-crud>
      </basic-container>
    </el-drawer>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getWaybillList } from '@/api/chain/matchOrders'
import { expotOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {},
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      tableData: [],
      btnLoading: false,
      paramsSearch: { },
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 12,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: true,
        delBtn: false,
        menu: false,
        searchMenuSpan: 6,
        index: false,
        refreshBtn: false,
        column: [
          {
            label: "入场编号",
            prop: "garbageEntranceNo",
          },
          {
            label: "车牌",
            prop: "garbageTruckCode",
          },
          {
            label: "土质",
            prop: "garbageSoilType",
          },
          {
            label: "入场签单时间",
            prop: "garbageDatetime",
          },
          {
            label: "结算周期",
            prop: "settleCycle",
          },
        ],
      },
    }
  },
  created () {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage (page, params = {}) {
      this.tableLoading = true
      getWaybillList(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        id: this.info.id,
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },

  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
</style>
