import {isMobileNumber} from '@/util/validate'
export const tableOption = (value)=>{
  let that = value
  console.log(that);
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    index:true,
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    dialogClickModal:false,
    dialogWidth:600,
    excelBtn: false,
    printBtn: false,
    addBtn: false,
    viewBtn: false,
    searchSpan:8,
    searchLabelWidth:110,
    searchMenuSpan: 6,
    labelWidth:120,
    menu:false,
    column: [
      {
        label: "类型",
        prop: "type",
        type: "select", // 下拉选择
        display:false,
        sortable: true,
        dicData: [
          {
            label: "售票",
            value: "1",
          },
          {
            label: "退票",
            value: "2",
          },
        ],
        minWidth:70,
        overHidden:true,
      },
      {
        label: "客户手机号码",
        prop: "garbageCustomerMobile",
        search:true,
        span:24,
        display:true,
        sortable: true,
        maxlength:11,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
          {
            validator: isMobileNumber,
            trigger: "blur",
          },
        ],
        minWidth:120,
        overHidden:true,
      },
      {
        label: "客户名称",
        prop: "garbageCustomerName",
        search:true,
        span:24,
        sortable: true,
        maxlength:40,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:94,
        overHidden:true,
      },
      {
        label: "客户",
        prop: "garbageCustomerId",
        span:24,
        type:'select',
        display:false,
        hide:true,
        showColumn:false,
        filterable:true,
        props: {
          label: 'id',
          value: 'id'
        },
        typeformat(item, label, value) {
          return `${item['name']}-${item['mobile']}`
        },
        dicUrl:"/chain/garbagecustomer/list",
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      {
        label: "项目名称(泥尾)",
        prop: "projectInfoId",
        span:24,
        sortable: true,
        search: true,
        display:false,
        type:'select',
        props: {
          label: 'projectName',
          value: 'id'
        },
        filterable:true,
        dicUrl: "/chain/garbagecustomersales/getProjectInfoList",
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        minWidth:130,
        overHidden:true,
      },
      {
        label: "项目名称(泥尾)",
        prop: "projectInfoId2",
        span:24,
        hide:true,
        showColumn:false,
        type:'select',
        props: {
          label: 'projectName',
          value: 'id'
        },
        filterable:true,
        dicUrl: '/chain/projectinfo/list',
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
      },
      {
        label: "票数",
        prop: "qty",
        sortable: true,
        display:false,
        span:24,
        overHidden:true,
      },
      {
        label: "单价(元/张)",
        prop: "price",
        type:'number',
        span:24,
        sortable: true,
        controls:false,
        minRows: 0,
        maxRows: 99999.99,
        precision: 2,
        // rules: [
        //   {
        //     required: true,
        //     message: "请输入单价",
        //     trigger: "blur",
        //   },
        //   {
        //     min: 0,
        //     type: "number",
        //     message: "值不能小于0",
        //     trigger: "blur",
        //   },
        // ],
        minWidth:110,
        overHidden:true,
      },
      {
        label: "经办日期",
        span:24,
        sortable: true,
        prop: "searchDate",
        // sortable: true,
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange:true,
        search:true,
        hide:true,
        showColumn:false,
        display:false,
        placeholder:'请选择',
      },
      {
        label: "经办日期",
        span:24,
        sortable: true,
        prop: "operateDate",
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        placeholder:'请选择',
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        minWidth:100,
        overHidden:true,
      },
      {
        label: "经办人",
        prop: "operator",
        search:true,
        sortable: true,
        maxlength:40,
        span:24,
        placeholder:'请输入',
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:84,
        overHidden:true,
      },
      {
        label: "票号",
        prop: "ticketNo",
        sortable: true,
        display:false,
        minWidth:160,
        overHidden:true,
      },
      {  //开始票号
        label: "票号",
        prop: "beginNo",
        span:14,
        hide:true,
        showColumn:false,
        placeholder:'开始票号',
        maxlength:11,
        value:new Date().getFullYear(),
        blur:({value})=>{
          console.log(value);
          if (value&&/^([0-9][0-9]*)$/.test(value)&&that.form.endNo&&/^([0-9][0-9]*)$/.test(that.form.endNo)) {
            that.form.num = Number(that.form.endNo) - Number(value)+1
          }else{
            that.form.num = 0
          }
        },
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if(value&&value!=''){
                if(/^([0-9][0-9]*)$/.test(value)){
                  if(Number(that.form.endNo)>0&&Number(value)>Number(that.form.endNo)){
                    callback(new Error('开始票号不能大于结束票号'));
                  }else{
                    callback();
                  }
                }else{
                  callback(new Error('输入不正确'));
                }
              }
              else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      {  // -
        label: "",
        labelWidth:0,
        span:2,
        prop: "line",
        hide:true,
        showColumn:false,
      },
      {
        label: "",
        labelWidth:0,
        prop: "endNo",
        span:8,
        hide:true,
        showColumn:false,
        maxlength:11,
        placeholder:'结束票号',
        value:new Date().getFullYear(),
        blur:({value})=>{
          if (value&&/^([0-9][0-9]*)$/.test(value)&&that.form.beginNo&&/^([0-9][0-9]*)$/.test(that.form.beginNo)) {
            that.form.num = Number(value) - Number(that.form.beginNo)+1
          }else{
            that.form.num = 0
          }
        },
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if(value&&value!=''){
                if(/^([0-9][0-9]*)$/.test(value)){
                  console.log(value<=that.form.beginNo);
                  if(that.form.beginNo!=''&&Number(value)<Number(that.form.beginNo)){
                    callback(new Error('结束票号不能小于开始票号'));
                  }else{
                    callback();
                  }
                }else{
                  callback(new Error('输入不正确'));
                }
              }
              else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      {
        label: "销售数",
        prop: "num",
        span:24,
        disabled:true,
        hide:true,
        showColumn:false,
        placeholder:'请输入',
        overHidden:true,
      },
      {
        label: "创建人",
        prop: "createName",
        sortable: true,
        display:false,
        minWidth:84,
        overHidden:true,
      },
      {
        label: "创建时间",
        prop: "createDatetime",
        sortable: true,
        display:false,
        minWidth:140,
        overHidden:true,
      },
    ],
  };
}

