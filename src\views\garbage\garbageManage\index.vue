<template>
  <div class="execution">
    <basic-container>
      <my-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        :before-open="beforeOpen"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="handleDel"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <template slot="menuLeft" slot-scope="{ size }">
          <el-button
            type="primary"
            icon="el-icon-download"
            v-if="permissions['chain:garbage:excel']"
            size="small"
            :loading="tableLoading"
            @click="exOut"
            >导出</el-button>
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="small"
            @click="showDialog"
            >添加政府备案泥尾</el-button
          >
        </template>
        <template slot="mapForm" slot-scope="{type}">
          <!-- <div style="margin-bottom: 20px; width: 50vw">
            <el-input
              v-model="form.fence"
              placeholder="请在地图选择定位"
              :disabled="true"
              style="width: 40%"
            ></el-input>
          </div> -->

          <div style="padding-bottom: 20px">
            <el-amap-search-box
              class="search-box"
              v-if="type!='view'"
              :search-option="searchOption"
              :on-search-result="onSearchResult"
            >
            </el-amap-search-box>
          </div>
          <el-amap
            vid="amapDemo"
            :center="center"
            :amap-manager="amapManager"
            :zoom="zoom"
            :events="events"
            class="amap-demo"
            style="height: 300px; width: 100%"
          >
            <el-amap-marker
              ref="marker"
              vid="component-marker"
              :position="marker.position"
              :events="marker.events"
              :visible="marker.visible"
              :draggable="marker.draggable"
            ></el-amap-marker>
          </el-amap>
        </template>
      </my-crud>
    </basic-container>
    <!-- 添加政府备案泥尾弹窗 -->
    <el-dialog
      width="800px"
      title="添加政府备案泥尾"
      :visible="visible"
      :before-close="closeVisible"
      :close-on-click-modal="false"
    >
      <avue-form :option="optionForm" v-model="addform" @submit="submit" v-if="visible">
        <template slot="name" slot-scope="scope">
          <el-autocomplete
            size="small"
            style="width:100%"
            clearable
            :fetch-suggestions="querySearchAsync"
            @select="handleSelect"
            placeholder="请选择消纳场所名称"
            v-model="addform.name"
          ></el-autocomplete>
        </template>
      </avue-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPage,
  getObj,
  addObj,
  putObj,
  delObj,
  garbageRecordList,
  addGarbagerecord,
  checkExistsProject
} from "@/api/garbage/garbage";
import { tableOption } from "@/const/crud/garbage/garbage";
import { mapGetters } from "vuex";
import { AMapManager } from "vue-amap";
import { expotOut } from "@/util/down.js";
const amapManager = new AMapManager();
export default {
  name: "garbage2",
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      visible: false,
      searchOption: {
        // 限制搜索城市的范围
        citylimit: false,
      },
      zoom: 14,
      center: [113.98074, 22.55251],
      marker: {
        position: [113.98074, 22.55251],
        visible: true,
        draggable: false,
      },
      amapManager,
      events: {
        click: (e) => {
          console.log(e);
          // this.mapInfo.lng = e.lnglat.lng;
          // this.mapInfo.lat = e.lnglat.lat;
          // this.mapInfo.lnglat = [e.lnglat.lng, e.lnglat.lat];
          this.marker.position = [e.lnglat.lng, e.lnglat.lat];
          this.form.fence = e.lnglat.lng + "," + e.lnglat.lat;
        },
      },
      addform: {},
      optionForm: {
        labelWidth: 120,
        column: [
          {
            label: "消纳场所名称",
            prop: "name",
            formslot:true,
            order: 1,
            span: 12,
            rules: [
              {
                required: true,
                message: "请选择消纳场所名称",
                trigger: "change",
              },
            ],
          },
          {
            label: "泥土类型",
            prop: "soliTypes",
            span: 12,
            sortable: true,
            type: "select", // 下拉选择
            multiple: true,
            props: {
              label: "itemName",
              value: "itemValue",
            },
            // search:true,
            dicUrl:
              "/chain/systemdictionaryitem/listDictionaryItem?dictionary=match_soil_type",
            rules: [
              {
                required: true,
                message: "请选择泥土类型",
                trigger: "change",
              },
            ],
          },
          {
            label: "市",
            prop: "city",
            disabled: true,
            span: 12,
          },
          {
            label: "区/县",
            disabled: true,
            prop: "district",
            span: 12,
          },
          {
            label: "场所类型",
            prop: "type",
            disabled: true,
            span: 12,
          },
          {
            label: "运营单位",
            disabled: true,
            prop: "company",
            span: 12,
          },
          {
            label: "地址",
            prop: "address",
            disabled: true,
            span: 12,
          },
          {
            label: "入口坐标",
            prop: "gps",
            disabled: true,
            span: 12,
            rules: [
              {
                required: true,
                message: "请选择消纳场所",
                trigger: "blur",
              },
            ]
          },
          {
            label: "消纳状态",
            prop: "status",
            disabled: true,
            span: 12,
            type: "select",
            dicData: [{
                label: '正常消纳',
                value: 1
            }, {
                label: '暂停消纳',
                value: 2
            }]
          },
          {
            label: "设计总量",
            prop: "total",
            disabled: true,
            span: 12,
          },
        ]
      },
    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:garbage:add"] ? true : false,
        delBtn: this.permissions["chain:garbage:del"] ? true : false,
        editBtn: this.permissions["chain:garbage:edit"] ? true : false,
        viewBtn: this.permissions["chain:garbage:get"] ? true : false,
      };
    },
  },
  methods: {
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
  async handleUpdate(row, index, done, loading) {
    console.log(row);
      // row.soilTypes = Array.isArray(row.soilTypes)&&row.soilTypes.length > 0 ? row.soilTypes.join(",") : row.soilTypes||"";
      let message = ""
      if(row.isDel==1){
       message = await checkExistsProject(row.id).then(res=>{
          return res.data.data
        })
      }
      if(message !=""){
        this.$confirm(message, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(()=>{
            this.putObj(row, index, done, loading)
          })
          .catch((err) =>{
            loading();
          });
      }else{
        this.putObj(row, index, done, loading)
      }

    },
    putObj(row, index, done, loading){
      putObj(row).then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      // row.soilTypes = Array.isArray(row.soilTypes)&&row.soilTypes.length > 0 ? row.soilTypes.join(",") : row.soilTypes||"";
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    closeVisible() {
      this.visible = false;
    },
    showDialog() {
      console.log(this.$refs.crud, "crud");
      this.addform = {};
      this.visible = true;
    },
    onSearchResult(pois) {
      this.center = [pois[0].location.lng, pois[0].location.lat];
      this.marker.position = [pois[0].location.lng, pois[0].location.lat];
      this.form.fence = pois[0].location.lng + "," + pois[0].location.lat;
    },
    beforeOpen(done, type, value) {
      if (type == "add") {
        setTimeout(() => {
          this.form.fence = "";
          this.form.$fence = "";
          this.center = [113.98074, 22.55251];
          this.marker.position = [113.98074, 22.55251];
        }, 100);
      } else {
        setTimeout(() => {
          // if (this.form.soilTypes && this.form.soilTypes.length > 0) {
          //   this.form.soilTypes = this.form.soilTypes.split(",");
          //   this.form.$soilTypes = this.form.$soilTypes.split(",");
          // }
          this.center = this.form.fence.split(",");
          this.marker.position = this.form.fence.split(",");
        }, 100);
      }
      done();
    },
    //自动模糊搜索人员
    querySearchAsync(queryString, callback) {
      console.log(queryString);
      let list = [];
      let param = {
        name: queryString,
      };
      garbageRecordList(param).then((res) => {
        console.log(res);
        if (res.status == 200) {
          for (let i of res.data.data) {
            i.value = i.name; //将想要展示的数据作为value
          }
          list = res.data.data;
          console.log(list);
          callback(list);
        }
        console.log(list);
      });
    },
    //搜索后赋值
    handleSelect(item) {
      console.log(item);
      this.addform.province = item.province
      this.addform.city = item.city
      this.addform.district = item.district
      this.addform.address = item.address
      this.addform.gps = item.gps
      this.addform.type = item.type
      this.addform.company = item.company
      this.addform.status = item.status
      this.addform.total = item.total
    },
    submit(form,done) {
      this.addform.soliTypes = Array.isArray(this.addform.soliTypes)&&this.addform.soliTypes.length > 0 ? this.addform.soliTypes.join(",") : this.addform.soliTypes||''
      setTimeout(()=>{
        done()
      },5000)
      addGarbagerecord(this.addform).then(res=>{
        console.log(res);
        done()
        this.visible = false
        this.getPage(this.page)
      })
    },
    exOut() {
      let params = Object.assign({},this.paramsSearch)
      this.tableLoading=true
      let url = "/chain/garbage/exportExcelV2";
      // params.id = this.info.id
      expotOut(params, url, "泥尾").then(()=>{
        this.tableLoading=false
      }).catch(()=>{
        this.tableLoading=false
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
