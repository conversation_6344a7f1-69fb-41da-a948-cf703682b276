import Layout from "@/page/index/";

export default [


  {
    path: "/login",
    name: "登录页",
    component: () =>
      import(/* webpackChunkName: "page" */ "@/page/login/buildLogin"),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
      activeName: "user",
    },
  },
  // {
  //   path: "/login",
  //   name: "登录页",
  //   component: () =>
  //     import(/* webpackChunkName: "page" */ "@/page/login/index"),
  //   meta: {
  //     keepAlive: true,
  //     isTab: false,
  //     isAuth: false,
  //     activeName: "user",
  //   },
  // },
  // {
  //   path: "/register",
  //   name: "注册页",
  //   component: () =>
  //     import(/* webpackChunkName: "page" */ "@/page/login/index"),
  //   meta: {
  //     keepAlive: true,
  //     isTab: false,
  //     isAuth: false,
  //     activeName: "register",
  //   },
  // },
  {
    path: "/companyAuth",
    name: "企业认证页",
    component: () =>
      import(/* webpackChunkName: "page" */ "@/page/login/companyAuth"),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
      activeName: "companyAuth",
    },
  },
  {
    path: "/garbageRegister",
    name: "泥尾注册",
    component: () =>
      import(/* webpackChunkName: "page" */ "@/page/login/garbageRegister"),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
      activeName: "garbageRegister",
    },
  },
  {
    path: "/lock",
    name: "锁屏页",
    component: () => import(/* webpackChunkName: "page" */ "@/page/lock/index"),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: "/404",
    component: () =>
      import(/* webpackChunkName: "page" */ "@/components/error-page/404"),
    name: "404",
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: "/403",
    component: () =>
      import(/* webpackChunkName: "page" */ "@/components/error-page/403"),
    name: "403",
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: "/500",
    component: () =>
      import(/* webpackChunkName: "page" */ "@/components/error-page/500"),
    name: "500",
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: "/",
    name: "主页",
    redirect: "/wel",
  },
  {
    path: "/myiframe",
    component: Layout,
    redirect: "/myiframe",
    children: [
      {
        path: ":routerPath",
        name: "iframe",
        component: () =>
          import(/* webpackChunkName: "page" */ "@/components/iframe/main"),
        props: true,
      },
    ],
  },
  {
    path: "*",
    redirect: "/404",
  },
  {
    path: "/authredirect",
    name: "授权页",
    component: () =>
      import(/* webpackChunkName: "page" */ "@/page/login/authredirect"),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
];
