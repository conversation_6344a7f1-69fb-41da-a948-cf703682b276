export const tableOption = (value)=>{
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    // stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: false, // 显示搜索字段
    search:false,
    searchShowBtn:false,
    excelBtn: !value,
    // printBtn: true,
    labelWidth: 150,
    addBtn: false,
    editBtn: false,
    delBtn:false,
    selection:false,
    menu:false,
    defaultSort: {
      prop: "goDatetime",
      order: "descending",
    },
    viewBtn: true,
    excelBtn: false,
    searchMenuSpan: 6,
    searchLabelWidth:100,
    menuWidth:160,
    searchIcon:true,
    searchIndex:3,
    column: [
      {
        label: "ID",
        prop: "id",
        sortable: true,
        hide: true, //列表页字段隐藏
        disabled: true, //弹窗表单字段不允许输入
        display: false, //弹窗表单字段隐藏
        rules: [
          {
            required: true,
            message: "请输入ID",
            trigger: "blur",
          },
          {
            max: 36,
            message: "长度在不能超过36个字符",
          },
        ],
        minWidth:160,
        overHidden:true,
      },
      {
        label: "企业名称",
        prop: "companyAuthId",
        type: "select", // 下拉选择
        search: true,
        searchOrder:2,
        editDisabled:true,
        props: {
          label: "companyName",
          value: "id",
        },
        dicUrl: "/chain/companyauth/list",
        filterable: true, //是否可以搜索
        minWidth:180,
        overHidden:true,
      },
      {
        label: "运单号",
        prop: "no",
        sortable: 'custom',
        search: true,
        editDisabled:true,
        searchOrder:3,
        minWidth:120,
        minWidth:160,
        overHidden:true,
      },
      {
        label: "项目名称",
        prop: "projectInfoId",
        search: true,
        type: "select",
        searchOrder:2,
        editDisabled:true,
        multiple:true,
        width:120,
        props: {
          label: 'projectName',
          value: 'id'
        },
        dicUrl: '/chain/projectinfo/list',
        filterable: true,  //是否可以搜索
        minWidth:180,
        overHidden:true,
      },
      // {
      //   label: "项目合作方",
      //   prop: "agentInfoId",
      //   search: true,
      //   type: "select",
      //   editDisabled:true,
      //   searchOrder:3,
      //   minWidth:82,
      //   props: {
      //     label: 'agent_name',
      //     value: 'agent_info_id'
      //   },
      //   dicUrl: '/chain/companywaybill/getAgentList',
      //   filterable: true,  //是否可以搜索
      // },
      {
        label: "项目负责人",
        prop: "leadingNames",
        minWidth:82,
        searchOrder:8,
        search: true,
        editDisabled:true,
        minWidth:160,
        overHidden:true,
      },
      {
        label: "挖机签单员",
        prop: "inStaffName",
        searchOrder:6,
        minWidth:82,
        search: true,
        editDisabled:true,
        minWidth:90,
        overHidden:true,
      },
      {
        label: "出场签单员",
        prop: "goStaffName",
        minWidth:82,
        searchOrder:7,
        search: true,
        editDisabled:true,
        minWidth:90,
        overHidden:true,
      },
      {
        label: "挖机的土质",
        prop: "inSoilType",
        editDisabled:true,
        hide: true, //列表页字段隐藏
        minWidth:90,
        overHidden:true,
      },
      {
        label: "出场签单土质",
        prop: "goSoilType",
        minWidth:96,
        editDisabled:true,
        overHidden:true,
      },
      {
        label: "挖机GPS",
        prop: "inGps",
        hide: true, //列表页字段隐藏
        editDisabled:true,
        minWidth:80,
        overHidden:true,
      },
      {
        label: "出场签单GPS",
        prop: "goGps",
        hide: true, //列表页字段隐藏
        editDisabled:true,
        minWidth:90,
        overHidden:true,
      },
      {
        label: "挖机GPS地址",
        prop: "inAddr",
        hide: true, //列表页字段隐藏
        editDisabled:true,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "出场签单地址",
        prop: "goAddr",
        hide: true, //列表页字段隐藏
        editDisabled:true,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "确认卸土GPS",
        prop: "completeGps",
        hide: true, //列表页字段隐藏
        editDisabled:true,
        minWidth:90,
        overHidden:true,
      },
      {
        label: "卸土地址",
        prop: "completeAddr",
        hide: true, //列表页字段隐藏
        editDisabled:true,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "运输类型",
        prop: "tpMode",
        type: "select", // 下拉选择
        search: true,
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode',
        minWidth:80,
        overHidden:true,
      },
      {
        label: "泥尾",
        prop: "garbageId",
        // sortable: true,
        type: "select", // 下拉选择
        editDisabled:true,
        // search:true,
        props: {
          label: "names",
          value: "id",
        },
        // hide:true,
        dicUrl: "/chain/garbage/list",
        filterable: true, //是否可以搜索
        minWidth:120,
        overHidden:true,
      },
      {
        label: "是否有泥尾票",
        prop: "isTicket",
        search:true,
        type:'select',
        dicData: [
          {
            label: "否",
            value: "0",
          },
          {
            label: "是",
            value: "1",
          },
        ],
        minWidth:96,
        overHidden:true,
      },
      {
        label: "泥尾票土质",
        prop: "goTicketSoilType",
        search:true,
        minWidth:90,
        overHidden:true,
      },
      {
        label: "司机联系方式",
        prop: "mobile",
        hide: true,   //列表页字段隐藏
        editDisabled:true,
        minWidth:110,
        overHidden:true,
      },
      {
        label: "出场班次",
        prop: "goShiftTypeName",
        minWidth:120,
        overHidden:true,
      },
      {
        label: "单位",
        prop: "weightUnit",
        type: "select", // 下拉选择
        search: true,
        editDisabled:true,
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=weight_unit',
        minWidth:80,
        overHidden:true,
      },
      {
        label: "数量",
        prop: "weightTons",
        display:false,
        minWidth:80,
        overHidden:true,
      },
      {
        label: "车牌号",
        prop: "goTruckCode",
        search: true,
        searchOrder:11,
        minWidth:90,
        overHidden:true,
      },
      {
        label: "司机",
        prop: "fleetName",
        searchOrder:9,
        search: true,
        editDisabled:true,
        minWidth:80,
        overHidden:true,
      },

      {
        label: "挖机签单图片",
        prop: "inPicture",
        editDisabled:true,
        hide:true,
      },
      {
        label: "出场签单图片",
        prop: "goPicture",
        editDisabled:true,
        hide:true
      },
      {
        label: "司机结算",
        prop: "driverStatus",
        type: "select", // 下拉选择
        editDisabled:true,
        hide:true,
        dicData: [
          {
            label: "未结算",
            value: "1",
          },
          {
            label: "已结算",
            value: "2",
          },
        ],
        minWidth:80,
        overHidden:true,
      },
      {
        label: "司机报价",
        prop: "price",
        editDisabled:true,
        minWidth:80,
        overHidden:true,
      },
      {
        label: "企业结算价",
        prop: "settlePrice",
        editDisabled:true,
        minWidth:90,
        overHidden:true,
      },
      {
        label: "运单状态",
        prop: "status",
        type: "select", // 下拉选择
        search: true,
        editDisabled:true,
        // props: {
        //   label: "itemName",
        //   value: "itemValue",
        // },
        // dataType:'string',
        // dicUrl:"/chain/systemdictionaryitem/listDictionaryItem?dictionary=waybill_status",
        formatter:(val)=>{
          return val.statusName
        },
        minWidth:80,
        overHidden:true,
      },
      {
        label: "结算状态",
        prop: "agentStatus",
        type: "select", // 下拉选择
        search: !value,  //新增结算单只查询未结算的，不需要搜索
        editDisabled:true,
        searchOrder:5,
        dicData: [
          {
            label: '未结算',
            value: '1'
          },
          {
            label: '结算中',
            value: '2'
          },
          {
            label: '已结算',
            value: '3'
          },
       ],
       minWidth:80,
       overHidden:true,
      },
      {
        label: "是否异常申请",
        prop: "isException",
        editDisabled:true,
        type: "select", // 下拉选择
        search:true,
        dicData: [
          {
            label: '否',
            value: '0'
          },
          {
            label: '是',
            value: '1'
          },
       ],
       minWidth:96,
       overHidden:true,
      },
      {
        label: "是否申领运单",
        prop: "isClaim",
        type: "select", // 下拉选择
        search:true,
        editDisabled:true,
        dicData: [
          {
            label: '否',
            value: '0'
          },
          {
            label: '是',
            value: '1'
          },
          {
            label: '司机和车队长未认领',
            value: '2'
          },
          {
            label: '司机未认领和车队长已认领',
            value: '3'
          },
          {
            label: '司机已认领和车队长未认领',
            value: '4'
          },
          {
            label: '司机和车队长已认领',
            value: '5'
          },
       ],
       minWidth:96,
       overHidden:true,
      },
      {
        label: "挖机备注",
        prop: "inRemark",
        editDisabled:true,
        hide:true,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "出场备注",
        prop: "goRemark",
        editDisabled:true,
        hide:true,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "运单结算类型",
        prop: "settleType",
        type: "select",
        search:true,
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=settle_type',
        minWidth:96,
        overHidden:true,
      },
      {
        label: "司机出场确认时间",
        prop: "confirmGoDatetime",
        type:'datetime',
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        minWidth:150,
        overHidden:true,
        sortable: "custom",
      },
      {
        label: "挖机签单时间",
        prop: "inDatetime",
        type:'datetime',
        sortable: 'custom',
        searchRange:true,
        search: true,
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        minWidth:150,
        overHidden:true,
        sortable: "custom",
      },
      {
        label: "出场签单时间",
        prop: "goDatetime",
        type:'datetime',
        sortable: 'custom',
        searchRange:true,
        search: true,
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        minWidth:150,
        overHidden:true,
        sortable: "custom",
      },
      {
        label: "完成时间",
        prop: "completeDatetime",
        sortable: 'custom',
        type:'datetime',
        searchRange:true,
        search: true,
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        minWidth:150,
        overHidden:true,
        sortable: "custom",
      },
      {
        label: "挖机手工录入车牌",
        prop: "inDriverTruckCode",
        hide: true, //列表页字段隐藏
        minWidth:10,
        overHidden:true,
      },
      {
        label: "认领运单录入车牌",
        prop: "goDriverTruckCode",
        hide: true, //列表页字段隐藏
        minWidth:100,
        overHidden:true,
      },
      {
        label: "创建时间",
        prop: "createDatetime",
        editDisabled:true,
        hide: true,   //列表页字段隐藏
        minWidth:150,
        overHidden:true,
        sortable: "custom",
      },
      {
        label: "修改时间",
        prop: "updateDatetime",
        editDisabled:true,
        hide: true, //列表页字段隐藏
        minWidth:150,
        overHidden:true,
        sortable: "custom",
      },
      {
        label: "电子结算卡",
        prop: "settleCardNo",
        searchOrder:1,
        searchSpan:24,
        searchSlot:true,
        hide:!value,
        showColumn:!!value,
        search: !!value,
        searchRules: [{
          required: true,
          message: "请输入电子结算卡",
          trigger: "blur"
        }],

        minWidth:160,
        overHidden:true,
      },
    ],
  }
}
