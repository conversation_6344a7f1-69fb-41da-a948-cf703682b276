<template>
  <div class="searchInfo">
    <slot name="count"></slot>
    <div class="search flex flex-between">
      <div class="left">
        <div class="num" v-if="tab == 1">
          挖机装车数：<span>{{ total }}</span>
        </div>
        <div class="num" v-else>
          台班总时长：<span>{{ totalWorkHours }}</span
          >小时
        </div>
        <el-radio-group
          v-model="form.checkDynamic"
          size="small"
          style="margin-right: 10px; "
          @change="changeStatus"
        >
          <el-radio-button label=""> 全部项目 </el-radio-button>
          <el-radio-button label="1"> 活跃项目 </el-radio-button>
        </el-radio-group>
      </div>

      <slot name="searchLeft"></slot>
      <div class="searchContent">
        <slot name="searchRight" class="item"></slot>
        <!-- <el-radio-group v-model="form.weightUnit" @change="changeUnit" size="small" style="margin-right:10px;" v-if="source == '4'"
          class="item">
          <el-radio-button label="方">
          </el-radio-button>
          <el-radio-button label="吨">
          </el-radio-button>
        </el-radio-group> -->
        <div class="item">
          <span style="font-size: 14px; margin-left: 8px">班次：</span>
          <el-select v-model="form.inShiftType" placeholder="请选择" clearable >
            <el-option
              v-for="item in classes"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </div>
        <div class="item" v-if="tab==2">
          <span style="font-size: 14px; margin-left: 8px">工作类型：</span>
          <el-select v-model="form.ledgerType" placeholder="请选择" clearable >
            <el-option
              v-for="item in workTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="item" v-if="tab==1">
          <span style="font-size: 14px; margin-left: 8px">挖机车主：</span>
          <el-input
            v-model="form.ownerName"
            placeholder="请输入挖机车主"
            clearable
          ></el-input>
        </div>
        <div class="item" v-if="tab==1">
          <span style="font-size: 14px; margin-left: 8px">挖机签单员：</span>
          <el-input
            v-model="form.inStaffName"
            placeholder="请输入挖机签单员"
            clearable
          ></el-input>
        </div>
        <div class="item" v-if="tab==1">
          <span style="font-size: 14px; margin-left: 8px">挖机型号：</span>
          <el-input
            v-model="form.machineCode"
            placeholder="请输挖机型号"
            clearable
          ></el-input>
        </div>
        <div class="item" v-if="tab==1">
          <span style="font-size: 14px; margin-left: 8px">关联签单状态：</span>
          <el-select v-model="form.checkRelevance" placeholder="请选择" clearable >
            <el-option
              v-for="item in status"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <el-radio-group
          v-model="radio"
          size="small"
          @change="changeTime"
          class="item"
        >
          <el-radio-button label="week">最近一周</el-radio-button>
          <el-radio-button label="month">最近一个月</el-radio-button>
          <el-radio-button label="year">最近一年</el-radio-button>
        </el-radio-group>
        <div class="item">
          <span style="font-size: 14px; margin-left: 8px">{{tab==1?'运单创建时间':'班次日期'}}：</span>
          <el-date-picker
            style="margin-right: 8px; width: 300px"
            :editable="false"
            :pickerOptions="pickerOptions"
            v-model="form.searchTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :clearable="false"
          >
          </el-date-picker>
        </div>
        <div class="item">
          <el-button
            type="primary"
            size="small"
            @click="search"
            :loading="btnLoading"
            >搜索</el-button
          >
          <el-button
            type="primary"
            size="small"
            @click="exOut"
            :loading="btnLoading"
            >导出</el-button
          >
        </div>
      </div>
    </div>
    <slot name="center"></slot>
    <!-- //异常运单数searchTimeLabel才会传入申请时间 -->
    <projectList
      v-if="projectList && projectList.length > 0"
      :unit="unit"
      :text="''"
      @changeProject="changeProject"
      :active="form.projectInfoId"
      :projectList="projectList"
      :defaultProp="defaultProp"
    >
    </projectList>
  </div>
</template>

<script>
import projectList from "../projectList";
import {
  getProjectDynamicInfoByExcavatorLoading as getProjectList,
  getProjectDynamicInfoByExcavatedNumber,
  getShiftOfProjectByCompanyAuthId,
  getProjectDynamicInfoByDig,
  getCompanyLedgerTypeByCompanyId
} from "@/api/chain/board";

export default {
  props: {
    //传进来的数据
    info: {},
    isShowProject: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    //来源 1出车运单总数 2车队长总数  3 泥尾点总数  4总出土量/泥尾点总出土量  5运单费
    source: {
      type: String,
      default: () => {
        return "1";
      },
    },
    unit: {
      type: String,
      default: () => {
        return "";
      },
    },
    type: {
      type: Number,
      default: () => {
        return 1;
      },
    },
    total: {
      type: Number,
      default: () => {
        return 0;
      },
    },
    tab: {
      type: String,
      default: "",
    },
  },
  components: {
    projectList,
  },
  data() {
    return {
      workTypes:[],
      status: [
        {
          label: "全部",
          value: "",
        },
        {
          label: "关联",
          value: "1",
        },
        {
          label: "未关联",
          value: "0",
        },
      ],
      classes: [],
      projectList: [],
      form: {
        ledgerType:"",
        inStaffName: "", //挖机签单员
        machineCode: "", // 挖机型号
        inShiftType: "", // 班次
        ownerName: "", //挖机车主
        checkDynamic: "", // 是否活跃
        startDate: "",
        endDate: "",
        checkRelevance: "", // 是否关联运单 0否1是
        searchTime: [],
      },
      projectInfo: {},
      radio: "",
      btnLoading: false,
      defaultProp:{
        label:"projectName",
          value:"id",
          cnt:"billCount",
      },
      totalWorkHours:0,
      // 时间跨度为之前一年
      pickerOptions: {
        disabledDate: () => false,
        onPick: ({ maxDate, minDate }) => {
          if (minDate && !maxDate) {
            const oneYear = 365 * 24 * 60 * 60 * 1000;
            this.pickerOptions.disabledDate = time => {
              return time.getTime() < minDate.getTime() || time.getTime() > minDate.getTime() + oneYear;
            };
          } else {
            this.pickerOptions.disabledDate = () => false;
          }
        },
      },
    };
  },
  created() {
    this.getShiftOfProjectByCompanyAuthId();
    if(this.tab==2){
      this.getCompanyLedgerTypeByCompanyId()
    }
    if(this.tab==1 && this.type){
      this.form.checkRelevance= this.info.ledgerDigActive=='关联'?"1":"0"
    }
    console.info(this.info)
    this.form.checkDynamic = this.info.checkDynamic;
    this.form.projectInfoId = this.info.projectInfoId || "";
    this.radio = this.info.radio;

    if (this.info.startDate) {
      this.form.searchTime = [this.info.startDate, this.info.endDate];
    }
    if (this.isShowProject) {
      this.getProjectList();
    }
  },
  mounted() {},
  computed: {
    filterLabel() {
      switch (this.source) {
        case "1":
          return "运单创建的时间";
          break;
        case "2":
          return "车队长创建时间";
          break;
        case "3":
          return "泥尾创建时间";
          break;
        case "4":
          return "出场签单时间";
          break;
        case "5":
          return "支付单生成时间";
          break;
        case "6":
          return "支付单创建时间";
          break;
        default:
          break;
      }
    },
  },
  methods: {
    getCompanyLedgerTypeByCompanyId(){
      getCompanyLedgerTypeByCompanyId().then(res=>{
          this.workTypes = res.data.data
      })
    },
    getShiftOfProjectByCompanyAuthId() {
      getShiftOfProjectByCompanyAuthId().then((res) => {
        this.classes = res.data.data;
      });
    },
    getProjectList() {
      let param = {
        startDate: this.form.searchTime[0], //开始时间
        endDate: this.form.searchTime[1], //结束时间
        type: this.type,
        checkReturnAll: true,
      };
      this.getList(param);
    },
    getList(param) {
      let data = JSON.parse(JSON.stringify(Object.assign(this.form, param)))
      delete data.searchTime
      delete data.projectInfoId
      if (this.tab == 1) {
        getProjectList(data).then((res) => {
          this.projectInfo = res.data.data;
          this.projectList =
            this.form.checkDynamic == 1
              ? this.projectInfo.projectDynamicList
              : this.projectInfo.projectList;
          console.log(this.form.projectInfoId);
        });
      } else {
        this.defaultProp.cnt="workHour"

        getProjectDynamicInfoByDig(data).then(
          (res) => {
            this.projectInfo = res.data.data;
            this.projectList =
              this.form.checkDynamic == 1
                ? this.projectInfo.projectDynamicList
                : this.projectInfo.projectList;
                this.totalWorkHours =  this.projectList[0].workHour
            console.log(this.form.projectInfoId);
          }
        );
      }
    },
    changeStatus(val) {
      console.info(val)
      //更换项目列表  有些表格需要变换数据
      if (this.isShowProject) {
        if (val == 1) {
          this.projectList = this.projectInfo.projectDynamicList;
          this.form.projectInfoId = this.projectList[0].id;
        } else {
          this.projectList = this.projectInfo.projectList;
          this.form.projectInfoId = this.projectList[0].id;
        }
        this.searchData();
      }
      this.$emit("changeStatus", val);
    },
    changeUnit(val) {
      //更换单位，表格需要变换数据
      this.$emit("changeUnit", val);
    },
    changeProject(val) {
      this.form.projectInfoId = val;
      console.log(this.form.projectInfoId);
      this.searchData();
    },
    changeTime(val) {
      let startDate = "";
      let endDate = this.$moment().format("YYYY-MM-DD");
      switch (val) {
        case "week":
          startDate = this.$moment().subtract(7, "days").format("YYYY-MM-DD");
          break;
        case "month":
          startDate = this.$moment().subtract(1, "months").format("YYYY-MM-DD");
          break;
        case "year":
          startDate = this.$moment().subtract(1, "years").format("YYYY-MM-DD");
          break;

        default:
          break;
      }
      this.form.searchTime = [startDate, endDate];
      console.log(val);
    },
    search() {
      this.searchData();
      if (this.isShowProject) {
        this.getProjectList();
      }
    },
    searchData() {
      let param = {
        startDate: this.form.searchTime[0], //开始时间
        endDate: this.form.searchTime[1], //结束时间
        checkDynamic: this.form.checkDynamic, //是否活跃项目
        projectInfoId: this.form.projectInfoId,
      };
      console.info(param)
      let data = JSON.parse(JSON.stringify(Object.assign(this.form, param)))
      delete data.searchTime
      console.info(data)
      this.btnLoading = true;
      this.$emit(
        "searchChange",
        data,
        this.stopLoading
      );
    },
    stopLoading() {
      this.btnLoading = false;
    },
    exOut() {
      let param = {
        startDate: this.form.searchTime[0], //开始时间
        endDate: this.form.searchTime[1], //结束时间
        checkDynamic: this.form.checkDynamic, //是否活跃项目
        projectInfoId: this.form.projectInfoId,
      };
      let data = JSON.parse(JSON.stringify(Object.assign(this.form, param)))
      delete data.searchTime
      this.$emit("exOut", data, this.stopLoading);
    },
  },
};
</script>

<style lang="scss" scoped>
.search {
  .left {
    .num {
      line-height: 36px;
      height: 36px;
      margin-bottom: 10px;
      white-space: nowrap;
      min-width: 180px;
      span {
        color: #409eff;
        font-weight: bold;
      }
    }
  }
  .searchContent {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .item {
      display: flex;
      align-items: center;
      white-space: nowrap;
      flex-grow: 1;
      margin-bottom: 10px;
    }
  }
}
</style>
