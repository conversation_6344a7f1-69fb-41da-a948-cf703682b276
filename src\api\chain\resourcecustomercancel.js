import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/resourcecustomerwaybill/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/resourcecustomersales',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/resourcecustomersales/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/resourcecustomersales/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/resourcecustomerwaybill',
        method: 'put',
        data: obj
    })
}
