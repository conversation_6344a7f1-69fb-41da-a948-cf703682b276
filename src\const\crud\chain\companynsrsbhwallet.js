export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  searchSpan: 6,
  searchMenuSpan: 6,
  searchLabelWidth: 100,
  routerName:"companynsrsbhwallet",
  useVirtual:true,
  height:"auto",
  calcHeight:137,
  rowKey:"rowNumber",
  menuWidth:110,
  column: [
    {
      label: "交易时间",
      prop: "transactionDatetime",
      sortable: "custom",
      minWidth: 140,
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      overHidden: true,
    },
    {
      label: "业务流水号",
      prop: "businessSerialNo",
      sortable: "custom",
      search: true,
      minWidth: 160,
      overHidden: true,
    },
    {
      label: "交易类型",
      prop: "transactionType",
      type: "select", // 下拉选择
      props: {
        label: "itemName",
        value: "itemValue",
      },
      search: true,
      dicUrl:
      "/chain/systemdictionaryitem/listDictionaryItem?dictionary=transaction_type",
      minWidth: 100,
      overHidden: true,
    },
    {
      label: "交易结果",
      prop: "sttResult",
      minWidth: 96,
      overHidden: true,
      search:true,
      type:"select",
      dicData: [
        {
          label: '交易失败',
          value: '0'
        },
        {
          label: '交易成功',
          value: '1'
        },
        {
          label: '银行处理中',
          value: '2'
        },
      ],
    },
    {
      label: "支付单号",
      prop: "paymentNo",
      search:true,
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "交易金额",
      prop: "transactionAmount",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "变动前余额",
      prop: "balanceBalance",
      minWidth: 96,
      overHidden: true,
    },
    {
      label: "变动后余额",
      prop: "adjustedBalance",
      minWidth: 96,
      overHidden: true,
    },
    {
      label: "益路银行企业",
      prop: "nsrmc",
      minWidth: 120,
      overHidden: true,
    },
    {
      label: "收款人",
      prop: "accountName",
      search:true,
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "收款卡号",
      prop: "bankAccount",
      minWidth: 120,
      overHidden: true,
    },
    {
      label: "联系电话",
      prop: "mobile",
      minWidth: 100,
      overHidden: true,
    },
    {
      label: "益路银行",
      prop: "platformBranchIds",
      search:true,
      type:"select",
      props: {
        label: "platformBranchNsrmc",
        value: "platformBranchId",
      },
      searchMultiple:true,
      dataType:'string',
      hide:true,
      showColumn:false,
    },
    {
      label: "交易时间",
      prop: "searchDate",
      sortable: "custom",
      type: "datetime",
      searchRange: true,
      search: true,
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      hide:true,
      showColumn:false,
      minWidth: 140,
      overHidden: true,
    },
    {
      label: "备注",
      prop: "sttRemark",
      minWidth: 96,
      overHidden: true,
    },
  ],
};
