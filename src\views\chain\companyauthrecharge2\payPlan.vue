<template>
  <div class="driverTaskDetail">
    <el-drawer size="90%"
               :data="visible"
               :visible.sync="visible"
               title="资金支付计划"
               :before-close="cancelModal">
      <basic-container>
        <avue-crud ref="crud"
                   :data="tableData"
                   :table-loading="tableLoading"
                   :option="tableOption">
          <span slot="empty">暂无数据</span>
          <template slot="menu"
                    slot-scope="scope">
            <el-button type="text"
                       v-if="scope.row.companyPaymentPlanNo&&(scope.row.auditStatus==2||scope.row.auditStatus==9)&&scope.row.notPaidPrice>0"
                       icon="el-icon-check"
                       size="small"
                       plain
                       @click="payment(scope.row)">
              付款</el-button>
            <el-button type="text"
                       v-if="scope.row.companyPaymentPlanNo&&(scope.row.auditStatus==2||scope.row.auditStatus==9)&&scope.row.notPaidPrice==0"
                       icon="el-icon-check"
                       size="small"
                       plain
                       @click="payment(scope.row,2)">
              查看</el-button>
            <el-button type="text"
                       v-if="scope.row.companyPaymentPlanNo&&(scope.row.auditStatus==2||scope.row.auditStatus==9)"
                       icon="el-icon-document-delete"
                       size="small"
                       plain
                       @click="reject(scope.row,scope.index)">
              驳回</el-button>
            <el-button type="text"
                       v-if="scope.row.companyPaymentPlanNo"
                       icon="el-icon-view"
                       size="small"
                       plain
                       @click="view(scope.row,scope.index)">
              查看审核</el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
    <confirmPayment v-if="confirmVisible" v-on:refreshChange="getData" :isView="isView" :info="currentForm" :visible.sync="confirmVisible"></confirmPayment>
    <flowView v-if="flowVisible" :info="currentForm" :visible.sync="flowVisible"></flowView>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getPlanByPaymentId, rejectPaymentPlan } from "@/api/chain/companyauthrecharge";
import confirmPayment from './confirmPayment.vue';
import flowView from './components/flowView';
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  components:{
    confirmPayment,
    flowView
  },
  data () {
    return {
      tableData: [],
      tableOption: {
        header: false,
        page: false,
        editBtn: false,
        delBtn: false,
        menuWidth: 180,
        column: [
          {
            label: "计划号",
            prop: "companyPaymentPlanNo",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "计划名称",
            prop: "companyPaymentPlanName",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "审核状态",
            prop: "auditStatusName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "计划创建人",
            prop: "createName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "计划创建时间",
            prop: "createDatetime",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "计划付款金额",
            prop: "companyPaymentPlanAmount",
            minWidth: 100,
            overHidden: true,
            formatter:(val)=>{
              return `<span ${val.checkAmount!=val.companyPaymentPlanAmount?'style="color:red"':""}>${val.companyPaymentPlanAmount}</span>`
            },
          },
          {
            label: "核算金额",
            prop: "checkAmount",
            minWidth: 80,
            overHidden: true,
            formatter:(val)=>{
              return `<span ${val.checkAmount!=val.companyPaymentPlanAmount?'style="color:red"':""}>${val.checkAmount}</span>`
            },
          },
          {
            label: "核算驳回金额",
            prop: "rejectPrice",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "已付金额",
            prop: "paidPrice",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "处理中金额",
            prop: "processingPrice",
            minWidth: 96,
            overHidden: true,
          },
          {
            label: "未付金额",
            prop: "notPaidPrice",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "最新支付时间",
            prop: "latestPayTime",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "状态",
            prop: "payStatus",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "计划驳回备注",
            prop: "remark",
            minWidth: 140,
            overHidden: true,
          },
        ]
      },
      tableLoading: false,
      currentForm:{},
      confirmVisible:false,
      isView:false,
      flowVisible:false,
    };
  },
  created () {
    this.getData()
  },
  mounted () {
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    getData () {
      getPlanByPaymentId({ companyPaymentId: this.info.id }).then(res => {
        this.tableData = res.data.data
      })
    },
    cancelModal () {
      this.$emit("update:visible", false);
    },
    payment (row,type=1) {
      this.currentForm = Object.assign({},this.info)
      this.currentForm.planId = row.id
      this.isView = type==2
      this.confirmVisible = true
    },
    reject (row) {
      let _this = this;
      this.$prompt("驳回备注", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入驳回备注",
        inputPattern: /\S/,
        inputValidator: (value) => {
          if (value && value.length > 100) {
            return "超出限制字数100";
          }
        },
        inputErrorMessage: "请输入备注内容",
      })
        .then(function ({ value }) {
          return rejectPaymentPlan({ remark: value, companyPaymentPlanId: row.id });
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "驳回成功",
            type: "success",
          });
          this.getData();
        })
        .catch(function (err) { });

    },
    view(row){
      this.currentForm = Object.assign({},row)
      this.flowVisible = true
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
