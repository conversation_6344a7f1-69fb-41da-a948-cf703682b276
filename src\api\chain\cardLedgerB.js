import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companycard/getBCardAccountPage',
        method: 'get',
        params: query
    })
}


export function getLists(params) {
  return request({
      url: '/chain/companycard/getCCardAccountPage',
      method: 'get',
      params
  })
}
export function getBCardAccountCount(data) {
  return request({
      url: '/chain/companycard/getBCardAccountCount',
      method: 'post',
      data
  })
}
