import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/matchGarbagePlan/getPage',
        method: 'get',
        params: query
    })
}
//根据计划id复制计划
export function copyPlanByPlanId(data) {
    return request({
        url: '/chain/matchGarbagePlan/copyPlanByPlanId',
        method: 'post',
        data
    })
}
//根据计划id修改单个计划
export function editPlan(data) {
    return request({
        url: '/chain/matchGarbagePlan/editPlan',
        method: 'post',
        data
    })
}
//根据计划id查看单个计划
export function getMatchGarbagePlanDetailById(data) {
    return request({
        url: '/chain/matchGarbagePlan/getMatchGarbagePlanDetailById',
        method: 'post',
        data
    })
}
//根据计划id取消计划
export function cancelPlan(data) {
    return request({
        url: '/chain/matchGarbagePlan/cancelPlan',
        method: 'post',
        data
    })
}
//根据计划id开启匹配
export function openMatch(data) {
    return request({
        url: '/chain/matchGarbagePlan/openMatch',
        method: 'post',
        data
    })
}
//根据计划id获取进行中的订单数
export function getUnderwayOrderCount(data) {
    return request({
        url: '/chain/matchGarbagePlan/getUnderwayOrderCount',
        method: 'post',
        data
    })
}
//根据计划id查询订单进度
export function getProgress(data) {
    return request({
        url: '/chain/matchGarbagePlan/getProgress',
        method: 'post',
        data
    })
}
//根据计划查询是否存在可操作的队列
export function getCountByPlanId(data) {
    return request({
        url: '/chain/matchgarbageprojectplan/getCountByPlanId',
        method: 'post',
        data
    })
}

