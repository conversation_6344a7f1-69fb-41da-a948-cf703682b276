export const tableOption = (that) => {
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    index: true,
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    // excelBtn: true,
    // printBtn: true,
    viewBtn: true,
    delBtn: true,
    searchMenuSpan: 6,
    menuWidth:190,
    routerName:"systemdictionaryitem2",
    column: [
      {
        label: "数据字典",
        prop: "systemDictionaryId",
        type: "select",
        sortable: true,
        search: true,
        props: {
          label: "dictionaryName",
          value: "id",
        },
        dicUrl: "/chain/systemdictionary/listCompany",
        minWidth:94,
        overHidden:true,
      },
      // {
      //   label: "项目值",
      //   prop: "itemValue",
      //   sortable: true,
      //   search: true,
      //   rules: [
      //     {
      //       required: true,
      //       message: "请输入项目值",
      //       trigger: "blur",
      //     },
      //     {
      //       max: 100,
      //       message: "长度在不能超过100个字符",
      //     },
      //   ],
      //   minWidth:84,
      //   overHidden:true,
      // },
      {
        label: "项目名",
        prop: "itemName",
        sortable: true,
        rules: [
          {
            required: true,
            message: "请输入项目值",
            trigger: "blur",
          },
          {
            max: 100,
            message: "长度在不能超过100个字符",
          },
        ],
        minWidth:84,
        overHidden:true,
      },
      {
        label: "项目描述",
        prop: "itemDesc",
        sortable: true,
        rules: [
          {
            max: 200,
            message: "长度在不能超过200个字符",
          },
        ],
        minWidth:120,
        overHidden:true,
      },
      {
        label: "项目排序",
        prop: "itemSort",
        type: "number",
        sortable: true,
        rules: [
          {
            required: true,
            message: "请输入项目排序",
            trigger: "blur",
          },
        ],
        minWidth:94,
        overHidden:true,
      },
      {
        label: "创建时间",
        prop: "createDatetime",
        sortable: true,
        hide: false, //列表页字段隐藏
        disabled: true, //弹窗表单字段不允许输入
        display: false, //弹窗表单字段隐藏
        addDisplay: false,
        editDisplay: false,
        minWidth:170,
        overHidden:true,
      },
      {
        label: "创建用户",
        prop: "createName",
        sortable: true,
        hide: true, //列表页字段隐藏
        disabled: true, //弹窗表单字段不允许输入
        display: false, //弹窗表单字段隐藏
        addDisplay: false,
        editDisplay: false,
        minWidth:84,
        overHidden:true,
      },
      {
        label: "修改时间",
        prop: "updateDatetime",
        sortable: true,
        hide: true, //列表页字段隐藏
        disabled: true, //弹窗表单字段不允许输入
        display: false, //弹窗表单字段隐藏
        addDisplay: false,
        editDisplay: false,
        minWidth:170,
        overHidden:true,
      },
      {
        label: "修改用户",
        prop: "updateName",
        sortable: true,
        hide: true, //列表页字段隐藏
        disabled: true, //弹窗表单字段不允许输入
        display: false, //弹窗表单字段隐藏
        addDisplay: false,
        editDisplay: false,
        minWidth:84,
        overHidden:true,
      },
    ],
  };
};
