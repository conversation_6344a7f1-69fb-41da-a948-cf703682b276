<template>
    <div class="execution">
        <basic-container>
            <avue-crud ref="crud" :page.sync="page" :data="tableData" :permission="permissionList"
                :table-loading="tableLoading" :option="tableOption" v-model="form"
                @on-load="getPage" @refresh-change="refreshChange" @row-update="handleUpdate" @row-save="handleSave"
                @row-del="handleDel" @sort-change="sortChange" @search-change="searchChange">

                <template slot="waybillCount" slot-scope="scope">
                    <el-link type="primary" @click="toWaybill(scope.row)">{{ scope.row.waybillCount }}</el-link>
                </template>
                <template slot="workTimeCount" slot-scope="scope">
                    <el-link type="primary" @click="toWorkTime(scope.row)">{{ scope.row.workTimeCount }}</el-link>
                </template>
            </avue-crud>
        </basic-container>
    </div>
</template>

<script>
import { getPage, getObj, addObj, putObj, delObj, askForInvoice, getPaymentOfInvoice } from '@/api/navvyOwner/businessStatis'
import { tableOption } from '@/const/crud/navvyOwner/businessStatis'
import { mapGetters } from 'vuex'

export default {
    name: 'businessStatis',
    data() {
        return {
            form: {},
            tableData: [],
            page: {
                total: 0, // 总页数
                currentPage: 1, // 当前页数
                pageSize: 20, // 每页显示多少条
                ascs: [],//升序字段
                descs: []//降序字段
            },
            paramsSearch: {},
            tableLoading: false,
            tableOption: tableOption,
        }
    },
    created() {
    },
    mounted: function () {
    },
    computed: {
        ...mapGetters(['permissions']),
        permissionList() {
            return {
                addBtn: this.permissions['chain:companypayment:add'] ? true : false,
                delBtn: this.permissions['chain:companypayment:del'] ? true : false,
                editBtn: this.permissions['chain:companypayment:edit'] ? true : false,
                viewBtn: this.permissions['chain:companypayment:get'] ? true : false
            };
        }
    },
    methods: {
        toWorkTime(row){
            let info = {
              projectInfoId:row.projectInfoId,
              companyAuthId:row.companyAuthId,
              machineId:row.machineId,
              ownerName:row.ownerName,
              inStaffId:row.inStaffId,  //挖机司机 签单员
              startTime:this.paramsSearch.startTime,
              endTime:this.paramsSearch.endTime,
            }
            this.$router.push({path:"/owner/workTime",query:{info:JSON.stringify(info)}})
        },
        toWaybill(row){
              let info = {
                status: "2,3,4,11,12,21,22",
                projectInfoId:row.projectInfoId,
                companyAuthId:row.companyAuthId,
                machineId:row.machineId,
                ownerName:row.ownerName,
                inStaffId:row.inStaffId,  //挖机司机 签单员
                goDatetimeStart:this.paramsSearch.startTime,
                goDatetimeEnd:this.paramsSearch.endTime,
              }
            this.$router.push({path:"/owner/waybill",query:{info:JSON.stringify(info)}})
        },
        searchChange(params, done) {
            params = this.filterForm(params)
            this.paramsSearch = params
            this.page.currentPage = 1
            this.getPage(this.page, params)
            done()
        },
        sortChange(val) {
            let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
            if (val.order == 'ascending') {
                this.page.descs = []
                this.page.ascs = prop
            } else if (val.order == 'descending') {
                this.page.ascs = []
                this.page.descs = prop
            } else {
                this.page.ascs = []
                this.page.descs = []
            }
            this.getPage(this.page)
        },
        getPage(page, params) {
            this.tableLoading = true
            if (params) {

                  if (params.hasOwnProperty("dateRange")) {
                    params.startTime = params.dateRange[0];
                    params.endTime = params.dateRange[1];
                    delete params.dateRange;
                  }
                }
            getPage(Object.assign({
                current: page.currentPage,
                size: page.pageSize,
                descs: this.page.descs,
                ascs: this.page.ascs,
            }, params, this.paramsSearch)).then(response => {
                this.tableData = response.data.data.records
                this.page.total = response.data.data.total
                this.page.currentPage = page.currentPage
                this.page.pageSize = page.pageSize
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        /**
         * @title 数据删除
         * @param row 为当前的数据
         * @param index 为当前删除数据的行数
         *
         **/
        handleDel: function (row, index) {
            let _this = this
            this.$confirm('是否确认删除此数据', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(function () {
                return delObj(row.id)
            }).then(data => {
                _this.$message({
                    showClose: true,
                    message: '删除成功',
                    type: 'success'
                })
                this.getPage(this.page)
            }).catch(function (err) {
            })
        },
        /**
         * @title 数据更新
         * @param row 为当前的数据
         * @param index 为当前更新数据的行数
         * @param done 为表单关闭函数
         *
         **/
        handleUpdate: function (row, index, done, loading) {
            putObj(row).then(response => {
                this.$message({
                    showClose: true,
                    message: '修改成功',
                    type: 'success'
                })
                done()
                this.getPage(this.page)
            }).catch(() => {
                loading()
            })
        },
        /**
         * @title 数据添加
         * @param row 为当前的数据
         * @param done 为表单关闭函数
         *
         **/
        handleSave: function (row, done, loading) {
            addObj(row).then(response => {
                this.$message({
                    showClose: true,
                    message: '添加成功',
                    type: 'success'
                })
                done()
                this.getPage(this.page)
            }).catch(() => {
                loading()
            })
        },
        /**
         * 刷新回调
         */
        refreshChange(page) {
            this.getPage(this.page)
        },
        askForInvoice(row) {
            askForInvoice(row.id).then(res => {
                this.$message.success(res.data.msg)
                this.getPage(this.page)
            })
        },
    }
}
</script>

<style lang="scss" scoped>
</style>
