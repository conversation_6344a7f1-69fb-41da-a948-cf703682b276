export const tableOption = (val)=>{
  let that = val
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    // stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    menu:false,
    searchShow: true, // 显示搜索字段
    // excelBtn: true,
    // printBtn: true,
    labelWidth: 150,
    searchLabelWidth:100,
    addBtn:false,
    editBtn:false,
    viewBtn:false,
    delBtn:false,
    selection:true,
    // viewBtn: true,
    searchMenuSpan: 6,
    selectable:(row)=>{
      if(that.selectList&&that.selectList.length>0){
        let status = that.selectList[0].status
        let id = that.selectList[0].id
        let projectInfoId = that.selectList[0].projectInfoId
        let agentName = that.selectList[0].agentName
        if(status=='9'){
          //选中审核中的
          return row.id == id
        }else {
          //选中已审核的
          return row.projectInfoId==projectInfoId&&row.agentName==agentName&&row.status==status
        }

      }else{
        return true

      }
    },
    column: [
      {
        label: "所属项目",
        prop: "projectInfoId",
        type: "select", // 下拉选择
        search: true,
        props: {
          label: "projectName",
          value: "id",
        },
        dicUrl: "/chain/companypayment/getProjectList",
        minWidth:150,
        overHidden:true,
      },
      {
        label: "结算单号",
        prop: "settleNo",
        search:true,
        minWidth:160,
        overHidden:true,
      },
      {
        label: "结算申请时间",
        prop: "applyDatetime",
        type: "datetime",
        searchRange:true,
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        minWidth:160,
        overHidden:true,
        sortable:"custom",
      },
      {
        label: "结算申请人",
        prop: "agentName",
        search: true,
        minWidth:106,
        overHidden:true,
        sortable:"custom",
      },
      {
        label: "核算人",
        prop: "settleName",
        search: true,
        minWidth:86,
        overHidden:true,
        sortable:"custom",
      },
      {
        label: "承运人",
        prop: "payeeName",
        search: true,
        minWidth:70,
        overHidden:true,
      },
      {
        label: "结算数量",
        prop: "settleNum",
        minWidth:80,
        overHidden:true,
      },
      {
        label: "结算金额",
        prop: "settleAmount",
        minWidth:80,
        overHidden:true,
      },
      {
        label: "结算单状态",
        prop: "status",
        type: "select", // 下拉选择
        dicData: [
          {
            label: "核算中",
            value: "1",
          },
          {
            label: "已审核",
            value: "2",
          },
          {
            label: "已打款",
            value: "3",
          },
          {
            label: "已驳回",
            value: "4",
          },
          {
            label: "已取消",
            value: "5",
          },
          {
            label: "审核中",
            value: "9",
          },
        ],
        search:true,
        minWidth:106,
        overHidden:true,
        sortable:"custom",
      },
      {
        label: "证件齐全运单数",
        prop: "companyWaybillCount",
        minWidth:130,
        overHidden:true,
      },
      {
        label: "证件齐全金额",
        prop: "completeDocumentsPrice",
        minWidth:106,
        overHidden:true,
      },
      {
        label: "拆单备注",
        prop: "staffRemark",
        minWidth:130,
        overHidden:true,
        search: true,
      },
    ],
  };
}
export const tableOption2 = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true, // 显示搜索字段
  // excelBtn: true,
  // printBtn: true,
  menu:true,
  labelWidth: 150,
  searchLabelWidth:100,
  addBtn:false,
  editBtn:false,
  viewBtn:false,
  delBtn:false,
  // viewBtn: true,
  searchMenuSpan: 6,
  menuWidth:200,
  column: [
    {
      label: "所属项目",
      prop: "projectInfoId",
      type: "select", // 下拉选择
      search: true,
      props: {
        label: "projectName",
        value: "id",
      },
      dicUrl: "/chain/companypayment/getProjectList",
      minWidth:150,
      overHidden:true,
    },
    {
      label: "支付单号",
      prop: "paymentNo",
      search:true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "结算单号",
      prop: "settleNo",
      search:true,
      minWidth:160,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "结算申请人",
      prop: "agentName",
      search: true,
      minWidth:106,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "承运人",
      prop: "payeeName",
      search: true,
      minWidth:70,
      overHidden:true,
    },
    {
      label: "结算单数量",
      prop: "settleCnt",
      minWidth:100,
      overHidden:true,
    },
    {
      label: "结算运单数量",
      prop: "waybillCnt",
      minWidth:106,
      overHidden:true,
    },
    {
      label: "结算合计金额(元)",
      prop: "settleAmt",
      minWidth:116,
      overHidden:true,
    },
    {
      label: "结算单状态",
      prop: "companySettleStatus",
      type: "select", // 下拉选择
      dicData: [
        {
          label: "核算中",
          value: "1",
        },
        {
          label: "已审核",
          value: "2",
        },
        {
          label: "已打款",
          value: "3",
        },
        {
          label: "已驳回",
          value: "4",
        },
        {
          label: "已取消",
          value: "5",
        },
        {
          label: "审核中",
          value: "9",
        },
      ],
      search:true,
      minWidth:106,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "支付单状态",
      prop: "status",
      type: "select", // 下拉选择
      dicData: [
        {
          label: "待支付",
          value: "1",
        },
        {
          label: "已审批",
          value: "2",
        },
        {
          label: "已驳回",
          value: "3",
        },
        {
          label: "已支付",
          value: "4",
        },
        {
          label: "支付中",
          value: "9",
        },
      ],
      search:true,
      minWidth:106,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "证件齐全运单数",
      prop: "companyWaybillCount",
      minWidth:130,
      overHidden:true,
    },
    {
      label: "证件齐全金额",
      prop: "completeDocumentsPrice",
      minWidth:106,
      overHidden:true,
    },
    // {
    //   label: "流程",
    //   prop: "test2",
    // },
  ],
};
