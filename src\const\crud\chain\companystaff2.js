import {isMobileNumber} from '@/util/validate'

export const tableOption = {
  dialogType: "drawer",
  dialogWidth: "50%",
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  searchSpan: 8,
  searchMenuSpan: 8,
  delBtnText:'禁用',
  // searchIcon:true,
  // searchIndex:2,
  searchCustom:2,
  routerName:"companystaff",
  selection: false,  //表格第一列多选框
  column: [
    {
      label: "姓名",
      prop: "name",
      sortable: true,
      search: true,
      order:2,
      // editDisabled: false,
      rules: [
        {
          required: true,
          message: "请输入姓名",
          trigger: "blur",
        },
        {
          max: 50,
          message: "长度在不能超过50个字符",
        },
      ],
      overHidden:true,
    },
    {
      label: "手机号码",
      prop: "mobile",
      sortable: true,
      search: true,
      maxlength: 11,
      // editDisabled: false,
      number: true,
      rules: [
        {
          required: true,
          message: "请输入手机号码",
          trigger: "blur",
        },
        {
          validator: isMobileNumber,
          trigger: "blur",
        },
      ],
      minWidth:110,
      overHidden:true,
    },
    {
      label: "所在部门",
      prop: "companyDeptId",
      sortable: true,
      type: "tree",
      search: true,
      searchOrder:1,
      order:2,
      props: {
        label: "deptName",
        value: "id",
      },
      defaultExpandAll: true,
      dicUrl: "/chain/companydept/tree",
      rules: [
        {
          required: true,
          message: "请选择所在部门",
          trigger: "blur",
        },
      ],
      width:160,
      overHidden:true,
    },
    {
      label: "性别",
      prop: "sex",
      sortable: true,
      hide: true,
      type: "radio",
      dicData: [
        {
          label: "男",
          value: "1",
        },
        {
          label: "女",
          value: "0",
        },
      ],
      rules: [
        {
          required: true,
          message: "请选择性别",
          trigger: "blur",
        },
      ],
      overHidden:true,
    },
    {
      label: "职位",
      prop: "companyPositionId",
      sortable: true,
      type: "select",
      props: {
        label: "positionName",
        value: "id",
      },
      dicUrl: "/chain/companyposition/list",
      search:true,
      rules: [
        {
          required: true,
          message: "请选择职位",
          trigger: "blur",
        },
      ],
      formatter:(val)=>{
        return  val.positionName
      },
      overHidden:true,
    },
    {
      label: '状态',
      prop: 'status',
      sortable: true,
      type: 'select',   // 下拉选择
      search: true,
      dicData: [
        {
          label: '有效',
          value: '1'
        },
        {
          label: '无效',
          value: '0'
        }],
      value:'1',
      searchIndex:0,
      // selectIndex:0,
      // cascaderIndex:0,   //默认选择第几项
      rules: [
        {
          required: true,
          message: '请输入状态：1=有效、0=无效',
          trigger: 'blur'
        },
        {
          max: 1,
          message: '长度在不能超过1个字符'
        },
      ],
      overHidden:true,
    },
  ],
};
