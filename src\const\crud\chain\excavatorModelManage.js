import { isMobileNumber } from "@/util/validate";
export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  index:true,
  searchMenuSpan: 6,
  labelWidth:110,
  menuWidth:140,
  column: [
    {
      label: "挖机车主",
      prop: "ownerName",
      search: true,
      sortable: true,
      maxlength: 10,
      span:24,
      rules: [
        {
          required: true,
          message: "请输入挖机车主",
          trigger: "blur",
        },
        {
          min: 2,
          message: "长度不能小于2个字符",
        },
      ],
      minWidth:96,
      overHidden:true,
    },
    {
      label: "挖机车主手机",
      prop: "ownerMobile",
      search: true,
      maxlength: 11,
      sortable: true,
      span:24,
      searchLabelWidth:110,
      rules: [
        {
          required: true,
          message: "请输入手机号码",
          trigger: "blur",
        },
        {
          validator: isMobileNumber,
          trigger: "blur",
        },
      ],
      minWidth:120,
      overHidden:true,
    },
    {
      label: "机械型号",
      prop: "machineCode",
      search: true,
      sortable: true,
      maxlength: 30,
      span:24,
      rules: [
        {
          required: true,
          message: "请输入机械型号",
          trigger: "blur",
        },
        {
          min: 2,
          message: "长度不能小于2个字符",
        },
      ],
      minWidth:160,
      overHidden:true,
    },
    {
      label: "新增人",
      prop: "createName",
      sortable: true,
      display:false,
      minWidth:86,
      overHidden:true,
    },
    {
      label: "新增时间",
      prop: "createDatetime",
      sortable: true,
      display:false,
      minWidth:140,
      overHidden:true,
    },
    {
      label: "修改人",
      prop: "updateName",
      sortable: true,
      display:false,
      minWidth:86,
      overHidden:true,
    },
    {
      label: "修改时间",
      prop: "updateDatetime",
      sortable: true,
      display:false,
      minWidth:140,
      overHidden:true,
    },

  ],
};
