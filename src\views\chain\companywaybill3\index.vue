<template>
  <div class="execution">
    <basic-container>
      <my-crud
        ref="crud"
        :page.sync="page"
        :search.sync="search"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @row-update="handleUpdate"
        @selection-change="handleSelectionChange"
        @row-save="handleSave"
        @row-del="handleDel"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <template slot="settleCardNoSearch" slot-scope="{ row }">
          <tags v-model="statements"></tags>
        </template>
        <template slot="inPicture" slot-scope="{ row }">
          <el-image
            v-if="row.inPicture"
            style="width: 20px; height: 20px"
            :src="filterImg(row.inPicture)"
            :preview-src-list="filterImgs(row.inPicture)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="goPicture" slot-scope="{ row }">
          <el-image
            v-if="row.goPicture"
            style="width: 20px; height: 20px"
            :src="filterImg(row.goPicture)"
            :preview-src-list="filterImgs(row.goPicture)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="entrancePictureForm" slot-scope="{ row }">
          <span v-if="row.entrancePicture">
            <el-tooltip
              class="item"
              effect="dark"
              content="此处只显示一张，有多张请点击图片预览"
              placement="top-start"
            >
              <el-image
                style="width: 20px; height: 20px"
                :src="filterImg(row.entrancePicture)"
                :preview-src-list="filterImgs(row.entrancePicture)"
              >
              </el-image>
            </el-tooltip>
          </span>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="inPictureForm" slot-scope="{ row }">
          <span v-if="row.inPicture">
            <el-tooltip
              class="item"
              effect="dark"
              content="此处只显示一张，有多张请点击图片预览"
              placement="top-start"
            >
              <el-image
                style="width: 50px; height: 50px"
                :src="filterImg(row.inPicture)"
                :preview-src-list="filterImgs(row.inPicture)"
              >
              </el-image>
            </el-tooltip>
          </span>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="goPictureForm" slot-scope="{ row }">
          <span v-if="row.goPicture">
            <el-tooltip
              class="item"
              effect="dark"
              content="此处只显示一张，有多张请点击图片预览"
              placement="top-start"
            >
              <el-image
                style="width: 50px; height: 50px"
                :src="filterImg(row.goPicture)"
                :preview-src-list="filterImgs(row.goPicture)"
              >
              </el-image>
            </el-tooltip>
            <span v-if="filterImgs(row.goPicture).length > 1"
              >多张图片点击图片进行预览</span
            >
          </span>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="ticketImgForm" slot-scope="{ row }">
          <span v-if="row.ticketImg">
            <el-tooltip
              class="item"
              effect="dark"
              content="此处只显示一张，有多张请点击图片预览"
              placement="top-start"
            >
              <el-image
                style="width: 50px; height: 50px"
                :src="filterImg(row.ticketImg)"
                :preview-src-list="filterImgs(row.ticketImg)"
              >
              </el-image>
            </el-tooltip>
            <span v-if="filterImgs(row.ticketImg).length > 1"
              >多张图片点击图片进行预览</span
            >
          </span>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="menu" slot-scope="scope" class="menuSlot">
          <el-button
            type="text"
            v-if="permissions['chain:companywaybill3:remark']"
            icon="el-icon-document-add"
            size="small"
            plain
            @click="setRemark(scope.row, scope.index)"
          >
            设置PC后台备注</el-button
          >
          <el-button
            icon="el-icon-document-add"
            size="small"
            type="text"
            plain
            v-if="permissions['chain:ledgerDig:setLoadCarPrice']"
            @click="batchSetLoadCarPrice(scope.row, scope.index)"
          >
            设置装车价
          </el-button>
        </template>
        <template slot="menuLeft" slot-scope="{ row }">
          <el-button
            icon="el-icon-download"
            v-if="permissions['chain:companywaybill3:excel']"
            size="small"
            :loading="btnLoading"
            type="primary"
            @click="exOutExcel"
          >
            导出
          </el-button>
          <el-button
            icon="el-icon-document-add"
            v-if="permissions['chain:companywaybill3:remark']"
            size="mini"
            type="primary"
            :disabled="multipleSelection.length == 0"
            @click="batchSetRemark"
          >
            批量设置PC后台备注
          </el-button>
          <!-- v-if="permissions['chain:ledgerDig:setLoadCarPrice']" -->

          <el-button
            icon="el-icon-document-add"
            size="mini"
            type="primary"
            v-if="permissions['chain:ledgerDig:setLoadCarPrice']"
            :disabled="multipleSelection.length == 0"
            @click="batchSetLoadCarPrice"
          >
            批量设置装车价
          </el-button>
        </template>
      </my-crud>
    </basic-container>
  </div>
</template>

<script>
import {
  queryExcavatorLoadingPage as getPage,
  getObj,
  addObj,
  putObj,
  delObj,
  batchUpdateRemark,
  batchUpdateInPreparePrice,
  exportExcavatorLoadingInfo,
} from "@/api/chain/companywaybill";
import { tableOption } from "@/const/crud/chain/companywaybill3";
import { mapGetters } from "vuex";
import { exportOut } from "@/util/down.js";
import tags from "./tags.vue";
export default {
  name: "companywaybill3",
  data() {
    return {
      statements: [],
      form: {},
      tableData: [],
      page: {
        pageSizes: [10, 20, 50, 100, 500, 1000, 5000],
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: "in_datetime", //降序字段
      },
      paramsSearch: {},
      searchForm: {},
      tableLoading: false,
      tableHeight: "100px",
      tableOption: tableOption,
      marker: null,
      map: null,
      polyline: null,
      speed: 50,
      firstArr: [113.98074, 22.55251],
      lineArr: [
        [121.5389385, 31.21515044],
        [121.5389385, 31.29615044],
        [121.5273285, 31.21515044],
      ],
      searchDom: {},
      btnLoading: false,
      isOne: true,
      search: {
        projectInfoId: "",
        machineCode: "",
        ownerName: "",
      },
      multipleSelection: [],
    };
  },
  components: {
    tags,
  },
  created() {},
  mounted() {
    // this.searchDom = this.$refs.crud.$refs.headerSearch;
    // window.addEventListener("resize", this.func);
  },
  activated() {
    // setTimeout(() => {
    //   let tableHeight = document.querySelector(".el-table").offsetTop;
    //   this.$refs.crud.tableHeight = window.innerHeight - tableHeight - 182;
    //   this.$refs.crud.tableOption.height =
    //     window.innerHeight - tableHeight - 182;
    //   this.$refs.crud.doLayout();
    // }, 100);
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        // addBtn: this.permissions['chain:companywaybill:add'] ? true : false,
        // delBtn: this.permissions['chain:companywaybill:del'] ? true : false,
        // editBtn: this.permissions['chain:companywaybill:edit'] ? true : false,
        viewBtn: this.permissions["chain:companywaybill3:get"] ? true : false,
      };
    },
  },
  watch: {
    // "searchDom.searchShow": {
    //   handler(newVal, oldVal) {
    //     setTimeout(() => {
    //       let tableHeight = document.querySelector(".el-table").offsetTop;
    //       this.$refs.crud.tableHeight = window.innerHeight - tableHeight - 182;
    //       this.$refs.crud.tableOption.height =
    //         window.innerHeight - tableHeight - 182;
    //       this.$refs.crud.doLayout();
    //     }, 300);
    //   },
    //   deep: true,
    // },
  },
  methods: {
    batchUpdateInPreparePrice(data) {
      batchUpdateInPreparePrice(data).then((res) => {
        this.$message.success("操作成功");
        this.getPage(this.page);
      });
    },

    batchSetLoadCarPrice(row) {
      this.$prompt("设置装车价", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "number",
        // inputPattern:/^[0-9]+(\.[0-9]{1,2})?$/,
        inputPlaceholder: "请设置装车价",
        inputValidator: (value) => {
          if (!value) {
            return "装车价不能为空";
          } else if (!/^[0-9]+(\.[0-9]{1,2})?$/.test(value)) {
            return "请输入两位小数";
          }
        },
        // inputErrorMessage: "请输入装车价",
      })
        .then(({ value }) => {
          let data = {};
          if (row.id) {
            data = {
              inPreparePrice: value,
              ids: [row.id],
            };
          } else {
            data = {
              inPreparePrice: value,
              ids: this.multipleSelection.map((v) => v.id),
            };
          }

          this.batchUpdateInPreparePrice(data);
        })
        .catch(() => {});
    },
    exportOut,
    // func() {
    //   let tableHeight = document.querySelector(".el-table").offsetTop;
    //   this.$refs.crud.tableHeight = window.innerHeight - tableHeight - 182;
    //   this.$refs.crud.tableOption.height =
    //     window.innerHeight - tableHeight - 182;
    //   this.$refs.crud.doLayout();
    // },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      console.log(this.paramsSearch);
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params = {}) {
      this.tableLoading = true;
      //第一次调用
      console.log(params);
      if (this.isOne && this.$route.query.info) {
        let info = JSON.parse(this.$route.query.info);
        this.search.projectInfoId = info.projectInfoId;
        this.search.machineCode = info.machineCode;
        this.search.ownerName = info.ownerName;
        this.search.shiftName = info.shiftName;
        this.search.inStaffName = info.inStaffName;
        this.search.goDatetimeStart = info.goDatetimeStart;
        this.search.goDatetimeEnd = info.goDatetimeEnd;
        this.search.status = info.status;
        if (info.goDatetimeStart) {
          this.search.goDatetime = [info.goDatetimeStart, info.goDatetimeEnd];
        }
      }
      Object.assign(params, this.search);
      if (params) {
        if (params.hasOwnProperty("inDatetime")) {
          params.inDatetimeStart = params.inDatetime[0];
          params.inDatetimeEnd = params.inDatetime[1];
          delete params.inDatetime;
        }
        if (params.hasOwnProperty("goDatetime")) {
          params.goDatetimeStart = params.goDatetime[0];
          params.goDatetimeEnd = params.goDatetime[1];
          delete params.goDatetime;
        }
        if (params.hasOwnProperty("inShiftTime")) {
          params.inShiftTimeStart = params.inShiftTime[0];
          params.inShiftTimeEnd = params.inShiftTime[1];
          delete params.inShiftTime;
        }
        if (params.hasOwnProperty("goShiftTime")) {
          params.goShiftTimeStart = params.goShiftTime[0];
          params.goShiftTimeEnd = params.goShiftTime[1];
          delete params.goShiftTime;
        }
      }
      if (this.statements.length > 0) {
        params.settleCardNo = this.statements.join(",");
      }
      getPage(
        this.filterForm(Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
            pageRoute: "companywaybill3",
          },
          params,
          this.paramsSearch
        ))
      )
        .then((response) => {
          this.isOne = false;
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.isOne = false;
          this.tableData = [];
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    filterImg(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? null : url[0];
    },
    filterImgs(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? [] : url;
    },
    exOut(value) {
      this.$router.push({ path: "/exportExc/exportExc" });
    },
    exOutExcel() {
      let params = this.filterForm(Object.assign(
        { descs: this.page.descs, ascs: this.page.ascs },
        this.paramsSearch,
        this.search
      ))
      this.getPage(this.page);

      if (params) {
        if (params.hasOwnProperty("inDatetime")) {
          params.inDatetimeStart = params.inDatetime[0];
          params.inDatetimeEnd = params.inDatetime[1];
          delete params.inDatetime;
        }
        if (params.hasOwnProperty("goDatetime")) {
          params.goDatetimeStart = params.goDatetime[0];
          params.goDatetimeEnd = params.goDatetime[1];
          delete params.goDatetime;
        }
        if (params.hasOwnProperty("inShiftTime")) {
          params.inShiftTimeStart = params.inShiftTime[0];
          params.inShiftTimeEnd = params.inShiftTime[1];
          delete params.inShiftTime;
        }
        if (params.hasOwnProperty("goShiftTime")) {
          params.goShiftTimeStart = params.goShiftTime[0];
          params.goShiftTimeEnd = params.goShiftTime[1];
          delete params.goShiftTime;
        }
      }
      for (const key in params) {
        if (key.includes("$") || params[key] == undefined) {
          delete params[key];
        }
      }
      params.pageRoute = "companywaybill3";
      let url = "/chain/companywaybill/exportExcavatorLoadingInfo";
      if(this.page.total>50000){
        this.$confirm("导出数据量太大,有可能导出失败,建议按条件查询分批导出,是否继续？", "提示", {
          confirmButtonText: "继续",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(()=>{
          this.exCel(params,url)
        }).catch(()=>{})
      }else{
        this.exCel(params,url)
      }
    },
    exCel(params,url){
      this.btnLoading = true
      exportExcavatorLoadingInfo(params).then(res=>{
        this.btnLoading = false
        this.$store.commit("SET_DOWN_EXCEL_SHOW", true);
      }).catch(err=>{
        this.$message.error('导出失败')
        this.btnLoading = false
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    batchSetRemark() {
      this.$prompt("PC后台备注", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入PC后台备注",
        inputValidator: (value) => {
          if (!value) {
            return "PC后台备注不能为空";
          }
        },
        inputErrorMessage: "请输入PC后台备注",
      })
        .then(({ value }) => {
          let arr = this.multipleSelection.map((item) => item.id);
          let obj = {
            remark: value,
            ids: arr,
          };
          this.editPcRemark(obj);
        })
        .catch(() => {});
    },
    setRemark(row) {
      this.$prompt("PC后台备注", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入PC后台备注",
        inputValue: row.inPcRemark || "",
        inputValidator: (value) => {
          if (!value) {
            return "PC后台备注不能为空";
          }
        },
        inputErrorMessage: "请输入PC后台备注",
      })
        .then(({ value }) => {
          this.editPcRemark({
            ids: [row.id],
            remark: value,
          });
        })
        .catch(() => {});
    },
    editPcRemark(arr) {
      batchUpdateRemark(arr).then((res) => {
        this.$message.success("操作成功");
        this.getPage(this.page);
      });
    },
  },
  destroyed() {
    // window.removeEventListener("resize", this.func);
  },
};
</script>
<style>
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
<style lang="scss" scoped>
.tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .tag {
    margin-right: 10px;
  }
}
.input-card {
  position: absolute;
  right: 40px;
  bottom: 40px;
  background-color: #fff;
  padding: 10px;
  .my-row {
    margin-top: 15px;
  }
}
/deep/ .amap-marker-label {
  background: rgba(0, 0, 0, 0.4);
}
/deep/.left-label {
  padding: 2px;
  display: flex;
  align-items: center;
  color: #ffff;
}

/deep/ .left-label img {
  width: 14px;
  height: 14px;
  margin-right: 10px;
}
</style>
