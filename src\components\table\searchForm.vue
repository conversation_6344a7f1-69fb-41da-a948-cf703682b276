<!--表格组件 -->
<template>
  <div style="margin-top: 10px" class="searchForm">
    <avue-form
      :option="searchOption"
      ref="form"
      @submit="searchChange"
      v-model="searchForm"
    >
    </avue-form>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
export default {
  props: {
    searchOption: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      searchForm: {}, //筛选值
    };
  },
  computed: {},
  components: {},
  watch: {},
  mounted() {
    //先获取配置
    console.log('searchOption',this.searchOption);
  },
  methods: {
    //点击查询
    searchChange(form, done) {
      console.log(form);
      this.$emit("searchChange", form, done);
    },
  },
  created() {},
};
</script>
<style lang="scss"></style>
