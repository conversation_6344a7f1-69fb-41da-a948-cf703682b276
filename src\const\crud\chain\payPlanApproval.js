export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true, // 显示搜索字段
  // excelBtn: true,
  // printBtn: true,
  labelWidth: 150,
  addBtn: false,
  editBtn: false,
  viewBtn: false,
  defaultSort: {
    prop: "flowDatetime",
    order: "descending",
  },
  // viewBtn: true,
  searchIndex:3,
  searchIcon:true,
  searchMenuSpan: 6,
  searchLabelWidth: 100,
  menuWidth: 120,
  column: [
    {
      label: "所属项目",
      prop: "projectName",
      minWidth:150,
      search:true,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "结算单号",
      prop: "settleNo",
      search: true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "结算申请时间",
      prop: "applyDatetime",
      type: "datetime",
      searchRange: true,
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:160,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "审批发起时间",
      prop: "flowDatetime",
      type: "datetime",
      searchRange: true,
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:160,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "计划名称",
      prop: "companyPaymentPlanName",
      minWidth:106,
      search: true,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "计划号",
      prop: "companyPaymentPlanNo",
      search: true,
      minWidth:86,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "创建人",
      prop: "createName",
      search: true,
      minWidth:86,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "承运人",
      prop: "payeeName",
      search: true,
      minWidth:86,
      overHidden:true,
      sortable:"custom",
    },
    // {
    //   label: "结算数量",
    //   prop: "settleNum",
    // },
    {
      label: "支付计划金额",
      prop: "companyPaymentPlanAmount",
      minWidth:126,
      search: true,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "核算数量",
      prop: "checkNum",
      minWidth:100,
      // search: true,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "核算金额",
      prop: "checkAmount",
      minWidth:100,
      search: true,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "核算驳回数量",
      prop: "rejectCount",
      minWidth:100,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "核算驳回金额",
      prop: "rejectPrice",
      minWidth:100,
      search: true,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "结算申请时间",
      prop: "searchDate",
      type: "date",
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd',
      hide:true,
      showColumn:false,
    },
    {
      label: "审批发起时间",
      prop: "searchDate2",
      type: "date",
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd',
      hide:true,
      showColumn:false,
    },
  ],
};
