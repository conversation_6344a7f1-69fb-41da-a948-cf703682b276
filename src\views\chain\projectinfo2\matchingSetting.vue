<template>
  <div class="driverDetail">
    <el-drawer
      size="700px"
      title="撮合设置"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
      :wrapperClosable='false'
    >
      <basic-container>
        <el-tabs :value="activeName" @tab-click="tabClick">
          <el-tab-pane label="车队" name="1"> </el-tab-pane>
          <el-tab-pane label="泥尾" name="2"> </el-tab-pane>
          <el-tab-pane label="资源" name="3"> </el-tab-pane>
          <el-tab-pane label="回填" name="4"> </el-tab-pane>
        </el-tabs>
        <avue-form ref="form" :option="option" v-model="form" @submit="submit">
          <template slot="price" slot-scope="scope">
            <div style="display: flex">
              <el-input-number style="margin-right:2px;flex:1" @blur="changPrice" v-model="form.minPayPrice" placeholder="开始价格" :min="0" :max="999999.99" :precision="2" :controls="false" size="small" :step="0.01" step-strictly></el-input-number>
              <span>至</span>
              <el-input-number style="margin-left: 2px;flex:1" @blur="changPrice" v-model="form.maxPayPrice" placeholder="结束价格" :min="0" :max="999999.99" :precision="2" :controls="false" size="small" :step="0.01" step-strictly></el-input-number>
            </div>
        </template>
          <template slot="menuForm">
            <el-button icon="el-icon-close" @click="cancelModal"
              >取消</el-button
            >
          </template>
        </avue-form>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { projectakematch, projectakematchset } from "@/api/chain/projectinfo";
import COLUMN from "./matchingData.js";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  data() {
    return {
      form: {},
      counmn:COLUMN(this),
      option: {
        submitBtn: true,
        emptyBtn: false,
        column: COLUMN(this).COLUMN1,
      },
      activeName: "1",
    };
  },
  created() {},
  mounted() {
    this.getData();
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    tabClick(val, event) {
      if (this.activeName == val.name) {
        return false;
      }
      this.activeName = val.name;
      this.form = {
        id:"",
        minPayPrice:undefined,
        maxPayPrice:undefined,
        remark:"",
        imgPath:"",
        carsNumber:undefined,
        soilType:"",
        settlementCycle:"",
      }
      this.$refs.form.form = {}
      this.option.column = this.counmn['COLUMN'+this.activeName]
      console.log(this.$refs.form);
      this.getData();
    },
    getData() {
      projectakematch(this.info.id, this.activeName).then((res) => {
        this.form = res.data.data ||{}
        //项目地址 //土质 //泥尾 赋值
        this.form.projectAddress = this.info.projectAddress
        this.form.listSoilType = this.info.listSoilType.join(',')
        this.form.garbageNames = this.info.garbageNames
      }).catch(()=>{
        this.form.projectAddress = this.info.projectAddress
        this.form.listSoilType = this.info.listSoilType.join(',')
        this.form.garbageNames = this.info.garbageNames
      })
    },
    changPrice(){
      this.$refs.form.validateField('price')
    },
    submit(form, done) {
      console.log(form);
      console.log(this.form);
      form.makeMatchType = this.activeName
      form.projectInfoId = this.info.id
      projectakematchset(form).then(res=>{
        done()
        this.$message.success("设置成功！")
      }).catch(()=>{
        done()
      })
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
/deep/ .el-upload-list--picture-card .el-upload-list__item{
    width: 70px;
    height: 70px;
}
 /deep/ .el-upload--picture-card{
    width: 70px;
    height: 70px;
    line-height: 70px;
  }
</style>
