<template>
  <div class="vehicleOwner">
    <el-drawer size="100%"
               :data="visible"
               :visible.sync="visible"
               title="运单列表"
               :before-close="cancelModal">
        <companyWaybill :info="info"></companyWaybill>
    </el-drawer>
  </div>
</template>

<script>
import companyWaybill from '@/views/chain/waybillPreset';
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: ()=>{
        return {}
      },
    },
  },
  components: {
    companyWaybill,
  },
  data () {
    return {

    };
  },
  created () {},
  mounted () {},
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__body{
  padding: 0;
}
</style>
