import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/matchplan/getFleetMatchList',
        method: 'get',
        params: query
    })
}
export function addObj(obj) {
  return request({
      url: '/chain/matchplan/postPlan',
      method: 'post',
      data: obj
  })
}
export function pause(id) {
  return request({
      url: '/chain/matchplan/pause/'+id,
      method: 'get',
  })
}
export function renew(id) {
  return request({
      url: '/chain/matchplan/renew/'+id,
      method: 'get',
  })
}
export function listByGarbage() {
  return request({
      url: '/chain/garbage/listByGarbage',
      method: 'get',
  })
}

export function getPoolPage(params) {
  return request({
      url: '/chain/matchplanpool/getPoolPage',
      method: 'get',
      params
  })
}
//邀请
export function invite(id,scene) {
  return request({
      url: `/chain/matchplanpool/invite/${id}/${scene}`,
      method: 'get',
  })
}
//接受邀请
export function acceptInvite(id,scene) {
  return request({
      url: `/chain/matchplanpool/receive/${id}/${scene}`,
      method: 'get',
  })
}
//取消邀请
export function cancelInvite(id) {
  return request({
      url: `/chain/matchplanpool/cancel/${id}`,
      method: 'get',
  })
}
//匹配刷新
export function matchRefresh(params) {
  return request({
      url: `/chain/matchplanpool/garbageFleetMatchPlan`,
      method: 'get',
      params
  })
}
