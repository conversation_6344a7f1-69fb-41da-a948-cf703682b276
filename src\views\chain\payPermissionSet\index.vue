<template>
  <div class="payPassSetting">
    <basic-container>
      <el-tabs v-model="activeName">
        <el-tab-pane label="支付密码设置" name="1" value="1">
          <avue-form
            :option="option1"
            ref="form1"
            v-model="form1"
            @submit="submit"
          >
          </avue-form>
        </el-tab-pane>
        <el-tab-pane
          label="手机验证码设置"
          :disabled="tabDisabled"
          name="2"
          value="2"
        >
          <avue-form
            :option="option"
            ref="form"
            v-model="form"
            @submit="submit"
          >
            <template slot-scope="{ disabled, size }" slot="sourcePayPhone">
              <el-input
                size="small"
                v-model="form.sourcePayPhone"
                disabled
                readonly
                maxlength="11"
              >
                <template slot="append">
                  <span
                    @click="getCode(1, form.sourcePayPhone)"
                    style="color: #66b1ff; cursor: pointer"
                    :class="[{ display: msgKey1 }]"
                    >{{ msgText1 }}</span
                  >
                </template>
              </el-input>
            </template>
            <template slot-scope="{ disabled, size }" slot="payPhone">
              <el-input
                size="small"
                v-model="form.payPhone"
                maxlength="11"
                placeholder="请输入 手机号"
              >
                <template slot="append">
                  <span
                    @click="getCode(2, form.payPhone)"
                    style="color: #66b1ff; cursor: pointer"
                    :class="[{ display: msgKey2 }]"
                    >{{ msgText2 }}</span
                  >
                </template>
              </el-input>
            </template>
          </avue-form>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import { getCode, updatePayPwd, getConfig } from "@/api/chain/payPermissionSet";
import { mapGetters } from "vuex";
import { isMobileNumber } from "@/util/validate";
const MSGINIT = "发送验证码",
  MSGSCUCCESS = "${time}秒后可重发",
  MSGTIME = 60;

export default {
  name: "payPermissionSet",
  inject: ["reload"],
  data() {
    var validatePass = (rule, value, callback) => {
      if (this.form1.confirmPayPwd !== "") {
        if (value !== this.form1.payPwd) {
          callback(new Error("两次输入密码不一致!"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      form1: {
        sourcePayPwd: "",
        isPayPwd: "1",
        changPass: "0",
        payPwd: "",
        confirmPayPwd: "",
      },
      form: {
        isPayPhoneVerification: "0",
        sourcePayPhone: "",
        sourcePayPhoneVerifyCode: "",
        changMobile: "0",
        payPhone: "",
        payPhoneVerifyCode: "",
      },
      option1: {
        labelWidth: 210,
        submitText: "保存",
        position: "left",
        emptyBtn: false,
        cancelBtn: true,
        tabs: true,
        column: [
          {
            label: "支付密码设置",
            prop: "isPayPwd",
            type: "switch",
            row: true,
            dicData: [
              {
                label: "",
                value: "0",
              },
              {
                label: "",
                value: "1",
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "原密码",
            prop: "sourcePayPwd",
            row: true,
            type: "password",
            display: true,
            showPassword: true,
            maxlength: 6,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
              {
                min:6,
                message: "不能少于6位",
                trigger: "blur",
              },
            ],
          },
          {
            label: "更改密码",
            prop: "changPass",
            type: "switch",
            row: true,
            change: ({ value }) => {
              var payPwd = this.findObject(this.option1.column, "payPwd"); //密码
              var confirmPayPwd = this.findObject(
                this.option1.column,
                "confirmPayPwd"
              ); //新手机验证码
              if (value) {
                payPwd.display = value == 1;
                confirmPayPwd.display = value == 1;
              }
            },
            dicData: [
              {
                label: "",
                value: "0",
              },
              {
                label: "",
                value: "1",
              },
            ],
          },
          {
            label: "密码",
            prop: "payPwd",
            row: true,
            type: "password",
            display: true,
            showPassword: true,
            maxlength: 6,
            blur: () => {
              this.$refs.form.validateField("confirmPayPwd");
            },
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
              {
                min:6,
                message: "不能少于6位",
                trigger: "blur",
              },
            ],
          },
          {
            label: "确认密码",
            prop: "confirmPayPwd",
            row: true,
            type: "password",
            display: true,
            showPassword: true,
            maxlength: 6,
            rules: [
              { required: true, validator: validatePass, trigger: "blur" },
              {
                min:6,
                message: "不能少于6位",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      option: {
        labelWidth: 210,
        submitText: "保存",
        position: "left",
        emptyBtn: false,
        cancelBtn: true,
        tabs: true,
        column: [
          {
            label: "手机验证码设置",
            prop: "isPayPhoneVerification",
            type: "switch",
            row: true,
            dicData: [
              {
                label: "",
                value: "0",
              },
              {
                label: "",
                value: "1",
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "原手机号",
            prop: "sourcePayPhone",
            maxlength: 11,
            display: true,
            row: true,
            rules: [
              {
                required: true,
                validator: isMobileNumber,
                trigger: "blur",
              },
            ],
          },
          {
            label: "验证码",
            prop: "sourcePayPhoneVerifyCode",
            row: true,
            display: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "更改手机号",
            prop: "changMobile",
            type: "switch",
            row: true,
            change: ({ value }) => {
              var payPhone = this.findObject(this.option.column, "payPhone"); //新手机号
              var payPhoneVerifyCode = this.findObject(
                this.option.column,
                "payPhoneVerifyCode"
              ); //新手机验证码
              console.log(value);
              if (value) {
                console.log(12121);
                payPhone.display = value == 1;
                payPhoneVerifyCode.display = value == 1;
              }
            },
            dicData: [
              {
                label: "",
                value: "0",
              },
              {
                label: "",
                value: "1",
              },
            ],
          },
          {
            label: "手机号",
            prop: "payPhone",
            maxlength: 11,
            row: true,
            display: true,
            rules: [
              {
                required: true,
                validator: isMobileNumber,
                trigger: "blur",
              },
            ],
          },
          {
            label: "验证码",
            prop: "payPhoneVerifyCode",
            row: true,
            display: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      msgText1: MSGINIT,
      msgTime1: MSGTIME,
      msgKey1: false,
      msgText2: MSGINIT,
      msgTime2: MSGTIME,
      msgKey2: false,
      time1: null, //定时器
      time2: null, //定时器
      hasPayPwd: false, //是否设置支付密码
      activeName: "1",
      tabDisabled: false,
    };
  },
  watch: {},
  created() {},
  mounted() {
    this.getConfig();
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  methods: {
    isMobileNumber,
    getConfig() {
      getConfig().then((res) => {
        this.hasPayPwd = res.data.data.hasPayPwd;
        this.form1.isPayPwd = res.data.data.isPayPwd || "1";
        this.form.isPayPhoneVerification =
          res.data.data.isPayPhoneVerification || "0";
        this.form.sourcePayPhone = res.data.data.sourcePayPhone;
        var sourcePayPwd = this.findObject(this.option1.column, "sourcePayPwd");
        var sourcePayPhone = this.findObject(
          this.option.column,
          "sourcePayPhone"
        );
        var sourcePayPhoneVerifyCode = this.findObject(
          this.option.column,
          "sourcePayPhoneVerifyCode"
        );
        var payPhone = this.findObject(this.option.column, "payPhone"); //新手机号
        var payPhoneVerifyCode = this.findObject(
          this.option.column,
          "payPhoneVerifyCode"
        ); //新手机验证码
        var payPwd = this.findObject(this.option1.column, "payPwd"); //密码
        var confirmPayPwd = this.findObject(
          this.option1.column,
          "confirmPayPwd"
        ); //再次密码
        var changMobile = this.findObject(this.option.column, "changMobile"); //更改手机号
        var changPass = this.findObject(this.option1.column, "changPass"); //更改手机号
        this.tabDisabled = !this.hasPayPwd; //未设置密码不能点击第二个
        sourcePayPwd.display = this.hasPayPwd; //原密码隐藏
        changPass.display = this.hasPayPwd; //原密码隐藏
        // sourcePayPhone.display = this.hasPayPwd; //原手机号隐藏
        // sourcePayPhoneVerifyCode.display = this.hasPayPwd; //原手机验证码隐藏
        payPwd.display = !this.hasPayPwd;
        confirmPayPwd.display = !this.hasPayPwd;
        //有手机号
        sourcePayPhone.display = !!this.form.sourcePayPhone; //原手机号隐藏
        sourcePayPhoneVerifyCode.display = !!this.form.sourcePayPhone; //原手机验证码隐藏
        changMobile.display = !!this.form.sourcePayPhone; //更改手机号
        payPhone.display = !this.form.sourcePayPhone;
        payPhoneVerifyCode.display = !this.form.sourcePayPhone;
        console.log(payPhone);
      });
    },
    getCode(type, payPhone) {
      if (this["msgKey" + type]) return;
      const reg = /^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/
      const isPhone = reg.test(payPhone);
      if(payPhone.length<0||!isPhone){
        this.$message.error("请输入正确手机号码")
        return false
      }
      let param = {
        payPhone,
      };
      getCode(param).then((res) => {
        this.$message.success("验证码发送成功");
        this["msgText" + type] = MSGSCUCCESS.replace(
          "${time}",
          this["msgTime" + type]
        );
        this["msgKey" + type] = true;
        this["time" + type] = setInterval(() => {
          this["msgTime" + type]--;
          this["msgText" + type] = MSGSCUCCESS.replace(
            "${time}",
            this["msgTime" + type]
          );
          if (this["msgTime" + type] == 0) {
            this["msgTime" + type] = MSGTIME;
            this["msgText" + type] = MSGINIT;
            this["msgKey" + type] = false;
            clearInterval(this["time" + type]);
          }
        }, 1000);
      });
    },
    submit(form, done) {
      console.log(form);
      //没有选择更改密码
      if(form.sourcePayPwd&&form.changPass!=1){
        form.payPwd =  ""
        form.confirmPayPwd = ""
      }
      if(form.sourcePayPhone&&form.changMobile!=1){
        form.payPhone =  ""
        form.payPhoneVerifyCode = ""
      }
      updatePayPwd(form)
        .then((res) => {
          this.$message.success("设置成功");
          this.init();
          this.getConfig();
          done();
        })
        .catch(() => {
          done();
        });
    },
    init() {
      this.form = this.$options.data().form;
      this.form1 = this.$options.data().form1;
      clearInterval(this.time1);
      clearInterval(this.time2);
      this.msgText1= MSGINIT
      this.msgTime1= MSGTIME
      this.msgKey1= false
      this.msgText2= MSGINIT
      this.msgTime2= MSGTIME
      this.msgKey2= false
      this.time1= null //定时器
      this.time2= null //定时器
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
/deep/ .iphone .el-form-item__content {
  width: 100%;
}
</style>
