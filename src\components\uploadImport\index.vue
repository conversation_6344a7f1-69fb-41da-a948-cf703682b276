<template>
  <!-- 导入 -->
  <el-dialog
    width="400px"
    title="导入"
    center
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <slot></slot>
    <el-row>
      <el-col :span="5">
        <div style="line-height: 28px">上传文件：</div>
      </el-col>
      <el-col :span="19">
        <el-upload
          class="upload"
          action="#"
          :on-change="handleExcel"
          :limit="1"
          :file-list="fileList"
          :on-remove="handleRemove"
          accept=".xlsx,.xls"
          :auto-upload="false"
          :headers="headers"
        >
          <el-button size="mini" type="primary">选择文件</el-button>
        </el-upload>
      </el-col>
    </el-row>
    <el-row>
      备注：只支持xlsx、xls文件
      <span
        @click="openTemp"
        style="
          display: inline-block;
          color: #409eff;
          cursor: pointer;
          margin-top: 20px;
          margin-left: 20px;
        "
        >下载模板</span
      >
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="imtExcel"
        >导入</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default:()=>{
        return false
      },
    },
    templateUrl:{//导入模板地址
      type:String,
      default:""
    }
  },
  data() {
    return {
      btnLoading: false,
      formData:null,
      fileList:[],
      headers: {"Content-Type": "multipart/form-data;charset=UTF-8"},
    };
  },
  created() {},
  destroyed() {},
  mounted() {},
  beforeDestroy() {},
  computed: {},
  methods: {
    handleClose() {
      this.$emit("update:visible", false);
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      this.formData = null;
      this.fileList = [];
    },
    //导入表格
    handleExcel(file) {
      console.log(file);
      this.formData = new FormData(); //声明一个FormDate对象
      this.formData.append("file", file.raw); //把文件信息放入对象中
    },
    //上传表格
    imtExcel() {
      if(this.formData){
        this.btnLoading = true;
        this.$emit('imtExcel',this.formData)
      }else{
        this.$message.error("请先上传文件")
      }
    },
    init(){
      this.btnLoading = false
      this.formData = null;
      this.fileList = [];
    },
    openTemp() {
      if(this.templateUrl){
        window.open(this.templateUrl);
      }else{
        this.$emit('openTempUrl')
      }
    },
  },
};
</script>

<style lang="scss" scoped>

</style>
