import { isMobileNumber } from "@/util/validate";

export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  index: true,
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  dialogClickModal: false,
  searchLabelWidth: 100,
  excelBtn: false,
  printBtn: false,
  addBtn: false,
  editBtn: false,
  viewBtn: false,
  searchSpan:6,
  menu:false,
  searchMenuSpan: 6,
  labelWidth: 110,
  column: [
    //     {
    //     label: 'ID',
    //     prop: 'id',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
    //     {
    //     label: '企业认证ID',
    //     prop: 'companyAuthId',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入企业认证ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
    {
      label: "项目",
      prop: "projectInfoName",
      search: true,
      sortable: true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "挖机车主",
      prop: "ownerName",
      search: true,
      sortable: true,
      rules: [
        {
          required: true,
          message: "请输入姓名",
          trigger: "blur",
        },
        {
          max: 50,
          message: "长度在不能超过50个字符",
        },
      ],
      minWidth:94,
      overHidden:true,
    },
    {
      label: "挖机车主手机",
      prop: "ownerMobile",
      search: true,
      sortable: true,
      rules: [
        {
          required: true,
          message: "请输入手机号码",
          trigger: "blur",
        },
        {
          validator: isMobileNumber,
          trigger: "blur",
        },
      ],
      minWidth:120,
      overHidden:true,
    },

    {
      label: "机械型号",
      prop: "machineCode",
      search: true,
      sortable: true,
      rules: [
        {
          required: true,
          message: "请输入机械类型型号",
          trigger: "blur",
        },
        {
          max: 50,
          message: "长度在不能超过50个字符",
        },
      ],
      minWidth:94,
      overHidden:true,
    },
    {
      label: "挖机司机",
      prop: "inStaffName",
      search: true,
      display: false,
      sortable: true,
      minWidth:94,
      overHidden:true,
    },
    // {
    //   label: "属在企业",
    //   prop: "companyAuthName",
    //   sortable: true,
    //   search: true,
    //   display: false,
    // },
    {
      label: "班次",
      prop: "shiftType",
      search:true,
      hide:true,
      display:false,
      showColumn:false,
      type:"select",
      dicUrl:"/chain/projectinfo/getShiftOfProject",
      dicFormatter: (res) => {
        return res.data.map((item)=>{
          return {
            label:item,
            value:item,
          }
        })
      },
      searchFilterable:true,
      overHidden:true,
    },
    {
      label: "上班时间",
      prop: "searchDate",
      search: true,
      display: false,
      hide:true,
      showColumn:false,
      type:'datetime',
      // searchSpan:12,
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      searchRange:true,
      sortable: true,
      overHidden:true,
    },
    {
      label: "运单数",
      prop: "waybillCount",
      sortable: true,
      minWidth:84,
      overHidden:true,
    },
    {
      label: "工时",
      prop: "workTimeCount",
      sortable: true,
      minWidth:84,
      overHidden:true,
    },

    //     {
    //     label: '是否删除：1=已删除、0=未删除',
    //     prop: 'isDel',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入是否删除：1=已删除、0=未删除',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 1,
    //                 message: '长度在不能超过1个字符'
    //             },
    //         ]
    // },
    // {
    //   label: "新增时间",
    //   prop: "createDatetime",
    //   sortable: true,
    //   display:false,
    // },
    //     {
    //     label: '创建ID',
    //     prop: 'createId',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入创建ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
    //     {
    //     label: '修改日期时间',
    //     prop: 'updateDatetime',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入修改日期时间',
    //                 trigger: 'blur'
    //             },
    //                         ]
    // },
    //     {
    //     label: '修改ID',
    //     prop: 'updateId',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入修改ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
  ],
};
