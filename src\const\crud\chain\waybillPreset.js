
export const searchOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  // stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  index:false,
  searchShow: true, // 显示搜索字段
  excelBtn: false,
  // printBtn: true,
  labelWidth: 114,
  addBtn: false,
  editBtn: false,
  delBtn:false,
  viewBtn: true,
  span: 6,
  menuSpan:6,
  menu:false,
  submitText:'搜索',
  submitIcon: "el-icon-search",
  column: [
    {
      label: "运单号",
      prop: "no",
      sortable: 'custom',
      search: true,
      searchOrder:1,
      minWidth:120,
    },
    {
      label: "项目名称",
      prop: "projectInfoId",
      type: "select",
      searchOrder:2,
      // multiple:true,
      width:120,
      props: {
        label: 'projectName',
        value: 'id'
      },
      dicUrl: '/chain/projectinfo/list',
      // searchFilterable: true,  //是否可以搜索
      filterable: true, //是否可以搜索

    },
    {
      label: "出场签单员",
      prop: "goStaffName",
      minWidth:82,
      searchOrder:7,
    },
    {
      label: "出场签单土质",
      prop: "goSoilType",
      minWidth:96,
      type: 'select',   // 下拉选择
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=soil_type',
    },
    {
      label: "运输类型",
      prop: "tpMode",
      type: "select", // 下拉选择
      props: {
        label: "itemName",
        value: "itemValue",
      },
      multiple:true,
      dataType:'string',
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode',
    },
    {
      label: "泥尾",
      prop: "garbageName",
    },

    {
      label: "单位",
      prop: "weightUnit",
      type: "select", // 下拉选择
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=weight_unit',
    },

    {
      label: "车牌号",
      prop: "goTruckCode",
      searchOrder:11,
    },
    {
      label: "车队长",
      prop: "fleetCaptainName",
      searchOrder:10,
    },

    {
      label: "司机",
      prop: "fleetName",
      searchOrder:9,
    },
    {
      label: "运单状态",
      prop: "status",
      type: "select", // 下拉选择
      span:12,
      props: {
        label: "itemName",
        value: "itemValue",
      },
      multiple:true,
      dataType:'string',
      value:"2,3,4,11,12,21,22",
      dicUrl:"/chain/systemdictionaryitem/listDictionaryItem?dictionary=waybill_status",
    },
    {
      label: "收款人/承运人",
      prop: "carrierName",
    },
    {
      label: "出场签单时间",
      prop: "goDatetime",
      type:'datetimerange',
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
    },
    // {
    //   label: "完成时间",
    //   prop: "completeDatetime",
    //   type:'datetimerange',
    //   valueFormat: 'yyyy-MM-dd HH:mm:ss',
    // },
    {
      label: "是否生成结算单",
      prop: "isSettle",
      searchClearable:false,
      hide:true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: '否',
          value: '0'
        },
        {
          label: '是',
          value: '1'
        },
     ],
    },
    {
      label: "异常申请",
      prop: "isException",
      type: "select", // 下拉选择
      dicData: [
        {
          label: '否',
          value: '0'
        },
        {
          label: '是',
          value: '1'
        },
     ],
    },
    {
      label: "支付单号",
      prop: "companyPaymentNo",
    },
    {
      label: "结算单号",
      prop: "companySettleNo",
    },
    {
      label: "交卡人",
      prop: "giveCardUserId",
    },
    {
      label: "交卡回执单号",
      prop: "giveCardNo",
    },
    // {
    //   label: "是否直付",
    //   prop: "isDirectPay",
    //   type: "select", // 下拉选择
    //   dicData: [
    //     {
    //       label: '全部',
    //       value: ''
    //     },
    //     {
    //       label: '是',
    //       value: '1'
    //     },
    //     {
    //       label: '否',
    //       value: '0'
    //     },
    //   ]
    // },
    {
      label: "证件是否齐全",
      prop: "checkCompleteDocuments",
      type: "select", // 下拉选择
      dicData: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '是',
          value: '1'
        },
        {
          label: '否',
          value: '0'
        },
      ]
    },
    {
      label: "预设价",
      prop: "payeePrice",
    },
    {
      label: "运单结算类型",
      prop: "settleType",
      type: "select",
      dataType:'string',
      multiple:true,
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=settle_type',
    },
    {
      label: "是否开票",
      prop: "invoiceStatus",
      type: "select", // 下拉选择
      dicData: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '是',
          value: '1'
        },
        {
          label: '否',
          value: '0'
        },
      ]
    },
    {
      label: "班次日期",
      prop: "goShiftTime",
      type:'daterange',
      valueFormat: 'yyyy-MM-dd',
    },
    {
      label: "出场班次",
      prop: "goShiftType",
      type: "select", // 下拉选择
      dataType:'string',
      multiple:true,
      dicUrl:"/chain/projectinfo/getShiftOfProject",
      dicFormatter: (res) => {
        return res.data.map((item)=>{
          return {
            label:item,
            value:item,
          }
        })
      },
    },
    {
      label: "支付计划名称",
      prop: "companyPaymentPlanName",
      search:true,
    },
  ],
};
