export const tableOption = {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    // stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: false, // 显示搜索字段
    searchShowBtn:false,
    // excelBtn: true,
    // printBtn: true,
    labelWidth: 150,
    addBtn: false,
    editBtn: false,
    delBtn:false,
    menu:false,
    defaultSort: {
      prop: "goDatetime",
      order: "descending",
    },
    viewBtn: true,
    // searchBtn:false,
    searchMenuSpan: 6,
    searchLabelWidth:100,
    menuWidth:160,
    column: [
      {
        label: "ID",
        prop: "id",
        sortable: true,
        hide: true, //列表页字段隐藏
        disabled: true, //弹窗表单字段不允许输入
        display: false, //弹窗表单字段隐藏
        rules: [
          {
            required: true,
            message: "请输入ID",
            trigger: "blur",
          },
          {
            max: 36,
            message: "长度在不能超过36个字符",
          },
        ],
        minWidth:160,
        overHidden:true,
      },
      {
        label: "机械型号ID",
        prop: "machineId",
        hide: true, //列表页字段隐藏
        disabled: true, //弹窗表单字段不允许输入
        display: false, //弹窗表单字段隐藏
        minWidth:120,
        overHidden:true,
      },
      {
        label: "运单编号",
        prop: "no",
        sortable: 'custom',
        search: false,
        editDisabled:true,
        searchOrder:1,
        minWidth:160,
        overHidden:true,
      },
      {
        label: "施工单位",
        prop: "companyAuthId",
        type: "select", // 下拉选择
        search: true,
        searchOrder:1,
        editDisabled:true,
        props: {
          label: "companyName",
          value: "id",
        },
        dicUrl: "/chain/companyauth/list",
        filterable: true, //是否可以搜索
        minWidth:180,
        overHidden:true,
      },

      {
        label: "项目名称",
        prop: "projectInfoName",
        search: true,
        type: "select",
        searchOrder:2,
        editDisabled:true,
        multiple:true,
        props: {
          label: 'projectName',
          value: 'id'
        },
        dicUrl: '/chain/projectinfo/list',
        filterable: true,  //是否可以搜索
        minWidth:180,
        overHidden:true,
      },
      {
        label: "挖机司机",
        prop: "inStaffName",
        searchOrder:6,
        minWidth:82,
        search: true,
        hide:true,
        editDisabled:true,
        minWidth:80,
        overHidden:true,
      },
      {
        label: "土质类型",
        prop: "inSoilType",
        editDisabled:true,
        hide: true, //列表页字段隐藏
        minWidth:80,
        overHidden:true,
      },
      {
        label: "车牌号",
        prop: "goTruckCode",
        search: false,
        searchOrder:11,
        minWidth:90,
        overHidden:true,
      },
      {
        label: "挖机签单时间",
        prop: "inDatetime",
        type:'datetime',
        sortable: 'custom',
        searchRange:true,
        search: true,
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        minWidth:140,
        overHidden:true,
      },
      {
        label: "出场签单时间",
        prop: "goDatetime",
        type:'datetime',
        sortable: 'custom',
        searchRange:true,
        search: true,
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        minWidth:140,
        overHidden:true,
      },
      {
        label: "班次",
        prop: "inShiftType",
        type: "select", // 下拉选择
        search: true,
        hide:true,
        editDisabled:true,
        dicUrl:"/chain/projectinfo/getShiftOfProject",
        dicFormatter: (res) => {
          return res.data.map((item)=>{
            return {
              label:item,
              value:item,
            }
          })
        },
        formatter:(val)=>{
          return val.inShiftTypeName
        },
        searchFilterable:true,
        minWidth:120,
        overHidden:true,
      },
      {
        label: "机械类型",
        prop: "machineCode",
        editDisabled:true,
        hide: true, //列表页字段隐藏
        minWidth:120,
        overHidden:true,
      },
      {
        label: "签单图片",
        prop: "inPicture",
        editDisabled:true,
        hide:true
      },

    ],
  };
