import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companypayment/getPage',
        method: 'get',
        params: query
    })
}

export function getInvoicePageList(data) {
    return request({
        url: '/chain/companypayment/getBuilderInvoicePageList',
        method: 'post',
        data
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companypayment',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companypayment/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companypayment/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companypayment',
        method: 'put',
        data: obj
    })
}
//支付
export function payment(id) {
  return request({
      url: '/chain/companypayment/payment/'+id,
      method: 'put',
  })
}
//支付前检查
export function checkPayment(id) {
  return request({
      url: '/chain/companypayment/checkPayment/'+id,
      method: 'put',
  })
}
//索票
export function askForInvoice(id) {
  return request({
      url: '/chain/companypayment/askForInvoice/'+id,
      method: 'put',
  })
}
//已开票支付单记录
export function getPaymentOfInvoice(id) {
  return request({
      url: '/chain/companypayment/getPaymentOfInvoice/'+id,
      method: 'get',
  })
}
//已开票运单记录
export function getProjectIdWaybills(data) {
  return request({
      url: '/chain/companysettlewaybill/getProjectIdWaybills',
      method: 'post',
      data
  })
}
//批量索票
export function batchAdd(data) {
  return request({
      url: '/chain/companypaymentinvoice/batchClaim',
      method: 'post',
      data
  })
}

//运单
export function selectInvoiceOfWill(obj) {
  return request({
      url: '/chain/companypaymentinvoice/selectInvoiceOfWill',
      method: 'get',
      params: obj
  })
}
