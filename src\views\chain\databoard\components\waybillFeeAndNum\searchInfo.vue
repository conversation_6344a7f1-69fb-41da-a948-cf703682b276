<template>
  <div class="searchInfo">
    <slot name="count"></slot>
    <div class="search flex flex-between">
      <el-radio-group
        v-model="form.checkDynamic"
        size="small"
        style="margin-right: 10px; min-width: 160px"
        @change="search"
      >
        <el-radio-button label=""> 全部项目 </el-radio-button>
        <el-radio-button label="1"> 活跃项目 </el-radio-button>
      </el-radio-group>
      <slot name="searchLeft"></slot>
      <div class="searchContent">
        <slot name="searchRight" class="item"></slot>
        <!-- <el-radio-group v-model="form.weightUnit" @change="changeUnit" size="small" style="margin-right:10px;" v-if="source == '4'"
          class="item">
          <el-radio-button label="方">
          </el-radio-button>
          <el-radio-button label="吨">
          </el-radio-button>
        </el-radio-group> -->
        <el-radio-group
          v-model="radio"
          size="small"
          @change="changeTime"
          class="item"
        >
          <el-radio-button label="week">最近一周</el-radio-button>
          <el-radio-button label="month">最近一个月</el-radio-button>
          <el-radio-button label="year">最近一年</el-radio-button>
        </el-radio-group>
        <div class="item">
          <span style="font-size: 14px; margin-left: 8px"
            >{{ filterLabel }}：</span
          >
          <el-date-picker
            style="margin-right: 8px; width: 300px"
            :editable="false"
            :pickerOptions="pickerOptions"
            v-model="form.searchTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :clearable="false"
          >
          </el-date-picker>
        </div>
        <div class="item">
          <el-button
            type="primary"
            size="small"
            @click="search"
            :loading="btnLoading"
            >搜索</el-button
          >
          <el-button
            type="primary"
            size="small"
            @click="exOut"
            :loading="btnLoading"
            >导出</el-button
          >
        </div>
      </div>
    </div>
    <slot name="center" :total="projectInfo"></slot>
    <!-- //异常运单数searchTimeLabel才会传入申请时间 -->
    <projectList
      v-if="projectList && projectList.length > 0"
      :text="''"
      @changeProject="changeProject"
      :active="form.projectInfoId"
      :projectList="projectList"
      :defaultProp="defaultProp"
      :unit="unit"
    >
    </projectList>
  </div>
</template>

<script>
import projectList from "../projectList";
import {
  getCompanyPaymentWaybillProjectInfo,
  getProjectDynamicInfoByExcavatedNumber,
} from "@/api/chain/board";

export default {
  props: {
    //传进来的数据
    info: {},
    isShowProject: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    //来源 1出车运单总数 2车队长总数  3 泥尾点总数  4总出土量/泥尾点总出土量  5运单费
    source: {
      type: String,
      default: () => {
        return "1";
      },
    },
    type: {
      type: Number,
      default: () => {
        return 1;
      },
    },
    unit: {
      //单位
      type: String,
      default: () => {
        return "";
      },
    },
    payType: {
      //单位
      type: String,
      default: () => {
        return "";
      },
    },
    tab: {
      type: String,
      default: "",
    },
  },
  components: {
    projectList,
  },
  data() {
    return {
      projectList: [],
      form: {
        searchTime: [],
      },
      projectInfo: {},
      radio: "",
      btnLoading: false,
      defaultProp: {
        label: "projectName",
        value: "projectInfoId",
        cnt: this.tab == 1 ? "totalCost" : "waybillCnt",
      },
      // 时间跨度为之前一年
      pickerOptions: {
        disabledDate: () => false,
        onPick: ({ maxDate, minDate }) => {
          if (minDate && !maxDate) {
            const oneYear = 365 * 24 * 60 * 60 * 1000;
            this.pickerOptions.disabledDate = (time) => {
              return (
                time.getTime() < minDate.getTime() ||
                time.getTime() > minDate.getTime() + oneYear
              );
            };
          } else {
            this.pickerOptions.disabledDate = () => false;
          }
        },
      },
    };
  },
  watch: {
    payType(val) {
      console.info(val);
      if (this.tab == 1) {
        this.defaultProp.cnt =
          val == 1
            ? "totalCost"
            : val == 2
            ? "waitPaymentTotalAmount"
            : "havePaid";
      } else {
        this.defaultProp.cnt =
          val == 1
            ? "waybillCnt"
            : val == 2
            ? "waitWaybillTotal"
            : "bdcWaybillCnt";
      }
      this.search();
    },
  },
  created() {
    console.info(this.info);
    this.form = {
      checkDynamic: this.info.checkDynamic,
      projectInfoId: this.info.projectInfoId || "",
      searchTime: [],
    };
    this.radio = this.info.radio;

    if (this.info.startDate) {
      this.form.searchTime = [this.info.startDate, this.info.endDate];
    }
    if (this.isShowProject) {
      this.getCompanyPaymentWaybillProjectInfo();
    }
  },
  mounted() {},
  computed: {
    filterLabel() {
      switch (this.source) {
        case "1":
          return "运单创建的时间";
          break;
        case "2":
          return "车队长创建时间";
          break;
        case "3":
          return "泥尾创建时间";
          break;
        case "4":
          return "出场签单时间";
          break;
        case "5":
          return "支付单生成时间";
          break;
        case "6":
          return "支付单创建时间";
          break;
        default:
          break;
      }
    },
  },
  methods: {
    getCompanyPaymentWaybillProjectInfo() {
      let param = {
        dateStartStr: this.form.searchTime[0], //开始时间
        dateEndStr: this.form.searchTime[1], //结束时间
        isHProjectInfoCount: this.form.checkDynamic,
        paymentMethodType: this.payType,
        // projectInfoId: this.form.projectInfoId,
      };
      this.getList(param);
    },
    getList(param) {
      getCompanyPaymentWaybillProjectInfo(param).then((res) => {
        // if(res.data.data.projectList.length>0){
        //   this.form.projectInfoId = res.data.data.projectList[0].projectInfoId
        // }
        res.data.data.projectList.unshift({
          projectName: `全部${this.payType==1?'应付':this.payType==2?'待付':'已付'}(${this.tab == 1 ? "元" : "单"})`,
          projectInfoId: "",
          totalCost: res.data.data.totalAmount,
          waybillCnt: res.data.data.totalWaybill,
          waitPaymentTotalAmount: res.data.data.waitPaymentTotalAmount,
          waitWaybillTotal: res.data.data.waitWaybillTotal,
          bdcWaybillCnt: res.data.data.paidWaybillTotal,
          havePaid: res.data.data.paidTotalAmount,
        });
        console.info(res.data);
        this.projectInfo = res.data.data;
        this.projectList = res.data.data.projectList;
        console.log(this.form.projectInfoId);
      });
    },
    changeStatus(val) {
      //更换项目列表  有些表格需要变换数据
      // if (this.isShowProject) {
      //   if (val == 1) {
      //     this.projectList = this.projectInfo.projectDynamicList
      //     this.form.projectInfoId = this.projectList[0].id
      //   } else {
      //     this.projectList = this.projectInfo.projectList
      //     this.form.projectInfoId = this.projectList[0].id
      //   }
      // }
      this.searchData();

      this.$emit("changeStatus", val);
    },
    changeUnit(val) {
      //更换单位，表格需要变换数据
      this.$emit("changeUnit", val);
    },
    changeProject(val) {
      this.form.projectInfoId = val;
      console.log(this.form.projectInfoId);
      this.searchData();
    },
    changeTime(val) {
      let startDate = "";
      let endDate = this.$moment().format("YYYY-MM-DD");
      switch (val) {
        case "week":
          startDate = this.$moment().subtract(7, "days").format("YYYY-MM-DD");
          break;
        case "month":
          startDate = this.$moment().subtract(1, "months").format("YYYY-MM-DD");
          break;
        case "year":
          startDate = this.$moment().subtract(1, "years").format("YYYY-MM-DD");
          break;

        default:
          break;
      }
      console.log(startDate, endDate);
      let _this = this;
      this.$nextTick(() => {
        _this.form.searchTime = [startDate, endDate];
      });
      console.log(this.form.searchTime);
      console.log(val);
    },
    search() {
      this.searchData();
      this.getCompanyPaymentWaybillProjectInfo();
    },
    searchData() {
      let param = {
        dateStartStr: this.form.searchTime[0], //开始时间
        dateEndStr: this.form.searchTime[1], //结束时间
        isHProjectInfoCount: this.form.checkDynamic, //是否活跃项目
        projectInfoId: this.form.projectInfoId,
      };
      console.info(param);
      this.btnLoading = true;
      this.$emit("searchChange", param, this.stopLoading);
    },
    stopLoading() {
      this.btnLoading = false;
    },
    exOut() {
      let param = {
        dateStartStr: this.form.searchTime[0], //开始时间
        dateEndStr: this.form.searchTime[1], //结束时间
        isHProjectInfoCount: this.form.checkDynamic, //是否活跃项目
        paymentMethodType: this.payType,
        projectInfoId: this.form.projectInfoId,
      };
      this.$emit("exOut", param, this.stopLoading);
    },
  },
};
</script>

<style lang="scss" scoped>
.search {
  .searchContent {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .item {
      flex-grow: 1;
      margin-bottom: 10px;
    }
  }
}
</style>
