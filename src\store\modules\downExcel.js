import { getStore, setStore } from '@/util/store'

const downExcel = {
  state: {
    newDownload: getStore({ name: 'newDownload' }) || false
  },
  actions: {
    // 添加下载
    setDownExcelShow ({commit}, boolean) {
      commit('SET_DOWN_EXCEL_SHOW', boolean)
    }
  },
  mutations: {
    SET_DOWN_EXCEL_SHOW: (state,boolean) => {
      state.newDownload = boolean
      setStore({ name: 'newDownload', content: state.newDownload })
    }
  }

}

export default downExcel
