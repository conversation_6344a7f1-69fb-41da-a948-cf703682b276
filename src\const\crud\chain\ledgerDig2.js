export const tableOption = (value) => {
  let that = value
  console.info(that)
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    // index:true,
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    dialogClickModal: false,
    selection: true,
    excelBtn: false,
    printBtn: false,
    addBtn: false,
    viewBtn: false,
    delBtn: false,
    searchSpan: 6,
    searchMenuSpan: 6,
    searchLabelWidth: 116,
    defaultSort: {
      prop: "startDatetime",
      order: "descending",
    },
    column: [
      {
        label: "项目",
        prop: "projectName",
        sortable: true,
        search: true,
        display: false,
        order: 1,
        width: 160,
        overHidden: true,
      },
      {
        label: "项目",
        prop: "projectInfoId",
        order: 1,
        hide: true,
        search: false,
        type: "select", // 下拉选择
        searchFilterable: true,
        filterable: true,
        props: {
          label: "projectName",
          value: "id",
          manHourType: "manHourType"
        },
        rules: [
          {
            required: true,
            message: "请选择项目",
            trigger: "blur",
          },
        ],
        cascader: ['staffId'],
        change: (val) => {

          that.tableOption.column[that.tableOption.column.findIndex(v => v.prop == 'staffId')].display = Boolean(val.value)
          that.tableOption.column[that.tableOption.column.findIndex(v => v.prop == 'inShiftTypeForm')].display = Boolean(val.value)
          that.tableOption.column[that.tableOption.column.findIndex(v => v.prop == 'ledgerTypeForm')].display = Boolean(val.value)
          that.tableOption.column[that.tableOption.column.findIndex(v => v.prop == 'machineId')].display = Boolean(val.value)
          that.tableOption.column[that.tableOption.column.findIndex(v => v.prop == 'unitWorkForm')].display = Boolean(val.value)

          if (val.value) {
            let tmp = that.$refs.crud.DIC.projectInfoId.find(v => v.id == val.value)

            let manHourTypeList = tmp.manHourType ? tmp.manHourType.split(",").map(v => {
              return {
                label: v,
                value: v
              }
            }) : []
            let landParcel = tmp.landParcel ? tmp.landParcel.split(",").map(v => {
              return {
                label: v,
                value: v
              }
            }) : []
            let inUnitWork = tmp.inUnitWork ? tmp.inUnitWork.split(",").map(v => {
              return {
                label: v,
                value: v
              }
            }) : []

            that.$refs.crud.updateDic('inShiftTypeForm', tmp.projectShifts);
            that.$refs.crud.updateDic('landName', landParcel);
            that.tableOption.column[that.tableOption.column.findIndex(v => v.prop == 'landName')].display = Boolean(val.value) && landParcel.length>0
            that.$refs.crud.updateDic('ledgerTypeForm', manHourTypeList);
            that.$refs.crud.updateDic('unitWorkForm', inUnitWork);
               that.tableOption.column[that.tableOption.column.findIndex(v => v.prop == 'machineId')].dicUrl = `/chain/companymachine/listByProjectId/${val.value}`
            that.$refs.crud.updateDic('machineId');

          }

        },
        dicUrl: `/chain/projectinfo/page?size=999`,
        dicFormatter: (res) => {
          return res.data.records
        },
        span: 12,
        overHidden: true,
      },
      {
        label: "签单员",
        prop: "staffId",
        order: 2,
        hide: true,
        display: false,
        search: false,
        type: "select", // 下拉选择
        searchFilterable: true,
        filterable: true,
        props: {
          label: "name",
          value: "id",
        },
        rules: [
          {
            required: true,
            message: "请选择签单员",
            trigger: "blur",
          },
        ],
        dicFlag: false,
        dicFormatter: (res) => {
          return res.data.inList
        },
        dicUrl: "/chain/addWaybill/infoList?projectInfoId={{key}}&selectType=6",
        span: 12,
        overHidden: true,
      },
      {
        label: "班次",
        prop: "inShiftTypeForm",
        order: 3,
        hide: true,
        display: false,
        search: false,
        type: "radio", // 下拉选择
        button: true,
        props: {
          label: "shiftName",
          value: "shiftName"
        },
        rules: [
          {
            required: true,
            message: "请选择班次",
            trigger: "blur",
          },
        ],
        span: 12,
        overHidden: true,
      },

      {
        label: "作业类型",
        prop: "ledgerTypeForm",
        order: 15,
        hide: true,
        display: false,
        search: false,
        type: "radio", // 下拉选择
        button: true,
        rules: [
          {
            required: true,
            message: "请选择作业类型",
            trigger: "blur",
          },
        ],
        dicUrl: "",
        span: 24,
        overHidden: true,
      },
      {
        label: "单位",
        prop: "unitWorkForm",
        order: 16,
        hide: true,
        display: false,
        search: false,
        type: "radio", // 下拉选择
        button: true,
        rules: [
          {
            required: true,
            message: "请选择单位",
            trigger: "blur",
          },
        ],
        span: 24,
        overHidden: true,
      },

      {
        label: "台班编号",
        prop: "ledgerNo",
        search: true,
        display: false,
        order: 2,
        minWidth: 180,
        overHidden: true,
        sortable: 'custom',
      },
      {
        label: "挖机车主姓名",
        prop: "ownerName",
        display:false,
        search: true,
        width: 96,
        overHidden: true,
      },
      {
        label: "挖机车主手机号",
        display:false,
        prop: "ownerMobile",
        search: true,
        width: 110,
        overHidden: true,
      },
      // {
      //   label: "商务备注",
      //   prop: "businessAuditRemark",
      //   sortable: true,
      //   width:100,
      //   overHidden:true,
      // },
      {
        label: "挖机员",
        prop: "staffName",
        display:false,
        sortable: true,
        search: true,
        width: 90,
        overHidden: true,
      },
      {
        label: "作业类型",
        prop: "ledgerType",
        sortable: true,
        display:false,
        searchMultiple: true,
        dataType: 'string',
        search: true,
        type: "radio", // 下拉选择=
        button: true,
        searchType: "select",
        dicUrl: "/chain/projectinfoext/getCompanyLedgerType",
        width: 100,
        overHidden: true,
        rules: [
          {
            required: true,
            message: "请选择作业类型",
            trigger: "blur",
          },
        ],
        span:24,
      },
      {
        label: "挖机班次",
        prop: "inShiftType",
        sortable: true,
        display:false,
        search: true,
        type: "select",
        dicUrl: "/chain/projectinfo/getShiftOfProject",
        dicFormatter: (res) => {
          return res.data.map((item) => {
            return {
              label: item,
              value: item,
            }
          })
        },
        formatter: (value) => {
          return value.inShiftTypeName
        },
        width: 110,
        overHidden: true,
        searchFilterable: true,
      },
      {
        label: "台班状态",
        prop: "auditStatus",
        display:false,
        sortable: true,
        search: true,
        type: "select", // 下拉选择
        dicData: [
          {
            label: "提交",
            value: "0",
          },
          {
            label: "审批中",
            value: "1",
          },
          {
            label: "已审批",
            value: "2",
          },
          {
            label: "已驳回",
            value: "3",
          },
          {
            label: "已删除",
            value: "7",
          },

        ],
        width: 100,
        overHidden: true,
      },
      {
        label: "我的审核",
        prop: "approveStatus",
        display:false,
        sortable: true,
        search: true,
        type: "select", // 下拉选择
        dicData: [
          {
            label: "待审核",
            value: "1",
          },
          {
            label: "已审核",
            value: "2",
          },
          {
            label: "已驳回",
            value: "3",
          },
        ],
        hide: true,
        showColumn: false,
        width: 90,
        overHidden: true,
      },
      {
        label: "选择职位",
        prop: "companyPositionId",
        display:false,
        sortable: true,
        search: true,
        type: "select", // 下拉选择
        props: {
          label: "positionName",
          value: "id",
        },
        searchMultiple: true,
        dataType: "string",
        searchFilterable: true,
        dicUrl: "/chain/companyposition/listForWorkFlow",
        hide: true,
        showColumn: false,
        width: 100,
        overHidden: true,
      },

      {
        label: "挖机类型",
        prop: "inType",
        sortable: true,
        search: true,
        order:5,
        type: "radio", // 下拉选择,
        button:true,
        searType:"select",
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl: "/chain/systemdictionaryitem/listDictionaryItem?dictionary=in_type",
        width: 100,
        overHidden: true,
        rules: [
          {
            required: true,
            message: "请选择挖机类型",
            trigger: "blur",
          },
        ],
      },
      {
        label: "机械型号",
        prop: "machineId",
        width: 120,
        search: false,
        hide:true,
        display: false,
        type:"select",
        searchType: "input", // 下拉选择
        order: 6,
        props: {
          label: "machineCode",
          value: "id"
        },
        rules: [
          {
            required: true,
            message: "请选择机械型号",
            trigger: "blur",
          },
        ],
        dicFlag: false,
        dicUrl: "",
        span: 24,
        overHidden: true,
      },
      {
        label: "机械型号",
        prop: "machineCode",
        display:false,
        width:120,
        overHidden:true,
        search: true,
      },
      {
        label: "完成量",
        order:18,
        prop: "scheduleWork",
        type:'number',
         rules: [
          {
            required: true,
            message: "请选择输入",
            trigger: "blur",
          },
        ],
        width: 80,
        overHidden: true,
      },
      {
        label: "单位",
        prop: "unitWork",
        display:false,
        type: "select", // 下拉选择
        dicData: [
          {
            label: "车",
            value: "1",
          },
          {
            label: "方",
            value: "2",
          },
        ],
        width: 80,
        overHidden: true,
      },
      {
        label: "地块",
        order: 14,
        prop: "landName",
        display: false,
        search: true,
        width: 120,
        overHidden: true,
        type:"select",
        searchType: "input", // 下拉选择
        searchFilterable: true,
        filterable: true,
        rules: [
          {
            required: true,
            message: "请选择地块",
            trigger: "blur",
          },
        ],
        dicUrl: "",
        span: 12,
      },
      {
        label: "作业地点",
        order: 17,
        prop: "address",
        width: 120,
        overHidden: true,
        rules: [
          {
            required: true,
            message: "请选择输入",
            trigger: "blur",
          },
        ],
      },
      {
        label: "开始时间",
        order: 7,
        prop: "startDatetime",
        sortable: true,
        width: 140,
        overHidden: true,
        type: 'datetime',
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        rules: [
          {
            required: true,
            message: "请选择开始时间",
            trigger: "blur",
          },
        ],
        change: (val) => {
          that.form.workTime = calculateHourDifference(val.value, that.form.endDatetime)
        }
      },
      {
        label: "结束时间",
        prop: "endDatetime",
        sortable: true,
        order: 10,
        width: 140,
        overHidden: true,
        type: 'datetime',
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        rules: [
          {
            required: true,
            message: "请选择结束时间",
            trigger: "blur",
          },
        ],
        change: (val) => {
          that.form.workTime = calculateHourDifference(that.form.startDatetime, val.value)
        }
      },
      {
        label: "时长",
        prop: "workTime",
        order: 13,
        disabled: true,
        sortable: true,
        width: 80,
        overHidden: true,
      },
      {
        label: "转换时长",
        display:false,
        prop: "workHour",
        width: 80,
        overHidden: true,
      },
      {
        label: "是否删除",
        prop: "isDel",
        sortable: true,
        display:false,
        search: true,
        type: "select", // 下拉选择
        dicData: [
          {
            label: "是",
            value: "1",
          },
          {
            label: "否",
            value: "0",
          },
        ],
        width: 94,
        overHidden: true,
      },
      {
        label: "是否签证",
        prop: "isVisa",
        order:33,
        search: false,
        display:false,
        type: "switch", // 下拉选择
        searType:"select",
        dicData: [
          {
            label: "否",
            value: "0",
          },
          {
            label: "是",
            value: "1",
          },

        ],
        value:"0",
        hide: true,
        showColumn: false,
        width: 80,
        overHidden: true,
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
      },
      {
        label: "备注",
        prop: "ledgerRemark",
        type:"textarea",
        width: 120,
        overHidden: true,
        search: true,
        span:24
      },
      {
        label: "开始码表拍照",
        order: 9,
        prop: "beginClockUrl",
        hide: true,
        display:false,
        showColumn: false,
        dataType: 'string',
        width: 100,
        labelWidth: 110,
        overHidden: true,
        //多图片上传和显示
        type: "upload", //上传一张头像图
        listType: "picture-img",
        action: "/upms/file/upload?fileType=image&dir=",
        propsHttp: {
          url: "link",
        },
        width: 200,
        loadText: "图上上传中，请稍等",
        tip: "只能上传jpg/png文件，且不超过1000kb",
        span: 24
      },
      {
        label: "开始码表",
        prop: "beginClock",
        display:false,
        order: 8,
        hide: true,
        type:"number",
        showColumn: false,
        width: 80,
        overHidden: true,
      },
      {
        label: "结束码表拍照",
        prop: "endClockUrl",
        display:false,
        order: 12,
        labelWidth: 110,
        hide: true,
        showColumn: false,
        //多图片上传和显示
        type: "upload", //上传一张头像图
        listType: "picture-img",
        action: "/upms/file/upload?fileType=image&dir=",
        propsHttp: {
          url: "link",
        },
        width: 200,
        loadText: "图上上传中，请稍等",
        tip: "只能上传jpg/png文件，且不超过1000kb",
        dataType: 'string',
        width: 100,
        overHidden: true,
        span: 24
      },
      {
        label: "结束码表",
        prop: "endClock",
        display:false,
        order: 11,
        hide: true,
        type:"number",
        showColumn: false,
        width: 80,
        overHidden: true,
      },
      {
        label: "加油升数",
        order:19,
        type:'number',
        prop: "refuelingLitres",
        hide: true,
        display:false,
        showColumn: false,
        width: 80,
        overHidden: true,
      },
      {
        label: "费用(元)",
        order:20,
        prop: "cost",
        type:'number',
        display:false,
        hide: true,
        showColumn: false,
        width: 80,
        overHidden: true,
      },
      {
        label: "是否餐补",
        prop: "isSubsidizedMeals",
        order:22,
        hide: true,
        showColumn: false,
        display:false,
        width: 80,
        overHidden: true,
        type:"switch",
        dicData:[{
          label:'否',
          value:0
        },{
          label:'是',
          value:1
        }],
        value:"0"
      },
      {
        label: "加班时长(小时)",
        prop: "overtimeHours",
        order:21,
        hide: true,
        display:false,
        type:'number',
        showColumn: false,
        width: 110,
        overHidden: true,
        span:24,
      },
      // {
      //   label: "项目审核人",
      //   prop: "confirmStaffName",
      //   width:90,
      //   overHidden:true,
      // },
      // {
      //   label: "项目审核时间",
      //   prop: "confirmDatetime",
      //   sortable: true,
      //   width:140,
      //   overHidden:true,
      // },
      // {
      //   label: "核算审核人",
      //   prop: "checkStaffName",
      //   sortable: true,
      //   width:110,
      //   overHidden:true,
      // },
      // {
      //   label: "核算审核时间",
      //   prop: "checkDatetime",
      //   width:140,
      //   overHidden:true,
      //   sortable: 'custom',
      // },
      {
        label: "创建日期",
        prop: "searchDate",
        type: 'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange: true,
        search: true,
        showColumn: false,
        hide: true,
        display: false,
      },
      {
        label: "班次日期",
        prop: "inShiftTime",
        order: 4,
        type: 'date',
        display: true,
        sortable: 'custom',
        searchRange: true,
        search: true,
        valueFormat: 'yyyy-MM-dd',
        minWidth: 140,
        overHidden: true,
        hide: true,
        showColumn: false,
        rules: [
          {
            required: true,
            message: "请选择班次日期",
            trigger: "blur",
          },
        ],
      },
      {
        label: "PC后台备注",
        display:false,
        prop: "pcLedgerRemark",
        width: 120,
        overHidden: true,
        sortable: 'custom',
        search: true,
      },
      // {
      //   label: "审批备注",
      //   prop: "pcAuditRemark",
      //   width:120,
      //   overHidden:true,
      //   sortable: 'custom',
      // },
      {
        label: "台班价",
        prop: "isSet",
        display:false,
        type: "select", // 下拉选择
        search: true,
        dicData: [
          {
            label: '未设置',
            value: '0'
          },
          {
            label: '已设置',
            value: '1'
          },
        ],
        hide: true,
        showColumn: false,
      },
      {
        label: "台班价",
        display:false,
        prop: "preparePrice",
        minWidth: 96,
        overHidden: true,
        sortable: 'custom',
      },
      {
        label: "现场照片",
        prop: "pictureUrl",
        type: "upload",
        listType: "picture-card",
        action: "/upms/file/upload?fileType=image&dir=batchRecharge/",
        dataType: 'string',
        propsHttp: {
          url: "link",
        },
        accept: ".jpg,.png",
        loadText: "附件上传中，请稍等",
        span: 24,
        tip: "只能上传jpg/png文件",
        hide:true,
        showColumn:false,
        limit:9,
      },
    ],
  }
};


// 计算两个日期时间之间的小时差
function calculateHourDifference(startTime, endTime) {
  if (startTime && endTime) {
    const start = new Date(startTime);
    const end = new Date(endTime);

    // 计算时间差（单位为毫秒）
    const timeDiff = Math.abs(end - start);

    // 将毫秒转换为小时数
    const hours = timeDiff / (1000 * 60 * 60);
    const tmpHours = parseFloat(hours.toFixed(2))
    return tmpHours + '小时'; // 保留两位小数
  }
  return '0小时'
}
