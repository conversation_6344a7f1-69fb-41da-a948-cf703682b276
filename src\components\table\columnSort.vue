<!--表格组件 -->
<template>
  <div class="dialog" ref="dialog" v-if="visible" >
      <div class="btns">
        <el-button size="mini" type="primary" :loading="loading" @click="save">保存</el-button>
        <el-button size="mini" type="primary" @click="cancelModal">关闭</el-button>
      </div>
      <ul v-loading='loading' element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading">
        <draggable v-model="defaultColumn" @update="datadragEnd" :move="checkMove" :options="{animation:500}">
        <li v-for="(item,index) in defaultColumn" :key="index">
            <span class="left"><i class="el-icon-rank"></i>{{item.label}}</span>
            <div class="right">
              <i class="el-icon-upload2" v-if="index!=0" @click="sortList(index,index-1)"></i>
              <span v-if="index!=0&&index!=defaultColumn.length-1">|</span>
              <i class="el-icon-download" v-if="index!=defaultColumn.length-1" @click="sortList(index,index+1)"></i>
            </div>
        </li>
        </draggable>
      </ul>

    </div>
</template>

<script>
import draggable from 'vuedraggable'
export default {
  props: {
  //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    column: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      defaultColumn:[],
      loading:false,
    };
  },
  computed: {

  },
  components: {
    draggable
  },
  watch: {},
  mounted() {
    //先获取配置
    this.defaultColumn = this.column
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
     checkMove (evt) {
        //如果是最后一个,不给换位置
        // console.log(evt);
        // if(evt.draggedContext.futureIndex==this.defaultColumn.length-1||evt.draggedContext.index==this.defaultColumn.length-1) return false
    },
    datadragEnd (evt) {
        console.log(evt);
        this.loading = true
        evt.preventDefault();
        this.$emit("datadragEnd",this.defaultColumn,this.stop)
    },
    stop(){
      this.loading = false
    },
    save(){
      this.loading = true
      this.$emit("save",this.stop)
    },
    sortList(oldIndex,newIndex){
      this.loading = true
    console.log(oldIndex,newIndex);
      // [this.defaultColumn[newIndex],this.defaultColumn[oldIndex]] = [this.defaultColumn[oldIndex],this.defaultColumn[newIndex]]
      this.defaultColumn[oldIndex] = this.defaultColumn.splice(newIndex,1,this.defaultColumn[oldIndex])[0];
      console.log(this.defaultColumn);
      // this.$forceUpdate()
      this.$emit("datadragEnd",this.defaultColumn,this.stop)
    },
  },
  created() {},
};
</script>

<style lang="scss" scoped>
  .dialog {
    position: absolute;
    right: 80px;
    top: 36px;
    z-index: 999;
    background: #fff;
    padding: 10px 0px;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 300px;
    min-height: 100px;
    box-sizing: border-box;
    .btns{
      text-align: right;
      padding-right: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    ul{
      padding: 10px;
      max-height: 300px;
      overflow-y: auto;
      li{
        line-height: 36px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: #303133;
        // font-weight: 700;
        width: 100%;
        border-bottom: 1px solid #ebeef5;
        cursor: move;
        &:hover{
          background-color: #ebeef5;
        }
        .left,.right{
          flex: 1;
        }
        i{
          margin-right: 10px;
          cursor: pointer;
          color: #409eff;
          font-weight: 700;
        }
        div{
        text-align: right;
          span{
            margin-right: 10px;
            color: #ccc;
          }
        }
      }
    }
  }

</style>
