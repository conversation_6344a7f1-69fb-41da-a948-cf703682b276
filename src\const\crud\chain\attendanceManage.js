import { isMobileNumber } from "@/util/validate";

export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  defaultSort: {
    prop: "attendanceDate",
    order: "descending",
  },
  viewBtn: false,
  menu: false,
  searchMenuSpan: 6,
  column: [
    {
      label: "日期",
      prop: "attendanceDate",
      sortable: "custom",
      type: "date",
      searchRange: true,
      search: true,
      valueFormat: "yyyy-MM-dd",
      width:120,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "姓名",
      prop: "name",
      sortable: "custom",
      search: true,
      width:90,
      overHidden:true,
    },
  ],
};
