import moment from "moment"; //导入文件

export const tableOption = (value) => {
  let that = value
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    // stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    index: true,
    searchShow: true, // 显示搜索字段
    searchShowBtn: false,
    excelBtn: false,
    // printBtn: true,
    labelWidth: 150,
    addBtn: false,
    editBtn: false,
    delBtn: false,
    defaultSort: {
      prop: "goDatetime",
      order: "descending",
    },
    viewBtn: false,
    searchMenuSpan: 4,
    selection: true,
    searchLabelWidth: 80,
    menuWidth: 140,
    // filterBtn:true,
    useVirtual: true,
    height: "auto",
    calcHeight: 137,
    searchIndex: 4,
    searchCustom: 1,
    routerName: "companywaybill2",
    column: [
      {
        label: "项目名称",
        prop: "projectInfoId",
        search: true,
        type: "select",
        searchOrder: 2,
        searchSpan: 5,
        // dataType:'string',
        searchMultiple: true,
        props: {
          label: 'projectName',
          value: 'id'
        },
        dicUrl: '/chain/projectinfo/list',
        searchFilterable: true,  //是否可以搜索
        width: 160,
        overHidden: true,
      },
      {
        label: "运输类型",
        prop: "tpMode",
        type: "select", // 下拉选择
        search: true,
        searchOrder: 4,
        dataType: 'string',
        searchMultiple: true,
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode',
        width: 80,
        overHidden: true,
      },
      {
        label: "泥尾",
        prop: "garbageId",
        // sortable: true,
        type: "select", // 下拉选择
        // search:true,
        props: {
          label: "names",
          value: "id",
        },
        // hide:true,
        dicUrl: "/chain/garbage/list",
        filterable: true, //是否可以搜索
        width: 120,
        overHidden: true,
      },
      {
        label: "泥尾",
        prop: "garbageName",
        search: true,
        hide: true,
        display: false,
        showColumn: false,
      },

      {
        label: "出场签单土质",
        prop: "goSoilType",
        search: true,
        dataType: 'string',
        searchMultiple: true,
        editDisabled: true,
        type: 'select',   // 下拉选择
        props: {
          label: "itemName",
          value: "itemValue",
        },
        // search:true,
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=soil_type',
        searchFilterable: true,  //是否可以搜索
        width: 96,
        overHidden: true,
      },
      {
        label: "运单状态",
        prop: "status",
        type: "select", // 下拉选择
        search: true,
        searchOrder: 4,
        dataType: 'string',
        searchMultiple: true,
        searchSpan: 12,
        searchValue: "2,3,4,11,12,21,22",
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl: "/chain/systemdictionaryitem/listDictionaryItem?dictionary=waybill_status",
        width: 100,
        overHidden: true,
        sortable: 'custom',
      },
      {
        label: "运单号",
        prop: "no",
        sortable: 'custom',
        search: true,
        searchOrder: 4,
        width: 160,
        overHidden: true,
      },
      {
        label: "车队长",
        prop: "fleetCaptainName",
        // search: true,
        // searchOrder:3,
        // searchSpan:5,
        width: 90,
        // overHidden:true,
      },
      {
        label: "车牌号",
        prop: "goTruckCode",
        search: true,
        searchOrder: 3,
        searchSpan: 5,
        width: 90,
        overHidden: true,
      },
      {
        label: "班次日期",
        prop: "goShiftTime",
        type: 'date',
        sortable: 'custom',
        searchRange: true,
        search: true,
        valueFormat: 'yyyy-MM-dd',
        width: 140,
        overHidden: true,
        hide: true,
        showColumn: false,
        searchOrder: 1,
        searchSpan: 5,
      },
      {
        label: "出场班次",
        prop: "goShiftType",
        type: "select", // 下拉选择
        search: true,
        dataType: 'string',
        searchMultiple: true,
        searchOrder: 1,
        searchSpan: 5,
        dicUrl: "/chain/projectinfo/getShiftOfProject",
        dicFormatter: (res) => {
          return res.data.map((item) => {
            return {
              label: item,
              value: item,
            }
          })
        },
        formatter: (value) => {
          return value.goShiftTypeName
        },
        width: 120,
        overHidden: true,
      },
      {
        label: "数量",
        prop: "weightTons",
        display: false,
        width: 70,
        overHidden: true,
      },
      {
        label: "出场备注",
        prop: "goRemark",
        search: true,
        width: 156,
        overHidden: true,
      },
      {
        label: "PC后台备注",
        prop: "pcRemark",
        search: true,
        width: 120,
        overHidden: true,
        sortable: 'custom',
      },
      {
        label: "出场签单员",
        prop: "goStaffName",
        searchOrder: 7,
        search: true,
        width: 82,
        overHidden: true,
      },
      {
        label: "出场签单日期",
        prop: "goDate",
        display: false,
        // formatter: (val) => {
        //   return val.goDatetime && moment(val.goDatetime).format("YYYY-MM-DD");
        // },
        width: 118,
        overHidden: true,
        sortable: 'custom',
      },
      {
        label: "挖机签单员",
        prop: "inStaffName",
        searchOrder: 6,
        search: true,
        width: 82,
        overHidden: true,
      },
      {
        label: "挖机签单时间",
        prop: "inDatetime",
        type: 'datetime',
        sortable: 'custom',
        searchRange: true,
        search: true,
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        width: 160,
        overHidden: true,
      },
      {
        label: "挖机签单土质",
        prop: "inSoilType",
        width: 96,
        overHidden: true,
      },
      {
        label: "项目负责人",
        prop: "leadingNames",
        width: 82,
        searchOrder: 8,
        search: true,
        width: 100,
        hide: true,
        overHidden: true,
      },
      {
        label: "地块",
        prop: "landParcel",
        width: 100,
        hide: true,
        overHidden: true,
      },



      {
        label: "挖机签单GPS",
        prop: "inGps",
        hide: true, //列表页字段隐藏
        width: 82,
        overHidden: true,
      },
      {
        label: "出场签单GPS",
        prop: "goGps",
        hide: true, //列表页字段隐藏
        width: 100,
        overHidden: true,
      },
      {
        label: "挖机签单地址",
        prop: "inAddr",
        hide: true, //列表页字段隐藏
        width: 140,
        overHidden: true,
      },
      {
        label: "出场签单地址",
        prop: "goAddr",
        hide: true, //列表页字段隐藏
        width: 140,
        overHidden: true,
      },
      {
        label: "卸土GPS",
        prop: "completeGps",
        hide: true, //列表页字段隐藏
        width: 110,
        overHidden: true,
      },
      {
        label: "卸土地址",
        prop: "completeAddr",
        hide: true, //列表页字段隐藏
        width: 140,
        overHidden: true,
      },

      {
        label: "是否有泥尾票",
        prop: "isTicket",
        search: true,
        type: 'select',
        dicData: [
          {
            label: "否",
            value: "0",
          },
          {
            label: "是",
            value: "1",
          },
        ],
        width: 94,
        hide: true,
        overHidden: true,
      },
      {
        label: "泥尾票土质",
        prop: "goTicketSoilType",
        search: true,
        width: 94,
        hide: true,
        overHidden: true,
      },
      {
        label: "泥尾票票号",
        prop: "ticketNo",
        width: 100,
        overHidden: true,
        formatter: (val) => {
          return val.ticketNo && val.manualSelectTicket == '0' ? `${val.ticketNo}(系统分配)` : val.ticketNo || ''
        }
      },
      {
        label: "泥尾票单价",
        prop: "ticketPrice",
        width: 94,
        overHidden: true,
      },
      {
        label: "拍照泥尾票",
        prop: "ticketImg",
        type: 'upload',
        hide: true,
        width: 100,
        // listType: 'picture-img',
      },
      {
        label: "司机联系方式",
        prop: "mobile",
        hide: true,   //列表页字段隐藏
        width: 100,
        overHidden: true,
      },

      // {
      //   label: "运单计价类型",
      //   prop: "weightType",
      //   type: "select", // 下拉选择
      //   search: true,
      //   // searchOrder:4,
      //   hide:true,
      //   dicData: [
      //     {
      //       label: '不限',
      //       value: ''
      //     },
      //     {
      //       label: '按车计价',
      //       value: '1'
      //     },
      //     {
      //       label: '称重计价',
      //       value: '2'
      //     },
      //     // {
      //     //   label: '未知',
      //     //   value: '3'
      //     // },
      //   ]
      // },


      {
        label: "单位",
        prop: "weightUnit",
        type: "select", // 下拉选择
        search: true,
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=weight_unit',
        display: false,
        width: 60,
        hide: true,
        overHidden: true,
      },
      {
        label: "进量(吨)",
        prop: "load",
        // display:false,
        width: 70,
        hide: true,
        overHidden: true,
      },
      {
        label: "扣量(吨)",
        prop: "cut",
        // display:false,
        width: 70,
        hide: true,
        overHidden: true,
      },
      {
        label: "单价(元/吨)",
        prop: "resourcePrice",
        width: 90,
        hide: true,
        overHidden: true,
      },
      {
        label: "磅单图片",
        prop: "imgUrl",
        type: 'upload',
        hide: true,
        showColumn: false,
        width: 100,
      },

      {
        label: "行驶证车辆车型",
        prop: "brandType",
        width: 106,
        hide: true,
        overHidden: true,
      },
      {
        label: "车队名称",
        prop: "captainFleetName",
        search: true,
        searchOrder: 10,
        width: 70,
        hide: true,
        overHidden: true,
      },
      {
        label: "车队长手机号",
        prop: "captainMobile",
        width: 100,
        hide: true,
        overHidden: true,
      },
      {
        label: "司机",
        prop: "fleetName",
        searchOrder: 9,
        search: true,
        width: 70,
        hide: true,
        overHidden: true,
      },
      {
        label: "入场签单",
        prop: "isEntrance",
        type: 'select',
        search: true,
        width: 70,
        hide: true,
        overHidden: true,
        dicData: [
          {
            label: '否',
            value: '0'
          },
          {
            label: '是',
            value: '1'
          },
        ]
      },
      {
        label: "司机结算",
        prop: "driverStatus",
        type: "select", // 下拉选择
        hide: true,
        dicData: [
          {
            label: "未结算",
            value: "1",
          },
          {
            label: "已结算",
            value: "2",
          },
        ],
        width: 70,
        overHidden: true,
      },
      {
        label: "预设价",
        prop: "payeePrice",
        width: 70,
        hide: true,
        overHidden: true,
      },
      {
        label: "结算价",
        prop: "settlePrice",
        width: 70,
        hide: true,
        overHidden: true,
      },
      {
        label: "税费",
        prop: "taxFee",
        width: 70,
        hide: true,
        overHidden: true,
      },
      {
        label: "税率",
        prop: "taxPoint",
        formatter: (val) => {
          return val.taxPoint && val.taxPoint + "%";
        },
        width: 70,
        hide: true,
        overHidden: true,
      },
      {
        label: "实付价",
        prop: "realPayPrice",
        width: 70,
        hide: true,
        overHidden: true,
      },
      {
        label: "承运人",
        prop: "carrierName",
        search: true,
        width: 70,
        hide: true,
        overHidden: true,
      },

      {
        label: "是否异常申请",
        prop: "isException",
        type: "select", // 下拉选择
        dicData: [
          {
            label: '否',
            value: '0'
          },
          {
            label: '是',
            value: '1'
          },
        ],
        width: 94,
        hide: true,
        overHidden: true,
      },


      {
        label: "挖机备注",
        prop: "inRemark",
        hide: true,
        width: 80,
        overHidden: true,
      },

      {
        label: "运单结算类型",
        prop: "settleType",
        type: "select",
        search: true,
        dataType: 'string',
        searchMultiple: true,
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=settle_type',
        width: 110,
        hide: true,
        overHidden: true,
      },
      {
        label: "入场签单车牌",
        prop: "entranceTruckCode",
        search: true,
        width: 110,
        hide: true,
        overHidden: true,
      },
      {
        label: "入场签单人",
        prop: "entranceStaffName",
        search: true,
        width: 86,
        hide: true,
        overHidden: true,
      },
      {
        label: "出口签单车型",
        prop: "goVehicleType",
        search: true,
        type: "select",
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl: `/chain/systemdictionaryitem/listDictionaryItem?dictionary=go_vehicle_type`,
        width: 120,
        hide: true,
        overHidden: true,
        sortable: 'custom',
      },
      {
        label: "车辆所有人",
        prop: "truckOwnerName",
        search: true,
        width: 84,
        hide: true,
        overHidden: true,
      },
      {
        label: "入场签单备注",
        prop: "entranceRemark",
        hide: true,
        width: 70,
        overHidden: true,
      },
      // {
      //   label: "是否直付",
      //   prop: "isDirectPay",
      //   type: "select", // 下拉选择
      //   search:true,
      //   dicData: [
      //     {
      //       label: '全部',
      //       value: ''
      //     },
      //     {
      //       label: '是',
      //       value: '1'
      //     },
      //     {
      //       label: '否',
      //       value: '0'
      //     },
      //   ],
      //   width:80,
      //   hide:true,
      //   overHidden:true,
      //   // hide:true,
      // },
      {
        label: "运单创建时间",
        prop: "generationTime",
        sortable: 'custom',
        type: 'datetime',
        searchRange: true,
        search: true,
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        width: 160,
        hide: true,
        overHidden: true,
      },
      {
        label: "入场签单时间",
        prop: "entranceDatetime",
        sortable: 'custom',
        type: 'datetime',
        searchRange: true,
        search: true,
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        width: 160,
        hide: true,
        overHidden: true,
      },


      {
        label: "出场签单时间",
        prop: "goTime",
        type: 'datetime',
        // sortable: 'custom',
        searchRange: true,
        search: true,
        // formatter: (val) => {
        //   return val.goDatetime && moment(val.goDatetime).format("HH:mm:ss");
        // },
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        width: 118,
        hide: true,
        overHidden: true,
        sortable: 'custom',
      },
      {
        label: "完成日期",
        prop: "completeDate",
        display: false,
        width: 94,
        hide: true,
        overHidden: true,
        sortable: 'custom',
        // formatter: (val) => {
        //   return val.completeDatetime && moment(val.completeDatetime).format("YYYY-MM-DD");
        // },
      },
      {
        label: "完成时间",
        prop: "completeTime",
        // sortable: 'custom',
        type: 'datetime',
        searchRange: true,
        search: true,
        // formatter: (val) => {
        //   return val.completeDatetime && moment(val.completeDatetime).format("HH:mm:ss");
        // },
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        width: 94,
        hide: true,
        overHidden: true,
        sortable: 'custom',
      },

      // {
      //   label: "挖机员工ID",
      //   prop: "inStaffId",
      //   sortable: true,
      //   display: false,
      //   hide: true, //列表页字段隐藏
      // },
      // {
      //   label: "出场签单员ID",
      //   prop: "goStaffId",
      //   sortable: true,
      //   display: false,
      //   hide: true, //列表页字段隐藏
      // },
      // {
      //   label: "挖机司机用户ID",
      //   prop: "inDriverUserId",
      //   sortable: true,
      //   hide: true, //列表页字段隐藏
      //   display: false,
      // },
      // {
      //   label: "挖机车辆ID",
      //   prop: "inDriverTruckId",
      //   sortable: true,
      //   display: false,
      //   hide: true, //列表页字段隐藏
      // },
      // {
      //   label: "逻辑删除",
      //   prop: "isDel",
      //   hide: true, //列表页字段隐藏
      //   disabled: true, //弹窗表单字段不允许输入
      //   display: false, //弹窗表单字段隐藏
      //   value: "0",
      // },
      // {
      //   label: "代理商任务ID",
      //   prop: "agentTaskId",
      //   display: false,
      //   sortable: true,
      //   hide: true, //列表页字段隐藏
      // },
      // {
      //   label: "代理商报价ID",
      //   prop: "agentTaskQuoteId",
      //   display: false,
      //   sortable: true,
      //   hide: true, //列表页字段隐藏
      // },
      // {
      //   label: "车队长任务ID",
      //   prop: "driverCaptainTaskId",
      //   display: false,
      //   sortable: true,
      //   hide: true, //列表页字段隐藏
      // },
      // {
      //   label: "司机任务ID",
      //   prop: "driverTaskId",
      //   display: false,
      //   sortable: true,
      //   hide: true, //列表页字段隐藏
      // },
      // {
      //   label: "创建ID",
      //   prop: "createId",
      //   sortable: true,
      //   hide: true, //列表页字段隐藏
      //   disabled: true, //弹窗表单字段不允许输入
      //   display: false, //弹窗表单字段隐藏
      // },
      {
        label: "电子结算卡",
        prop: "settleCardNo",
        component: 'myCards',
        searchSpan: 24,
        type: "card",
        hide: true,
        search: true,
        width: 160,
        overHidden: true,
        sortable: 'custom',
      },

      {
        label: "创建时间",
        prop: "createDatetime",
        hide: true,   //列表页字段隐藏
        width: 160,
        overHidden: true,
      },
      {
        label: "修改时间",
        prop: "updateDatetime",
        hide: true, //列表页字段隐藏
        width: 160,
        overHidden: true,
      },

      {
        label: "泥尾价",
        prop: "isSetGarbagePreparePrice",
        type: "select", // 下拉选择
        search: true,
        hide: true, //列表页字段隐藏
        showColumn: false,
        dicData: [
          {
            label: '未设置',
            value: '0'
          },
          {
            label: '已设置',
            value: '1'
          },

        ],
        width: 96,
        overHidden: true,
        sortable: 'custom',
      },
      {
        label: "泥尾价",
        prop: "garbagePreparePrice",
        width: 120,
        hide: true,
        overHidden: true,
        sortable: 'custom',
      },
      {
        label: "结算单号",
        prop: "companySettleNo",
        search: true,
        hide: false,
        minWidth: 160,
        overHidden: true,
      },
      {
        label: "运单类型",
        prop: "waybillSourceType",
        type: "select", // 下拉选择
        search: true,
        hide: true,
        minWidth: 160,
        overHidden: true,
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl:
          "/chain/systemdictionaryitem/listDictionaryItem?dictionary=waybill_source_type",
      },
      {
        label: "标签类型",
        prop: "wayBillTagType",
        type: "select", // 下拉选择
        search: true,
        hide: true,
        minWidth: 160,
        overHidden: true,
        searchMultiple: true,
        props: {
          label: "itemName",
          value: "itemValue",
        },
        dicUrl:
          "/chain/systemdictionaryitem/listDictionaryItem?dictionary=waybill_tag_type",
      },
    ],
  };
}
