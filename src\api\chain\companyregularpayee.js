import request from '@/router/axios'

export function getPage(data) {
    return request({
        url: '/chain/companyregularpayee/getRegularPayeePage',
        method: 'post',
        data
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companyregularpayee/saveRegularPayee',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companyregularpayee/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companyregularpayee/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companyregularpayee/updateRegularPayee',
        method: 'post',
        data: obj
    })
}
//导入收款人
export function importExcel(data) {
  return request({
      url: '/chain/companyregularpayee/importRegularPayeeExcel',
      method: 'post',
      data
  })
}
//发短信给常用收款人
export function sendRegularPayeeSms(data) {
  return request({
      url: '/chain/companyregularpayee/sendRegularPayeeSms',
      method: 'post',
      data
  })
}
//批量发短信给常用收款人
export function batchSendRegularPayeeSms(data) {
  return request({
      url: '/chain/companyregularpayee/batchSendRegularPayeeSms',
      method: 'post',
      data
  })
}
