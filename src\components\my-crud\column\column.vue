<template>
  <div>
    <slot name="header"></slot>
    <!-- 动态列 -->
    <template v-for="(column,index) in list">
      <column-dynamic
        v-if="column.children && column.children.length > 0"
        :columnOption="column"
        :key="column.label"
      >
        <template v-for="item in crud.mainSlot" slot-scope="scope" :slot="item">
          <slot v-bind="scope" :name="item"></slot>
        </template>
      </column-dynamic>
      <column-slot v-else :column="column" :key="index" :column-option="columnOption">
        <template v-for="item in crud.mainSlot" slot-scope="scope" :slot="item">
          <slot v-bind="scope" :name="item"></slot>
        </template>
      </column-slot>
    </template>
    <slot name="footer"></slot>
  </div>
</template>

<script>
import columnDynamic from "./column-dynamic";
import columnSlot from "./column-slot";
import { arraySort } from "@/util/util";
export default {
  name: "crud",
  data() {
    return {
      filterList:[]
    };
  },
  components: {
    columnSlot,
    columnDynamic,
  },
  inject: ["crud"],
  provide() {
    return {
      crud: this.crud,
      dynamic: this,
    };
  },
  props: {
    columnOption: Array,
  },
  computed: {
      list () {
        let result = [...this.columnOption];
        result = arraySort(result, 'order', (a, b) => this.crud.objectOption[a.prop]?.order - this.crud.objectOption[b.prop]?.order)
        return result;
      }
  },

  mounted() {},
  methods: {
    //表格筛选逻辑
    handleFilterMethod(value, row, column) {
      const columnNew = this.columnOption.filter(
        (ele) => ele.prop === column.property
      )[0];
      if (typeof columnNew.filterMethod === "function") {
        return columnNew.filterMethod(value, row, columnNew);
      } else {
        return row[columnNew.prop] === value;
      }
    },
    //表格筛选字典
    handleFilters (column, flag) {
      if (flag !== true) return undefined;
      let DIC = this.crud.DIC[column.prop] || []
      let list = [];
      if (!this.validatenull(DIC)) {
        DIC.forEach(ele => {
          const props = column.props || this.crud.tableOption.props || {};
          list.push({
            text: ele[props.label || "label"],
            value: ele[props.value || "value"]
          });
        });
      } else {
        // let result = [];
        // function findProp(list = []) {
        //   if (!Array.isArray(list)) return;
        //   list.forEach((ele) => {
        //     if (Array.isArray(ele.children)){
        //       result.push(ele)
        //       findProp(ele.children);
        //     }else result.push(ele);
        //   });
        // }
        // findProp(this.crud.list);
        this.crud.list.forEach(ele => {
          if (!list.map(item => item.text).includes(ele[column.prop])) {
            list.push({
              text: ele[column.prop],
              value: ele[column.prop]
            });
          }
        });
      }
      return list;
    },

    getColumnProp(column, type) {
      let obj = this.crud.objectOption[column.prop] || {};
      if (type === "filterMethod") return obj?.filters;
      if (this.crud.isMobile && ["fixed"].includes(type)) return false;
      let result = obj?.[type];
      if (type == "width" && result == 0) {
        return undefined;
      }
      if (type == 'filters') return this.handleFilters(column, result)
      if (type == "hide") return obj?.hide !== true;
      else return result;
    },
  },
};
</script>
