<template>
    <div class="execution">
        <basic-container>
          <el-radio-group style="margin-bottom:20px"  v-model="tabPosition"  @change="changeTab">
            <el-radio-button  label="1">未生成支付单</el-radio-button>
            <el-radio-button label="2">已生成支付单</el-radio-button>
          </el-radio-group>
            <avue-crud ref="crud"
                class="tabPosition1"
                v-show="tabPosition==1"
                :page.sync="page"
                :data="tableData"
                :permission="permissionList"
                :table-loading="tableLoading"
                :option="tableOption"
                v-model="form"
                @on-load="getPage"
                @selection-change="selectionChange"
                @refresh-change="refreshChange"
                @sort-change="sortChange"
                @search-change="searchChange">
                <template slot="projectInfoId" slot-scope="scope">
                    <span>{{scope.row.projectName}}</span>
                </template>
                <template slot="agentInfoId" slot-scope="scope">
                    <span>{{scope.row.agentName}}</span>
                </template>
                <template slot="header"  slot-scope="scope" v-if="tabPosition==1">
                    <el-button style="margin-left:14px" :disabled="selectList.length<1" v-if="permissions['chain:createPayOrder:create']&&tabPosition==1"
                    icon="el-icon-document"
                    size="small"
                    type="primary"
                    @click="confirmRemit(scope.row,scope.index)">
                    生成支付单
                  </el-button>
                </template>
                <template slot="companyWaybillCountHeader" slot-scope="{column}">
                    <el-tooltip class="item" effect="dark" content="证件齐全：运单有司机身份证与驾驶证和有车辆的行驶证与道路运输证" placement="top">
                      <span>{{(column || {}).label}} <i class="el-icon-question"></i></span>
                    </el-tooltip>
                </template>
            </avue-crud>
            <avue-crud ref="crud2"
                v-show="tabPosition==2"
                :page.sync="page2"
                :data="tableData2"
                :permission="permissionList"
                :table-loading="tableLoading"
                :option="tableOption2"
                v-model="form2"
                @on-load="getPages"
                @refresh-change="refreshChange"
                @sort-change="sortChange2"
                @search-change="searchChange">
                <template slot="projectInfoId" slot-scope="scope">
                    <span>{{scope.row.projectName}}</span>
                </template>
                <template slot="agentInfoId" slot-scope="scope">
                    <span>{{scope.row.agentName}}</span>
                </template>
                <template slot="settleCnt" slot-scope="scope">
                   <div style="color:#409eff;cursor:pointer" @click="settleDetail(scope.row)">{{scope.row.settleCnt}}</div>
                </template>
                <template slot="menu" slot-scope="scope">
                     <el-button type="text"
                    v-if="permissions['chain:createPayOrder:get']"
                    icon="el-icon-view"
                    size="small"
                    plain
                    @click="viewRow(scope.row,scope.index)">
                  查看</el-button>
                  <el-popconfirm
                    @confirm="cancelRow(scope.row,scope.index)"
                    title="确认是否取消？">
                      <el-button type="text"
                      style="margin-left:10px"
                        v-if="permissions['chain:createPayOrder:cancel']&&(scope.row.status==1||scope.row.status==2)"
                        icon="el-icon-document"
                        size="small"
                        plain
                        slot="reference">
                      取消</el-button>
                  </el-popconfirm>
                </template>
                <template slot="companyWaybillCountHeader" slot-scope="{column}">
                    <el-tooltip class="item" effect="dark" content="证件齐全：运单有司机身份证与驾驶证和有车辆的行驶证与道路运输证" placement="top">
                      <span>{{(column || {}).label}} <i class="el-icon-question"></i></span>
                    </el-tooltip>
                </template>
            </avue-crud>
        </basic-container>
         <!-- 结算单明细 -->
        <el-dialog title="结算单明细" class="settleDialog" :visible.sync="settleDialog" width="1000px" :close-on-click-modal="false">
          <div class="dialogContent" :id="'settleId'+(index+1)" v-for="(settleForm,index) in settleDetailList" :key="index">
            <div class="dialogTitle">结算单</div>
            <el-form  label-width="120px" class="demo-ruleForm">
              <el-row :gutter="10">
                <el-col :span="7">
                  <el-form-item label="结算单号：" >
                    <span>{{settleForm.settleNo}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="所属项目：" >
                    <span>{{settleForm.projectName}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="结算申请人：" >
                    <span>{{settleForm.agentName}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="7">
                  <el-form-item label="结算申请时间：" >
                    <span>{{settleForm.applyDatetime}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="结算人：" >
                    <span>{{settleForm.applyName}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="7">
                  <el-form-item label="核算运单数：" >
                    <span>{{settleForm.checkNum}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="核算运单金额：" >
                    <span>{{settleForm.checkAmount}}元</span>
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="核算人：" >
                    <span>{{settleForm.settleName}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-divider></el-divider>
            </el-form>
            <div class="myTable">
              <el-table :data="settleForm.waybillList" border height="500px">
                <el-table-column v-for="(item,index) in column" :key="index" :property="item.property" :label="item.label" ></el-table-column>
              </el-table>
            <div class="total">
                核算合计：共 <span>{{settleForm.checkNum}}</span>车，总计需要支付运费￥<span>{{settleForm.checkAmount}}</span> 元
            </div>
            </div>
            <div class="btn">
              <el-button  size="small" type="primary" @click="downExport(settleForm.waybillList)">导出</el-button>
              <el-button size="small" @click="print(index)">打印</el-button>
            </div>
          </div>

          <template slot="footer">
            <div style="text-align:center">
              <el-button
                      icon="el-icon-circle-check" size="small"
                      type="primary"
                      @click="settleDialog=false">
                    关闭</el-button>
            </div>
          </template>
        </el-dialog>
         <!-- 生成支付单 -->
        <el-dialog title="生成支付单" class="createDialog" :visible.sync="createDialog" width="900px" :close-on-click-modal="false">
          <el-form :model="createForm"  ref="createForm" label-width="110px" class="demo-ruleForm">
            <el-row>
              <el-col :span="12">
                <el-form-item label="结算申请人：" >
                  <span>{{createForm.agentName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属项目：" >
                  <span>{{createForm.projectName}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="24">
                <el-form-item label="已选结算单号：" >
                  <div>
                    <el-tag style="margin-right:10px" size="mini" v-for="(item,index) in selectIds" :key="index">{{item}}</el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="24">
                <el-form-item label="结算合计金额：" >
                  <div>{{createForm.settleAmount}}</div>
                </el-form-item>
              </el-col>
            </el-row>
             <!-- <el-divider></el-divider> -->
             <!-- <div class="content">
               <div>
                 <el-button
                      :loading="visibleBtnLoading"
                      icon="el-icon-plus" size="small"
                      type="primary"
                      @click="addList">
                    冲减费</el-button>
               </div>
               <ul v-if="createForm.list&&createForm.list.length>0">
                 <li v-for="(item,index) in createForm.list" :key="index">
                   <el-form-item label="冲减项费用金额："  label-width="136px" :prop="'list.'+index+'.amt'" :rules="{required: true, message: '请输入', trigger: 'blur'}">
                      <el-input v-model="item.amt"
                      oninput="value=value.replace(/[^\d.\-]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
                        @blur="handleInput(item, 'amt')"
                       placeholder="请输入冲减项费用金额" size="small" style="width:170px"></el-input>
                    </el-form-item>
                   <el-form-item label="说明：" label-width="76px">
                      <el-input v-model="item.item" placeholder="请输入说明" size="small" style="width:170px"></el-input>
                    </el-form-item>
                    <i @click="delList(index)" class="el-icon-circle-close"></i>
                 </li>
               </ul>
             </div> -->
          </el-form>
          <!-- <div class="remark">
                实际支付金额 = 结算合计金额 - 冲减项费用金额，{{createForm.settleAmount}}  {{amountTotal>=0?'+'+amountTotal:amountTotal}}元 = {{createForm.settleAmount+amountTotal}}元
            </div> -->
          <template slot="footer">
            <div style="text-align:center">
              <el-button
                      :loading="visibleBtnLoading"
                      icon="el-icon-circle-check" size="small"
                      type="primary"
                      @click="confirm">
                    确认</el-button>
              <el-button
              icon="el-icon-circle-close" size="small"
                      @click="createDialog=false">
                    取消</el-button>
            </div>
          </template>
        </el-dialog>
        <!-- 查看支付单 -->
        <el-drawer title="支付单审批" :visible.sync="paymentVisible" size="580px" :data="paymentVisible">
              <h3>支付单信息</h3>
              <el-table
                :data="paymentList"
                border
                :show-header="false"
                style="width: 100%">
                <el-table-column prop="payLabel"></el-table-column>
                <el-table-column prop="payValue"></el-table-column>
                <el-table-column prop="payRemark"></el-table-column>
              </el-table>
              <div class="approvalInfo">
              <h3 v-if="approvalform.companyPaymentFlowList&&approvalform.companyPaymentFlowList.length>0">审批流程</h3>
                <div class="info" v-if="approvalform.companyPaymentFlowList&&approvalform.companyPaymentFlowList.length>0">
                  <div class="approvalFlow">
                    <el-timeline >
                      <el-timeline-item :timestamp="index===0?'发起申请':item.positionName" placement="top" :class="item.approve?'myActive':''" :color="item.approve?'#409eff':'#e4e7ed'" v-for="(item,index) in approvalform.companyPaymentFlowList" :key="index">
                        <!-- <div slot="dot" style="width:30px;height:30px;background:#ccc;">张三</div> -->
                          <div style="display:flex;align-items: center;justify-content: space-between;margin-bottom:10px"><h4>{{item.checkName}}<span v-if="index!==0" style="margin-left:6px">({{item.passName}})</span> </h4><span>{{$moment(item.updateDatetime).format('YYYY-MM-DD HH:mm:ss')}}</span></div>
                          <el-input type="textarea"  v-if="index!=0&&item.approve" v-model="item.approve" :autosize="{ minRows: 3, maxRows: 8}" disabled></el-input>
                      </el-timeline-item>
                    </el-timeline>
                  </div>
                </div>
              </div>
        </el-drawer>
    </div>
</template>

<script>
    import {getPage, getObj, addObj, putObj, delObj,getPages,genPayment,paymentInfo,cannelPayment,getAllSettle} from '@/api/chain/createPayOrder'
    import {tableOption,tableOption2} from '@/const/crud/chain/createPayOrder2'
    import {mapGetters} from 'vuex'
    import {clearNoNum} from '@/util/util.js'
    import $Print from 'avue-plugin-print'

    //引入账单核算
    // import billAccount from './billAccount.vue'
    // import accountSheet from './accountSheet.vue'
    export default {
        name: 'createPayOrder2',
        components:{
          // billAccount,
          // accountSheet,
        },
        data() {
            return {
                form: {},
                form2: {},
                tableData: [],
                tableData2: [],
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                    ascs: [],//升序字段
                    descs: []//降序字段
                },
                page2: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                    ascs: [],//升序字段
                    descs: []//降序字段
                },
                paramsSearch: {},
                tableLoading: false,
                tableLoading2: false,
                tableOption: tableOption(this),
                tableOption2: tableOption2,
                tabPosition:'1',
                selectList:[],
                settleDialog:false,
                createDialog:false,
                paymentVisible:false,
                visibleBtnLoading:false,
                createForm:{
                  list:[
                    {
                     amt:0,
                     item:''
                    },
                  ]
                },
                settleForm:{
                  list:[
                    {
                      no:1,
                      truckCode:2,
                      agentName:3,
                      driverCaptainName:4,
                      driverName:5,
                      inStaffName:6,
                      goStaffName:7,
                      inDatetime:'2021-04-14 17:02:00',
                      goDatetime:'2021-04-14 17:01:00',
                      settleDatetime:'2021-04-14 17:00:00',
                      settlePrice:10,
                    },
                    {
                      no:2,
                      truckCode:3,
                      agentName:4,
                      driverCaptainName:5,
                      driverName:6,
                      inStaffName:7,
                      goStaffName:8,
                      inDatetime:'2021-04-14 17:03:00',
                      goDatetime:'2021-04-14 17:04:00',
                      settleDatetime:'2021-04-14 17:05:00',
                      settlePrice:11,
                    },
                  ],
                },
                column:[
                  {
                    label:'运单号',
                    property:'no',
                  },
                  {
                    label:'车牌号',
                    property:'truckCode',
                  },
                  {
                    label:'项目合作方',
                    property:'agentName',
                  },
                  {
                    label:'车队长',
                    property:'driverCaptainName',
                  },
                  {
                    label:'司机',
                    property:'driverName',
                  },
                  {
                    label:'挖机签单员',
                    property:'inStaffName',
                  },
                  {
                    label:'出场签单员',
                    property:'goStaffName',
                  },
                  {
                    label:'挖机签单时间',
                    property:'inDatetime',
                  },
                  {
                    label:'出场签单时间',
                    property:'goDatetime',
                  },
                  {
                    label:'完成时间',
                    property:'settleDatetime',
                  },
                  // {
                  //   label:'结算价',
                  //   property:'price',
                  // },
                  {
                    label:'核算价',
                    property:'settlePrice',
                  },
                ],  //查看运单数据列数据
                approvalform:{},
                paymentList:[],
                selectIds:[],
                settleDetailList:[],
            }
        },
        created() {
        },
        mounted: function () {
        },
        computed: {
            ...mapGetters(['permissions']),
            permissionList() {
                return {
                    addBtn: this.permissions['chain:createPayOrder:add'] ? true : false,
                    delBtn: this.permissions['chain:createPayOrder:del'] ? true : false,
                    editBtn: this.permissions['chain:createPayOrder:edit'] ? true : false,
                    viewBtn: this.permissions['chain:createPayOrder:get'] ? true : false
                };
            },
            // isDisabled() {
            //   return this.selectList&&this.selectList.length>0&&this.selectList.every(item=>{
            //     return (item.projectName ==this.selectList[0].projectName)&&(item.agentName ==this.selectList[0].agentName)
            //   })
            // },
            //总加减项
            amountTotal() {return this.createForm.list.map((row) =>row.amt).reduce((acc, cur) => parseFloat(cur) + acc, 0)},
        },
        methods: {
            searchChange(params,done) {
                params = this.filterForm(params)
                this.paramsSearch = params
                if(this.tabPosition == 1){
                  this.page.currentPage = 1
                  this.getPage(this.page, params)
                }else{
                  this.page2.currentPage = 1
                  this.getPages(this.page2, params)
                }
                done()
            },
            sortChange(val) {
                let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
                if (val.order == 'ascending') {
                    this.page.descs = []
                    this.page.ascs = prop
                } else if (val.order == 'descending') {
                    this.page.ascs = []
                    this.page.descs = prop
                } else {
                    this.page.ascs = []
                    this.page.descs = []
                }
                this.getPage(this.page)
            },
            sortChange2(val) {
                let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
                if (val.order == 'ascending') {
                    this.page2.descs = []
                    this.page2.ascs = prop
                } else if (val.order == 'descending') {
                    this.page2.ascs = []
                    this.page2.descs = prop
                } else {
                    this.page2.ascs = []
                    this.page2.descs = []
                }
                this.getPages(this.page2)
            },
            getPage(page, params) {
                this.tableLoading = true
                getPage(Object.assign({
                    current: page.currentPage,
                    size: page.pageSize,
                    descs: this.page.descs,
                    ascs: this.page.ascs,
                    searchType:3,
                }, params, this.paramsSearch)).then(response => {
                    this.tableData = response.data.data.records
                    this.page.total = response.data.data.total
                    this.page.currentPage = page.currentPage
                    this.page.pageSize = page.pageSize
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            },
            getPages(page, params) {
                this.tableLoading = true
                getPages(Object.assign({
                    current: page.currentPage,
                    size: page.pageSize,
                    descs: this.page2.descs,
                    ascs: this.page2.ascs,
                    searchType:3,
                }, params, this.paramsSearch)).then(response => {
                    this.tableData2 = response.data.data.records
                    this.page2.total = response.data.data.total
                    this.page2.currentPage = page.currentPage
                    this.page2.pageSize = page.pageSize
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            },
            /**
             * 刷新回调
             */
            refreshChange(page) {
              if(this.tabPosition==1){
                this.getPage(this.page)
              }else{
                this.getPages(this.page2)
              }
            },
            selectionChange(e,) {
              this.selectList = e
            },
            changeTab() {
              if(this.tabPosition==2){
                //已审批
                this.page2.currentPage = 1
                this.getPages(this.page2)
              }else{
                //待审批
                this.page.currentPage = 1
                this.getPage(this.page)
              }
              this.selectList = []
              this.$refs.crud.selectClear()
              this.$refs.crud.refreshTable()
            },
            cancelRow(row) {
              console.log(row);
              this.tableLoading = true
              let param = {
                id:row.id
              }
              setTimeout(()=>{
                this.tableLoading = false
              },3000)
              cannelPayment(param).then(res=>{
                this.tableLoading = false
                this.getPages(this.page2)
              })
            },
            viewRow(row) {
              paymentInfo(row.id).then(res=>{
                console.log(res);
                this.paymentList = [
                  {
                    payLabel:'支付单号',
                    payValue:res.data.data.paymentNo,
                    payRemark:'说明',
                  },
                  {
                    payLabel:'结算申请人',
                    payValue:res.data.data.agentName,
                    payRemark:'',
                  },
                  {
                    payLabel:'承运人',
                    payValue:res.data.data.payeeName,
                    payRemark:'',
                  },
                  {
                    payLabel:'所属项目',
                    payValue:res.data.data.projectName,
                    payRemark:'',
                  },
                  {
                    payLabel:'结算单数量',
                    payValue:res.data.data.settleCnt,
                    payRemark:'',
                  },
                  {
                    payLabel:'结算运单数量',
                    payValue:res.data.data.waybillCnt,
                    payRemark:'',
                  },
                  {
                    payLabel:'结算合计金额',
                    payValue:res.data.data.settleAmt||0+'元',
                    payRemark:'',
                  },

                ]
                if(res.data.data.companyPaymentItemList&&res.data.data.companyPaymentItemList.length>0){
                  res.data.data.companyPaymentItemList.forEach((item,index)=>{
                    this.paymentList.push({
                      payLabel:'冲减项费用金额'+(index+1),
                      payValue:item.amt+'元',
                      payRemark:item.item,
                    })
                  })
                }
                this.paymentList.push({
                  payLabel:'实际支付金额',
                  payValue:res.data.data.amt+'元',
                  payRemark:'',
                })
                this.approvalform = res.data.data
                this.paymentVisible = true
              })
            },
            confirmRemit(){
              this.createForm = {
                list:[]
              }
              this.createForm = Object.assign(this.createForm,this.selectList[0])
              let settleAmount = 0
              let settleIds = []
              let settleNos = []
              this.selectList.forEach(item=>{
                settleAmount=item.settleAmount?settleAmount+Number(item.settleAmount):settleAmount
                settleIds.push(item.id)
                settleNos.push(item.settleNo)
              })
              this.selectIds = settleNos
              this.createForm.settleAmount = settleAmount.toFixed(2)
              this.createForm.settleIds = settleIds.join(',')
              this.createDialog = true
            },
            confirm() {
              let param = {
                settleIds:this.createForm.settleIds,
                items:this.createForm.list
              }
              genPayment(param).then(res=>{
                this.createDialog = false
                this.getPage(this.page)
              })
            },
            addList() {
              console.log(this.createForm);
              this.createForm.list.push({
                amt:0,
                item:'',
              })
            },
            delList(index) {
              this.createForm.list.splice(index,1)
            },
            handleInput(obj,value){
              obj[value] = clearNoNum(obj[value] + "")
            },
            //查看结算单详情
            settleDetail(row){
              this.tableLoading = true
              setTimeout(()=>{
                this.tableLoading = false
              },3000)
              getAllSettle(row.id).then(res=>{
                console.log(res);
                this.settleDetailList = res.data.data
                this.tableLoading = false
                this.$router.push({path:'/waybill/waybillDetail',query:{value:JSON.stringify(this.settleDetailList)}})
                // params:JSON.stringify(this.settleDetailList)
                // this.settleDialog = true
              })
            },
            //导出
            downExport(data){
            console.log(data);
              this.$export.excel({
                title:'结算单',
                columns:[
                    {
                      label:'运单号',
                      prop:'no',
                    },
                    {
                      label:'车牌号',
                      prop:'truckCode',
                    },
                    {
                      label:'项目合作方',
                      prop:'agentName',
                    },
                    {
                      label:'车队长',
                      prop:'driverCaptainName',
                    },
                    {
                      label:'司机',
                      prop:'driverName',
                    },
                    {
                      label:'挖机签单员',
                      prop:'inStaffName',
                    },
                    {
                      label:'出场签单员',
                      prop:'goStaffName',
                    },
                    {
                      label:'挖机签单时间',
                      prop:'inDatetime',
                    },
                    {
                      label:'出场签单时间',
                      prop:'goDatetime',
                    },
                    {
                      label:'完成时间',
                      prop:'settleDatetime',
                    },
                    // {
                    //   label:'结算价',
                    //   prop:'price',
                    // },
                    {
                      label:'核算价',
                      prop:'settlePrice',
                    },
                ],
                data:data,
              })
            },
            print(index){
              $Print('#settleId'+(index+1))
            },
        }
    }
</script>

<style lang="scss" scoped>
  /deep/ .el-dialog__body{
    padding-top: 0px;
    padding-bottom: 40px;
  }
   .el-form .el-form-item{
    margin-bottom: 0px;
  }
/deep/ .createDialog{
  .el-divider{
    margin-top: 10px;
  }
  .content{
    ul{
      padding-left: 20px;
      margin-top: 20px;
      li{
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .el-icon-circle-close{
          font-size: 20px;
          margin-left: 20px;
          cursor: pointer;
        }
      }
    }
  }
  .remark{
    position: absolute;
    left: 40px;
    bottom: 60px;
    color: #409eff;
  }
}
//结算单弹窗
/deep/ .settleDialog{
  .el-dialog__body{
    padding-top: 0;
  }
 .dialogContent{
  border: 1px solid #ccc;
  margin-bottom: 30px;
  .dialogTitle{
      font-size: 18px;
      font-weight: 700;
      text-align: center;
      line-height: 40px;
      border-bottom: 1px solid #ccc;
    }
    .myTable{
      padding: 0 10px;
    }
    .total{
      margin-top: 40px;
      background-color: #f2f2f2;
      color: #333;
      font-weight: 700;
      line-height: 40px;
      text-align: center;
    }
    .btn{
      margin-top: 20px;
      margin-bottom: 20px;
      text-align: center;
    }
}

}
//查看支付单开始
/deep/ .el-drawer__header{
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ccc;
}
/deep/ .el-drawer__body{
  padding: 20px;
  padding-top: 0px;
  .approvalInfo{
    text-align: left;
    display: inline-block;
    margin-top: 20px;
    width: 100%;
  }
   h3{
      margin-bottom: 20px;
      font-weight: 700;
      padding-left: 8px;
        text-align: left;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        color: #111528;
        border-left: 4px solid #4688f7;
    }
  .approvalInfo{
    .approvalTitle{
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 16px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ccc;
      margin-bottom: 20px;
      .approvalSource{
        padding-left: 8px;
        text-align: left;
        height: 18px;
        line-height: 18px;
        font-size: 16px;
        color: #111528;
        // border-left: 4px solid #4688f7;
      }
    }
    .approvalFlow{
      width: 400px;
    }

    .el-timeline-item__timestamp{
      color: #333;
    }
    .el-timeline-item__content{
      color: #909399;
    }
    .myActive .el-timeline-item__tail{
      border-left: 2px solid #409eff;
    }
    .approvalform{
      padding: 20px 20px 0px;
    }
    .info{
      padding: 0px 20px;
    }
  }
}
/deep/ .tabPosition1 .el-table__header .el-table-column--selection .el-checkbox{
  display: none;
}
//查看支付单结束
</style>
