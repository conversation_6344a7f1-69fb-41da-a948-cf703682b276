export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  addBtn: false,
  viewBtn: true,
  searchMenuSpan: 6,
  column: [
    {
      label: "企业名称",
      prop: "companyName",
      sortable: "custom",
      minWidth:150,
      overHidden:true,
    },

    {
      label: "截至年月",
      prop: "presentDate",
      sortable: true,
      search: true,
      type: "month",
      valueFormat: "yyyy-MM",
      minWidth:100,
      overHidden:true,
    //   pickerOptions: {
    //     disabledDate:date=> {
    //       // 大于某个日期不能选择
    //         let myDate = new Date();
    //         let _beforeDay = myDate.setDate(new Date().getDate());
    //       //不能大于5年
    //       const year = new Date().getFullYear() // 获取当前年份
    //       const mon = new Date().getMonth() + 1 // 获取当前月份份
    //       const day = new Date().getDate() // 获取当前日期
    //       const oneYear = year - 5 // 获取5年前
    //       const timeYear = new Date(oneYear + '/' + mon + '/' + day).getTime() // 将五年前的日期转换成时间戳
    //         return date.getTime() >= _beforeDay||date.getTime() < timeYear;
    //     },
    // },
    },
    {
      label: "运单总数",
      prop: "waybillTotal",
      sortable: true,
      minWidth:100,
      overHidden:true,
    },
    {
      label: "开票运单总数",
      prop: "khyWaybillTotal",
      rules: [],
      minWidth:104,
      overHidden:true,
    },
    {
      label: "收费运单总数",
      prop: "chargeWaybillTotal",
      minWidth:104,
      overHidden:true,
    },
    {
      label: "应付服务费用(元)",
      prop: "chargeTotal",
      minWidth:120,
      overHidden:true,
    },
    {
      label: "是否确认对账",
      prop: "isConfirm",
      sortable: true,
      search: true,
      searchLabelWidth:110,
      type: "select",
      dicData: [
        {
          label: "否",
          value: "0",
        },
        {
          label: "是",
          value: "1",
        },
      ],
      minWidth:120,
      overHidden:true,
    },
    {
      label: "是否付款",
      prop: "isPay",
      sortable: true,
      search: true,
      type: "select",
      dicData: [
        {
          label: "否",
          value: "0",
        },
        {
          label: "是",
          value: "1",
        },
      ],
      minWidth:100,
      overHidden:true,
    },
    // {
    //   label: "状态：1=发起对账 ，2=确认对账，3=已付款（默认1）",
    //   prop: "status",
    //   sortable: true,
    //   rules: [
    //     {
    //       max: 1,
    //       message: "长度在不能超过1个字符",
    //     },
    //   ],
    // },
    // {
    //   label: "是否删除：0=否；1=是（默认0）",
    //   prop: "isDel",
    //   sortable: true,
    //   rules: [
    //     {
    //       max: 1,
    //       message: "长度在不能超过1个字符",
    //     },
    //   ],
    // },
    // {
    //   label:
    //     '项目服务费json（[{"projectInfoId": "", "serviceCharge": 0.2, "updTime": ""}]）',
    //   prop: "projectServiceChargeJson",
    //   sortable: true,
    //   rules: [],
    // },

    // {
    //   label: "创建日期时间",
    //   prop: "createDatetime",
    //   sortable: true,
    //   hide: true,
    //   minWidth:140,
    //   overHidden:true,
    // },
    // {
    //   label: "修改日期时间",
    //   prop: "updateDatetime",
    //   hide: true,
    //   sortable: true,
    //   minWidth:140,
    //   overHidden:true,
    // },
  ],
};
