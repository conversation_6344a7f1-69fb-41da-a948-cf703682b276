<template>
  <div class="selectPersonnel">
    <el-dialog :visible.sync="visible"
               title="新增车队"
               :close-on-click-modal="false"
               width="700px"
               :before-close="oncancel">
      <avue-form :option="option"
                 ref="fleetForm"
                 v-model="form"
                 @submit="submit">

      </avue-form>
      <div slot="footer"
           class="dialog-footer">
        <el-button @click="oncancel">取 消</el-button>
        <el-button type="primary"
                   @click="$refs.fleetForm.submit()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {addObj} from '@/api/captain/fleetManage';
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    checkList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      form:{},
      option:{
        emptyBtn:false,
        submitBtn:false,
        column: [
          {
            label: "车队名称",
            prop: "fleetName",
            // sortable: true,
            rules: [
              {
                required: true,
                message: "请输入 车队名称",
                trigger: "blur",
              },
            ],
            span:24,
            minWidth:180,
            overHidden:true,
          },
          {
            label: "车队公告",
            prop: "fleetNotic",
            type:'textarea',
            span:24,
            rules: [
              {
                required: true,
                message: "请输入 车队公告",
                trigger: "blur",
              },
            ],
            hide:true,
            showColumn:false,
          }]
      }
    };
  },
  mounted() {
  },
  methods: {
    oncancel() {
      this.$emit("update:visible", false);
    },
    submit(form,done){
      console.log(form);
      addObj(form).then(res=>{
        this.$emit('complete')
      })
      done()
    },
  },
};
</script>

<style scoped lang="less">
</style>
