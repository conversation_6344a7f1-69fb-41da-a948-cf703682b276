<!--表格组件 -->
<template>
  <div class="programme">
    <div class="programmeTags">
      <el-tag
        :closable="tag.closable"
        :disable-transitions="false"
        :effect="isActive == index ? 'dark' : 'plain'"
        @close="handleClose(tag)"
        v-for="(tag, index) in tags"
        :key="index"
        @click="tagClick($event, index)"
      >
        {{ tag.name }}
      </el-tag>
      <el-button  class="button-new-tag" size="mini" @click="showDrawer"
        >+新增方案</el-button
      >
      <div>
        <searchForm ref="searchForm" :noSplice='true' v-for="(tag, index2) in tags" :key="index2" v-if="isActive == index2" :searchOption="filterOption(tag)" @searchChange="searchChange">
          <template slot="menuForm">
            <el-button v-if="isActive!=0" icon="el-icon-setting" type="primary" @click="edit(tag)">配置</el-button>
          </template>
        </searchForm>
      </div>
    </div>

    <checkSearch v-if="show" :list="searchOption.column" :type="type" :info="info" :visible.sync="show" @save='save'></checkSearch>
    <!-- <slot name="customForm" :row="form"></slot> -->
  </div>
</template>

<script>
import {mapState} from "vuex";
import searchForm from './searchForm';
import checkSearch from './checkSearch';

export default {
  props: {
    searchOption: {
      type:Object,
      default:()=>{
        return {}
      }
    },
    searchSlot:{
      type:Array,
      default:()=>{
        return []
      }
    }
  },
  components:{
    searchForm,
    checkSearch
  },
  inject: ["crud"],
  data() {
    return {
      tags: [{ name: "默认方案", closable: false,searchConfig:[]}],
      show: false,//弹窗
      isActive: 0,//选中方案index
      info:{},
      type:1,
      currentOption:{},
    };
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
  },
  watch: {},
  mounted() {
    //先获取配置
    this.getScheme()
  },
  methods: {
    //判断使用什么组件
    filterOption(tag) {
      if(tag.name=="默认方案"&&tag.closable==false){
        //默认方案直接返回整个
        return this.searchOption
      }else{
        let tempOption = this.deepClone(this.searchOption)
        let column = []
        tempOption.column.forEach(item=>{
          if(tag.searchConfig.includes(item.prop)){
            column.push(item)
          }
        })
        tempOption.column= column
        this.currentOption = tempOption
        return tempOption
      }
    },
    //删除方案
    handleClose(tag) {
       this.$confirm("是否确认删除此方案？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(()=> {
        if(this.isActive==this.tags.length-1){
          this.isActive -= 1
        }
        this.tags.splice(this.tags.indexOf(tag), 1);
        this.saveScheme()

      }).catch(function (err) {});
    },
    /**
     * type 1新增  2修改
     * form 数据
     * done  关闭弹窗及loading
     * loading  只关闭loading
     */
    //点击保存
    save(type,form,done,loading){
      if(type==1){
        //新增
        if(this.tags.find(item=>item.name==form.name)){
          this.$message.error('方案名称不能相同,请修改后再保存！')
          loading()
          return false
        }
        this.tags.push({ name: form.name, closable: true,searchConfig:form.searchConfig});
        this.isActive+=1
      }else{
        //修改
        this.tags.splice(this.isActive,1,{ name: form.name, closable: true,searchConfig:form.searchConfig})
        this.$refs.searchForm?.[0]?.$refs?.form?.resetForm()
      }
      done()
      this.saveScheme()
      this.$message.success("保存成功")

    },
    //获取方案配置
    getScheme(){
      let schemeConfig = localStorage.getItem('schemeConfig'+this.crud.option.routerName+this.userInfo.id)
      if(schemeConfig){
        this.tags = JSON.parse(schemeConfig)
      }
    },
    //点击新增
    showDrawer(){
      this.type = 1
      this.info = {}
      this.show = true
    },
    edit(tag){
      this.type = 2
      this.info = {
        checkItem:this.currentOption.column.map(item=>item.prop),
        name:tag.name
      }
      this.show = true
    },
    //保存方案配置
    saveScheme(){
      localStorage.setItem('schemeConfig'+this.crud.option.routerName+this.userInfo.id,JSON.stringify(this.tags))
    },
    //点击方案
    tagClick(event, index) {
      if (this.isActive == index) return false;
      // this.list = this.tags[index].searchConfig
      // this.form = {}
      this.isActive = index;
    },
    searchChange(form, done) {
      this.$emit("searchChange", form, done);
    },
  },
  created() {},
};
</script>
<style lang="scss" scoped>
.programme {
  position: relative;
  .programmeTags{
    // padding-bottom: 8px;
    // border-bottom: 1px solid #ebeef5;
  }
  .button-new-tag{
    margin-left: 10px;
  }
  .el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
  .dialog {
    position: absolute;
    left: 0;
    top: 36px;
    z-index: 999;
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-width: 532px;
    min-height: 100px;
    box-sizing: border-box;
  }
  .lists {
    margin-top: 10px;
    padding: 10px;
    background-color: #fff;
    max-height: 300px;
    overflow-y: auto;
    border-radius: 4px;
    min-width: 532px;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    ul {
      li {
        display: flex;
        line-height: 48px;
        .left {
          margin-right: 6px;
          width: 200px;
        }
      /deep/  .center {
          margin-right: 6px;
          width: 360px;
          .el-input__inner{
            width: 100%;
          }
          .el-select{
            width: 100%;
          }
         /deep/ .el-date-editor--datetimerange.el-input__inner{
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
