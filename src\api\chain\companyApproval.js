import request from '@/router/axios'
//流程模板列表
export function getPage(query) {
    return request({
        url: '/chain/companyflowsetting/getFlowList',
        method: 'get',
        params: query
    })
}
//获取审批人员信息
export function getFlowStaff(query) {
    return request({
        url: '/chain/companyflowsetting/getFlowStaff',
        method: 'get',
        params: query
    })
}
//获取流程开关
export function getFlowSetting(query) {
    return request({
        url: '/chain/companyflowsetting/getFlowSetting',
        method: 'get',
        params: query
    })
}
//流程模板保存
export function addObj(obj) {
    return request({
        url: '/chain/companyflowsetting/save',
        method: 'post',
        data: obj
    })
}
//流程模板保存
export function updateFlowSetting(obj) {
    return request({
        url: '/chain/companyflowsetting/updateFlowSetting',
        method: 'post',
        data: obj
    })
}


