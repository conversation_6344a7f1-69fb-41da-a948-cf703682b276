﻿/**
 * 配置参考:
 * https://cli.vuejs.org/zh/config/
 */
const url = 'http://127.0.0.1:9999'
//后台网关地址
//const url = 'http://127.0.0.1'     // 本地测试
//const url = 'http://************:9999'   //开发
//const url = 'http://***********:9999'   //开发
// const url = 'http://*************:9999'   //开发
// const url = 'http://**************:9999' //测试环境
// const url = 'http://jyb-test.szyl8.cn/' //测试环境
// const url = 'https://jyb-test.szyl8.cn/' //正式环境
// const url = 'http://rv20865193.wicp.vip' //测试环境
const TerserPlugin = require('terser-webpack-plugin');
const webpack = require("webpack");
// const BundleAnalyzerPlugin =require("webpack-bundle-analyzer").BundleAnalyzerPlugin;
const CompressionWebpackPlugin = require("compression-webpack-plugin");
const productionGzipExtensions = ["js", "css"];
const fs = require('fs')
const path = require('path')
function resolve(dir) {
  return path.join(__dirname, dir)
}
module.exports = {
  filenameHashing: true, // 打包后为文件名增加hash值
  lintOnSave: true,
  productionSourceMap: false,
  chainWebpack: config => {
    config.output.filename('js/[name].[hash].js').end()  // 打包后为文件名增加hash值
    // 忽略的打包文件
    config.externals({
      axios: "axios",
      xlsx: "XLSX",
    })
    const entry = config.entry('app')
    entry
      .add('babel-polyfill')
      .end()
    entry
      .add('classlist-polyfill')
      .end()
    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  },
  css: {
    extract: process.env.NODE_ENV === 'production' ? {
      ignoreOrder: true,
    } : false,
  },
  configureWebpack: (config) => {
    if (process.env.NODE_ENV === 'production') {// 为生产环境修改配置...
      config.mode = 'production';
      config["performance"] = {//打包文件大小配置
        "maxEntrypointSize": 10000000,
        "maxAssetSize": 30000000
      }
      config.optimization = {
        minimizer: [
          new TerserPlugin({
            terserOptions: {
              format: {
                comments: false,
              },
              ecma: undefined,
              warnings: false,
              parse: {},
              compress: {
                drop_console: true, // 清除 console 语句
                drop_debugger: false, // 清除 debugger 语句
                pure_funcs: ["console.log"], // 移除console
              },
            },
          }),
          //     new BundleAnalyzerPlugin({
          //   //  可以是`server`，`static`或`disabled`。
          //   //  在`server`模式下，分析器将启动HTTP服务器来显示软件包报告。
          //   //  在“静态”模式下，会生成带有报告的单个HTML文件。
          //   //  在`disabled`模式下，你可以使用这个插件来将`generateStatsFile`设置为`true`来生成Webpack Stats JSON文件。
          //   analyzerMode: "server",
          //   //  将在“服务器”模式下使用的主机启动HTTP服务器。
          //   analyzerHost: "127.0.0.1",
          //   //  将在“服务器”模式下使用的端口启动HTTP服务器。
          //   analyzerPort: 8888,
          //   //  路径捆绑，将在`static`模式下生成的报告文件。
          //   //  相对于捆绑输出目录。
          //   reportFilename: "report.html",
          //   //  模块大小默认显示在报告中。
          //   //  应该是`stat`，`parsed`或者`gzip`中的一个。
          //   //  有关更多信息，请参见“定义”一节。
          //   defaultSizes: "parsed",
          //   //  在默认浏览器中自动打开报告
          //   openAnalyzer: true,
          //   //  如果为true，则Webpack Stats JSON文件将在bundle输出目录中生成
          //   generateStatsFile: false,
          //   //  如果`generateStatsFile`为`true`，将会生成Webpack Stats JSON文件的名字。
          //   //  相对于捆绑输出目录。
          //   statsFilename: "stats.json",
          //   //  stats.toJson（）方法的选项。
          //   //  例如，您可以使用`source：false`选项排除统计文件中模块的来源。
          //   //  在这里查看更多选项：https：  //github.com/webpack/webpack/blob/webpack-1/lib/Stats.js#L21
          //   statsOptions: null,
          //   logLevel: "info", // 日志级别。可以是'信息'，'警告'，'错误'或'沉默'。
          // }),
          // 下面是下载的插件的配置
          new CompressionWebpackPlugin({
            algorithm: "gzip",
            test: new RegExp("\\.(" + productionGzipExtensions.join("|") + ")$"),
            threshold: 10240,
            minRatio: 0.8,
          }),
          new webpack.optimize.LimitChunkCountPlugin({
            maxChunks: 5,
            minChunkSize: 100,
          }),
        ]
      }
    }
  },
  // 本地开发环境配置
  devServer: {
    //https相关配置
    // https: {
    //   key: fs.readFileSync('E:/JL/GIT/all/file/2881827_test.joolun.com_nginx/2881827_test.joolun.com.key'),
    //   cert: fs.readFileSync('E:/JL/GIT/all/file/2881827_test.joolun.com_nginx/2881827_test.joolun.com.pem')
    // },
    disableHostCheck: true,
    port: 8082,
    //转发代理
    proxy: {
      "/auth": {
        target: url,
        ws: true,
        pathRewrite: {
          "^/auth": "/auth",
        },
      },
      "/upms": {
        target: url,
        ws: true,
        pathRewrite: {
          "^/upms": "/upms",
        },
      },
      "/code": {
        target: url,
        ws: true,
        pathRewrite: {
          "^/code": "/code",
        },
      },
      "/gen": {
        target: url,
        ws: true,
        pathRewrite: {
          "^/gen": "/gen",
        },
      },
      "/doc": {
        target: url,
        ws: true,
        pathRewrite: {
          "^/doc": "/doc",
        },
      },
      "/webjars": {
        target: url,
        ws: true,
        pathRewrite: {
          "^/webjars": "/webjars",
        },
      },
      "/swagger-resources": {
        target: url,
        ws: true,
        pathRewrite: {
          "^/swagger-resources": "/swagger-resources",
        },
      },
      "/weixin": {
        target: url,
        ws: true,
        pathRewrite: {
          "^/weixin": "/weixin",
        },
      },
      "/wxma": {
        target: url,
        ws: true,
        pathRewrite: {
          "^/wxma": "/wxma",
        },
      },
      "/mall": {
        target: url,
        ws: true,
        pathRewrite: {
          "^/mall": "/mall",
        },
      },
      "/chain": {
        target: url,
        ws: true,
        pathRewrite: {
          "^/chain": "/chain",
        },
      },
    },
  }
}
