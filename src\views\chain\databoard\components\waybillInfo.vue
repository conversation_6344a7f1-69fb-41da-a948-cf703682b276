<template>
  <div class="totalWaybill">
    <el-drawer size="80%" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <el-tabs v-model="activeName" >
          <el-tab-pane label="运单费" name="1">
            <waybillFee :info="info"  :tab="activeName" v-if="activeName==1"></waybillFee>
          </el-tab-pane>
          <el-tab-pane label="运单数" name="2">
            <waybillNum :info="info" :tab="activeName" v-if="activeName==2"></waybillNum>
          </el-tab-pane>
        </el-tabs>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import waybillFee from "./waybillFeeAndNum/waybillFee";
import waybillNum from "./waybillFeeAndNum/waybillNum";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  components: {
    waybillFee,
    waybillNum,
  },
  data () {
    return {
      activeName:"1",
    }
  },
  created () {
    this.activeName=this.info.wayRadio
  },
  mounted () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },

  },

};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
</style>
