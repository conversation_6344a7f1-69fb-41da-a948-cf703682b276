<template>
  <div class="companyApproval">
    <basic-container>
      <el-tabs v-model="activeName">
        <el-tab-pane label="流程设置" name="1">
          <el-form label-width="54px">
            <el-form-item
              label="是否开启审核流程："
              labelWidth="150"
              v-if="permissions['chain:companyApproval:open']"
            >
              <el-switch v-model="isOpen" @change="changeSwitch"></el-switch>
              <span style="color: red; margin-left: 20px; font-size: 14px"
                >更改状态后请记得提交哦!</span
              >
            </el-form-item>
            <div class="steps">
              <div class="steps-label">配置结算审批流程：</div>
              <div class="step_box">
                <div
                  class="step"
                  v-for="(v, i) in list"
                  :key="i"
                  v-if="list && list.length > 0"
                >
                  <div class="list">
                    <div class="title">第{{ nums[i] }}步</div>
                    <div class="content">
                      <el-form-item label="姓名：">
                        <el-select
                          v-model="v.companyStaffIdList"
                          multiple
                          filterable
                          size="mini"
                          placeholder="请选择"
                        >
                          <el-option
                            :label="item.name"
                            @click.native="changeStaff(v, item)"
                            :value="item.companyStaffId"
                            v-for="(item, index2) in staffList"
                            :key="index2"
                          >
                            <template>
                              <div class="select">
                                <div class="label">
                                  {{ item.name }}
                                </div>
                                <div class="tags">
                                  <div class="tag">
                                    {{ item.positionName }}
                                  </div>
                                </div>
                                <i
                                  class="el-icon-check"
                                  style="
                                    color: #409eff;
                                    font-size: 20px;
                                    font-weight: bold;
                                    margin-left: auto;
                                  "
                                  v-if="
                                    v.companyStaffIdList.includes(
                                      item.companyStaffId
                                    )
                                  "
                                ></i>
                              </div>
                            </template>
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <!-- <el-form-item label="职位：">
                              <span>{{v.positionName}}</span>
                            </el-form-item> -->
                      <div class="btn">
                        <el-button type="text" @click="del('list', i)"
                          >删除</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="add"
                  @click="addBefore('list')"
                  v-if="list && list.length < 9"
                >
                  <i class="el-icon-plus"></i>
                </div>
              </div>
            </div>
            <div class="steps">
              <div class="steps-label">配置支付单审批流程：</div>
              <div class="step_box">
                <div
                  class="step"
                  v-for="(v, i) in list2"
                  :key="i"
                  v-if="list2 && list2.length > 0"
                >
                  <div class="list">
                    <div class="title">第{{ nums[i] }}步</div>
                    <div class="content">
                      <el-form-item label="姓名：">
                        <el-select
                          v-model="v.companyStaffId"
                          size="mini"
                          placeholder="请选择"
                        >
                          <el-option
                            :label="item.name"
                            @click.native="changeStaff(v, item)"
                            :value="item.companyStaffId"
                            v-for="(item, index2) in staffList"
                            :key="index2"
                          >
                            <template>
                              <div class="select">
                                <div class="label">
                                  {{ item.name }}
                                </div>
                                <div class="tags">
                                  <div class="tag">
                                    {{ item.positionName }}
                                  </div>
                                </div>
                                <i
                                  class="el-icon-check"
                                  style="
                                    color: #409eff;
                                    font-size: 20px;
                                    font-weight: bold;
                                    margin-left: auto;
                                  "
                                  v-if="
                                    v.companyStaffId.includes(
                                      item.companyStaffId
                                    )
                                  "
                                ></i>
                              </div>
                            </template>
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item label="职位：">
                        <span>{{ v.positionName }}</span>
                      </el-form-item>
                      <div class="btn">
                        <el-button type="text" @click="del('list2', i)"
                          >删除</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="add"
                  @click="addBefore('list2')"
                  v-if="list2 && list2.length < 9"
                >
                  <i class="el-icon-plus"></i>
                </div>
              </div>
            </div>
            <div
              class="confirm-box"
              v-if="permissions['chain:companyApproval:save']"
            >
              <el-button
                type="success"
                size="medium"
                style="padding: 8px 18px; font-size: 14px"
                @click="confirm"
                >提交</el-button
              >
            </div>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="修改记录" name="2">
          <editLog
            @viewFlow="viewFlow"
            :approveType="form.approveType"
            ref="editLog"
          >
            <avue-form
              :option="option"
              v-model="form"
              @submit="submit"
              ref="form"
            ></avue-form>
          </editLog>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
    <poPerson
      v-if="personVisible"
      :personNames="personNames"
      :visible.sync="personVisible"
    ></poPerson>
    <flowView
      v-if="flowVisible"
      :flowList="flowList"
      :visible.sync="flowVisible"
    ></flowView>
  </div>
</template>

<script>
import {
  getPage,
  addObj,
  getFlowStaff,
  getFlowSetting,
  updateFlowSetting,
} from "@/api/chain/companyApproval";
import { mapGetters } from "vuex";
// import draggable from 'vuedraggable';
import poPerson from "@/views/chain/waybillProcessSettings/poPerson";
import editLog from "@/views/chain/waybillProcessSettings/editLog";
import flowView from "@/views/chain/waybillProcessSettings/flowView";
export default {
  name: "companyApproval2",
  components: {
    poPerson,
    editLog,
    flowView,
  },
  data() {
    return {
      list: [],
      list2: [],
      staffList: [],
      isOpen: false,
      nums: ["一", "二", "三", "四", "五", "六", "七", "八", "九"],
      personNames: "",
      personVisible: false,
      activeName: "1",
      flowList: [],
      flowVisible: false,
      approveType: "2,3",
      option: {
        submitBtn: true,
        emptyBtn: true,
        labelWidth: 80,
        menuSpan: 6,
        submitBtnIcon: "",
        submitText: "搜索",
        submitIcon: "el-icon-search",
        column: [
          {
            label: "审核流程",
            prop: "approveType",
            type: "select",
            span: 6,
            clearable: false,
            dicData: [
              {
                label: "全部",
                value: "91,92",
              },
              {
                label: "结算审批流程",
                value: "91",
              },
              {
                label: "支付单审批流程",
                value: "92",
              },
            ],
          },
        ],
      },
      form: {
        approveType: "91,92",
      },
    };
  },
  created() {
    //获取流程列表
    this.getPage();
    //获取人员选择列表
    this.getFlowStaff();
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    getPage() {
      getPage().then((res) => {
        this.list = res.data.data.settleFlow || [];
        this.list2 = res.data.data.paymentFlow || [];
        if (this.list.length > 0) {
          this.isOpen = this.list[0].isValid == 1 ? true : false;
        }
        if (this.list2.length > 0) {
          this.isOpen = this.list2[0].isValid == 1 ? true : false;
        }
      });
    },
    getFlowStaff() {
      getFlowStaff().then((res) => {
        this.staffList = res.data.data;
      });
    },
    changeStaff(v, e) {
      console.log(v);
      console.log(e);
      Object.assign(v, e);
    },
    addBefore(list) {
      if(list=="list"){
        this[list].push({ companyStaffIdList:[] });
      }else{
        this[list].push({ companyStaffId: "", positionName: "" });
      }
    },
    del(list, i) {
      this[list].splice(i, 1);
    },
    changeSwitch(e) {
      // if(e==false){
      //   this.$confirm('是否关闭审核流程？', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(() => {
      //     this.updateFlowSetting({isValid:0})
      //   }).catch(() => {
      //     this.isOpen = true
      //   });
      // }else{
      //   //开启
      //   this.$confirm('是否开启审核流程？', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(() => {
      //     this.updateFlowSetting({isValid:1})
      //   }).catch(() => {
      //     this.isOpen = false
      //   });
      // }
    },
    confirm() {
      if (this.isOpen == 1 && this.list.length == 0 && this.list2.length == 0) {
        this.$message.error(`开启审批流程时，审批流程必需添加审批职位!`);
        return false;
      }
      let listIsEmpty = true;
      let list2IsEmpty = true;
      if (this.list && this.list.length > 0) {
        listIsEmpty = this.list.every((item, index) => {
          if (item.companyStaffId == "") {
            this.$message.error(
              `请选择配置结算审批流程第${index + 1}步的审批人`
            );
          }
          return item.companyStaffId != "";
        });
      }
      if (!listIsEmpty) return false;
      if (this.list2 && this.list2.length > 0) {
        list2IsEmpty = this.list2.every((item, index) => {
          if (item.companyStaffId == "") {
            this.$message.error(
              `请选择配置支付单审批流程第${index + 1}步的审批人`
            );
          }
          return item.companyStaffId != "";
        });
      }
      let param = {
        settleFlow: this.list.map((item, index) => {
          return {
            positionName: this.staffList.filter(v=>item.companyStaffIdList.includes(v.companyStaffId)).map(v=>v.positionName).join(','),
            companyStaffId: item.companyStaffIdList.join(','),
            companyPositionId: this.staffList.filter(v=>item.companyStaffIdList.includes(v.companyStaffId)).map(v=>v.companyPositionId).join(','),
            companyAuthId: item.companyAuthId,
            type: 1,
            step: index + 1,
            isValid: this.isOpen ? 1 : 0,
          };
        }),
        paymentFlow: this.list2.map((item, index) => {
          return {
            positionName: item.positionName,
            companyStaffId: item.companyStaffId,
            companyPositionId: item.companyPositionId,
            companyAuthId: item.companyAuthId,
            type: 2,
            step: index + 1,
            isValid: this.isOpen ? 1 : 0,
          };
        }),
      };
      console.log(param);
      if (list2IsEmpty && listIsEmpty) {
        addObj(param).then((res) => {
          console.log(res);
          this.$message.success("更新成功");
          this.getPage();
          //更新记录
          this.$refs.editLog.refreshChange();
        });
      }
    },
    viewFlow(row, done) {
      this.flowList = row;
      this.flowVisible = true;
      done();
    },
    submit(form, done) {
      console.log(this.$refs.editLog);
      let obj = Object.assign({}, form);
      if (obj.approveType == "") {
        obj.approveType = "91,92";
      }
      this.$refs.editLog.searchChange(obj, done);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep.el-select {
  width: 100%;
  padding-right: 16px;
}
.select {
  padding: 14px 12px;
  display: flex;

  .label {
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; //溢出不换行
    width: 50px;
    font-weight: 500;
    font-size: 14px;
    color: #323233;
    margin-right: 8px;
  }
  .tags {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    max-width: 216px;
    .tag {
      background: rgba(64, 158, 255, 0.1);
      border-radius: 3px;
      font-weight: 400;
      font-size: 12px;
      color: #409eff;
      padding: 2px 6px;
      margin-bottom: 5px;
      line-height: 12px;
    }
  }
}
.companyApproval {
  height: 100%;
  .steps {
    display: flex;
    align-items: flex-start;
    border-bottom: 1px dotted #bbb;
    margin-bottom: 20px;
    .steps-label {
      min-width: 140px;
      font-size: 14px;
      color: #606266;
    }
  }
  .step_box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    /deep/ .step {
      display: flex;
      align-items: center;
      margin-bottom: 14px;
      margin-right: 1%;
      min-width: 24%;
      .title {
        background-color: #1890ff;
        color: #fff;
        text-align: center;
        height: 34px;
        line-height: 34px;
      }
      .list {
        width: 100%;
        min-height: 200px;
        border: 1px solid #ccc;
        border-radius: 4px;
        // height: 180px;
        position: relative;
        .content {
          padding-right: 10px;
          padding-top: 20px;
          padding-bottom: 40px;
        }
        .btn {
          position: absolute;
          height: 34px;
          width: 100%;
          text-align: center;
          left: 0;
          bottom: 0;
        }
      }
      .el-icon-arrow-right {
        font-size: 40px;
        color: #666;
      }
      .el-form-item {
        margin-bottom: 0;
        .el-form-item__label {
          padding-right: 0px;
        }
      }
    }
    .add {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 24%;
      min-height: 200px;
      height: 176px;
      border: 2px dotted #ccc;
      margin-bottom: 14px;
      cursor: pointer;
      .el-icon-plus {
        font-size: 40px;
        color: #666;
      }
    }
  }
  .confirm-box {
    text-align: center;
    margin-top: 30px;
  }
}
</style>
