<template>
    <div class="execution">
        <basic-container>
            <avue-crud ref="crud"
                       :page.sync="page"
                       :data="tableData"
                       :permission="permissionList"
                       :table-loading="tableLoading"
                       :option="tableOption"
                       v-model="form"
                       @on-load="getPage"
                       @refresh-change="refreshChange"
                       @sort-change="sortChange"
                       @selection-change="handleSelectionChange"
                       @search-change="searchChange">
                       <template slot="menu" slot-scope="{ row }">
                        <el-button
                          type="text"
                          v-if="permissions['chain:companywaybillupdatehistory2:view']"
                          icon="el-icon-view"
                          size="small"
                          @click="view(row,1)"
                          >查看</el-button>
                        <el-button
                          type="text"
                          v-if="permissions['chain:companywaybillupdatehistory2:audit']&&row.auditAuth"
                          icon="el-icon-check"
                          size="small"
                          @click="view(row,2)"
                          >审核</el-button>
                      </template>
                      <template slot="header" slot-scope="{ size }">
                          <div style="display: inline-block;position: relative;top: -3px;margin-left: 10px;">
                            <el-button
                              type="primary"
                              icon="el-icon-download"
                              v-if="permissions['chain:companywaybillupdatehistory2:excel']"
                              size="mini"
                              :loading="tableLoading"
                              @click="exOut"
                              >导出</el-button>
                            <el-button
                                icon="el-icon-check"
                                size="mini"
                                type="primary"
                                v-if="permissions['chain:companywaybillupdatehistory2:audit']"
                                :disabled="multipleSelection.length==0"
                                :loading='tableLoading'
                                @click="batchApproval(1)"
                              >
                              批量审批通过
                            </el-button>
                            <el-button
                                icon="el-icon-check"
                                size="mini"
                                type="primary"
                                v-if="permissions['chain:companywaybillupdatehistory2:audit']"
                                :disabled="multipleSelection.length==0"
                                :loading='tableLoading'
                                @click="batchApproval(2)"
                              >
                              批量审批驳回
                            </el-button>
                          </div>
                      </template>
            </avue-crud>
        </basic-container>
         <!-- //编辑运单 -->
        <edit-waybill
          v-if="editVisible"
          v-on:searchData="refreshChange"
          :info="info"
          :title="title"
          :visible.sync="editVisible"
        ></edit-waybill>
    </div>
</template>

<script>
    import {getPage,batchAudit} from '@/api/chain/companywaybillupdatehistory'
    import {tableOption} from '@/const/crud/chain/companywaybillupdatehistory'
    import {mapGetters} from 'vuex'
    import editWaybill from "./editWaybill.vue";
    import { expotOut } from "@/util/down.js";
    export default {
        name: 'companywaybillupdatehistory2',
        components: {
          editWaybill,
        },
        data() {
            return {
                form: {},
                tableData: [],
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                    ascs: [],//升序字段
                    descs: []//降序字段
                },
                paramsSearch: {},
                tableLoading: false,
                tableOption: tableOption,
                info:{},
                editVisible:false,
                title:'查看',
                multipleSelection:[],
            }
        },
        created() {
        },
        mounted: function () {
        },
        computed: {
            ...mapGetters(['permissions']),
            permissionList() {
                return {
                    addBtn: this.permissions['chain:companywaybillupdatehistory:add'] ? true : false,
                    delBtn: this.permissions['chain:companywaybillupdatehistory:del'] ? true : false,
                    editBtn: this.permissions['chain:companywaybillupdatehistory:edit'] ? true : false,
                    viewBtn: this.permissions['chain:companywaybillupdatehistory:get'] ? true : false
                };
            }
        },
        methods: {
            searchChange(params,done) {
                params = this.filterForm(params)
                this.paramsSearch = params
                this.page.currentPage = 1
                this.getPage(this.page, params)
                done()
            },
            sortChange(val) {
                let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
                if (val.order == 'ascending') {
                    this.page.descs = []
                    this.page.ascs = prop
                } else if (val.order == 'descending') {
                    this.page.ascs = []
                    this.page.descs = prop
                } else {
                    this.page.ascs = []
                    this.page.descs = []
                }
                this.getPage(this.page)
            },
            getPage(page, params) {
                this.tableLoading = true
                if (params) {
                  if (params.hasOwnProperty("createDatetime")) {
                    params.createDatetimeStart = params.createDatetime[0];
                    params.createDatetimeEnd = params.createDatetime[1];
                    delete params.createDatetime;
                  }
                  if (params.hasOwnProperty("auditDatetime")) {
                    params.auditDatetimeStart = params.auditDatetime[0];
                    params.auditDatetimeEnd = params.auditDatetime[1];
                    delete params.auditDatetime;
                  }
                }
                getPage(Object.assign({
                    current: page.currentPage,
                    size: page.pageSize,
                    descs: this.page.descs,
                    ascs: this.page.ascs,
                }, params, this.paramsSearch)).then(response => {
                    this.tableData = response.data.data.records
                    this.page.total = response.data.data.total
                    this.page.currentPage = page.currentPage
                    this.page.pageSize = page.pageSize
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            },
            /**
             * 刷新回调
             */
            refreshChange(page) {
                this.getPage(this.page)
            },
            view(row,mode){
              this.info = JSON.parse(row.waybillUpdateJson)
              console.log(this.info);
              this.info.companyAuthId = row.companyAuthId
              this.info.projectInfoId = row.projectInfoId
              this.info.id = row.id
              this.info.auditRemark = row.auditRemark
              this.info.companyWaybillId = row.companyWaybillId
              this.info.isEntranceWaybill = row.isEntranceWaybill
              this.info.isCubicPrice = row.isCubicPrice  //价格
              this.info.settleType = row.settleType  ////结算类型 3为pvc卡
              this.info.isGoVehicleType = row.isGoVehicleType  //出口签单车型
              this.title = mode==1?'查看':'审核'
              this.editVisible = true
            },
            exOut() {
              let params = Object.assign({},this.paramsSearch)
              if (params) {
                if (params.hasOwnProperty("createDatetime")) {
                  params.createDatetimeStart = params.createDatetime[0];
                  params.createDatetimeEnd = params.createDatetime[1];
                  delete params.createDatetime;
                }
                if (params.hasOwnProperty("auditDatetime")) {
                  params.auditDatetimeStart = params.auditDatetime[0];
                  params.auditDatetimeEnd = params.auditDatetime[1];
                  delete params.auditDatetime;
                }
              }
              this.tableLoading=true
              let url = "/chain/companywaybillupdatehistory/exportExcel";
              // params.id = this.info.id
              expotOut(params, url, "异常运单管理").then(()=>{
                this.tableLoading=false
              }).catch(()=>{
                this.tableLoading=false
              });
            },
            handleSelectionChange(val) {
              this.multipleSelection = val;
            },
            batchApproval(type){//type1 批量通过  批量驳回
              if(this.multipleSelection.length<2){
                this.$message.error("单个审核请在操作区进行逐条审核")
                return false
              }
              if(type==1){
                this.$prompt("确定对已选择的异常订单进行批量审批通过？", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  inputPlaceholder: "请输入审核备注",
                  // inputPattern: /\S/,
                  inputValidator: (value) => {
                    if(value&&value.length>500){
                      return "超出限制字数500";
                    }
                  },
                  inputErrorMessage: "请输入审核备注",
                }).then(({value})=>{
                    let arr = this.multipleSelection.map((item) => {
                      return item.id ;
                    });
                    let data = {
                      ids:arr,
                      status:2,
                      auditRemark:value,
                    }
                    this.tableLoading = true
                    batchAudit(data).then(res=>{
                      this.tableLoading = false
                      this.$message.success('操作成功')
                      this.getPage(this.page)
                    }).catch(err=>{
                      this.tableLoading = false
                    })
                  })
                  .catch(function (err) {});
              }else{
                this.$prompt("确定对已选择的异常订单进行批量审批驳回？", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  inputPlaceholder: "请输入驳回备注",
                  inputPattern: /\S/,
                  inputValidator: (value) => {
                    if(value&&value.length>500){
                      return "超出限制字数500";
                    }
                  },
                  inputErrorMessage: "请输入驳回备注",
                }).then(({value})=>{
                    let arr = this.multipleSelection.map((item) => {
                      return item.id ;
                    });
                    let data = {
                      ids:arr,
                      status:3,
                      auditRemark:value,
                    }
                    this.tableLoading = true
                    batchAudit(data).then(res=>{
                      this.tableLoading = false
                      this.$message.success('操作成功')
                      this.getPage(this.page)
                    }).catch(err=>{
                      this.tableLoading = false
                    })
                  })
                  .catch(function (err) {});
              }
            },
        }
    }
</script>

<style lang="scss" scoped>
</style>
