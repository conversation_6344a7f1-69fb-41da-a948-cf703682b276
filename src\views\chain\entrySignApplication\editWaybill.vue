<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%" :title="'异常申请运单' + title" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <avue-form ref="editForm" v-model="editForm" :option="editOption" @submit="((form, done) => audit(2, done))">
          <template slot-scope="{disabled,size}" slot="flow">
            <div class="approvalInfo">
              <div class="info" style="width:400px" v-if="flowList && flowList.length > 0">
                <div class="approvalFlow">
                  <el-timeline>
                    <el-timeline-item :timestamp="item.companyPositionName" placement="top" class="myActive"
                      color="#409eff" v-for="(item, index) in flowList" :key="index">
                      <i :class="item.isShow?'el-icon-arrow-up':'el-icon-arrow-down'"
                        @click="showMore(item)"
                        v-if="item.approveStatus==1"
                        style="cursor: pointer;position: absolute;right: -20px;top: 0px;font-size: 18px;"></i>
                      <div style="display:flex;align-items: center;justify-content: space-between;margin-bottom:10px">
                        <h4>{{ item.approveUsername }}</h4><span>{{ item.approveDatetime }}</span>
                      </div>
                      <el-input type="textarea" v-if="item.approveRemark" v-model="item.approveRemark"
                        :autosize="{ minRows: 3, maxRows: 8 }" disabled></el-input>
                      <div v-if="item.isShow">{{item.positionStaffName}}</div>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </div>
              <el-empty v-else></el-empty>
            </div>
          </template>
          <template slot="menuForm">
            <el-button v-if="title == '审核'" icon="el-icon-close" @click="audit(3)">驳回</el-button>
          </template>
          <template slot="title">
            <div style="font-size: 16px;font-weight: 700;color: #409eff;">{{this.info.type==2?'回填':''}}出场签单信息</div>
          </template>
        </avue-form>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {auditUpdateHistory} from "@/api/chain/companywaybill";
import { batchAudit } from '@/api/chain/entrySignApplication'
import {getFlowNode} from "@/api/chain/companywaybillupdatehistory";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
    title: {
      type: String,
      default: "查看",
    },
  },
  data () {
    var validateRemark = (rule, value, callback) => {
      console.log(value);
      if (this.isRequired && (value === '' || !value)) {
        callback(new Error('请输入审批备注'));
      } else {
        callback();
      }
    };
    return {
      editForm: {},
      isRequired: false,
      editOption: {
        submitBtn: this.title == '审核',
        labelWidth: 150,
        submitText: "通过",
        position: "left",
        emptyBtn: false,
        cancelBtn: true,
        tabs: true,
        group: [
          {
            label: "变更原因",
            arrow: false,
            prop: "group2",
            column: [
              {
                label: "变更原因",
                prop: "auditRemark",
                // type: 'textarea',
                span: 24,
                className: "red",
                disabled: true,
                maxlength: 50,
              },
              {
                label: "审核备注",
                prop: "reviewRemark",
                display: this.title == '审核',
                // type: 'textarea',
                span: 24,
                maxlength: 100,
                rules: [
                  {
                    validator: validateRemark,
                    trigger: "blur",
                  },
                ],
              },
            ],
          },
        ],
      },
      //空车重车入场的字段
      option1: {
        label: "入场签单信息",
        arrow: false,
        prop: "group1",
        tabs: false,
        column: [
          {
            label: "签单类型变更前",
            prop: "typeText",
            disabled: true,
          },
          {
            label: "签单类型变更后",
            prop: "typeTextAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单车牌变更前",
            prop: "entranceTruckCode",
            disabled: true,
          },
          {
            label: "签单车牌变更后",
            prop: "entranceTruckCodeAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单票号变更前",
            prop: "no",
            disabled: true,
          },
          {
            label: "签单票号变更后",
            prop: "noAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单班次变更前",
            prop: "shiftType",
            disabled: true,
          },
          {
            label: "签单班次变更后",
            prop: "shiftTypeAf",
            className: "red",
            disabled: true,
          },
          {
            label: "班次日期变更前",
            prop: "shiftTime",
            disabled: true,
          },
          {
            label: "班次日期变更后",
            prop: "shiftTimeAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单员工变更前",
            prop: "staffName",
            disabled: true,
          },
          {
            label: "签单员工变更后",
            prop: "staffNameAf",
            className: "red",
            disabled: true,
          },
          {
            label: "土质变更前",
            prop: "soilType",
            disabled: true,
          },
          {
            label: "土质变更后",
            prop: "soilTypeAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单时间变更前",
            prop: "entranceDatetime",
            disabled: true,
          },
          {
            label: "签单时间变更后",
            prop: "entranceDatetimeAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单备注变更前",
            prop: "entranceRemark",
            disabled: true,
          },
          {
            label: "签单备注变更后",
            prop: "entranceRemarkAf",
            className: "red",
            disabled: true,
          },

          {
            label: "签单照片变更前",
            prop: "entrancePicture",
            type: "upload",
            span: 24,
            listType: "picture-card",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            disabled: true,
            readonly: true,
          },
          {
            label: "签单照片变更后",
            prop: "entrancePictureAf",
            disabled: true,
            type: "upload",
            span: 24,
            listType: "picture-card",
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
          },
          {
            label: "",
            prop: "title",
            row: true,
            labelWidth: 0,
          },
          {
            label: "项目名称",
            prop: "waybillProjectName",
            disabled: true,
          },
          {
            label: "出场时间",
            prop: "goDatetime",
            disabled: true,
          },
          {
            label: "出场签单人",
            prop: "goStaffName",
            disabled: true,
            row: true,
          },
          {
            label: "泥尾票照片",
            prop: "ticketImg",
            type: "upload",
            span: 24,
            listType: "picture-card",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            disabled: true,
            readonly: true,
          },
          {
            label: "拍照票据凭证",
            prop: "goPicture",
            disabled: true,
            type: "upload",
            span: 24,
            listType: "picture-card",
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
          },
          {
            label: "审核备注",
            prop: "reviewRemark",
            display: this.title == '审核',
            // type: 'textarea',
            span: 24,
            maxlength: 100,
            rules: [
              {
                validator: validateRemark,
                trigger: "blur",
              },
            ],
          },
        ],
      },
      //回填入场的字段
      option2: {
        label: "入场签单信息",
        arrow: false,
        prop: "group1",
        tabs: false,
        column: [
          {
            label: "签单类型变更前",
            prop: "typeText",
            disabled: true,
          },
          {
            label: "签单类型变更后",
            prop: "typeTextAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单车牌变更前",
            prop: "truckCode",
            disabled: true,
          },
          {
            label: "签单车牌变更后",
            prop: "truckCodeAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单票号变更前",
            prop: "no",
            disabled: true,
          },
          {
            label: "签单票号变更后",
            prop: "noAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单班次变更前",
            prop: "shiftType",
            disabled: true,
          },
          {
            label: "签单班次变更后",
            prop: "shiftTypeAf",
            className: "red",
            disabled: true,
          },
          {
            label: "班次日期变更前",
            prop: "shiftTime",
            disabled: true,
          },
          {
            label: "班次日期变更后",
            prop: "shiftTimeAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单员工变更前",
            prop: "operator",
            disabled: true,
          },
          {
            label: "签单员工变更后",
            prop: "operatorAf",
            className: "red",
            disabled: true,
          },
          {
            label: "土质变更前",
            prop: "soilType",
            disabled: true,
          },
          {
            label: "土质变更后",
            prop: "soilTypeAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单时间变更前",
            prop: "operateDate",
            disabled: true,
          },
          {
            label: "签单时间变更后",
            prop: "operateDateAf",
            className: "red",
            disabled: true,
          },
          {
            label: "签单备注变更前",
            prop: "remark",
            disabled: true,
          },
          {
            label: "签单备注变更后",
            prop: "remarkAf",
            className: "red",
            disabled: true,
          },

          {
            label: "签单照片变更前",
            prop: "pictureUrl",
            type: "upload",
            span: 24,
            listType: "picture-card",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            disabled: true,
            readonly: true,
          },
          {
            label: "签单照片变更后",
            prop: "pictureUrlAf",
            disabled: true,
            type: "upload",
            span: 24,
            listType: "picture-card",
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
          },
          {
            label: "泥尾票照片变更前",
            prop: "noUrl",
            type: "upload",
            span: 24,
            listType: "picture-card",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            disabled: true,
            readonly: true,
          },
          {
            label: "泥尾票照片变更后",
            prop: "noUrlAf",
            disabled: true,
            type: "upload",
            span: 24,
            listType: "picture-card",
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
          },
          {
            label: "",
            prop: "title",
            row: true,
            labelWidth: 0,
          },
          {
            label: "项目名称",
            prop: "waybillProjectName",
            disabled: true,
          },
          {
            label: "出场时间",
            prop: "goDatetime",
            disabled: true,
          },
          {
            label: "出场签单人",
            prop: "goStaffName",
            disabled: true,
            row: true,
          },
          {
            label: "泥尾票照片",
            prop: "ticketImg",
            type: "upload",
            span: 24,
            listType: "picture-card",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            disabled: true,
            readonly: true,
          },
          {
            label: "拍照票据凭证",
            prop: "goPicture",
            disabled: true,
            type: "upload",
            span: 24,
            listType: "picture-card",
            tip: "只能上传jpg/png文件",
            action: "/upms/file/upload?fileType=image&dir=",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
          },
          {
            label: "审核备注",
            prop: "reviewRemark",
            display: this.title == '审核',
            span: 24,
            maxlength: 100,
            rules: [
              {
                validator: validateRemark,
                trigger: "blur",
              },
            ],
          },
        ],
      },
      flowForm: {
        label: "审核流程",
        arrow: false,
        prop: "group3",
        column: [
          {
            label: "",
            prop: "flow",
            labelWidth: 0,
            span: 24,
          },
        ],
      },
      statusForm: {},
      flowList: []
    };
  },
  created () { },
  mounted: function () {
    this.editForm = this.info;
    console.log(this.editForm);
    // 判断空车重车及回填的字段
      this.editOption.group.unshift(this.info.type==2?this.option2:this.option1)
    //查看增加审核流程
    if (this.title != '审核') {
      this.editOption.group.push(this.flowForm)
    }
    //获取审批流程信息
    getFlowNode(this.info.id).then(res => {
      this.flowList = res.data.data
    })
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    audit (type, done) {
      if (type == 3) {
        this.isRequired = true
        console.log(this.$refs.editForm);
        this.$refs.editForm.validateField("reviewRemark")
        this.isRequired = false
        if (!this.editForm.reviewRemark) {
          this.$message.error("请输入审核备注")
          done && done()
          return false
        }
      }
      // type 2审核通过  3驳回
      this.$confirm(`确认${type == 2 ? '通过' : '驳回'}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        batchAudit({ list: [{id:this.editForm.id,type:this.editForm.type}], status: type, auditRemark: this.editForm.reviewRemark }).then(res => {
          loading.close()
          this.$message({
            showClose: true,
            message: "操作成功",
            type: "success",
          });
          this.$emit("update:visible", false);
          this.$emit("searchData");
        }).catch(() => {
          loading.close()
        })
      }).catch(() => { });
      done && done()
    },
    showMore (item) {
      this.$set(item, 'isShow', !item.isShow)
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}

/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .drawerHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

/deep/ .red {
  .el-form-item__content input {
    color: red !important;
  }

  .is-checked .el-radio__label {
    color: red !important;
  }

  .is-checked .el-radio__inner {
    border-color: red;

    &::after {
      background-color: red;
    }
  }
}

.el-timeline-item__timestamp {
  color: #333;
}

.el-timeline-item__content {
  color: #909399;
}

.myActive .el-timeline-item__tail {
  border-left: 2px solid #409eff;
}
</style>
