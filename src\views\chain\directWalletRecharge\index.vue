<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @sort-change="sortChange"
        @search-change="searchChange"
        @search-reset="searchReset"
      >
        <template slot="amountSearch" slot-scope="scope">
          <div style="display: flex;">
            <el-input-number style="margin-right:2px;" v-model="minAmount" :min="0" :max='999999999.99'  :precision="2" :controls="false" size="small" :step="0.01" step-strictly></el-input-number>
            至
            <el-input-number style="margin-left: 2px;" v-model="maxAmount" :min="0" :max='999999999.99' :precision="2" :controls="false" size="small" :step="0.01" step-strictly></el-input-number>
          </div>
        </template>
        <template slot="menuLeft" slot-scope="scope">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            v-if="permissions['chain:directWalletRecharge:recharge']"
            @click="toDetail(scope.row,'rechargeVisible','充值')"
            >充值
          </el-button>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-check"
            style="margin-left: 6px; margin-right: 6px"
            v-if="permissions['chain:directWalletRecharge:withdraw']"
            @click="toDetail(scope.row,'withdrawVisible','提现')"
            >提现
          </el-button>
        </template>
        <template slot="menu" slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            v-if="permissions['chain:directWalletRecharge:view']"
            @click="toDetail(scope.row,scope.row.applyType==1?'rechargeVisible':'withdrawVisible','查看')"
            >查看
          </el-button>
        </template>
      </avue-crud>
    </basic-container>

    <withdraw-dialog
      v-if="withdrawVisible"
      :info="info"
      :title="title"
      v-on:refreshChange="refreshChange"
      :visible.sync="withdrawVisible"
    ></withdraw-dialog>
    <recharge-dialog
      v-if="rechargeVisible"
      v-on:refreshChange="refreshChange"
      :info="info"
      :title="title"
      :visible.sync="rechargeVisible"
    ></recharge-dialog>
  </div>
</template>

<script>
import {
  getPage,
} from "@/api/chain/directWalletRecharge";
import { tableOption } from "@/const/crud/chain/directWalletRecharge";
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";
import withdrawDialog from "./withdrawDialog.vue";
import rechargeDialog from "./rechargeDialog.vue";

export default {
  name: "directWalletRecharge",
  components: {
    withdrawDialog,
    rechargeDialog,
  },
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: "apply_datetime", //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {
        applyStatus:"1",
      },
      tableLoading: false,
      tableOption: tableOption,
      withdrawVisible: false,
      rechargeVisible: false,
      info: {},
      title:'确认到账',
      minAmount:'',
      maxAmount:undefined,
    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:directWalletRecharge:add"]
          ? true
          : false,
        delBtn: this.permissions["chain:directWalletRecharge:del"]
          ? true
          : false,
        editBtn: this.permissions["chain:directWalletRecharge:edit"]
          ? true
          : false,
        viewBtn: this.permissions["chain:directWalletRecharge:get"]
          ? true
          : false,
      };
    },
  },
  methods: {
    searchReset(event){
      Object.keys(event).forEach(key=>{event[key]=''})
      this.minAmount = ''
      this.maxAmount = undefined
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params = {}) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
            minAmount:this.minAmount,
            maxAmount:this.maxAmount
          },
          params,
          this.paramsSearch,
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    toDetail(row,dialog,text) {
      this.title = text;
      this.info = row;
      this[dialog] = true;
    },
  },
};
</script>

<style lang="scss" scoped></style>
