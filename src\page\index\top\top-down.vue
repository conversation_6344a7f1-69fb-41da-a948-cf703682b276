<template>
  <div class="topDown">
    <el-popover
      placement="left"
      width="320"
      popper-class="myPopover"
      ref="myPopover"
      v-model="box"
      v-clickoutside="clickEventspace"
      trigger="manual"
    >
      <div class="excelPage">
        <div class="header flex flex-items-center flex-between">
          <span class="title">近期7天下载内容</span>
          <i
            class="el-icon-close"
            style="cursor: pointer"
            @click="box = false"
          ></i>
        </div>
        <div class="content" v-if="list && list.length > 0">
          <ul>
            <el-tooltip
              v-for="item in list"
              :key="item.id"
              class="item"
              :disabled="item.exportStatus != 2"
              effect="dark"
              :content="item.exportError"
              placement="left"
            >
              <li class="flex flex-items-center flex-between">
                <div class="flex flex-items-center">
                  <i
                    v-if="item.exportStatus == 0"
                    class="el-icon-loading"
                    style="color: #109968; font-size: 18px"
                  ></i>
                  <i
                    v-else
                    class="el-icon-tickets"
                    :style="{
                      fontSize: '18px',
                      color: item.exportStatus == 1 ? '#109968' : '#f56c6c',
                    }"
                  ></i>
                  <div class="name">
                    <div class="top">{{ item.exportTaskName }}.xls</div>
                    <div class="bottom">
                      {{ item.createDatetime }}
                    </div>
                  </div>
                </div>
                <el-tooltip
                  v-if="item.exportStatus == 1 && item.exportObsPath"
                  effect="dark"
                  content="下载"
                  placement="bottom"
                >
                  <i
                    class="el-icon-download"
                    @click="downLoad(item.exportObsPath)"
                  ></i>
                </el-tooltip>
              </li>
            </el-tooltip>
          </ul>
        </div>
        <el-empty v-else description="暂无数据" :image-size="80"></el-empty>
      </div>
      <i
        slot="reference"
        style="cursor: pointer"
        :class="[
          'el-icon-download',
          newDownload ? 'active bounceInUp animated faster' : '',
        ]"
        @click="open"
      ></i>
    </el-popover>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { companyuserexporttask } from "@/api/chain/common";
export default {
  directives: {
    clickoutside: {
      bind: function (el, binding, vnode) {
        function documentHandler(e) {
          if (el.contains(e.target)) {
            return false;
          }
          if (binding.expression) {
            binding.value(e);
          }
        }
        el.__vueClickOutside__ = documentHandler;
        document.addEventListener("click", documentHandler);
      },
      unbind: function (el, binding) {
        document.removeEventListener("click", el.__vueClickOutside__);
        delete el.__vueClickOutside__;
      },
    },
  },
  data() {
    return {
      box: false,
      text: "",
      list: [],
      intervalId: null,
    };
  },
  computed: {
    ...mapGetters(["newDownload"]),
  },
  watch: {
    box(val) {
      if (val) {
        this.intervalId = setInterval(() => {
          this.getData();
        }, 15000);
      } else {
        clearInterval(this.intervalId);
      }
    },
    newDownload(val) {
      if (val) {
        this.open();
      }
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    clickEventspace() {
      if (this.box) {
        this.box = false;
      }
    },
    open() {
      this.box = true;
      this.getData();
      this.$store.commit("SET_DOWN_EXCEL_SHOW", false);
    },
    getData() {
      companyuserexporttask({ current: 1, size: -1 }).then((res) => {
        this.list = res.data.data.records;
      });
    },
    downLoad(val) {
      window.open(val);
    },
  },
  beforeDestroy() {
    clearInterval(this.intervalId);
  },
};
</script>

<style lang="scss" scoped>
.active {
  color: #409eff;
  font-weight: 700;
}
.excelPage {
  .header {
    height: 20px;
    padding-bottom: 12px;
    font-size: 16px;
    position: sticky;
  }
  .content {
    max-height: 468px;
    overflow-y: auto;
    ul {
      li {
        padding-right: 30px;
        height: 54px;
        padding-left: 10px;
        .name {
          margin-left: 14px;
          .bottom {
            font-size: 12px;
            color: #575b60;
            margin-top: 4px;
          }
        }
        &:hover {
          background-color: #e3e3e4;
        }
        .el-icon-download {
          color: #409eff;
          cursor: pointer;
          font-size: 18px;
        }
      }
    }
  }
}
</style>
