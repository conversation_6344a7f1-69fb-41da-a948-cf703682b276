<template>
  <div class="checkSearch">
    <el-drawer size="700px" :title="type==1?'新增方案':'修改方案'"  custom-class="detailDialog" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <div class="checkHeader flex flex-items-center">
        <label>方案名称</label>
        <el-input v-model.trim="schemeName" placeholder="请输入 方案名称"></el-input>
      </div>
      <div class="schemeContent">
        <el-checkbox :indeterminate="false" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        <div style="margin: 15px 0;"></div>
        <el-checkbox-group v-model="checkItem" @change="handleCheckedCitiesChange">
          <el-checkbox v-for="item in searchList" :label="item.prop" :key="item.prop">{{item.label}}</el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="btns">
        <el-button type="primary" size="small" :loading="btnLoading" @click="save">保存方案</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    //type 1新增  2修改
    type: {
      type: Number,
      default: 1
    },
    //信息
    info: {
      type: Object,
      default:()=>{
        return {}
      }
    },
    list: {
      type:Array,
      default:()=>{
        return []
      }
    },
  },
  data() {
    return {
      schemeName:"",
      checkAll:false,
      searchList:[],
      checkItem:[],
      btnLoading:false,
    }
  },
  created() {
  },
  mounted() {
    this.searchList = this.list
    if(this.type==2){
      this.checkItem = this.info?.checkItem
      this.schemeName = this.info?.name
    }
  },
  methods: {
    cancelModal() {
      this.btnLoading = false
      this.$emit("update:visible", false);
    },
    handleCheckAllChange(val) {
      this.checkItem = val ? this.searchList.map(item=>item.prop) : [];
      console.log(this.checkItem);
    },
    handleCheckedCitiesChange(value) {
    console.log(value);
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.searchList.length;
    },
    done(){
      this.btnLoading = false
    },
    save(){
      if(!this.schemeName){
        this.$message.error('请输入方案名称')
        return false
      }
      if(this.checkItem.length<1){
        this.$message.error('请选择最少一个搜索条件')
        return false
      }
      let form = {
        name:this.schemeName,
        searchConfig:this.checkItem
      }
      this.$emit("save", this.type,form,this.cancelModal,this.done);
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header{
  margin-bottom: 20px;
}
.checkHeader{
padding: 0 30px;
  label{
    min-width: 86px;
    &::before{
      content: '*';
      color: #F56C6C;
      margin-right: 4px;
    }
  }
 /deep/ .el-input__inner{
    width: 280px;
  }
}
/deep/ .schemeContent{
  margin: 30px 20px 10px;
  padding: 10px;
  border: 1px solid #DCDFE6;
  .el-checkbox{
    margin-bottom: 0px;
  }
  .el-checkbox-group{
    .el-checkbox{
      width: 152px;
      overflow:hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-bottom: 6px;
      margin-right: 10px;
      &:nth-child(4n){
        margin-right: 0px;
      }
    }
  }
}
.btns{
  margin-top: 50px;
  text-align: center;
}
</style>
