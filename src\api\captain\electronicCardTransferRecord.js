import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companycardwaybillchangelog/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companycardwaybillchangelog',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companycardwaybillchangelog/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companycardwaybillchangelog/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companycardwaybillchangelog',
        method: 'put',
        data: obj
    })
}
