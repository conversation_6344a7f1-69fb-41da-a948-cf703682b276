<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="70%"
      title="查看运单"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <avue-crud
          ref="crud"
          :data="tableData"
          :page.sync="page"
          @sort-change="sortChange"
          :table-loading="tableLoading"
          :option="tableOption"
          v-model="form"
          @on-load="getPage"
        >
          <template slot="menuLeft" slot-scope="{ row }">
            <!-- <el-button
                size="small"
                icon="el-icon-download"
                type="primary"
                @click="exOut"
              >
                导出
            </el-button> -->
            <!-- <el-button
                size="small"
                icon="el-icon-refresh"
                :loading="btnLoading"
                type="primary"
                @click="recount"
              >
              重新计算月末转月初
            </el-button> -->
          </template>
          <!-- <template slot-scope="{ row,index }" slot="type">
            <span>{{filterType(row.type)}}</span>
          </template> -->
        </avue-crud>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getWaybill,selectWaybillsCardGenerated } from "@/api/chain/companysettle";
import { expotOut } from "@/util/down.js";

export default {
  props: {
    isHistory: {
      type: Boolean,
      default: false,
    },
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    cardNo: {
      type: String,
      default: "",
    },
    giveCardNo: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: "purchase_date", //升序字段
        descs: [], //降序字段
      },
      form: {},
      tableData: [],
      btnLoading: false,
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 12,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: true,
        delBtn: false,
        menu: false,
        searchMenuSpan: 6,
        index: false,
        refreshBtn: false,
        defaultSort: {
          prop: "purchaseDate",
          order: "ascending",
        },
        column: [
          {
            label: "项目名称",
            prop: "projectName",
          },
          {
            label: "运单号",
            prop: "no",
          },
          {
            label: "车牌",
            prop: "truckCode",
          },
          {
            label: "运输方式",
            prop: "tpMode",
          },
          {
            label: "土质",
            prop: "goSoilType",
          },
          {
            label: "计价类型",
            prop: "weightType",
          },
          {
            label: "重量",
            prop: "weightTons",
          },
          {
            label: "签单时间",
            prop: "goDatetime",
          },
          {
            label: "签单员",
            prop: "goStaffName",
          },
          {
            label: "司机",
            prop: "driverName",
          },
          {
            label: "车队长",
            prop: "captainName",
          },
        ],
      },
    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    filterType(type) {
      let text = "";
      switch (type) {
        case "1":
          text = "购票";
          break;
        case "2":
          text = "退票";
          break;
        case "3":
          text = "月末转月初";
          break;
      }
      return text;
    },
    cancelModal() {
      this.$emit("update:visible", false);
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params = {}) {
      this.tableLoading = true;
      if (this.isHistory) {
        selectWaybillsCardGenerated(
          Object.assign(
            {
              current: page.currentPage,
              size: page.pageSize,
              cardNo: this.cardNo,
              giveCardNo:this.giveCardNo
            },
            params,
            this.paramsSearch
          )
        )
          .then((response) => {
            this.tableData = response.data.data;
            this.tableLoading = false;
          })
          .catch(() => {
            this.tableLoading = false;
          });
      } else {
        getWaybill(
          Object.assign(
            {
              current: page.currentPage,
              size: page.pageSize,
              cardNo: this.cardNo,
            },
            params,
            this.paramsSearch
          )
        )
          .then((response) => {
            this.tableData = response.data.data;
            this.tableLoading = false;
          })
          .catch(() => {
            this.tableLoading = false;
          });
      }
    },
    exOut() {
      let params = Object.assign({}, this.paramsSearch);
      let url = "/chain/deliveryItem/excel";
      params.garbageId = this.info.garbageId;
      params.yearMonth = this.info.purchaseMonth;
      expotOut(params, url, "剩余总数明细");
    },
    recount() {
      this.btnLoading = true;
      recount({
        garbageId: this.info.garbageId,
        yearMonth: this.info.purchaseMonth,
        soilType: this.info.soilType,
      })
        .then((res) => {
          this.btnLoading = false;
          this.$message.success("重新计算成功");
        })
        .catch((err) => {
          this.btnLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .drawerHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  li {
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-weight: 700;
    margin-right: 30px;
  }
}

.demo-block-control {
  border-top: 1px solid #eaeefb;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;
  background-color: #fff;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  text-align: center;
  margin-top: -1px;
  color: #d3dce6;
  cursor: pointer;
  position: relative;

  &:hover {
    color: #409eff;
    background-color: #f9fafc;
  }
}
</style>
