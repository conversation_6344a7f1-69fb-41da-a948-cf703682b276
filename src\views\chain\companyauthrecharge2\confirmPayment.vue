<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="80%"
      title="付款"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <el-tabs v-model="activeName">
          <el-tab-pane label="付运费" name="1">
            <avue-crud
              ref="crud"
              class="confirmCrud"
              :data="tableData"
              :table-loading="tableLoading"
              :option="tableOption"
              v-model="form"
              @on-load="getPage"
              @selection-change="handleSelectionChange"
            >
              <template slot="header" slot-scope="{ row }">
                <div
                  style="
                    display: inline-block;
                    position: relative;
                    top: -3px;
                    margin-left: 10px;
                  "
                >
                  <el-button
                    size="mini"
                    icon="el-icon-check"
                    type="primary"
                    :disabled="
                      multipleSelection && multipleSelection.length == 0
                    "
                    v-if="!isView"
                    :loading="tableLoading"
                    @click="paymentBefore(false)"
                  >
                    批量付款
                  </el-button>
                  <el-button
                    size="mini"
                    icon="el-icon-download"
                    type="primary"
                    :disabled="tableData && tableData.length < 1"
                    :loading="btnLoading"
                    @click="exOut"
                  >
                    导出
                  </el-button>
                </div>
              </template>
              <template slot="menu" slot-scope="{ row }">
                <!-- <el-button
                    size="small"
                    icon="el-icon-document"
                    type="text"
                    plain
                    v-if="row.sttText != '未付款'"
                    @click="voucher(row)"
                  >
                    凭证
                </el-button>
                <el-button
                    size="small"
                    icon="el-icon-check"
                    type="text"
                    plain
                    :disabled="row.payeeName=='未设置承运人'"
                    v-if="row.sttText != '交易成功' && row.sttText != '交易异常'"
                    @click="paymentBefore(row)"
                  >
                    {{isView?'查看':'付款'}}
                </el-button>
                <el-button
                    size="small"
                    icon="el-icon-view"
                    type="text"
                    plain
                    :disabled="row.sttText == '未付款'"
                    @click="payDetail(row)"
                  >
                  付款明细
                </el-button> -->
                <el-button
                  size="small"
                  icon="el-icon-document"
                  type="text"
                  plain
                  @click="payDetail(row)"
                >
                  付款
                </el-button>
              </template>
            </avue-crud>
          </el-tab-pane>
          <el-tab-pane label="付税费" name="2">
            <payTaxesAndFees
              :info="info"
              @payLog="logVisible = true"
              @submit="payTaxesSubmit"
              @cancelModal="cancelModal"
              :isView="isView"
            />
          </el-tab-pane>
        </el-tabs>
      </basic-container>
    </el-drawer>
    <avue-sign ref="sign"></avue-sign>
    <Payment
      v-if="paymentVisible"
      @refreshChange="getPage"
      :isView="isView"
      :id="id"
      :payeeId="payeeId"
      :planId="this.info.planId"
      :visible.sync="paymentVisible"
    ></Payment>
    <paymentDetail
      v-if="payDetailVisible"
      :id="id"
      :payeeId="payeeId"
      :planId="this.info.planId"
      :visible.sync="payDetailVisible"
      @refreshChange="getPage"
    ></paymentDetail>
    <!-- 批量打印凭证 -->
    <voucher
      v-if="detailVisible"
      @refreshChange="getPage"
      :detailList="detailList"
      :paymentIdList="paymentIdList"
      :visible.sync="detailVisible"
    ></voucher>
    <!-- 未开通快钱司机列表 -->
    <no-real-name-driver
      v-if="driverVisible"
      :tableData="driverList"
      :visible.sync="driverVisible"
    ></no-real-name-driver>
    <!-- 五证不齐全列表 -->
    <checkPaymentExistDocIncomplete
      :submitBtnText="isView ? '查看' : '继续付款'"
      :cancelBtnText="isView ? '取消' : '停止付款'"
      @continuePayment="continuePayment"
      :tip="tip"
      @exOut="exOutExc"
      v-if="checkVisible"
      :tableData="checkList"
      :visible.sync="checkVisible"
    ></checkPaymentExistDocIncomplete>
    <!-- 确认密码和验证码 -->
    <confirmPass
      @submit="passSubmit"
      v-if="passVisible"
      :info="passObj"
      :visible.sync="passVisible"
    ></confirmPass>
    <!-- 税费支付记录 -->
    <payTaxesLog
      v-if="logVisible"
      :info="info"
      :visible.sync="logVisible"
    ></payTaxesLog>
    <!-- 充值 -->
    <addRecharge
      v-if="addVisible"
      :detailForm="addForm"
      :option="addOption"
      :showCancel="true"
      ref="addForm"
      size="634px"
      :visible.sync="addVisible"
      @submit="rechargeSubmit"
      :title="title"
    ></addRecharge>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getPage2 } from "@/api/chain/garbagecustomercount";
import { expotOut } from "@/util/down.js";
import { payment } from "@/api/chain/companypayment";
import {
  getPayeeGroupByPaymentId,
  batchTrans4047ByPayment,
  checkPayment,
  getEvidenceWhere,
  getCompanyPayAuthority,
  getDocCompleteRateByCompanyPaymentIdList,
  payTaxFee,
  paymentCheck,
  getProjectBankInfoByPaymentId,
} from "@/api/chain/companyauthrecharge";
import Payment from "./components/payment.vue";
import paymentDetail from "./components/paymentDetail.vue";
import voucher from "./voucher.vue";
import noRealNameDriver from "./components/noRealNameDriver.vue";
import checkPaymentExistDocIncomplete from "./components/checkPaymentExistDocIncomplete.vue";
import confirmPass from "./components/confirmPass.vue";
import payTaxesAndFees from "./payTaxesAndFees.vue";
import payTaxesLog from "./payTaxesLog.vue";
import addRecharge from "@/components/formDetail/index.vue";
import { saveForWallet } from "@/api/chain/companynsrsbhwallet";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    //是否查看
    isView: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  components: {
    Payment,
    paymentDetail,
    voucher,
    noRealNameDriver,
    checkPaymentExistDocIncomplete,
    confirmPass,
    payTaxesAndFees,
    payTaxesLog,
    addRecharge,
  },
  data() {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      form: {},
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        // maxHeight:[800],
        // height:'auto',
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: false,
        refreshBtn: false,
        columnBtn: false,
        menuWidth: 150,
        showSummary: true,
        sumColumnList: [
          {
            label: "合计：",
            name: "amount",
            type: "sum",
          },
          {
            name: "bankPaySuccessAmount",
            type: "sum",
          },
          {
            name: "bankPayProcessingAmount",
            type: "sum",
          },
          {
            name: "bankNoPayAmount",
            type: "sum",
          },
          {
            name: "remainingTranAmount",
            type: "sum",
          },
        ],
        selection: true,
        selectable: (row) => {
          return row.payeeName != "未设置承运人";
        },
        column: [
          {
            label: "交易类型",
            prop: "type",
            formatter: (val) => {
              return "支付";
            },
            minWidth: 70,
          },
          {
            label: "收款人名称",
            prop: "payeeName",
            minWidth: 90,
          },
          {
            label: "手机号码",
            prop: "payeeMobile",
            minWidth: 96,
          },
          {
            label: "收款卡号",
            prop: "bindingBankNo",
            minWidth: 150,
          },
          {
            label: "收款银行",
            prop: "bindingBankName",
            // sortable: true,
            minWidth: 120,
          },
          {
            label: "收款金额(合计金额)",
            prop: "amount",
            minWidth: 130,
          },
          {
            label: "已付金额",
            prop: "bankPaySuccessAmount",
            minWidth: 70,
          },
          {
            label: "支付中金额",
            prop: "bankPayProcessingAmount",
            minWidth: 84,
          },
          // {
          //   label: "未付金额",
          //   prop: "bankNoPayAmount",
          //   minWidth: 70,
          // },
          {
            label: "剩余未支付金额",
            prop: "remainingTranAmount",
            minWidth: 130,
          },
          {
            label: "最新申请时间",
            prop: "applyDatetime",
            type: "datetime",
            minWidth: 140,
            
          },
          {
            label: "最新支付时间",
            prop: "payTimes",
            type: "datetime",
            minWidth: 140,
          },
          {
            label: "状态",
            prop: "sttText",
            minWidth: 70,
          },
          {
            label: "备注",
            prop: "exMsg",
            minWidth: 140,
          },
        ],
      },
      multipleSelection: [],
      detailVisible: false,
      img: "",
      currentForm: {},
      paymentVisible: false,
      payDetailVisible: false,
      id: "",
      payeeId: "",
      detailList: [],
      paymentIdList: [],
      driverList: [], //检查司机未开通快钱列表
      driverVisible: false, //检查司机未开通快钱
      checkVisible: false, //检查五证齐全弹窗
      checkList: [], //检查五证齐全列表
      paymentType: 1, //1单个付款，2批量付款
      passObj: {
        isPayPwd: false,
        isPayPhoneVerification: false,
        payPhone: "",
      },
      passVisible: false,
      btnLoading: false,
      activeName: "1",
      logVisible: false,
      title: "充值",
      addVisible: false, //充值
      addForm: {},
      addOption: {
        labelWidth: 100,
        emptyBtn: false,
        column: [
          {
            label: "充值金额(¥)",
            prop: "money",
            span: 24,
            type: "number",
            minRows: 0.01,
            maxRows: 999999999.99,
            precision: 2,
            controls: false,
            rules: [
              {
                required: true,
                message: "请输入 充值金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "充值企业银行",
            prop: "platformBranchNsrmc",
            span: 24,
            disabled: true,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 3,
            maxRows: 5,
            span: 24,
          },
          {
            label: "上传凭证",
            prop: "pic",
            type: "upload",
            listType: "picture-card",
            action: "/upms/file/upload?fileType=image&dir=batchRecharge/",
            dataType: "string",
            propsHttp: {
              url: "link",
            },
            loadText: "附件上传中，请稍等",
            span: 24,
            tip: "只能上传jpg/png文件，且不超过500kb",
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
        ],
      },
    };
  },
  created() {},
  mounted() {
    console.log(this.info.id);
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
      if (!this.isView) {
        this.$emit("refreshChange");
      }
    },
    getPage() {
      this.tableLoading = true;
      getPayeeGroupByPaymentId({
        paymentId: this.info.id,
        planId: this.info.planId,
      })
        .then((response) => {
          this.tableData = response.data.data;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    exOut() {
      let param = {
        paymentId: this.info.id,
        planId: this.info.planId,
      };
      expotOut(param, "/chain/companypayment/downloadPaymentPayee", "付款")
        .then(() => {
          this.btnLoading = false;
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
    //付款
    paymentBefore(row) {
      console.log(row);
      this.paymentType = row ? 1 : 2; //1单个付款，2批量付款
      let param = {};
      param.paymentId = this.info.id;
      param.planId = this.info.planId;
      param.payeeIdList = this.multipleSelection
        .map((item) => item.payeeId)
        .join(",");
      if (row) {
        this.id = row.companyPaymentId;
        this.payeeId = row.payeeId;
        param.paymentId = row.companyPaymentId;
        param.payeeIdList = row.payeeId;
      }
      //批量充值需要先判断余额
      if (this.paymentType == 2) {
        //判断余额是否足够付钱
        this.tableLoading = true;
        let amount = this.multipleSelection
          .map((row) => row.bankNoPayAmount)
          .reduce((acc, cur) => parseFloat(cur) + acc, 0)
          .toFixed(2);
        paymentCheck({
          paymentId: this.info.id,
          amount: amount,
        })
          .then((res) => {
            if (res.data.data == "788") {
              //已充值未到账
              this.tableLoading = false;
              this.$alert("请联系益路公司确认到账", "提示", {
                confirmButtonText: "确定",
              });
            } else if (res.data.data == "777") {
              //未充值  去充值
              getProjectBankInfoByPaymentId({ paymentId: this.info.id })
                .then((response) => {
                  this.tableLoading = false;
                  let datas = response.data.data;
                  this.title = `当前余额${
                    datas.balance || 0
                  }，不足以付款，请充值`;
                  this.addForm.platformBranchId = datas.platformBranchId;
                  this.addForm.platformBranchNsrmc = datas.platformBranchNsrmc;
                  this.addVisible = true;
                })
                .catch((err) => {
                  this.tableLoading = false;
                });
            } else {
              //够钱支付
              this.tableLoading = false;
              this.checkPayment(param);
            }
          })
          .catch((err) => {
            this.tableLoading = false;
          });
      } else {
        this.checkPayment(param);
      }
    },
    checkPayment(param) {
      //付款前检查证件齐全
      checkPayment(param).then((res) => {
        this.checkList = res.data.data.map((item) => {
          item.roadStatus = item.roadStatus ? item.roadStatus.split(",") : [];
          item.truckStatus = item.truckStatus
            ? item.truckStatus.split(",")
            : [];
          return item;
        });
        if (this.checkList && this.checkList.length > 0) {
          getDocCompleteRateByCompanyPaymentIdList(param).then((res) => {
            this.tip = res.data.data.message;
            this.checkVisible = true;
          });
        } else {
          this.continuePayment();
        }
      });
    },
    continuePayment() {
      this.checkVisible = false;
      if (this.paymentType == 1) {
        this.paymentVisible = true;
      } else {
        //检查是否需要支付密码
        this.getCompanyPayAuthority();
      }
    },
    //导出
    exOutExc() {
      let param = {
        paymentId: this.info.id,
        planId: this.info.planId,
      };
      expotOut(
        param,
        "/chain/companypayment/downloadPaymentCheckLicense",
        "证书检查"
      );
    },
    getCompanyPayAuthority() {
      getCompanyPayAuthority()
        .then((res) => {
          console.log(res);
          this.passObj = {
            isPayPwd: res.data.data.isPayPwd == 1,
            isPayPhoneVerification: res.data.data.isPayPhoneVerification == 1,
            payPhone: res.data.data.payPhone,
          };
          if (this.passObj.isPayPwd || this.passObj.isPayPhoneVerification) {
            this.passVisible = true;
          } else {
            // 不需要密码或者手机验证 批量付款
            this.batchPayment();
          }
        })
        .catch(() => {
          this.$$message.error("获取失败");
        });
    },
    passSubmit(val) {
      let obj = JSON.parse(val);
      console.log(obj);
      this.batchPayment(obj);
    },
    batchPayment(obj) {
      this.tableLoading = true;
      let param = {
        paymentId: this.info.id,
        planId: this.info.planId,
        paymentPayeeList: this.multipleSelection,
      };
      if (obj) {
        param.payPwd = obj.pass;
        param.payPhoneVerificationCode = obj.code;
      }
      console.log(param);
      batchTrans4047ByPayment(param)
        .then((res) => {
          console.log(res);
          if (res.data.code == 999) {
            this.tableLoading = false;
            this.driverList = res.data.data;
            this.driverVisible = true;
          } else {
            setTimeout(() => {
              this.tableLoading = false;
              this.$message.success("操作成功");
              this.getPage();
            }, 1500);
          }
        })
        .catch((err) => {
          this.tableLoading = false;
          this.getPage();
        });
    },

    payDetail(row) {
      this.id = row.companyPaymentId;
      this.payeeId = row.payeeId;
      this.payDetailVisible = true;
    },
    voucher(row) {
      let param = {
        paymentIdList: [row.companyPaymentId],
        payeeId: row.payeeId,
        planId: this.info.planId,
      };
      this.paymentIdList = [row.companyPaymentId];
      this.$refs.sign.clear();
      getEvidenceWhere(param).then((res) => {
        this.detailList = res.data.data;
        this.detailVisible = true;
        setTimeout(() => {
          this.detailList.forEach((item, index) => {
            this.$refs.sign.getStar("支付", item.companyName, item.companyNo);
            this.$set(item, "img", this.$refs.sign.submit(80, 50));
            this.$refs.sign.clear();
          });
        }, 100);
      });
    },
    /**
     * form 表单数据
     * function done 表单loading
     * getData 重新获取数据
     */
    payTaxesSubmit(form, done, getData) {
      form.paymentId = this.info.id;
      console.log(form);
      if (form.amount > Number(form.unPaidTaxFee)) {
        this.$message.error("支付税费必须小于或等于待支付税费");
        done();
        return false;
      }
      //判断余额是否足够付钱
      paymentCheck({
        paymentId: this.info.id,
        amount: form.amount,
      })
        .then((res) => {
          if (res.data.data == "788") {
            //已充值未到账
            done();
            this.$alert("请联系益路公司确认到账", "提示", {
              confirmButtonText: "确定",
            });
          } else if (res.data.data == "777") {
            //未充值  去充值
            getProjectBankInfoByPaymentId({ paymentId: this.info.id })
              .then((response) => {
                done();
                let datas = response.data.data;
                this.title = `当前余额${
                  datas.balance || 0
                }，不足以付款，请充值`;
                this.addForm.platformBranchId = datas.platformBranchId;
                this.addForm.platformBranchNsrmc = datas.platformBranchNsrmc;
                this.addVisible = true;
              })
              .catch((err) => {
                done();
              });
          } else {
            //够钱支付
            this.$confirm(
              `当前支付单将支付税费金额${form.amount},点击确定将从钱包余额扣除`,
              "提示",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                closeOnClickModal: false,
                type: "warning",
              }
            )
              .then(() => {
                payTaxFee(form)
                  .then((res) => {
                    this.$message.success("操作成功");
                    getData();
                    done();
                  })
                  .catch(() => {
                    done();
                  });
              })
              .catch(() => {
                done();
              });
          }
        })
        .catch((err) => {
          done();
        });
    },
    rechargeSubmit(form, done) {
      console.log(form);
      let param = Object.assign({}, form);
      saveForWallet(param)
        .then((res) => {
          this.$message.success("操作成功");
          this.addVisible = false;
          done();
        })
        .catch(() => {
          done();
        });
    },
  },
  destroyed() {
    this.$refs.sign && this.$refs.sign.clear();
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
/deep/ .avue-sign {
  display: none;
}

/deep/ .confirmCrud .avue-crud__menu {
  display: none;
}
//右侧按钮悬浮出现滚动条拖不动问题
/deep/ .el-table .el-table__fixed-right {
  height: auto !important;
  bottom: 0px !important;
  &::before {
    background-color: transparent;
  }
}
//左侧按钮悬浮出现滚动条拖不动问题
/deep/ .el-table .el-table__fixed {
  height: auto !important;
  bottom: 0px !important;
  &::before {
    background-color: transparent;
  }
}

</style>
