<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 :defaults.sync="defaults"
                 v-model="form"
                 @on-load="getPage"
                 @selection-change="handleSelectionChange"
                 @refresh-change="refreshChange"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="header"
                  slot-scope="{ row }">
          <div style="
              display: inline-block;
              position: relative;
              top: -3px;
              margin-left: 10px;
            ">
            <el-button icon="el-icon-check"
                       v-if="permissions['chain:ledgerDig:businessApproval']"
                       size="mini"
                       type="primary"
                       :disabled="multipleSelection.length == 0"
                       @click="batchApprove">
              批量审核通过
            </el-button>
            <el-button icon="el-icon-remove-outline"
                       v-if="permissions['chain:ledgerDig:businessReject']"
                       size="mini"
                       type="primary"
                       :disabled="multipleSelection.length == 0"
                       @click="batchReject">
              批量驳回
            </el-button>
            <!-- <el-button
              icon="el-icon-close"
              v-if="permissions['chain:ledgerDig:del']"
              size="mini"
              type="primary"
              :disabled="multipleSelection.length == 0"
              @click="batchDel"
            >
              批量删除
            </el-button> -->
            <el-button icon="el-icon-download"
                       v-if="permissions['chain:ledgerDig:excel']"
                       size="mini"
                       type="primary"
                       @click="exOut">
              导出
            </el-button>
            <!-- <el-button
              icon="el-icon-document-add"
              v-if="permissions['chain:ledgerDig:remark']"
              size="mini"
              type="primary"
              :disabled="multipleSelection.length == 0"
              @click="batchSetRemark"
            >
              批量设置PC后台备注
            </el-button>
            <el-button
              icon="el-icon-document-add"
              size="mini"
              type="primary"
              v-if="permissions['chain:ledgerDig:setLedgerPrice']"
              :disabled="multipleSelection.length == 0"
              @click="batchSetLedgerPrice"
            >
              批量设置台班价
            </el-button> -->
          </div>
        </template>
        <template slot="menu"
                  slot-scope="{ row }">
          <el-button type="text"
                     v-if="permissions['chain:ledgerDig:businessApproval'] && row.ledgerStatus == 2"
                     icon="el-icon-check"
                     size="small"
                     plain
                     @click="approve(row)">
            审核通过</el-button>
          <el-button type="text"
                     v-if="
              permissions['chain:ledgerDig:businessReject'] && row.ledgerStatus == 2
            "
                     icon="el-icon-remove-outline"
                     size="small"
                     plain
                     @click="reject(row)">
            驳回</el-button>
          <!-- <el-button
            type="text"
            v-if="permissions['chain:ledgerDig:del'] && row.ledgerStatus == 2"
            icon="el-icon-delete"
            size="small"
            plain
            @click="handleDel(row)"
          >
            删除</el-button
          >
          <el-button
            type="text"
            v-if="permissions['chain:ledgerDig:remark']"
            icon="el-icon-document-add"
            size="small"
            plain
            @click="setRemark(row)"
          >
            设置PC后台备注</el-button
          ><el-button
            icon="el-icon-document-add"
            size="small"
            type="text"
            plain
            v-if="permissions['chain:ledgerDig:setLedgerPrice']"
            @click="batchSetLedgerPrice(row)"
          >
            设置台班价
          </el-button>
          <el-button
            type="text"
            icon="el-icon-notebook-2"
            v-if="permissions['chain:ledgerDig:history'] && row.hasHistory == 1"
            size="small"
            plain
            @click="getHistory(row)"
          >
            历史记录</el-button
          > -->
        </template>
        <template slot="menuLeft"
                  slot-scope="scope">
          <div class="statistics">
            <label>合计工作时长：</label><span>{{ totalWorkingHours }}小时</span>
          </div>
        </template>
      </avue-crud>
      <historyRecord v-if="historyVisible"
                     :visible.sync="historyVisible"
                     :info="info"></historyRecord>
    </basic-container>
  </div>
</template>

<script>
import {
  queryExcavatorStageCrewPage as getPage,
  delObj,
  getSettingAll,
  editPcLedgerRemark,
  batchEditPrice,
  getWorkTimeStatistic,
  batchAuditByBusinessStaff
} from "@/api/chain/ledgerDig3";
import { tableOption } from "@/const/crud/chain/ledgerDig3";
import { mapGetters } from "vuex";
import { exportOut } from "@/util/down.js";
import historyRecord from "./historyRecord.vue";
export default {
  name: "ledgerDig3",
  components: { historyRecord },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: "start_datetime", //降序字段
      },
      paramsSearch: {
        ledgerStatus:"2"
      },
      tableLoading: false,
      tableOption: tableOption,
      multipleSelection: [],
      defaults: {},
      historyVisible: false,
      info: {},
      totalWorkingHours: 0
    };
  },
  created () { },
  mounted: function () {
    this.getPermiss();
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList () {
      return {
        addBtn: this.permissions["chain:ledgerDig:add"] ? true : false,
        delBtn: this.permissions["chain:ledgerDig:del"] ? true : false,
        editBtn: this.permissions["chain:ledgerDig:edit"] ? true : false,
        viewBtn: this.permissions["chain:ledgerDig:get"] ? true : false,
        excelBtn: this.permissions["chain:ledgerDig:excel"] ? true : false,
      };
    },
  },
  methods: {
    batchEditPrice (data) {
      batchEditPrice(data).then((res) => {
        this.$message.success("操作成功");
        this.getPage(this.page);
      });
    },

    batchSetLedgerPrice (row) {
      this.$prompt("设置台班价", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "number",
        // inputPattern:/^[0-9]+(\.[0-9]{1,2})?$/,
        inputPlaceholder: "请设置台班价",
        inputValidator: (value) => {
          if (!value) {
            return "台班价不能为空";
          } else if (!/^[0-9]+(\.[0-9]{1,2})?$/.test(value)) {
            return "请输入两位小数";
          }
        },
        // inputErrorMessage: "请输入台班价",
      })
        .then(({ value }) => {
          let data = {};
          if (row.id) {
            data = {
              preparePrice: value,
              ledgerIds: [row.id],
            };
          } else {
            data = {
              preparePrice: value,
              ledgerIds: this.multipleSelection.map((v) => v.id),
            };
          }

          this.batchEditPrice(data);
        })
        .catch(() => { });
    },
    getPermiss () {
      getSettingAll().then((res) => {
        console.log(this.$refs.crud);
        this.tableOption.column.forEach(item => {
          if (item.prop == 'isVisa') {
            item.search = false
          }
        })
        let arr = [
          "beginClockUrl",
          "beginClock",
          "endClockUrl",
          "endClock",
          "refuelingLitres",
          "cost",
          "isSubsidizedMeals",
          "overtimeHours",
          "isVisa"
        ];
        if (res.data.data && res.data.data.length > 0) {
          res.data.data.forEach(item => {
            if (arr.includes(item.key)) {
              this.defaults[item.key].hide = false;
              this.defaults[item.key].showColumn = true;
            }
            if (item.key == 'isVisa') {
              this.tableOption.column.forEach(item => {
                if (item.prop == 'isVisa') {
                  item.search = true
                }
              })
            }
          })
        }
      });
    },
    searchChange (params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange (val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage (page, params) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
        if (params.hasOwnProperty("inShiftTime")) {
          params.inShiftTimeStart = params.inShiftTime[0];
          params.inShiftTimeEnd = params.inShiftTime[1];
          delete params.inShiftTime;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
            isBusiness:1,//商务
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
      this.getWorkTimeStatistic(params);
    },
    getWorkTimeStatistic (params = {}) {
      getWorkTimeStatistic(Object.assign(
        params,
        {
          isBusiness:1,//商务
        },
        this.paramsSearch
      )).then((res) => {
        this.totalWorkingHours = res.data.data;
      });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj([row.id]);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) { });
    },
    batchDel () {
      let isTrue = this.multipleSelection.every((item) => {
        return item.ledgerStatus == 2;
      });
      if (!isTrue) {
        return this.$message.error(
          "请选择需要审核的台班进行操作"
        );
      }
      let _this = this;
      this.$confirm(
        `是否确认删除这${this.multipleSelection.length}条数据`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delObj(_this.multipleSelection.map((item) => item.id));
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) { });
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page);
    },
    //批量审核通过
    batchApprove () {
      let isTrue = this.multipleSelection.every((item) => {
        return item.ledgerStatus == 2;
      });
      if (!isTrue) {
        return this.$message.error(
          "请选择需要审核的台班进行操作"
        );
      }
      let _this = this;
      this.$prompt(`是否确认将这${this.multipleSelection.length}条数据审批通过`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入备注内容",
        inputPattern: /\S/,
        inputValidator: (value) => {
          if (value&&value.length > 150) {
            return "超出限制字数150";
          }
        },
        inputErrorMessage: "请输入备注内容",
      })
        .then(function ({ value }) {
          return batchAuditByBusinessStaff({ remark: value, ids: _this.multipleSelection.map((item) => item.id),status:1 });
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "审核通过成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) { });
    },
    //审核通过
    approve (row) {
      let _this = this;
      this.$prompt(`是否确认审核通过此数据`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入备注内容",
        inputPattern: /\S/,
        inputValidator: (value) => {
          if (value&&value.length > 150) {
            return "超出限制字数150";
          }
        },
        inputErrorMessage: "请输入备注内容",
      })
        .then(function ({ value }) {
          return batchAuditByBusinessStaff({ remark: value, ids: [row.id],status:1 });
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "审核通过成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) { });
    },
    //批量审核驳回
    batchReject () {
      let isTrue = this.multipleSelection.every((item) => {
        return item.ledgerStatus == 2;
      });
      if (!isTrue) {
        return this.$message.error(
          "请选择需要审核的台班进行操作"
        );
      }
      let _this = this;
      const h = this.$createElement
      this.$prompt(h('p', null, [h('i', { style: 'color: red' }, '* '), h('span', null, `是否确认将这${this.multipleSelection.length}条数据驳回`)]), "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入备注内容",
        inputPattern: /\S/,
        inputValidator: (value) => {
          if (value&&value.length > 150) {
            return "超出限制字数150";
          }
        },
        inputErrorMessage: "请输入备注内容",
      })
        .then(function ({ value }) {
          return batchAuditByBusinessStaff({ remark: value, ids: _this.multipleSelection.map((item) => item.id),status:0 });
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "驳回成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) { });
    },
    //驳回
    reject (row) {
      let _this = this;
      const h = this.$createElement
      this.$prompt(h('p', null, [h('i', { style: 'color: red' }, '* '), h('span', null, ' 是否确认驳回此数据')]), "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入备注内容",
        inputPattern: /\S/,
        inputValidator: (value) => {
          if (value&&value.length > 150) {
            return "超出限制字数150";
          }
        },
        inputErrorMessage: "请输入备注内容",
      })
        .then(function ({ value }) {
          return batchAuditByBusinessStaff({ remark: value, ids: [row.id],status:0 });
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "驳回成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) { });
    },
    getHistory (row) {
      this.info = row;
      this.historyVisible = true;
    },
    exOut () {
      let params = Object.assign({}, this.paramsSearch);
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
        if (params.hasOwnProperty("inShiftTime")) {
          params.inShiftTimeStart = params.inShiftTime[0];
          params.inShiftTimeEnd = params.inShiftTime[1];
          delete params.inShiftTime;
        }
      }
      let url = "/chain/ledgerDig/exportExcavatorStageCrewExcel";
      exportOut(params, url, "挖机台班管理", 'post');
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },
    batchSetRemark () {
      this.$prompt("PC后台备注", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入PC后台备注",
        inputValidator: (value) => {
          if (!value) {
            return "PC后台备注不能为空";
          }
        },
        inputErrorMessage: "请输入PC后台备注",
      })
        .then(({ value }) => {
          let arr = this.multipleSelection.map((item) => {
            return {
              id: item.id,
              pcLedgerRemark: value,
            };
          });
          this.editPcLedgerRemark(arr);
        })
        .catch(() => { });
    },

    setRemark (row) {
      this.$prompt("PC后台备注", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入PC后台备注",
        inputValue: row.pcLedgerRemark || "",
        inputValidator: (value) => {
          if (!value) {
            return "PC后台备注不能为空";
          }
        },
        inputErrorMessage: "请输入PC后台备注",
      })
        .then(({ value }) => {
          this.editPcLedgerRemark([
            {
              id: row.id,
              pcLedgerRemark: value,
            },
          ]);
        })
        .catch(() => { });
    },
    editPcLedgerRemark (arr) {
      editPcLedgerRemark(arr).then((res) => {
        this.$message.success("操作成功");
        this.getPage(this.page);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.statistics {
  line-height: 40px;
  color: #606266;
  span {
    color: red;
    margin-right: 20px;
  }
}
</style>
