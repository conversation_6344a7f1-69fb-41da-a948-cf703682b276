.theme-gradual {
    .avue-contail {
        background: linear-gradient(120deg, #bc00e3, #4efffb) !important;
    }
    .avue-header,
    .tags-container,
    .avue-logo {
        background-color: transparent;
    }
    .el-card {
        opacity: .9;
    }
    .top {
        .el-dropdown {
            color: #fff;
            i {
                color: #fff;
            }
        }
        .top-item {
            i {
                color: #fff;
            }
        }
    }
    .avue-tabs {
        padding: 0 20px !important;
    }
    .avue-sidebar,
    .logo,
    .el-menu-item,
    .el-submenu__title,
    .el-menu {
        background-color: transparent !important
    }
    .logo_subtitle {
        color: #ccc !important;
    }
    .logo_title,
    .avue-breadcrumb
    {
        color: #fff !important;
        i {
            color: #fff;
        }
    }
    .el-menu--horizontal>.el-menu-item.is-active {
        border-bottom: none;
    }
    .top {
        border-bottom: none;
    }
    .avue-tags {
        background-color: transparent;
        border: none;
    }
    .tag-item {
        color: #fff !important;
        border: none !important;
        background-color: rgba(255, 255, 255, .5) !important;
        &.is-active {
            color: #fff !important;
            border: 1px solid #fff !important;
            background: linear-gradient(90deg, hsla(0, 0%, 100%, .28), hsla(0, 0%, 100%, 0)) !important;
            .tag-item-icon {
                color: #fff !important;
            }
        }
    }
    .el-menu-item {
        i,
        span {
            color: #fff;
        }
        &:hover {
            span,
            i {
                color: #fff !important;
            }
        }
        &.is-active {
            &::before {
                background: #fff;
            }
            span,
            i {
                color: #fff !important;
            }
            background: linear-gradient(90deg, hsla(0, 0%, 100%, .28), hsla(0, 0%, 100%, 0)) !important;
            &:hover {
                color: #fff !important;
                background: linear-gradient(90deg, hsla(0, 0%, 100%, .28), hsla(0, 0%, 100%, 0)) !important;
            }
        }
    }
    .el-submenu__title {
        i,
        span {
            color: #fff !important;
        }
    }
    .el-submenu .el-menu-item {
        &:hover {
            span,
            i {
                color: #fff !important;
            }
        }
        &.is-active {
            background: linear-gradient(90deg, hsla(0, 0%, 100%, .28), hsla(0, 0%, 100%, 0)) !important;
            span,
            i {
                color: #fff !important;
            }
            &:hover {
                background: linear-gradient(90deg, hsla(0, 0%, 100%, .28), hsla(0, 0%, 100%, 0)) !important;
            }
        }
    }
    .top-bar__item {
        i {
            color: #fff;
        }
    }
    .el-tabs--card>.el-tabs__header .el-tabs__item {
        &.is-active {
            border: 1px solid #fff;
            color: #fff;
        }
    }
}