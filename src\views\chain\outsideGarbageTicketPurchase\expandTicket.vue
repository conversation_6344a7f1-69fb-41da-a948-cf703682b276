<template>
  <div class="expandTicket">
    <el-drawer size="900px"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <basic-container>
        <my-crud ref="expandTicket"
                 :data="tableData"
                 :page.sync="page"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @search-change="searchChange"
                 @on-load="getPage">
        </my-crud>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { getRefundTicketNo } from '@/api/chain/outsideGarbageTicketPurchase'

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    //弹窗状态
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data () {
    return {
      page: {
        pageSizes:[10, 20, 50, 100],
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      tableData: [],
      paramsSearch: {

      },
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: false,
        align: "center",
        searchShow: true,
        labelWidth: 150,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: false,
        menu: false,
        refreshBtn: false,
        selection: false,
        reserveSelection: true,
        rowKey: "id",
        header:false,
        column: [
          {  //票号前缀
            label: "泥尾票前缀",
            prop: "prefix",
          },
          {
            label: "泥尾票编号",
            prop: "noText",
          },
          {
            label: "价格",
            prop: "price",
          },
        ],
      },
      btnLoading: false,
    }
  },
  created () {
  },
  mounted () {
    console.log(11111);
  },
  computed: {
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    searchChange (params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    getPage (page, params = {}) {
      this.tableLoading = true
      getRefundTicketNo(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        purchaseId : this.info.id,
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.myCrud{
  margin-bottom: 14px;
}
</style>
