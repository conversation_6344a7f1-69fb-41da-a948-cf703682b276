<template>
  <div class="Echarts">
    <div class="subTitle" v-if="type == 1">
      运单共计出土 {{ echartData.biPhaseSquareTitle.totalSquareSum }}方，均车
      {{ echartData.biPhaseSquareTitle.avgSquare }}方
      <el-tooltip
        class="item"
        effect="dark"
        placement="top-start"
        ><i class="el-icon-question"></i>
        <template #content>
            单位为非车方吨{{
              titleData.unitNotCorrectCount
            }}单不参与统计<br />单位为吨土质不设置单位换算{{
              titleData.soilTypeNotCorrectCount
            }}单不参与统计 <br />内运，内转回填运单{{
              titleData.tpModeInnerNotCorrectCount
            }}单不参与统计
        </template>
      </el-tooltip>
      <div class="link" @click="toDetail">
        <el-link type="primary" :disabled="!id">查看详情</el-link>
      </div>
    </div>

    <div v-if="type == 2">
      <div class="subTitle">
        成本占比<el-tooltip
        class="item"
        effect="dark"
        placement="top-start"
        ><i class="el-icon-question"></i>
        <template #content>
          单位为非车方吨{{ titleData.unitNotCorrectCount }}单不参与统计 <br />
          单位为吨土质不设置单位换算{{
            titleData.soilTypeNotCorrectCount
          }}单不参与统计<br />
          未设置装车价/泥尾价/核算价/预设价{{
            titleData.priceNotCorrectCount
          }}单不参与统计<br />
          未设置台班费{{ titleData.ledgerDigPriceNotCorrectCount }}项不参与统计
        </template> </el-tooltip
      >
    </div>

      <div  class="sub-desc" v-if="!isEmpty">

      总支出:{{ this.echartData.biPhaseCostTitle.totalCostSum }}元<br />

      运单运输成本:{{ this.echartData.biPhaseCostTitle.transportCostSum }}元
      <br />

      运单泥尾成本:{{ this.echartData.biPhaseCostTitle.garbageCostSum }}元<br />

      运单机械成本:{{ this.echartData.biPhaseCostTitle.machineInCostSum }}元<br />
      机械台班成本:{{ this.echartData.biPhaseCostTitle.machineLedgerDigCostSum }}万元
    </div>
    </div>
    <div class="empty" v-if="isEmpty">
      <el-empty description="暂无数据" ></el-empty>
    </div>
    <div ref="gaugechart" style="width: 100%; flex: 1" v-else></div>
  </div>
</template>

<script>
export default {
  name: "gaugechart",
  props: {
    isEmpty: {
      type: Boolean,
    },
    titleData: {
      type: Object,
      default: {},
    },
    echartData: {
      type: Object,
      default: {},
    },
    type: {
      type: String,
    },
    id: {
      type: String,
    },
  },
  watch: {
    isEmpty(val) {
      if (!val) {
        this.$nextTick(() => {
          this.myEcharts();
          window.addEventListener("resize", this.resizeHanlder);
        });
      }
    },
    echartData: {
      handler(val) {
        if (this.chart) {
          let option = this.chart.getOption();
          option.series[0].data[0].value =
            this.type == 1
              ? this.echartData.biPhaseCompletionProgressData
                  .totalCompletionProgress
              : this.echartData.biPhaseCostProgressData.totalCostProgress;
          option.series[0].data[1].value =
            this.type == 1
              ? this.echartData.biPhaseCompletionProgressData
                  .planCompletionProgress
              : this.echartData.biPhaseCostProgressData.planCostProgress;
          this.chart.setOption(option);
          // this.chart.resize()
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  methods: {
    toDetail() {
      if (this.id) {
        this.$router.push({
          path: "/planCompleteChart",
          query: { id: this.id },
        });
      }
    },
    myEcharts() {
      // 基于准备好的dom，初始化echarts实例
      this.chart = this.$echarts.init(this.$refs.gaugechart);
      let option = {
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            let str ="";
            // for (let i = 0; i < params.length; i++) {
              str +=
                params.marker +
                params.name +
                '：<span style="float:right;font-weight:600">' +
                params.value +
                `%</span><br/>`;
            // }
            return str;
          },
        },
        legend: {
          show: true,
          left: "center",
          top: "245",
          itemHeight: 13,
          itemWidth: 13,
          itemGap: 35,
          icon: "circle",
          data: this.echartData.biPhaseTruckTpModeTitle,
          formatter: function (name) {
            let str = "";
            return arr.join("\n");
          },
        },
        series: [
          {
            type: "gauge",
            startAngle: 90,
            endAngle: -270,
            center: this.type == 1 ? ["50%", "50%"] : ["60%", "50%"],
            pointer: {
              show: false,
            },
            progress: {
              show: true,
              overlap: false,
              roundCap: true,
              clip: false,
            },
            axisLine: {
              lineStyle: {
                width: 40,
              },
            },
            splitLine: {
              show: false,
              distance: 0,
              length: 10,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
              distance: 50,
            },
            data: [
              {
                value:
                  this.type == 1
                    ? this.echartData.biPhaseCompletionProgressData
                        .totalCompletionProgress
                    : this.echartData.biPhaseCostProgressData.totalCostProgress,
                name: this.type == 1 ? "工地完成度" : "总成本占比",
                color: "#409EFF",
                title: {
                  offsetCenter:
                    this.type == 1 ? ["-55%", "115%"] : ["-40%", "115%"],
                },
                detail: {
                  valueAnimation: true,
                  offsetCenter:
                    this.type == 1 ? ["-140%", "115%"] : ["-120%", "115%"],
                },
              },
              {
                value:
                  this.type == 1
                    ? this.echartData.biPhaseCompletionProgressData
                        .planCompletionProgress
                    : this.echartData.biPhaseCostProgressData.planCostProgress,
                name: this.type == 1 ? "计划完成度" : "计划成本占比",
                color: "#6be6c1",
                title: {
                  offsetCenter:
                    this.type == 1 ? ["145%", "115%"] : ["125%", "115%"],
                },
                detail: {
                  valueAnimation: true,
                  offsetCenter:
                    this.type == 1 ? ["60%", "115%"] : ["40%", "115%"],
                },
              },
            ],
            title: {
              fontSize: 14,
            },
            detail: {
              width: 50,
              height: 14,
              fontSize: 14,
              color: "inherit",
              borderColor: "inherit",
              borderRadius: 20,
              borderWidth: 1,
              formatter: "{value}%",
            },
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      this.chart.setOption(option);
    },
    resizeHanlder() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  },
  beforeDestroy() {
    // if (!this.chart) {
    //   return;
    // }
    // window.removeEventListener("resize", this.resizeHanlder);
    // this.chart.dispose();
    // this.chart = null;
  },
};
</script>

<style lang="scss" scoped>
.Echarts {
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  .sub-desc {
    position: absolute;
    top: 60px;
    left: 15px;
    color: #000;
    font-size: 12px;
    z-index: 999;
    line-height: 20px;
  }
}
.subTitle {
  display: flex;
  align-items: center;
  padding: 0 15px;
  padding-top: 15px;
  // text-align: center;
  line-height: 30px;
  color: #464646;
    font-size: 16px;
  .link {
    margin-left: auto;
  }
}
/deep/.el-empty{
  padding: 15px 0!important;
}
</style>
