<template>
  <div class="searchInfo">
    <slot name="count"></slot>
    <div class="search flex flex-between">
      <el-radio-group v-model="form.isHProjectInfoCount" size="small" style="margin-right:10px;min-width:160px"
        @change="changeStatus">
        <el-radio-button label="">
          全部项目
        </el-radio-button>
        <el-radio-button label="1">
          活跃项目
        </el-radio-button>
      </el-radio-group>
      <div class="searchContent">
        <div v-if="source==1">
          <span style="font-size:14px;">土质类型：</span>
          <el-select v-model="form.soilType" class="item" size="small" filterable placeholder="请选择运单修改项" clearable
            style="width:160px;margin-right:10px">
            <el-option v-for="item in soilTypeList" :key="item.itemValue" :label="item.itemName" :value="item.itemValue">
            </el-option>
          </el-select>
        </div>
        <div v-if="source==2">
          <span style="font-size:14px;">结算状态：</span>
          <el-select v-model="form.agentStatusType" class="item" size="small" filterable placeholder="请选择结算状态" clearable
            style="width:160px;margin-right:10px">
            <el-option v-for="item in agentStatusTypeList" :key="item.itemValue" :label="item.itemName" :value="item.itemValue">
            </el-option>
          </el-select>
        </div>
        <div v-if="source==3">
          <span style="font-size:14px;">运输类型：</span>
          <el-select v-model="form.tpModelType" class="item" size="small" filterable placeholder="请选择运输类型" clearable
            style="width:160px;margin-right:10px">
            <el-option v-for="item in tpModelTypeList" :key="item.itemValue" :label="item.itemName" :value="item.itemValue">
            </el-option>
          </el-select>
        </div>
        <div v-if="source==4">
          <span style="font-size:14px;">泥尾点：</span>
          <el-select v-model="form.garbageId" class="item" size="small" filterable placeholder="请选择泥尾点" clearable
            style="width:160px;margin-right:10px">
            <el-option v-for="item in garbageIdList" :key="item.garbageId" :label="item.names" :value="item.garbageId">
            </el-option>
          </el-select>
        </div>
        <el-radio-group v-model="radio" size="small" @change="changeTime" class="item">
          <el-radio-button label="week">最近一周</el-radio-button>
          <el-radio-button label="month">最近一个月</el-radio-button>
          <el-radio-button label="year">最近一年</el-radio-button>
        </el-radio-group>
        <div class="item">
          <span style="font-size:14px;margin-left:8px">运单创建的时间：</span>
          <el-date-picker style="margin-right: 8px;width:300px" :editable="false" v-model="form.searchTime" type="daterange" :pickerOptions="pickerOptions"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" :clearable="false">
          </el-date-picker>
        </div>
        <div class="item">
          <el-button type="primary" size="small" @click="search" :loading="btnLoading">搜索</el-button>
          <el-button type="primary" size="small" @click="exOut" :loading="btnLoading">导出</el-button>
        </div>
      </div>
    </div>
    <slot name="center"></slot>
    <!-- //异常运单数searchTimeLabel才会传入申请时间 -->
    <projectList v-if="projectList && projectList.length > 0" :isShowProjectNum="false" @changeProject="changeProject"
      :active="form.projectInfoId" :projectList="projectList" :defaultProp="defaultProp">
    </projectList>
  </div>
</template>

<script>
import projectList from '../projectList';
import { getWaybillAnalyzeProjectList,getWaybillAnalyzeGarbageCount } from "@/api/chain/board";

export default {
  props: {
    //传进来的数据
    info: {},
    //来源 1土质类型 2结算状态  3 运输类型  4运费类型  5异常运单
    source: {
      type: [Number, String],
      default: () => {
        return "1"
      }
    },
  },
  components: {
    projectList
  },
  data () {
    return {
      projectList: [],
      form: {},
      radio: "",
      btnLoading: false,
      defaultProp: {
        label: "projectName", value: "projectInfoId", cnt: "waybillCount"
      },
      soilTypeList: [
        {
          itemName: "全部",
          itemValue: "",
        },
        {
          itemName: "普通土质",
          itemValue: "1",
        },
        {
          itemName: "资源土质",
          itemValue: "2",
        },
        {
          itemName: "其它",
          itemValue: "3",
        },
      ],
      agentStatusTypeList: [
        {
          itemName: "全部",
          itemValue: "",
        },
        {
          itemName: "已结算",
          itemValue: "1",
        },
        {
          itemName: "未结算",
          itemValue: "2",
        },
        {
          itemName: "已支付",
          itemValue: "3",
        },
      ],
      tpModelTypeList: [
        {
          itemName: "全部",
          itemValue: "",
        },
        {
          itemName: "放飞",
          itemValue: "1",
        },
        {
          itemName: "运费",
          itemValue: "2",
        },
      ],
      garbageIdList:[],
      // 时间跨度为之前一年
      pickerOptions: {
        disabledDate: () => false,
        onPick: ({ maxDate, minDate }) => {
          if (minDate && !maxDate) {
            const oneYear = 365 * 24 * 60 * 60 * 1000;
            this.pickerOptions.disabledDate = time => {
              return time.getTime() < minDate.getTime() || time.getTime() > minDate.getTime() + oneYear;
            };
          } else {
            this.pickerOptions.disabledDate = () => false;
          }
        },
      },
    }
  },
  created () {
    this.form = {
      isHProjectInfoCount: this.info.isHProjectInfoCount,
      projectInfoId: this.info.projectInfoId || '',
      searchTime: [],
      soilType:"",
      agentStatusType:"",
      tpModelType:"",
      garbageId:"",
    }
    this.radio = this.info.radio
    if (this.info.dateStartStr) {
      this.form.searchTime = [this.info.dateStartStr, this.info.dateEndStr]
    }
    this.getWaybillAnalyzeProjectList()
    if(this.source==4){
      let param = {
        dateStartStr: this.form.searchTime[0],  //开始时间
        dateEndStr: this.form.searchTime[1],  //结束时间
        isHProjectInfoCount: this.form.isHProjectInfoCount,  //是否活跃项目
      }
      getWaybillAnalyzeGarbageCount(param).then(res=>{
        this.garbageIdList = res.data.data
      })
    }
  },
  mounted () {
  },
  methods: {
    getWaybillAnalyzeProjectList () {
      let param = {
        dateStartStr: this.form.searchTime[0],  //开始时间
        dateEndStr: this.form.searchTime[1],  //结束时间
        isHProjectInfoCount: this.form.isHProjectInfoCount,  //是否活跃项目
        waybillAnalyzeType: this.source,
        soilType:this.form.soilType,
        agentStatusType:this.form.agentStatusType,
        tpModelType:this.form.tpModelType,
        garbageId:this.form.garbageId,
      }
      this.getList(param)
    },
    getList (param) {
      getWaybillAnalyzeProjectList(param).then(res => {
        let obj =
        {
          projectInfoId: "",
          projectName: this.form.isHProjectInfoCount == 1 ? "全部活跃项目" : '全部项目',
          waybillCount: 0,
        }
        this.projectList = res.data.data.projectList || []
        this.projectList.unshift(obj)
      })
    },
    changeStatus (val) {
      //更换项目列表  有些表格需要变换数据
      this.form.projectInfoId = ""
      this.getWaybillAnalyzeProjectList()
      this.searchData()
      this.$emit("changeStatus", val)
    },
    changeProject (val) {
      this.form.projectInfoId = val
      console.log(this.form.projectInfoId);
      this.searchData()
    },
    changeTime (val) {
      let dateStartStr = ""
      let dateEndStr = this.$moment().format('YYYY-MM-DD')
      switch (val) {
        case 'week':
          dateStartStr = this.$moment().subtract(7, 'days').format('YYYY-MM-DD');
          break;
        case 'month':
          dateStartStr = this.$moment().subtract(1, 'months').format('YYYY-MM-DD');
          break;
        case 'year':
          dateStartStr = this.$moment().subtract(1, 'years').format('YYYY-MM-DD');
          break;

        default:
          break;
      }
      this.form.searchTime = [dateStartStr, dateEndStr]
      console.log(val);
    },
    search () {
      this.searchData()
      this.getWaybillAnalyzeProjectList()
    },
    searchData () {
      let param = {
        dateStartStr: this.form.searchTime[0],  //开始时间
        dateEndStr: this.form.searchTime[1],  //结束时间
        isHProjectInfoCount: this.form.isHProjectInfoCount,  //是否活跃项目
        projectInfoId: this.form.projectInfoId,
        waybillAnalyzeType: this.source,
        soilType:this.form.soilType,
        agentStatusType:this.form.agentStatusType,
        tpModelType:this.form.tpModelType,
        garbageId:this.form.garbageId,
      }
      this.btnLoading = true
      this.$emit('searchChange', param, this.stopLoading)
    },
    stopLoading () {
      this.btnLoading = false
    },
    exOut () {
      let param = {
        dateStartStr: this.form.searchTime[0],  //开始时间
        dateEndStr: this.form.searchTime[1],  //结束时间
        isHProjectInfoCount: this.form.isHProjectInfoCount,  //是否活跃项目
        projectInfoId: this.form.projectInfoId,
        waybillAnalyzeType: this.source,
        soilType:this.form.soilType,
        agentStatusType:this.form.agentStatusType,
        tpModelType:this.form.tpModelType,
        garbageId:this.form.garbageId,
      }
      this.$emit("exOut", param, this.stopLoading)
    },
  },

};
</script>

<style lang="scss" scoped>
.search {
  .searchContent {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .item {
      flex-grow: 1;
      margin-bottom: 10px;
    }
  }
}
</style>
