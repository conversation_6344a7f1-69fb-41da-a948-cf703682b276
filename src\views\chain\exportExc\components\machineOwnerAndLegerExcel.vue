<template>
    <div ref="print">
      <avue-crud
        ref="crud"
        :data="tableData"
        :table-loading="tableLoading"
        :option="tableOption"
      >
      </avue-crud>
    </div>
  </template>

  <script>
  import { tableOption8 } from "@/const/crud/chain/exportExc";
  import { mapGetters } from "vuex";
  import { queryByCode } from "@/api/chain/companywaybill.js";
  import { print } from "@/util/util.js";

  export default {
    name: "machineOwnerAndLegerExcel",
    data() {
      return {
        form: {},
        paramsSearch: {},
        tableLoading: false,
        tableOption: tableOption8,
        tableData:[],
      };
    },
    created() {},
    mounted: function () {
    },
    computed: {
      ...mapGetters(["permissions"]),

    },
    methods: {
      queryByCode(form) {
        let params = Object.assign({}, form);
        if (params.goDatetime && params.goDatetime.length > 0) {
          params.goDatetimeStart = params.goDatetime[0];
          params.goDatetimeEnd = params.goDatetime[1];
        }
        delete params.goDatetime;
        console.log(form);
        params.code = "machineOwnerAndLegerExcel";
        tableOption8.column[0].children[0].label = '项目名称:'+form.$projectInfoId
        this.tableOption = JSON.parse(JSON.stringify(tableOption8));
        this.$refs.crud.doLayout();
        this.$refs.crud.refreshTable();
        this.tableLoading = true;
        queryByCode(params)
          .then((res) => {
            this.tableLoading = false;
            this.tableData = res.data.data;
          })
          .catch(() => {
            this.tableLoading = false;
          });
      },
      print(){
        console.log(this.$refs.print);
        print(this.$refs.print)
      },
    },
  };
  </script>

  <style lang="scss" scoped>
  /deep/ .closeIcon,
  .checkIcon {
    font-size: 16px;
    color: red;
  }
  /deep/ .checkIcon {
    color: #3dcc90;
  }
  /deep/ .avue-crud__pagination{
    display: none;
  }
  </style>
