<template>
    <div class="execution">
        <basic-container>
            <avue-crud ref="crud"
                       :page="page"
                       :data="tableData"
                       :permission="permissionList"
                       :table-loading="tableLoading"
                       :option="tableOption"
                       v-model="form"
                       @on-load="getPage"
                       @refresh-change="refreshChange"
                       @sort-change="sortChange"
                       @selection-change="selectionChange"
                       @search-change="searchChange">
                       <template slot="header"  slot-scope="scope">
                      <div style="display: inline-block;position: relative;top: -3px;margin-left: 10px;">
                        <el-button style="margin-left:14px" :disabled="selectList.length<1"
                        icon="el-icon-check"
                        size="mini"
                        type="primary"
                        @click="openDialog">
                        交单收款
                        </el-button>
                      </div>
                    </template>
            </avue-crud>
        </basic-container>
         <!-- 任务弹窗 -->
        <el-dialog
          width="400px"
          title="交单收款"
          center
          :visible.sync="visible"
          :before-close="cancelModal"
          :close-on-click-modal="false">
          <avue-form ref="form2"
            v-model="form2"
            :option="option2">
          </avue-form>
          <span slot="footer" class="dialog-footer">
              <el-button type="primary" :loading="btnLoading" size="small" @click="submitForm('form2')"
                >确 认</el-button
              >
              <el-button size="small" @click="cancelModal">取 消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
    import {getPage, getObj, addObj, putObj, delObj} from '@/api/captain/companywaybilldelivery'
    import {tableOption} from '@/const/crud/captain/companywaybilldelivery'
    import {mapGetters} from 'vuex'
    import {isMobileNumber} from '@/util/validate'

    export default {
        name: 'companywaybilldelivery',
        data() {
            return {
                form: {},
                tableData: [],
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                    ascs: [],//升序字段
                    descs: []//降序字段
                },
                paramsSearch: {},
                tableLoading: false,
                tableOption: tableOption,
                selectList:[],
                visible:false,
                btnLoading:false,
                form2: {},
                option2: {
                  submitBtn:false,
                  emptyBtn:false,
                  labelWidth:124,
                  column: [
                    {
                      label: "收单付款手机号",
                      prop: "acceptMobileNew",
                      hide:true,
                      showColumn:false,
                      disabled:false,
                      type: 'select',
                      dicUrl: '/chain/delivery/fleet',
                      props: {
                        label: 'mobile',
                        value: 'mobile'
                      },
                      dicFormatter: (res) => {
                        this.acceptDic = res.data
                        return res.data || []
                      },
                      typeformat(item, label, value) {
                        return `${item['mobile']}-${item['userName']}`
                      },
                      change:(val)=>{
                        if (val.value) {
                          let obj = this.acceptDic.filter(v => v.mobile == val.value)[0]
                          this.form2.acceptNameNew = obj?obj.userName:""
                        }
                      },
                      allowCreate:true,
                      filterable:true,
                      maxlength:11,
                      span:24,
                      rules: [
                        {
                          required: true,
                          message: "请输入新收单手机号",
                          trigger: "blur",
                        },
                        {
                          validator: isMobileNumber,
                          trigger: "blur",
                        },
                      ],
                    },
                    {
                      label: "收单付款姓名",
                      prop: "acceptNameNew",
                      hide:true,
                      span:24,
                      showColumn:false,
                      rules: [
                        {
                          required: true,
                          message: "请输入",
                          trigger: "blur",
                        },
                      ],
                    },
                  ]
                },
            }
        },
        created() {
        },
        mounted: function () {
        },
        computed: {
            ...mapGetters(['permissions']),
            permissionList() {
                return {
                    // addBtn: this.permissions['chain:companywaybilldelivery:add'] ? true : false,
                    // delBtn: this.permissions['chain:companywaybilldelivery:del'] ? true : false,
                    // editBtn: this.permissions['chain:companywaybilldelivery:edit'] ? true : false,
                    // viewBtn: this.permissions['chain:companywaybilldelivery:get'] ? true : false
                };
            }
        },
        methods: {
            searchChange(params,done) {
                params = this.filterForm(params)
                this.paramsSearch = params
                this.page.currentPage = 1
                this.getPage(this.page, params)
                done()
            },
            sortChange(val) {
                let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
                if (val.order == 'ascending') {
                    this.page.descs = []
                    this.page.ascs = prop
                } else if (val.order == 'descending') {
                    this.page.ascs = []
                    this.page.descs = prop
                } else {
                    this.page.ascs = []
                    this.page.descs = []
                }
                this.getPage(this.page)
            },
            getPage(page, params) {
                this.tableLoading = true
                if (params) {
                  if (params.hasOwnProperty("searchDate")) {
                    params.begDate = params.searchDate[0];
                    params.endDate = params.searchDate[1];
                    delete params.searchDate;
                  }
                }
                getPage(Object.assign({
                    current: page.currentPage,
                    size: page.pageSize,
                    descs: this.page.descs,
                    ascs: this.page.ascs,
                }, params, this.paramsSearch)).then(response => {
                    this.tableData = response.data.data.records
                    this.page.total = response.data.data.total
                    this.page.currentPage = page.currentPage
                    this.page.pageSize = page.pageSize
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            },
            /**
             * 刷新回调
             */
            refreshChange(page) {
                this.getPage(this.page)
            },
            selectionChange(e,) {
              console.log(e);
              this.selectList = e
            },
            openDialog(){
              this.btnLoading=false
              this.visible = true
              this.$nextTick(()=>{
                this.$refs.form2.resetForm()
              })
            },
            cancelModal() {
              this.$refs.form2.allDisabled = false
              this.$refs.form2.resetForm()
              this.visible = false
            },
            submitForm(formName){
              let arr = this.selectList.map((item) => {
                return item.waybillId ;
              });
              this.$refs[formName].validate((valid) => {
                if (valid) {
                  let param = {
                    waybillIds:arr.join(","),
                    acceptMobile:this.form2.acceptMobileNew,
                    acceptName:this.form2.acceptNameNew,
                  }
                  this.btnLoading = true
                  addObj(param).then((res) => {
                    this.btnLoading = false
                    this.$message.success("提交成功");
                    this.$refs.form2.allDisabled = false
                    this.$refs.form2.resetForm()
                    this.visible = false;
                    this.getPage(this.page);
                  });
                } else {
                  this.btnLoading = false
                  return false;
                }
              });
            },
        }
    }
</script>

<style lang="scss" scoped>
</style>
