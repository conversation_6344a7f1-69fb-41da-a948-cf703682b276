<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <template slot="menu" slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-view"
            size="small"
            plain
            @click="lookWaybill(scope.row, scope.index)"
          >
            查看运单</el-button
          >
        </template>

        <template slot="menuLeft" slot-scope="scope">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="small"
            @click="add"
          >
            新增</el-button
          >
        </template>
        <template slot="projectInfoId" slot-scope="scope">
          <span>{{ scope.row.projectName }}</span>
        </template>
      </avue-crud>
    </basic-container>
    <add
      v-if="visible"
      v-on:searchData="refreshChange"
      :visible.sync="visible"
    ></add>
    <detail
      v-if="detialVisible"
      :visible.sync="detialVisible"
      :companySettleNo="companySettleNo"
    ></detail>
    
  </div>
</template>

<script>
import { getPage } from "@/api/captain/companySettle";
import { tableOption } from "@/const/crud/captain/companySettle";
import { mapGetters } from "vuex";
import add from "./add.vue";
import detail from "./detail.vue";
export default {
  name: "companySettle",
  components: {
    add,
    detail
  },
  data() {
    return {
        companySettleNo:"",
        detialVisible:false,
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      visible: false,
    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        // addBtn: this.permissions['chain:companySettle:add'] ? true : false,
        // delBtn: this.permissions['chain:companySettle:del'] ? true : false,
        // editBtn: this.permissions['chain:companySettle:edit'] ? true : false,
        // viewBtn: this.permissions['chain:companySettle:get'] ? true : false
      };
    },
  },
  methods: {
    lookWaybill(item){
        this.companySettleNo= item.settleNo
        this.detialVisible = true
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
        //   params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    add() {
      this.visible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
