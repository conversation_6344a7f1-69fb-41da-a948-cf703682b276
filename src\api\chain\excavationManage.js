import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/projectinfo/page',
        method: 'get',
        params: query
    })
}
export function saveOrUpdate(data) {
    return request({
        url: '/chain/matchProjectPlan/saveOrUpdate',
        method: 'post',
        data
    })
}
export function getMatchProjectPlanDetail(data) {
    return request({
        url: '/chain/matchProjectPlan/getMatchProjectPlanDetail',
        method: 'post',
        data
    })
}



