<template>
    <div class="execution">
        <basic-container>
            <avue-crud ref="crud"
                :page.sync="page"
                :data="tableData"
                :permission="permissionList"
                :table-loading="tableLoading"
                :option="tableOption"
                v-model="form"
                @on-load="getPage"
                @refresh-change="refreshChange"
                @sort-change="sortChange"
                @search-change="searchChange">
                <!-- <template slot="projectInfoId" slot-scope="scope">
                    <span>{{scope.row.projectName}}</span>
                </template> -->
                <template slot="menuLeft" slot-scope="{ size }">
                  <el-radio-group   v-model="tabPosition"  @change="changeTab">
                    <el-radio-button  label="1">待审批</el-radio-button>
                    <el-radio-button label="2">已审批</el-radio-button>
                  </el-radio-group>
                </template>
                <template slot="menu" slot-scope="scope">
                  <el-button type="text"
                    v-if="permissions['chain:approvalPayOrder:check']&&tabPosition==1"
                    icon="el-icon-document"
                    size="small"
                    plain
                    @click="approver(scope.row,scope.index)">
                  审批</el-button>
                     <el-button type="text"
                    v-if="permissions['chain:approvalPayOrder:get']&&tabPosition==2"
                    icon="el-icon-view"
                    size="small"
                    plain
                    @click="approver(scope.row,scope.index)">
                  查看</el-button>
                </template>
                 <template slot="projectInfoId" slot-scope="scope">
                    <span>{{scope.row.projectName}}</span>
                </template>
                <template slot="agentInfoId" slot-scope="scope">
                    <span>{{scope.row.agentName}}</span>
                </template>
                <template slot="settleCnt" slot-scope="scope">
                   <div style="color:#409eff;cursor:pointer" @click="settleDetail(scope.row)">{{scope.row.settleCnt}}</div>
                </template>
                <template slot="waybillCnt" slot-scope="scope">
                   <div style="color:#409eff;cursor:pointer" @click="waybillDetail(scope.row)">{{scope.row.waybillCnt}}</div>
                </template>
            </avue-crud>
        </basic-container>
          <!-- 查看支付单 -->
        <el-drawer title="支付单审批" :visible.sync="paymentVisible" size="580px" :data="paymentVisible">
              <h3>支付单信息</h3>
              <el-table
                :data="paymentList"
                border
                :show-header="false"
                style="width: 100%">
                <el-table-column prop="payLabel"></el-table-column>
                <el-table-column prop="payValue">
                  <template slot-scope="scope">
                    <span v-if="scope.row.payLabel=='结算运单数量'" style="cursor: pointer;color: #409eff;" @click="waybillDetail(approvalform)">{{scope.row.payValue}}</span>
                    <span v-else>{{scope.row.payValue }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="payRemark"></el-table-column>
              </el-table>
              <div class="approvalInfo">
              <h3>审批流程</h3>
                <div class="info">
                  <div class="approvalFlow">
                    <el-timeline >
                      <el-timeline-item :timestamp="index===0?'发起申请':item.positionName" placement="top" :class="item.approve?'myActive':''" :color="item.approve?'#409eff':'#e4e7ed'" v-for="(item,index) in approvalform.companyPaymentFlowList" :key="index">
                        <!-- <div slot="dot" style="width:30px;height:30px;background:#ccc;">张三</div> -->
                          <div style="display:flex;align-items: center;justify-content: space-between;margin-bottom:10px"><h4>{{item.checkName}}<span v-if="index!==0" style="margin-left:6px">({{item.passName}})</span> </h4><span>{{$moment(item.updateDatetime).format('YYYY-MM-DD HH:mm:ss')}}</span></div>
                          <el-input type="textarea"  v-if="index!=0&&item.approve" v-model="item.approve" :autosize="{ minRows: 3, maxRows: 8}" disabled></el-input>
                      </el-timeline-item>
                    </el-timeline>
                  </div>
                </div>
                <el-form class="approvalform" label-width="86px" :model="approvalform"  :rules="rules" ref="approvalform" v-if="tabPosition==1">
                  <el-form-item label="审核操作:" prop="isPass">
                    <el-radio-group v-model="approvalform.isPass">
                      <el-radio label="1" value="1">同意</el-radio>
                      <el-radio label="0" value="0">驳回</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="审核意见:"  prop="approve">
                    <el-input type="textarea" v-model="approvalform.approve"></el-input>
                  </el-form-item>

                  <el-form-item label-width="20px">
                    <el-button type="primary" size="small" icon="el-icon-circle-check" @click="submitForm('approvalform')">提交</el-button>
                    <el-button  size="small" icon="el-icon-circle-close" @click="paymentVisible=false">取消</el-button>
                  </el-form-item>
                </el-form>
              </div>
        </el-drawer>
        <waybillDetail v-if="waybillVisible" :visible.sync="waybillVisible" :info="info"></waybillDetail>
    </div>
</template>

<script>
    import {getPages,paymentInfo,flowDeal,getAllSettle} from '@/api/chain/createPayOrder'
    import {tableOption,tableOption2} from '@/const/crud/chain/approvalPayOrder2'
    import {mapGetters} from 'vuex'
    import waybillDetail from "@/components/waybillDialog";
    //引入账单核算
    // import billAccount from './billAccount.vue'
    // import accountSheet from './accountSheet.vue'
    export default {
        name: 'approvalPayOrder2',
        components:{
          waybillDetail,
          // accountSheet,
        },
        data() {
            return {
                form: {},
                form2: {},
                tableData: [],
                tableData2: [],
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                    ascs: [],//升序字段
                    descs: []//降序字段
                },
                page2: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                    ascs: [],//升序字段
                    descs: []//降序字段
                },
                paramsSearch: {},
                tableLoading: false,
                tableLoading2: false,
                tableOption: tableOption,
                tabPosition:'1',
                paymentVisible:false,
                paymentList:[],
                approvalform:{},
                rules: {
                  isPass: [
                    { required: true, message: '请选择审核操作', trigger: 'change' }
                  ],
                  approve: [
                    { required: true, message: '请填写审核意见', trigger: 'blur' }
                  ]
                },
                waybillVisible:false,
                info:{},
            }
        },
        created() {
        },
        mounted: function () {
        },
        computed: {
            ...mapGetters(['permissions']),
            permissionList() {
                return {
                    addBtn: this.permissions['chain:approvalPayOrder:add'] ? true : false,
                    delBtn: this.permissions['chain:approvalPayOrder:del'] ? true : false,
                    editBtn: this.permissions['chain:approvalPayOrder:edit'] ? true : false,
                    viewBtn: this.permissions['chain:approvalPayOrder:get'] ? true : false
                };
            }
        },
        methods: {
            searchChange(params,done) {
                params = this.filterForm(params)
                this.paramsSearch = params
                this.page.currentPage = 1
                this.getPage(this.page, params)
                done()
            },
            sortChange(val) {
                let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
                if (val.order == 'ascending') {
                    this.page.descs = []
                    this.page.ascs = prop
                } else if (val.order == 'descending') {
                    this.page.ascs = []
                    this.page.descs = prop
                } else {
                    this.page.ascs = []
                    this.page.descs = []
                }
                this.getPage(this.page)
            },
            getPage(page, params) {
                this.tableLoading = true
                getPages(Object.assign({
                    current: page.currentPage,
                    size: page.pageSize,
                    descs: this.page.descs,
                    ascs: this.page.ascs,
                    searchType:this.tabPosition
                }, params, this.paramsSearch)).then(response => {
                    this.tableData = response.data.data.records
                    this.page.total = response.data.data.total
                    this.page.currentPage = page.currentPage
                    this.page.pageSize = page.pageSize
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            },
            /**
             * 刷新回调
             */
            refreshChange(page) {
                this.getPage(this.page)
            },
            changeTab(){
              this.page.currentPage = 1
              this.getPage(this.page)
              this.$refs.crud.refreshTable()
            },
            cancelRow() {

            },
            approver(row){
              this.tableLoading = true
              setTimeout(()=>{
                this.tableLoading = false
              },3000)
               this.approvalform = {
                 id:'',
                isPass:'',
                approve:'',
              }
              paymentInfo(row.id).then(res=>{
                this.tableLoading = false
                this.paymentList = [
                  {
                    payLabel:'支付单号',
                    payValue:res.data.data.paymentNo,
                    payRemark:'说明',
                  },
                  {
                    payLabel:'结算申请人',
                    payValue:res.data.data.agentName,
                    payRemark:'',
                  },
                  {
                    payLabel:'承运人',
                    payValue:res.data.data.payeeName,
                    payRemark:'',
                  },
                  {
                    payLabel:'所属项目',
                    payValue:res.data.data.projectName,
                    payRemark:'',
                  },
                  {
                    payLabel:'结算单数量',
                    payValue:res.data.data.settleCnt,
                    payRemark:'',
                  },
                  {
                    payLabel:'结算运单数量',
                    payValue:res.data.data.waybillCnt,
                    payRemark:'',
                  },
                  {
                    payLabel:'结算合计金额',
                    payValue:res.data.data.settleAmt+'元',
                    payRemark:'',
                  },

                ]
                if(res.data.data.companyPaymentItemList&&res.data.data.companyPaymentItemList.length>0){
                  res.data.data.companyPaymentItemList.forEach((item,index)=>{
                    this.paymentList.push({
                      payLabel:'冲减项费用金额'+(index+1),
                      payValue:item.amt+'元',
                      payRemark:item.item,
                    })
                  })
                }
                this.paymentList.push({
                  payLabel:'实际支付金额',
                  payValue:res.data.data.amt+'元',
                  payRemark:'',
                })
                this.approvalform.companyPaymentFlowList = res.data.data.companyPaymentFlowList
                this.approvalform.id = row.id
                this.approvalform.paymentNo = row.paymentNo
                this.paymentVisible = true
              })
            },
            submitForm(formName) {
              this.$refs[formName].validate((valid) => {
                if (valid) {
                  // alert('submit!');
                  // console.log(this.approvalform);
                  // console.log(this.accountForm.id);
                  let param = Object.assign({},this.approvalform)
                  delete param.companyPaymentFlowList
                  flowDeal(param).then(res=>{
                    this.paymentVisible = false
                    this.getPage(this.page)
                  })
                } else {
                  return false;
                }
              });
            },
             //查看结算单详情
            settleDetail(row){
              this.tableLoading = true
              setTimeout(()=>{
                this.tableLoading = false
              },3000)
              getAllSettle(row.id).then(res=>{
                let settleDetailList = res.data.data
                this.tableLoading = false
                this.$router.push({path:'/waybill/waybillDetail',query:{value:JSON.stringify(settleDetailList)}})
                // params:JSON.stringify(this.settleDetailList)
                // this.settleDialog = true
              })
            },
             //查看运单详情
            waybillDetail(row){
              this.info= {
                companyPaymentNo:row.paymentNo
              }
              this.waybillVisible = true
            },
        }
    }
</script>

<style lang="scss" scoped>
//查看支付单开始
/deep/ .el-drawer__header{
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ccc;
}
/deep/ .el-drawer__body{
  padding: 20px;
  padding-top: 0px;
  .approvalInfo{
    text-align: left;
    display: inline-block;
    margin-top: 20px;
    width: 100%;
  }
   h3{
      margin-bottom: 20px;
      font-weight: 700;
      padding-left: 8px;
        text-align: left;
        height: 14px;
        line-height: 14px;
        font-size: 14px;
        color: #111528;
        border-left: 4px solid #4688f7;
    }
  .approvalInfo{
    .approvalTitle{
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 16px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ccc;
      margin-bottom: 20px;
      .approvalSource{
        padding-left: 8px;
        text-align: left;
        height: 18px;
        line-height: 18px;
        font-size: 16px;
        color: #111528;
        // border-left: 4px solid #4688f7;
      }
    }
    .approvalFlow{
      width: 400px;
    }

    .el-timeline-item__timestamp{
      color: #333;
    }
    .el-timeline-item__content{
      color: #909399;
    }
    .myActive .el-timeline-item__tail{
      border-left: 2px solid #409eff;
    }
    .approvalform{
      padding: 20px 20px 0px;
    }
    .info{
      padding: 0px 20px;
    }
  }
}
//查看支付单结束
</style>
