<template>
  <div class="execution">
    <basic-container>
      <!-- <el-row :span="24" :gutter="10">
        <el-col :xs="24" :sm="24" :md="4">
          <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <span>部门管理</span>
            </div>
            <avue-tree
              :option="treeOption"
              :data="treeOrganData"
              @node-click="nodeClick"
            ></avue-tree>
          </el-card>
        </el-col> -->
      <!-- <el-col :xs="24" :sm="24" :md="20" class="user__main"> -->
      <my-crud ref="crud"
               :page.sync="page"
               :data="treeOrganData"
               :permission="permissionList"
               :table-loading="tableLoading"
               :option="tableOption"
               v-model="form"
               @refresh-change="refreshChange"
               @row-update="handleUpdate"
               @row-save="handleSave"
               @row-del="handleDel">
      </my-crud>
      <!-- </el-col>
      </el-row> -->
    </basic-container>
  </div>
</template>

<script>
import {
  getPage,
  getObj,
  addObj,
  putObj,
  delObj,
  tree,
} from "@/api/chain/companydept";
import { tableOption } from "@/const/crud/chain/companydept2";
import { mapGetters } from "vuex";

export default {
  name: "companydept2",
  data () {
    return {
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        defaultExpandAll: true,
        props: {
          label: "deptName",
          value: "id",
        },
      },
      treeOrganData: [],
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
    };
  },
  created () {
  },
  mounted () {
    this.getTree();
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    permissionList (key, row, index) {
      if(key==='editBtn'){
            return this.permissions["chain:companydept:edit"]&&row.deptType!=1 ? true : false
        }else if(key==='delBtn'){
          return this.permissions["chain:companydept:del"]&&row.deptType!=1 ? true : false
        }else if(key==='addBtn'){
          return this.permissions["chain:companydept:add"] ? true : false
        }
        return true
    },
    nodeClick (data) {
      this.page.currentPage = 1;
      this.getPage(this.page, { parentDeptId: data.id });
    },
    getChildren (item) {
      if (item.children) {
        if (item.children.length > 0) {
          item.children.map((itss) => {
            itss.deptName = `${itss.deptName}(${itss.count})`;
            this.getChildren(itss);
          });
        }
      }
    },
    getTree () {
      tree().then((res) => {
        console.log(res.data.data);
        // let arr = [];
        // res.data.data.map((item) => {
        //   item.deptName = `${item.deptName}`;   //`${item.deptName}(${item.count})`;
        //   item.children.map((its) => {
        //     its.deptName = `${its.deptName}`;  //`${its.deptName}(${its.count})`;
        //     this.getChildren(its)
        //   });
        // });
        this.treeOrganData = res.data.data;
        console.log(this.$refs.crud);
        // this.$refs.crud.DIC.parentDeptId = res.data.data
        // const index = this.$refs.crud.findColumnIndex('parentDeptId');
        let column = this.tableOption.column[4];
        column = Object.assign(column, {
          dicData: res.data.data,
        })
        // setTimeout(()=>{
        //   this.$refs.crud.updateDic("parentDeptId",res.data.data)
        // },1000)
      });
    },
    getPage (page, params) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          response.data.data.records.map((item) => {
            // item.leadingId = item.leadingId.split(",");
          });

          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          // this.getPage(this.page);
          this.getTree();
        })
        .catch(function (err) { });
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      console.log("rows", row);
      // console.log("row.leadingId", row.leadingId);
      // row.leadingId = row.leadingId.join(",");
      // row.staffList = row.staffList.join(',')
      //
      // row.idList = row.staffList;
      // delete row.staffList;
      // console.log('row',row)
      if (row.parentDeptId == row.id) {
        this.$message.error('上级部门不能为自己')
        loading();
        return false
      }
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          // this.getPage(this.page);
          this.getTree();
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      console.log("rows", row);

      // row.leadingId = row.leadingId.join(",");
      // row.idList = row.staffList;
      // delete row.staffList;
      // delete row.idList;
      console.log("row", row);
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          // this.getPage(this.page);
          this.getTree();
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getTree();
    },
  },
};
</script>

<style lang="scss" scoped></style>
