import request from '@/router/axios'
//流程模板列表
export function getPage(query) {
    return request({
        url: '/chain/companyapproveflow/getCompanyApproveFlow',
        method: 'get',
        params: query
    })
}
//获取职位列表
export function getPositionList(query) {
  return request({
      url: '/chain/companyposition/listForWorkFlow',
      method: 'get',
      params: query
  })
}

//流程模板保存
export function addObj(obj) {
    return request({
        url: '/chain/companyapproveflow/saveCompanyApproveFlow',
        method: 'post',
        data: obj
    })
}
//通过岗位id批量查询岗位人员
export function getCompanyStaffByPositionId(obj) {
    return request({
        url: '/chain/companystaff/getCompanyStaffByPositionId',
        method: 'post',
        data: obj
    })
}
//修改工作流历史记录
export function getFlowHistoryPage(params) {
    return request({
        url: '/chain/companyapproveflowhistory/getPage',
        method: 'get',
        params
    })
}
//根据id获取工作流详情
export function getDetailById(data) {
    return request({
        url: '/chain/companyapproveflowhistory/getDetailById',
        method: 'post',
        data
    })
}


