export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  index:true,
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  viewBtn: true,
  searchSpan:8,
  searchMenuSpan: 6,
   defaultSort: {
    prop: "createDatetime",
    order: "descending",
  },
  menu:false,
  column: [
    {
      label: "卡号",
      prop: "cardNo",
      sortable: 'custom',
      search:true,
      minWidth:180,
      overHidden:true,
    },
    {
      label: "结算时间",
      prop: "searchDate",
      type:'date',
      valueFormat: 'yyyy-MM-dd',
      searchRange:true,
      search:true,
      hide:true,
      showColumn:false,
    },
    {
      label: "结算时间",
      prop: "latestSettleDatetime",
      sortable: true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "重用时间",
      prop: "searchDate2",
      type:'date',
      valueFormat: 'yyyy-MM-dd',
      searchRange:true,
      search:true,
      hide:true,
      showColumn:false,
    },
    {
      label: "重用时间",
      prop: "resetDatetime",
      sortable: true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "操作人",
      prop: "createName",
      sortable: true,
      search:true,
      minWidth:80,
      overHidden:true,
    },
  ],
};
