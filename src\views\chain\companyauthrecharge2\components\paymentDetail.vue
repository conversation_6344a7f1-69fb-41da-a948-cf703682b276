<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="70%"
      title="付款明细"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <el-descriptions>
          <el-descriptions-item label="交易类型">支付</el-descriptions-item>
          <el-descriptions-item label="收款人姓名">{{
            form.payeeName
          }}</el-descriptions-item>
          <el-descriptions-item label="手机号码">{{
            form.payeeMobile
          }}</el-descriptions-item>
          <el-descriptions-item label="收款卡号">{{
            form.bindingBankNo
          }}</el-descriptions-item>
          <el-descriptions-item label="收款银行">{{
            form.bindingBankName
          }}</el-descriptions-item>
          <el-descriptions-item label="收款金额(合计金额)"
            >{{ form.amount }}元</el-descriptions-item
          >
          <!-- <el-descriptions-item label="未付金额"
            >{{ form.bankNoPayAmount }}元</el-descriptions-item
          > -->
          <el-descriptions-item label="未付运单数"
            >{{ form.notWaybillCnt }}单</el-descriptions-item
          >
        </el-descriptions>
        <div class="collect">已付款金额：{{form.bankPaySuccessAmount}}元，未付金额：{{form.bankNoPayAmount}}元，支付中金额：{{form.bankPayProcessingAmount}}元，剩余未支付金额：{{form.remainingTranAmount}}元</div>
        <el-button
          type="primary"
          class="elButton"
          @click="paymentFormVisible = true"
          >支付申请</el-button
        >
        <!-- <el-collapse v-if="tableData && tableData.length > 0">
          <el-collapse-item v-for="(item, index) in tableData" :key="index">
            <template slot="title">
              <div
                class="remark"
                :style="{
                  color:
                    item[0].stt == 20
                      ? '#67c23a'
                      : item[0].stt == 40
                      ? '#409EFF'
                      : '#f56c6c',
                }"
              >
                第{{ index + 1 }}次付款：已选运单数：{{
                  item.length
                }}单，已选运单金额：{{ totalCount(item) }}元,{{
                  item[0].stt == 40
                    ? "银行处理中！"
                    : item[0].stt == 20
                    ? "付款成功！"
                    : "付款失败"
                }}
                <span style="color: blueviolet">
                  运单结余金额{{ item[0].remainMoney }}</span
                >
                <el-button
                  icon="el-icon-view"
                  size="mini"
                  type="primary"
                  :loading="btnLoading"
                  style="margin-left: 20px"
                  v-if="item[0].stt == 20"
                  @click.stop="voucher(item[0])"
                >
                  凭证</el-button
                >
                <el-button
                  icon="el-icon-view"
                  size="mini"
                  :loading="btnLoading"
                  type="primary"
                  style="margin-left: 20px"
                  v-if="item[0].stt == 40"
                  @click.stop="
                    dealSearch(item[0].companyPaymentId, item[0].batchNo)
                  "
                >
                  交易查询</el-button
                >
              </div>
            </template>
            <my-crud
              ref="paymentCrud"
              class="myCrud"
              :data="item"
              @selection-change="handleSelectionChange"
              :table-loading="tableLoading"
              :option="tableOption"
            >
            </my-crud>
          </el-collapse-item>
        </el-collapse> -->
        <el-table :data="payInfo" border style="width: 100%">
          <el-table-column type="index" label="序号" width="60" align="center">
          </el-table-column>
          <el-table-column
            prop="tranAmount"
            label="付款金额（元）"
            align="center"
          >
          </el-table-column>
          <el-table-column prop="waybillCnt" label="运单数" align="center">
          </el-table-column>
          <el-table-column
            prop="remainMoney"
            label="运单结余金额（元）"
            align="center"
          >
          </el-table-column>
          <el-table-column prop="payTime" label="付款时间" align="center">
          </el-table-column>
          <el-table-column
            prop="outBankReceipt"
            label="银行流水"
            align="center"
          >
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                size="small"
                icon="el-icon-document"
                type="text"
                plain
                disabled
                v-if="scope.row.stt != 20"
              >
                支付中
              </el-button>
              <!-- @click="voucher(scope.row)" -->
              <el-button
                size="mini"
                type="primary"
                @click="voucher(scope.row)"
                v-if="scope.row.stt == 20"
              >
                凭证
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="checkWaybill(scope.row)"
              >
                查看运单
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </basic-container>
    </el-drawer>

    <avue-sign ref="sign"></avue-sign>
    <!-- 批量打印凭证 -->
    <voucher
      v-if="detailVisible"
      :detailList="detailList"
      :sourceId="sourceId"
      :visible.sync="detailVisible"
    ></voucher>
    <!--付款信息  -->
    <el-dialog title="付款信息" :visible.sync="paymentFormVisible">
      <el-form :model="form" :inline="true" label-width="100px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="支付单号">
              <el-input
                disabled
                v-model="form.paymentNo"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="企业名称">
              <el-input
                disabled
                v-model="form.companyName"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12"
            ><el-form-item label="项目名称">
              <el-input
                disabled
                v-model="form.projectInfoName"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款人姓名">
              <el-input
                v-model="form.payeeName"
                autocomplete="off"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="收款账号">
              <el-input
                v-model="form.bindingBankNo"
                autocomplete="off"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款人银行">
              <el-input
                v-model="form.bindingBankName"
                autocomplete="off"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div style="font-size: 16px; margin-bottom: 12px">
          应付金额<span
            style="font-weight: bold; font-size: 18px; color: red"
            >{{ form.amount }}</span
          >元，已付款<span
            style="font-weight: bold; font-size: 18px; color: red"
            >{{ form.bankPaySuccessAmount }}</span
          >（上期结余<span
            style="font-weight: bold; font-size: 18px; color: red"
          >
            {{ form.remainMoney }} </span
          >元），剩余未付金额<span
            style="font-weight: bold; font-size: 18px; color: red"
            >{{ form.remainingTranAmount }}</span
          >元！
        </div>
        <el-form-item label="付款金额">
          <el-input
            v-model="form.tranAmount"
            autocomplete="off"
            type="number"
            @input="tranAmountInput"
            class="inputFund"
          >
            <template slot="append">
              <el-button class="entireprimary" type="primary" @click="entire"
                >余额全款</el-button
              ></template
            ></el-input
          >
        </el-form-item>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="付款金额(大写)" label-width="120px">
              <el-input
                v-model="bigAmount"
                autocomplete="off"
                placeholder="输入付款金额自动转换"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="paymentFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveBank">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 运单详细 -->
    <el-dialog title="运单详细" :visible.sync="dialogTableVisible">
      <my-crud
        ref="paymentCrud"
        class="myCrud"
        :data="waybillDetail"
        @selection-change="handleSelectionChange"
        :table-loading="tableLoading"
        :option="tableOption"
      >
      </my-crud>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getPaymentPayeeInfo,
  getTransStatus4047ByPaymentId,
  getEvidenceWhere,
  getBankVoucherPayInfo,
  saveBankVoucher,
} from "@/api/chain/companyauthrecharge";
import voucher from "../voucher.vue";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      //支付id
      type: String,
      default: () => {
        return "";
      },
    },
    payeeId: {
      //承运人id
      type: String,
      default: () => {
        return "";
      },
    },
    planId: {
      //资金支付计划id
      type: String,
      default: () => {
        return "";
      },
    },
  },
  components: {
    voucher,
  },
  data() {
    return {
      bigAmount: "",
      waybillDetail: [],
      dialogTableVisible: false,
      paymentFormVisible: false,
      form: {},
      tableData: [],
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        index: true,
        indexLabel: "序号",
        stripe: false,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        excelBtn: true,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        refreshBtn: false,
        useVirtual: true,
        height: "auto",
        calcHeight: 310,
        columnBtn: false,
        column: [
          {
            label: "运单号",
            prop: "no",
            overHidden: true,
          },
          {
            label: "项目名称",
            prop: "projectName",
            overHidden: true,
          },
          {
            label: "出场签单土质",
            prop: "goSoilType",
            overHidden: true,
          },
          {
            label: "运输类型",
            prop: "tpModeCn",
            overHidden: true,
          },
          {
            label: "车牌号",
            prop: "truckCode",
            overHidden: true,
          },
          {
            label: "结算价",
            prop: "amount",
            overHidden: true,
          },
        ],
      },
      multipleSelection: [],
      start: 1,
      end: 1,
      detailVisible: false,
      detailList: [],
      paymentIdList: [],
      btnLoading: false,
      payInfo: [], //支付信息
      sourceId: "",
    };
  },
  watch: {
    paymentFormVisible(val) {
      if (val == false) {
        this.form.tranAmount = "";
        this.bigAmount = "";
      }
    },
  },
  created() {},
  mounted() {
    this.getPage();
    this.getBankVoucherPayInfo();
  },
  computed: {
    ...mapGetters(["permissions"]),
    //已选运单金额
    settleAmtTotal() {
      return this.multipleSelection
        .map((row) => row.settleAmt)
        .reduce((acc, cur) => parseFloat(cur) + acc, 0)
        .toFixed(2);
    },
  },
  methods: {
    entire() {
      this.form.tranAmount = this.form.remainingTranAmount;
      this.$forceUpdate();
      this.bigAmount = this.convertCurrency(this.form.tranAmount);
    },
    cancelModal() {
      this.$emit("update:visible", false);
      this.$emit("refreshChange");
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    checkWaybill(row) {
      this.dialogTableVisible = true;
      this.waybillDetail = row.paymentPayeeWaybillInfoList;
    },
    tranAmountInput(e) {
      console.log(e);
      if (Number(e)> this.form.bankNoPayAmount) {
        this.$notify({
          title: "警告",
          message: "付款金额不能大于未付金额",
          type: "warning",
        });
        this.form.tranAmount = this.form.remainingTranAmount;
      }
      this.bigAmount = this.convertCurrency(this.form.tranAmount);
    },
    // 支付明细-添加付款信息
    saveBank() {
      saveBankVoucher({
        companyAuthId: this.form.companyAuthId,
        projectInfoId: this.form.projectInfoId,
        companyPaymentId: this.form.companyPaymentId,
        payeeName: this.form.payeeName,
        bindingBankNo: this.form.bindingBankNo,
        bindingBankName: this.form.bindingBankName,
        bindingBankCode: this.form.bindingBankCode,
        payeeId: this.form.payeeId,
        tranAmount: this.form.tranAmount,
        companyName: this.form.companyName,
        companyNo: this.form.companyNo,
      }).then((res) => {
        this.getPage();
        this.getBankVoucherPayInfo();
        this.$notify({
          title: "成功",
          message: "操作成功",
          type: "success",
        });
        this.paymentFormVisible = false;
      });
    },
    getPage() {
      this.tableLoading = true;
      getPaymentPayeeInfo({
        paymentId: this.id,
        payeeId: this.payeeId,
        planId: this.planId,
      })
        .then((response) => {
          this.form = response.data.data;
          let arr = response.data.data.paymentPayeeWaybillInfoList || [];
          if (arr && arr.length > 0) {
            this.tableData = [[]];
            let batchNo = arr[0].batchNo || "";
            arr.forEach((item) => {
              if (batchNo == item.batchNo) {
                this.tableData[this.tableData.length - 1].push(item);
              } else {
                this.tableData.push([]);
                this.tableData[this.tableData.length - 1].push(item);
                batchNo = item.batchNo;
              }
            });
            console.log(this.tableData);
          } else {
            this.tableData = [];
          }
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    getBankVoucherPayInfo() {
      getBankVoucherPayInfo({ paymentId: this.id, payeeId: this.payeeId }).then(
        (res) => {
          this.payInfo = res.data.data;
          console.log(this.payInfo);
        }
      );
    },
    certificate(row) {
      this.detailList = [];
      console.log(row);
      this.$refs.sign.clear();
      this.detailList.push(row.voucherPic);
      this.detailVisible = true;
    },
    voucher(row) {
      this.sourceId = row.paymentPayeeWaybillInfoList[0].thirdVoucher;
      console.log("row.thirdVoucher=" + row.thirdVoucher);
      let param = {
        paymentIdList: [row.companyPaymentId],
        payeeId: row.payeeId,
        thirdVoucher: row.thirdVoucher,
        batchNo: row.batchNo,
        planId: this.planId,
      };
      this.paymentIdList = [row.companyPaymentId];
      this.$refs.sign.clear();
      this.btnLoading = true;
      getEvidenceWhere(param)
        .then((res) => {
          this.btnLoading = false;
          this.detailList = res.data.data;
          this.detailVisible = true;
          setTimeout(() => {
            this.detailList.forEach((item, index) => {
              this.$refs.sign.getStar("支付", item.companyName, item.companyNo);
              this.$set(item, "img", this.$refs.sign.submit(80, 50));
              this.$refs.sign.clear();
            });
          }, 100);
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
    totalCount(arr) {
      return arr
        .map((row) => row.amount)
        .reduce((acc, cur) => parseFloat(cur) + acc, 0)
        .toFixed(2);
    },
    dealSearch(paymentId, batchNo) {
      this.btnLoading = true;
      getTransStatus4047ByPaymentId({ paymentId, batchNo })
        .then((res) => {
          console.log(res);
          this.btnLoading = false;
          this.$message.success("查询成功");
          this.getPage();
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
    convertCurrency(currencyDigits) {
      var MAXIMUM_NUMBER = 99999999999.99;
      var CN_ZERO = "零";
      var CN_ONE = "壹";
      var CN_TWO = "贰";
      var CN_THREE = "叁";
      var CN_FOUR = "肆";
      var CN_FIVE = "伍";
      var CN_SIX = "陆";
      var CN_SEVEN = "柒";
      var CN_EIGHT = "捌";
      var CN_NINE = "玖";
      var CN_TEN = "拾";
      var CN_HUNDRED = "佰";
      var CN_THOUSAND = "仟";
      var CN_TEN_THOUSAND = "万";
      var CN_HUNDRED_MILLION = "亿";
      var CN_SYMBOL = "";
      var CN_DOLLAR = "元";
      var CN_TEN_CENT = "角";
      var CN_CENT = "分";
      var CN_INTEGER = "整";

      var integral;
      var decimal;
      var outputCharacters;
      var parts;
      var digits, radices, bigRadices, decimals;
      var zeroCount;
      var i, p, d;
      var quotient, modulus;
      var strtemp;
      currencyDigits = currencyDigits.toString();
      if (currencyDigits == "") {
        return "";
      } else {
        strtemp = currencyDigits.split("-");
        if (strtemp.length > 1) {
          currencyDigits = strtemp[1];
        }
      }
      if (currencyDigits.match(/[^,.\d]/) != null) {
        return "";
      }
      if (
        currencyDigits.match(
          /^((\d{1,3}(,\d{3})*(.((\d{3},)*\d{1,3}))?)|(\d+(.\d+)?))$/
        ) == null
      ) {
        return "";
      }

      currencyDigits = currencyDigits.replace(/,/g, "");
      currencyDigits = currencyDigits.replace(/^0+/, "");

      if (Number(currencyDigits) > MAXIMUM_NUMBER) {
        return "";
      }
      parts = currencyDigits.split(".");
      if (parts.length > 1) {
        integral = parts[0];
        decimal = parts[1];
        decimal = decimal.substr(0, 2);
      } else {
        integral = parts[0];
        decimal = "";
      }
      digits = new Array(
        CN_ZERO,
        CN_ONE,
        CN_TWO,
        CN_THREE,
        CN_FOUR,
        CN_FIVE,
        CN_SIX,
        CN_SEVEN,
        CN_EIGHT,
        CN_NINE
      );
      radices = new Array("", CN_TEN, CN_HUNDRED, CN_THOUSAND);
      bigRadices = new Array("", CN_TEN_THOUSAND, CN_HUNDRED_MILLION);
      decimals = new Array(CN_TEN_CENT, CN_CENT);
      outputCharacters = "";
      if (Number(integral) > 0) {
        zeroCount = 0;
        for (i = 0; i < integral.length; i++) {
          p = integral.length - i - 1;
          d = integral.substr(i, 1);
          quotient = p / 4;
          modulus = p % 4;
          if (d == "0") {
            zeroCount++;
          } else {
            if (zeroCount > 0) {
              outputCharacters += digits[0];
            }
            zeroCount = 0;
            outputCharacters += digits[Number(d)] + radices[modulus];
          }
          if (modulus == 0 && zeroCount < 4) {
            outputCharacters += bigRadices[quotient];
          }
        }
        outputCharacters += CN_DOLLAR;
      }

      if (decimal != "") {
        for (i = 0; i < decimal.length; i++) {
          d = decimal.substr(i, 1);
          if (d != "0") {
            outputCharacters += digits[Number(d)] + decimals[i];
          }
        }
      }
      if (outputCharacters == "") {
        outputCharacters = CN_ZERO + CN_DOLLAR;
      }
      if (decimal == "" || decimal == 0) {
        outputCharacters += CN_INTEGER;
      }
      outputCharacters = CN_SYMBOL + outputCharacters;
      return outputCharacters;
    },
  },
  destroyed() {
    this.$refs.sign && this.$refs.sign.clear();
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
.remark {
  padding-top: 10px;
  color: red;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.lists {
  border: 1px solid #9a9a9a;
  padding: 10px 0px;
  .title {
    padding-left: 8px;
    text-align: left;
    height: 20px;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #111528;
    // border-left: 4px solid #4688f7;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #9a9a9a;
    span {
      display: inline-block;
      height: 16px;
      width: 4px;
      background-color: #4688f7;
      margin-right: 4px;
    }
  }
  ul {
    padding: 0px 30px;
    li {
      line-height: 30px;
      color: #303133;
    }
  }
}
/deep/ .el-dialog__wrapper .el-dialog .el-dialog__body {
  padding-top: 20px;
  .voucher {
    padding-top: 20px;
    padding-bottom: 30px;
    padding-right: 60px;
  }
}
/deep/ .el-collapse .el-collapse-item__wrap {
  border-bottom: none;
  .el-collapse-item__content {
    padding-bottom: 0;
  }
}
/deep/.elButton {
  width: 110px;
  height: 42px;
  font-size: 16px;
  margin: 15px 0;
}
/deep/.el-input-group__append {
  background: #66b1ff;
  border-color: #66b1ff;
  color: #fff;
}
/deep/.errButton {
  color: red;
}
::v-deep {
  .inputFund input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  .inputFund input[type="number"] {
    appearance: textfield;
    -moz-appearance: textfield;
  }
}
.collect{
  font-size: 18px;
  font-weight: bold;
  color: red;
}
</style>
