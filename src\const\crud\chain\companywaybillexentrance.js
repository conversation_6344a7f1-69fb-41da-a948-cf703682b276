export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  // stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  menuWidth:120,
  searchSpan:8,
  searchMenuSpan: 8,
  menu:false,
  defaultSort: {
    prop: "entranceDatetime",
    order: "descending",
  },
  column: [

    {
      label: "入场类型",
      prop: "type",
      search: true,
      type: "select",
      dicData:[
        {
          label:'空车入场',
          value:'0'
        },
        {
          label:'重车入场',
          value:'1'
        },
      ],
      overHidden:true,
    },
    {
      label: "项目名称",
      prop: "projectName",
      sortable: 'custom',
      search: true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "车牌",
      prop: "entranceTruckCode",
      sortable: 'custom',
      search: true,
      overHidden:true,
    },
    {
      label: "土质",
      prop: "soilType",
      sortable: 'custom',
      overHidden:true,
    },
    {
      label: "拍照车牌",
      prop: "entrancePicture",
      sortable: 'custom',
      type:"upload",
      minWidth:100,
      overHidden:true,
    },
    {
      label: "备注",
      prop: "entranceRemark",
      sortable: 'custom',
      overHidden:true,
    },
    {
      label: "签单人",
      prop: "entranceStaffName",
      sortable: 'custom',
      search: true,
      minWidth:86,
      overHidden:true,
    },
    {
      label: "是否删除",
      prop: "isDel",
      search: true,
      type: "select",
      dicData:[
        {
          label:'未删除',
          value:'0'
        },
        {
          label:'已删除',
          value:'1'
        },
      ],
      minWidth:80,
      overHidden:true,
    },
    {
      label: "班次",
      prop: "shiftType",
      minWidth:110,
      overHidden:true,
      sortable: 'custom',
      search:true,
      type:"select",
      dicUrl:"/chain/projectinfo/getShiftOfProject",
      dicFormatter: (res) => {
        return res.data.map((item)=>{
          return {
            label:item,
            value:item,
          }
        })
      },
      searchFilterable:true,
    },
    {
      label: "班次日期",
      prop: "shiftTime",
      type:'date',
      sortable: 'custom',
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd',
      width:140,
      overHidden:true,
    },
    {
      label: "签单时间",
      prop: "entranceDatetime",
      type:'date',
      sortable: 'custom',
      searchRange:true,
      search: true,
      searchValueFormat: 'yyyy-MM-dd',
      valueFormat: 'yyyy-MM-dd',
      minWidth:140,
      overHidden:true,
    },
  ],
};
