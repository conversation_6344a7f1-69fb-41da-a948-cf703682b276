<template>
  <div class="execution">
    <basic-container>
      <div v-show="!searchShow">
        <avue-form :option="option1" v-model="form" @submit="searchChange">
          <template slot="menuForm" slot-scope="{ size }">
            <el-button class="el-button--small" type="text" style="margin: 0;" plain icon="el-icon-caret-bottom"
              @click="expand">展开</el-button>
          </template>
        </avue-form>
      </div>
      <div v-show="searchShow">
        <avue-form :option="option" v-model="form" @submit="searchChange">
          <template slot="menuForm" slot-scope="{ size }">
            <el-button class="el-button--small" type="text" style="margin: 0;" plain icon="el-icon-caret-top"
              @click="expand">收起</el-button>
          </template>
        </avue-form>
      </div>
      <el-tabs tab-position="top" v-model="activeName" @tab-click="changeTab">
        <el-tab-pane :label="item.label" v-for="item in tabs" :key="item.id" :name="item.value">
        </el-tab-pane>
      </el-tabs>
      <!-- :page="page" -->
      <component v-loading="tableLoading" ref="myComponent" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
        :tableData="tableData" :paramsSearch="paramsSearch" :btnsLoading="btnsLoading" @sortChange="sortChange"
        @getPage="getPage" @exOut="exOut" v-bind:is="activeName"></component>
    </basic-container>
  </div>
</template>

<script>
  import {
    diagnosisPage as getPage,
    diagnosisExportExcel as exportExcel,
  } from "@/api/chain/khyTaxDataDiag";
  import {
    tableOption
  } from "@/const/crud/chain/khyTaxDataDiag";
  import {
    mapGetters
  } from "vuex";

  export default {
    name: "khyTaxDataDiag",
    components: {
      driverDiag: () => import("./components/driverDiag.vue"), //司机诊断
      vehicleDiag: () => import("./components/vehicleDiag.vue"), //车辆诊断
      gpsDiag: () => import("./components/gpsDiag.vue"), //运单GPS诊断
      importWaybill: () => import("./components/importWaybill.vue"), //运单GPS诊断
      waybillDiag: () => import("./components/waybillDiag.vue"), //运单GPS诊断
    },
    data() {
      return {
        form: {},
        tableData: [],
        page: {
          pageSizes: [20, 50, 100, 200, 400],
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 20, // 每页显示多少条
          ascs: [], //升序字段
          descs: [], //降序字段
        },
        paramsSearch: {

        },
        tableLoading: false,
        tableOption: tableOption,
        activeName: "driverDiag",
        tabs: [{
            label: "司机诊断",
            value: "driverDiag",
            id: 1,
          },
          {
            label: "车辆诊断",
            value: "vehicleDiag",
            id: 2,
          },
          {
            label: "运单GPS诊断",
            value: "gpsDiag",
            id: 3,
          },
          {
            label: "运单诊断",
            value: "waybillDiag",
            id: 5,
          },
          {
            label: "运单导入",
            value: "importWaybill",
            id: 4,
          },
        ],
        option: {
          labelWidth: 76,
          border: true,
          span: 4,
          menuSpan: 4,
          align: "center",
          menuAlign: "center",
          submitText: "查询",
          emptyText: "重置",
          column: [{
              label: "项目",
              prop: "projectName",
            },
            {
              label: "运单号",
              prop: "no",
            },
            {
              label: "结算单号",
              prop: "companySettleNo",
            },
            {
              label: "支付单号",
              prop: "companyPaymentNo",
            },
            {
              label: "司机姓名",
              prop: "fleetName",
            },
            {
              label: "车牌",
              prop: "truckCode",
            },
            {
              label: "运输方式",
              prop: "tpMode",
              type: "select", // 下拉选择
              dataType: 'string',
              multiple: true,
              props: {
                label: "itemName",
                value: "itemValue",
              },
              dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode',
            },
            {
              label: "是否合格",
              prop: "isOk",
              type: "select", // 下拉选择
              display:true,
              dicData: [{
                  label: "不合格",
                  value: "0",
                },
                {
                  label: "合格",
                  value: "1",
                },
              ],
            },
            {
              label: "风控诊断",
              prop: "waybillDiagnoseStatus",
              type: "select", // 下拉选择
              multiple: true,
              display:false,
              dicData: [
                {
                  label: "司机合格",
                  value: "1",
                },
                {
                  label: "货车合格",
                  value: "2",
                },
                {
                  label: "运单GPS合格",
                  value: "3",
                },
                {
                  label: "司机不合格",
                  value: "4",
                },
                {
                  label: "货车不合格",
                  value: "5",
                },
                {
                  label: "运单GPS不合格",
                  value: "6",
                },
              ],
            },
            {
              label: "出场时间",
              prop: "goDatetime",
              type: "datetimerange",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
            },
            {
              label: "出场备注",
              prop: "goRemark",
            },
            {
              label: "是否结算",
              prop: "isSettle",
              type: "select", // 下拉选择
              dicData: [{
                  label: "未生成结算单",
                  value: "0",
                },
                {
                  label: "已生成结算单",
                  value: "1",
                },
              ],
            },
          ],
        },
        currentIndex: 1,
        btnsLoading: false,
        searchShow: false,
        option1: {
          labelWidth: 76,
          border: true,
          span: 4,
          menuSpan: 4,
          align: "center",
          menuAlign: "center",
          submitText: "查询",
          emptyText: "重置",
          gutter:0,
          column: [{
              label: "项目",
              prop: "projectName",
            },
            {
              label: "运单号",
              prop: "no",
            },
            {
              label: "结算单号",
              prop: "companySettleNo",
            },
            {
              label: "支付单号",
              prop: "companyPaymentNo",
            },
            {
              label: "是否合格",
              prop: "isOk",
              type: "select", // 下拉选择
              display:true,
              dicData: [{
                  label: "不合格",
                  value: "0",
                },
                {
                  label: "合格",
                  value: "1",
                },
              ],
            },
            {
              label: "风控诊断",
              prop: "waybillDiagnoseStatus",
              type: "select", // 下拉选择
              multiple: true,
              display:false,
              dicData: [
                {
                  label: "司机合格",
                  value: "1",
                },
                {
                  label: "货车合格",
                  value: "2",
                },
                {
                  label: "运单GPS合格",
                  value: "3",
                },
                {
                  label: "司机不合格",
                  value: "4",
                },
                {
                  label: "货车不合格",
                  value: "5",
                },
                {
                  label: "运单GPS不合格",
                  value: "6",
                },
              ],
            },
          ],
        },
        calcHeight:310,
      };
    },
    created() {},
    mounted: function() {},
    provide() {
      return {
        khyHome: this,
      };
    },
    computed: {
      ...mapGetters(["permissions"]),
      permissionList() {
        return {
          addBtn: this.permissions["chain:khyTaxDataDiag:add"] ? true : false,
          delBtn: this.permissions["chain:khyTaxDataDiag:del"] ? true : false,
          editBtn: this.permissions["chain:khyTaxDataDiag:edit"] ? true : false,
          viewBtn: this.permissions["chain:khyTaxDataDiag:get"] ? true : false,
          excelBtn: this.permissions["chain:khyTaxDataDiag:excel"] ? true : false,
        };
      },
    },
    methods: {
      searchChange(params, done) {
        params = this.filterForm(params);
        this.paramsSearch = params;
        this.page.currentPage = 1;
        this.getPage(this.page, params);
        done();
      },
      sortChange(val) {
        console.log('sortChange', val);
        let prop = val.prop ?
          val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() :
          "";
        if (val.order == "ascending") {
          this.page.descs = [];
          this.page.ascs = prop;
        } else if (val.order == "descending") {
          this.page.ascs = [];
          this.page.descs = prop;
        } else {
          this.page.ascs = [];
          this.page.descs = [];
        }
        this.getPage(this.page);
      },
      getPage(page, params) {
        if (params) {
          if (params.hasOwnProperty("goDatetime")) {
            params.goDatetimeStart = params.goDatetime[0];
            params.goDatetimeEnd = params.goDatetime[1];
            delete params.goDatetime;
          }
          if (params.$tpMode) {
            delete params.$tpMode;
          }
        }
        this.tableLoading = true;
        getPage(
            Object.assign({
                current: page.currentPage,
                size: page.pageSize,
                descs: this.page.descs,
                ascs: this.page.ascs,
                type: this.currentIndex,
              },
              params,
              this.paramsSearch
            )
          )
          .then((response) => {
            this.tableData = response.data.data.records;
            // 只有current为1的时候才更新total,其他时候不查询total节省性能
            if (response.data.data.current == 1) {
              this.page.total = response.data.data.total;
            }
            this.page.currentPage = response.data.data.current;
            this.page.pageSize = page.pageSize;
            this.tableLoading = false;
          })
          .catch(() => {
            this.tableLoading = false;
          });
      },

      /**
       * 刷新回调
       */
      refreshChange(page) {
        this.getPage(this.page);
      },
      expand(){
        this.searchShow=!this.searchShow
        if(this.currentIndex==5){
          this.$refs.myComponent.resizeTable(this.searchShow)
        }
      },
      changeTab(tab, e) {
        if (this.currentIndex == this.tabs[tab.index].id) return false
        this.currentIndex = this.tabs[tab.index].id
        this.page.currentPage = 1
        this.btnsLoading = false
        this.tableData = []
        this.page.pageSizes = this.currentIndex==5?[20, 50, 100, 200, 400, 1000, 2000]:[20, 50, 100, 200, 400]
        let waybillDiagnoseStatus = this.findObject(this.option.column, 'waybillDiagnoseStatus');
        let waybillDiagnoseStatus2 = this.findObject(this.option1.column, 'waybillDiagnoseStatus');
        let isOk = this.findObject(this.option.column, 'isOk');
        let isOk2 = this.findObject(this.option1.column, 'isOk');
        console.log(waybillDiagnoseStatus);
        waybillDiagnoseStatus.display=this.currentIndex==5
        isOk.display=this.currentIndex!=5
        waybillDiagnoseStatus2.display=this.currentIndex==5
        isOk2.display=this.currentIndex!=5
      },
      exOut(name) {
        let params = Object.assign({}, this.paramsSearch)
        if (params) {
          if (params.hasOwnProperty("goDatetime")) {
            params.goDatetimeStart = params.goDatetime[0];
            params.goDatetimeEnd = params.goDatetime[1];
            delete params.goDatetime;
          }
        }
        params.type = this.currentIndex
        for (const key in params) {
          if (key.includes('$') || params[key] == undefined) {
            delete params[key]
          }
        }
        this.btnsLoading = true
        exportExcel(params).then(res => {
          this.btnsLoading = false
          this.$store.commit("SET_DOWN_EXCEL_SHOW", true)
        }).catch(() => {
          this.btnsLoading = false
        })
      },
    },
  };
</script>

<style lang="scss" scoped>
  /deep/ .closeIcon,
  .checkIcon {
    font-size: 16px;
    color: red;
  }

  /deep/ .checkIcon {
    color: #3dcc90;
  }
</style>
