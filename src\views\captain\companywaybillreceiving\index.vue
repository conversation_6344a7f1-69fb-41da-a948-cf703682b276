<template>
  <div class="execution">
      <basic-container>
          <avue-crud ref="crud"
                     :page.sync="page"
                     :data="tableData"
                     :permission="permissionList"
                     :table-loading="tableLoading"
                     :option="tableOption"
                     v-model="form"
                     @on-load="getPage"
                     @refresh-change="refreshChange"
                     @sort-change="sortChange"
                     @search-change="searchChange">
                     <template slot="menuLeft" slot-scope="{ row }">
                      <el-button
                          size="small"
                          icon="el-icon-download"
                          type="primary"
                          @click="exOut"
                        >
                          导出
                      </el-button>
                    </template>
                     <template slot="menu" slot-scope="{ row,index }">
                      <el-button
                            type="text"
                            v-if="row.deliveryStatus=='未付款' && row.isRight==1"
                            icon="el-icon-check"
                            size="small"
                            plain
                            @click="confirmPayment(row)"> 确认收款</el-button>
                      <el-button
                            type="text"
                            v-if="row.deliveryStatus=='已付款' && row.isRight==1"
                            icon="el-icon-remove-outline"
                            size="small"
                            plain
                            @click="revokePayment(row)"> 撤销收款</el-button>
                        <el-button
                          type="text"
                          icon="el-icon-view"
                          size="small"
                          @click="toDetail(row)"
                          plain>详情</el-button>
                      </template>
          </avue-crud>
      </basic-container>
      <detail v-if="detailVisible" :info="info" :visible.sync="detailVisible"></detail>
  </div>
</template>

<script>
  import {getPage,updateObj,revoke} from '@/api/captain/companywaybillreceiving'
  import {tableOption} from '@/const/crud/captain/companywaybillreceiving'
  import {mapGetters} from 'vuex'
  import detail from './detail.vue'
  import { expotOut } from "@/util/down.js";

  export default {
      name: 'companywaybillreceiving',
      components: {
        detail
      },
      data() {
          return {
              form: {},
              tableData: [],
              page: {
                  total: 0, // 总页数
                  currentPage: 1, // 当前页数
                  pageSize: 20, // 每页显示多少条
                  ascs: [],//升序字段
                  descs: []//降序字段
              },
              paramsSearch: {},
              tableLoading: false,
              tableOption: tableOption,
              detailVisible:false,
              info:{},
              acceptDic:[],
          }
      },
      created() {
      },
      mounted: function () {
      },
      computed: {
          ...mapGetters(['permissions']),
          permissionList() {
              return {
                  // addBtn: this.permissions['chain:companywaybilldelivery:add'] ? true : false,
                  // delBtn: this.permissions['chain:companywaybilldelivery:del'] ? true : false,
                  // editBtn: this.permissions['chain:companywaybilldelivery:edit'] ? true : false,
                  // viewBtn: this.permissions['chain:companywaybilldelivery:get'] ? true : false
              };
          }
      },
      methods: {
          searchChange(params,done) {
              params = this.filterForm(params)
              this.paramsSearch = params
              this.page.currentPage = 1
              this.getPage(this.page, params)
              done()
          },
          sortChange(val) {
              let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
              if (val.order == 'ascending') {
                  this.page.descs = []
                  this.page.ascs = prop
              } else if (val.order == 'descending') {
                  this.page.ascs = []
                  this.page.descs = prop
              } else {
                  this.page.ascs = []
                  this.page.descs = []
              }
              this.getPage(this.page)
          },
          getPage(page, params) {
              this.tableLoading = true
              if (params) {
                if (params.hasOwnProperty("searchDate")) {
                  params.handBegDate = params.searchDate[0];
                  params.handEndDate = params.searchDate[1];
                  delete params.searchDate;
                }
                if (params.hasOwnProperty("searchDate2")) {
                  params.acceptBegDate = params.searchDate2[0];
                  params.acceptEndDate = params.searchDate2[1];
                  delete params.searchDate2;
                }
              }
              getPage(Object.assign({
                  current: page.currentPage,
                  size: page.pageSize,
                  descs: this.page.descs,
                  ascs: this.page.ascs,
              }, params, this.paramsSearch)).then(response => {
                  this.tableData = response.data.data.records
                  this.page.total = response.data.data.total
                  this.page.currentPage = page.currentPage
                  this.page.pageSize = page.pageSize
                  this.tableLoading = false
              }).catch(() => {
                  this.tableLoading = false
              })
          },
          /**
           * 刷新回调
           */
          refreshChange(page) {
              this.getPage(this.page)
          },
          handleEdit(row, index) {
            this.$refs.crud.rowEdit(row, index);
          },
          toDetail(row){
            this.info = row
            this.detailVisible = true
          },
          exOut(){
            let params = Object.assign({},this.paramsSearch)
            if (params) {
              if (params.hasOwnProperty("searchDate")) {
                params.handBegDate = params.searchDate[0];
                params.handEndDate = params.searchDate[1];
                delete params.searchDate;
              }
              if (params.hasOwnProperty("searchDate2")) {
                params.acceptBegDate = params.searchDate2[0];
                params.acceptEndDate = params.searchDate2[1];
                delete params.searchDate2;
              }
            }
            let url = '/chain/delivery/deliveryExcel'
            expotOut( params,url,'收单付款');
          },
          confirmPayment(row){
            this.$confirm("确认收款?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(()=>{
                this.tableLoading = true
                updateObj({companyWaybillDeliveryId:row.id}).then(res=>{
                  this.tableLoading = false
                  // this.$message.success('付款成功')
                  this.getPage(this.page)
                }).catch(err=>{
                  this.tableLoading = false
                })
              })
              .catch(function (err) {});
          },
          revokePayment(row){
            this.$confirm("确定撤销收款?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(()=>{
                this.tableLoading = true
                revoke({companyWaybillDeliveryId:row.id}).then(res=>{
                  this.tableLoading = false
                  // this.$message.success('付款成功')
                  this.getPage(this.page)
                }).catch(err=>{
                  this.tableLoading = false
                })
              })
              .catch(function (err) {});
          },
      }
  }
</script>

<style lang="scss" scoped>
</style>
