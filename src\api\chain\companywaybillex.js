import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companywaybillex/getCompanyWaybillExPage',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companywaybillex',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
  return request({
    url: '/chain/companywaybillex/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/chain/companywaybillex/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/chain/companywaybillex',
    method: 'put',
    data: obj
  })
}

export function updateInStaffChangedWaybillEx(obj) {
    return request({
        url: '/chain/companywaybillex/updateInStaffChangedWaybillEx',
        method: 'post',
        data: obj
    })
}
export function updateGoStaffChangedWaybillEx(obj) {
    return request({
        url: '/chain/companywaybillex/updateGoStaffChangedWaybillEx',
        method: 'post',
        data: obj
    })
}
