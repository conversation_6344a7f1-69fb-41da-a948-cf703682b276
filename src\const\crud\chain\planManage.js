export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  searchMenuSpan: 6,
  column: [
    {
      label: "创建时间",
      prop: "createDatetime",
      sortable: "custom",
      minWidth: 160,
      overHidden: true,
    },
    {
      label: "计划编号",
      prop: "planNo",
      sortable: "custom",
      search:true,
      minWidth: 180,
      overHidden: true,
    },
    // {
    //   label: "公司名称",
    //   prop: "companyName",
    //   search: true,
    //   minWidth: 160,
    //   overHidden: true,
    // },
    {
      label: "项目名称",
      prop: "projectName",
      search: true,
      minWidth: 160,
      overHidden: true,
    },
    // {
    //   label: "工地地址",
    //   prop: "projectAddress",
    //   search: true,
    //   minWidth: 160,
    //   overHidden: true,
    // },
    {
      label: "计划出土方量(m³)",
      prop: "outSoilCube",
      minWidth: 120,
      overHidden: true,
    },
    {
      label: "土质类型",
      prop: "planSoil",
      search: true,
      minWidth: 120,
      overHidden: true,
    },
    {
      label: "出土开始时间",
      prop: "outSoilTimeStart",
      minWidth: 100,
      overHidden: true,
    },
    {
      label: "出土结束时间",
      prop: "outSoilTimeEnd",
      minWidth: 100,
      overHidden: true,
    },
    // {
    //   label: "车型",
    //   prop: "goVehicleType",
    //   type: "select",
    //   search: true,
    //   props: {
    //     label: "itemName",
    //     value: "itemValue",
    //   },
    //   dicUrl: `/chain/systemdictionaryitem/listDictionaryItem?dictionary=go_vehicle_type`,
    //   hide:true,
    //   showColumn:false,
    //   minWidth: 100,
    //   overHidden: true,
    // },
    {
      label: "出土日期",
      prop: "searchDate",
      type: "select",
      search: true,
      display: false,
      type: "date",
      valueFormat: "yyyy-MM-dd",
      searchRange: true,
      hide: true,
      showColumn: false,
    },
    {
      label: "匹配状态",
      prop: "planStatus",
      search: true,
      type: "select",
      dicData: [
        {
          label: "匹配中",
          value: "2",
        },
        {
          label: "已取消",
          value: "5",
        },
      ],
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "撮合时间",
      prop: "matchStart",
      minWidth: 140,
      overHidden: true,
    },
  ],
};
