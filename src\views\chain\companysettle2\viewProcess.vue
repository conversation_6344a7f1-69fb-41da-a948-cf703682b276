<template>
  <div class="driverTaskDetail">
    <el-drawer size="90%"
               :data="visible"
               :visible.sync="visible"
               title="查看流程"
               :before-close="cancelModal">
        <el-collapse>
          <h3 style="margin-top: 10px;">结算单信息</h3>
          <el-descriptions>
            <el-descriptions-item label="结算单号">{{accountForm.settleNo}}</el-descriptions-item>
            <el-descriptions-item label="所属项目">{{accountForm.projectName}}</el-descriptions-item>
            <el-descriptions-item label="结算申请人">{{accountForm.agentName}}</el-descriptions-item>
            <el-descriptions-item label="结算申请时间">{{accountForm.applyDatetime}}</el-descriptions-item>
            <el-descriptions-item label="承运人"
                                  :span="2">{{accountForm.applyName}}</el-descriptions-item>
            <el-descriptions-item label="核算运单数">{{accountForm.checkNum}}</el-descriptions-item>
            <el-descriptions-item label="核算运单金额">{{accountForm.settleAmount}}</el-descriptions-item>
            <el-descriptions-item label="核算人">{{accountForm.settleName}}</el-descriptions-item>
          </el-descriptions>
          <el-collapse-item name="1" v-if="accountForm.waybillList&&accountForm.waybillList.length>0">
            <template slot="title">
              <div class="detail">结算单详情</div>
            </template>
            <avue-crud :data="accountForm.waybillList"
                       :option="option">
            </avue-crud>
          </el-collapse-item>
          <div class="approvalInfo" v-if="accountForm.companySettleFlowList&&accountForm.companySettleFlowList.length>0">
            <h3>审批流程</h3>
            <div class="info">
              <div class="approvalFlow">
                <el-timeline >
                  <el-timeline-item :timestamp="item.step===0?'发起审批':item.positionName"
                                    placement="top"
                                    color="#409eff"
                                    v-for="(item,index) in accountForm.companySettleFlowList"
                                    :key="index">
                    <div style="display:flex;align-items: center;justify-content: space-between;margin-bottom:10px">
                      <h4>{{item.checkName}}<span v-if="item.step!==0"
                              style="margin-left:6px">({{item.passName}})</span> </h4><span>{{$moment(item.updateDatetime).format('YYYY-MM-DD HH:mm:ss')}}</span>
                    </div>
                    <div style="margin-bottom:6px;padding:10px;border-radius:4px;background-color:#f5f7fa;" v-if="item.approve">{{item.approve}}</div>
                    <ul v-if="item.list&&item.list.length>0">
                      <div class="subTitle">
                        <span></span>历史审批记录
                      </div>
                      <li v-for="(v,i) in item.list"
                          :key='i'
                          style="margin-bottom:6px;padding:10px;border-radius:4px;background-color:#f5f7fa;">
                        <div class="top flex flex-between flex-items-center"
                             style="margin-bottom:6px">
                          <div>{{v.companyStaffName}}{{v.statusName}}</div>
                          <div>{{v.updateDatetime}}</div>
                        </div>
                        <div class="bottom">{{v.approve}}</div>
                      </li>
                    </ul>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </div>
          </div>
        </el-collapse>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getPlanBySettleId, printCompanyPaymentPlanSettle
} from "@/api/chain/companysettle";
import printPlan from './printPlan.vue';
import flowView from '@/views/chain/companyauthrecharge2/components/flowView';
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    accountForm: {},
  },
  components: {
    printPlan,
    flowView
  },
  data () {
    return {
      option: {
        border: true,
        stripe: true,
        columnBtn: false,
        // showHeader:false,
        index: this.showIndex,
        size: 'mini',
        selection: false,
        addBtn: false,
        refreshBtn: false,
        menu: false,
        // page:this.showPage,
        align: 'center',
        menuAlign: 'center',
        height: 500,
        // menuType:this.menuType,
        // menuBtnTitle:'自定义名称',
        column: [
          {
            label: '运单信息',
            children: [
              {
                label: '运单号',
                prop: 'no',
                width: '180px'
              },
              {
                label: '出场日期',
                prop: 'goDatetime1',
                formatter: (val) => {
                  return val.goDatetime && this.$moment(val.goDatetime).format("YYYY-MM-DD");
                },
                width: '90px'
              },
              {
                label: '出场时间',
                prop: 'goDatetime',
                formatter: (val) => {
                  return val.goDatetime && this.$moment(val.goDatetime).format("HH:mm:ss");
                },
                width: '80px'
              },
              {
                label: '班次',
                prop: 'goShiftTypeCn',
              },
              {
                label: '签单员',
                prop: 'goStaffName',
              },
              {
                label: '车牌',
                prop: 'truckCode',
              },
              {
                label: '车型',
                prop: 'goVehicleType',
              },
              {
                label: '容量(方)',
                prop: 'capacity',
              },
              {
                label: '土质',
                prop: 'goSoilType',
              },
              {
                label: '运输方式',
                prop: 'tpModeCn',
              },
              {
                label: '泥尾',
                prop: 'garbageName',
              },
              {
                label: '司机',
                prop: 'driverName',
              },
              {
                label: '车队长',
                prop: 'driverCaptainName',
              },
              {
                label: '数量',
                prop: 'weightTons',
              },
              {
                label: '单位',
                prop: 'weightUnit',
              },
              {
                label: '单价',
                prop: 'unitPrice',
              },
              {
                label: '预付价',
                prop: 'payeePrice',
              },
              {
                label: '地块',
                prop: 'landParcel',
              },
              {
                label: '备注',
                prop: 'remark',
              },
            ]
          },
          {
            label: '结算信息',
            children: [
              {
                label: '单价',
                prop: 'settleWeightUnit',
              },
              {
                label: '核算价',
                prop: 'settlePrice',
              },
              {
                label: '增减值',
                prop: 'adjustAmt',
              },
            ]
          },

        ]
      },
    };
  },
  created () {
  },
  mounted () {
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {

    cancelModal () {
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ccc;
}
/deep/ .el-drawer__body {
  padding: 20px;
  padding-top: 0px;
  .el-collapse-item {
    .el-collapse-item__header {
      .detail {
        padding-left: 8px;
        text-align: left;
        font-weight: 700;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: #111528;
        border-bottom: none;
        border-left: 4px solid #4688f7;
      }
    }
  }
  .el-collapse-item__content {
    text-align: center;
    .avue-crud__menu {
      display: none;
    }
  }
  .approvalInfo {
    text-align: left;
    display: inline-block;
    margin-top: 20px;
    width: 100%;
  }
  h3 {
    margin-bottom: 20px;
    font-weight: 700;
    padding-left: 8px;
    text-align: left;
    height: 14px;
    line-height: 14px;
    font-size: 14px;
    color: #111528;
    border-left: 4px solid #4688f7;
  }
  .approvalInfo {
    .approvalTitle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 16px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ccc;
      margin-bottom: 20px;
      .approvalSource {
        padding-left: 8px;
        text-align: left;
        height: 18px;
        line-height: 18px;
        font-size: 16px;
        color: #111528;
        // border-left: 4px solid #4688f7;
      }
    }
    .approvalFlow {
      width: 400px;
    }

    .el-timeline-item__timestamp {
      color: #333;
    }
    .el-timeline-item__content {
      color: #909399;
    }
    .el-timeline-item__tail {
      border-left: 2px solid #409eff;
    }
    .approvalform {
      padding: 20px 20px 0px;
    }
    .info {
      padding: 0px 20px;
    }
    .subTitle {
      text-align: left;
      height: 20px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #111528;
      margin-bottom: 6px;
      span {
        display: inline-block;
        height: 16px;
        width: 4px;
        background-color: #4688f7;
        margin-right: 4px;
      }
    }
  }
}
</style>
