<template>
  <div class="driverTaskDetail">
    <el-dialog
      width="400px"
      title=""
      :visible.sync="visible"
      :before-close="cancelModal"
      :close-on-click-modal="false"
    >
        <el-form
          :model="ruleForm"
          status-icon
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="支付密码" prop="pass" v-if="info.isPayPwd">
            <el-input
              type="password"
              v-model="ruleForm.pass"
              clearable
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item label="手机验证码" prop="code" v-if="info.isPayPhoneVerification">
            <el-input v-model="ruleForm.code" clearable>
              <template slot="append">
                <span
                  @click="getCode"
                  style="color: #66b1ff; cursor: pointer"
                  :class="[{ display: msgKey }]"
                  >{{ msgText }}</span
                >
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" style="width:160px;margin-top:30px" @click="submitForm('ruleForm')"
              >确定</el-button
            >
          </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {getCode} from "@/api/chain/companyauthrecharge";

const MSGINIT = "发送验证码",
  MSGSCUCCESS = "${time}秒后可重发",
  MSGTIME = 60;
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      tableLoading: false,
      ruleForm: {
        pass: "",
        code: "",
      },
      rules: {
        pass: [{ required: true, message: "请输入密码", trigger: "blur" }],
        code: [
          { required: true, message: "请输入手机验证码", trigger: "blur" },
        ],
      },
      msgText: MSGINIT,
      msgTime: MSGTIME,
      msgKey: false,
      time: null, //定时器
    };
  },
  created() {},
  mounted: function () {},
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    getCode() {
      if (this.msgKey) return;
      let param = {
        payPhone:this.info.payPhone
      };
      getCode(param).then((res) => {
        this.$message.success("验证码发送成功");
        this.msgText = MSGSCUCCESS.replace("${time}", this.msgTime);
        this.msgKey = true;
        this.time = setInterval(() => {
          this.msgTime--;
          this.msgText = MSGSCUCCESS.replace("${time}", this.msgTime);
          if (this.msgTime == 0) {
            this.msgTime = MSGTIME;
            this.msgText = MSGINIT;
            this.msgKey = false;
            clearInterval(this.time);
          }
        }, 1000);
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit("update:visible", false);
          this.$emit("submit", JSON.stringify(this.ruleForm));
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
  .demo-ruleForm{
    margin-top: 20px;
  }
</style>
