<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        :search.sync="paramsSearch"
        @on-load="getPage"
        @search-change="searchChange"
        @refresh-change="refreshChange"
        @selection-change="selectionChange"
      >
        <template slot="menuLeft" slot-scope="{ row }">
          <el-button
            icon="el-icon-document-add"
            size="small"
            type="primary"
            :loading="tableLoading"
            :disabled="selectList.length == 0"
            @click="add"
          >
            生成结算单
          </el-button>
        </template>
        <template slot-scope="{ column }" slot="cardCountHeader">
            <el-tooltip class="item" effect="dark" content="交卡回执关联的电子结算卡张数" placement="top">
              <span>{{(column || {}).label}} <i class="el-icon-question"></i></span>
            </el-tooltip>
        </template>
        <template slot-scope="{ column }" slot="giveCardCntHeader">
            <el-tooltip class="item" effect="dark" content="交卡回执关联的运单数" placement="top">
              <span>{{(column || {}).label}} <i class="el-icon-question"></i></span>
            </el-tooltip>
        </template>
        <template slot-scope="{ column }" slot="settleAbleCountHeader">
            <el-tooltip class="item" effect="dark" content="无待审核运单修改申请、运单状态为已卸土且未生成结算单的运单" placement="top">
              <span>{{(column || {}).label}} <i class="el-icon-question"></i></span>
            </el-tooltip>
        </template>
        <template slot-scope="{ column }" slot="updateWaybillsCountHeader">
            <el-tooltip class="item" effect="dark" content="运单存在待审核修改申请，可前往“运单修改申请管理”审核完成后，重新点击搜索或者刷新界面查看数据" placement="top">
              <span>{{(column || {}).label}} <i class="el-icon-question"></i></span>
            </el-tooltip>
        </template>
        <template slot="menu" slot-scope="{ row }">
          <el-button type="text" size="small" plain @click="print(row)">
            打印</el-button
          >
          <el-button
            type="text"
            icon="el-icon-view"
            size="small"
            plain
            @click="lookWaybill(row)"
          >
            查看电子结算卡</el-button
          >
        </template>
      </avue-crud>
      <generate-card-receipt-page
        v-if="generateCardReceiptVisible"
        :cardReceiptList="generateCardReceiptData"
        :visible.sync="generateCardReceiptVisible"
      ></generate-card-receipt-page>
      <view-settele-scanInfo
        v-if="detailVisible"
        :info="info"
        :visible.sync="detailVisible"
      ></view-settele-scanInfo>

      <!-- 任务弹窗 -->
      <el-dialog
        width="500px"
        title="设置收款人"
        center
        :visible.sync="setPayeeVisible"
        :close-on-click-modal="false"
      >
        <avue-form ref="editForm" v-model="editForm" :option="editOption">
          <template slot-scope="{ disabled, size }" slot="mobile">
            <span>{{ editForm.mobile }}</span>
          </template>
          <template slot-scope="{ disabled, size }" slot="bindingBankNo">
            <span>{{ editForm.bindingBankNo }}</span>
          </template>
          <template slot-scope="{ disabled, size }" slot="bindingBankName">
            <span>{{ editForm.bindingBankName }}</span>
          </template>
          <template slot-scope="{ disabled, size }" slot="status">
            <span>{{ editForm.status }}</span>
          </template>
        </avue-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancel">取消</el-button>
          <el-button
            type="primary"
            :loading="btnSetPayLoading"
            :disabled="
              editForm.bindingBankName == '' ||
              editForm.bindingBankNo == '' ||
              editForm.status == '否'
            "
            @click="submitPayee"
            >提交</el-button
          >
        </span>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import {
  scanningSettle,
  batchAddWaybillSettle,
  exportWaybillsByCardNoExcel,
  updateCarrierName,
  getCardHistoryReceipt,
  batchAddWaybillCardSettle,
} from "@/api/chain/companysettle";
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";
import generateCardReceiptPage from "./generateCardReceipt.vue";
import viewSetteleScanInfo from "./viewSetteleScanInfo.vue";
export default {
  name: "historyCard",
  data() {
    return {
      info: {},
      btnSetPayLoading: false,
      editForm: {},
      setPayeeVisible: false,
      detailVisible: false,
      selectList: [],
      generateCardReceiptVisible: false,
      generateCardReceiptForm: {},
      generateCardReceiptData: {},
      form: {},
      tableData: [],
      page: {
        pageSizes: [10, 20, 50, 100, 500],
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      editOption: {
        column: [
          {
            label: "收款人",
            prop: "payeeId",
            type: "select",
            span: 24,
            remote: true,
            props: {
              label: "payeeId",
              value: "payeeId",
            },
            typeformat(item, label, value) {
              return `${item["carrierName"]}-${item["mobile"]}`;
            },
            dicFormatter: (res) => {
              this.payeeDic = res.data;
              return res.data || [];
            },
            dicUrl: `/chain/companywaybill/selectCarrierListByNameOrMobile?param={{key}}`,
            // allowCreate:true,
            filterable: true,
            change: ({ value }) => {
              console.log(value);
              if (value) {
                let tmp = this.payeeDic.filter((v) => v.payeeId == value)[0];
                console.log(tmp);
                if (tmp) {
                  this.editForm.mobile = tmp.mobile;
                  this.editForm.bindingBankNo = tmp.bindingBankNo;
                  this.editForm.bindingBankName = tmp.bindingBankName;
                  this.editForm.status = tmp.status;
                }
              }
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "手机号码",
            prop: "mobile",
            span: 24,
          },
          {
            label: "收款卡号",
            prop: "bindingBankNo",
            span: 24,
          },
          {
            label: "收款银行",
            prop: "bindingBankName",
            span: 24,
          },
          {
            label: "是否签约合同",
            prop: "status",
            span: 24,
          },
        ],
        labelWidth: 120,
        submitBtn: false,
        position: "left",
        emptyBtn: false,
        cancelBtn: true,
      },
      tableOption: {
        selection: true,
        selectable: (row, index) => {
          return row.settleAbleCount != 0;
        },
        addBtn: false,
        // menu: true,
        // menuSlot:true,
        border: true,
        refreshBtn: false,
        columnBtn: false,
        // header: false,
        align: "center",
        searchSpan: 8,
        // emptyBtn: false,
        column: [
         {
            label: "交卡回执单号",
            searchOrder: 1,
            searchLabelWidth: 110,
            search: true,
            prop: "giveCardNo",
          },
          // {
          //   label: "卡类型",
          //   prop: "cardType",
          //   searchDisabled: true,
          //   searchReadonly: true,
          //   disabled: true,
          //   showColumn: false,
          //   searchOrder: 2,
          // },
          // {
          //   label: "项目名称",
          //   prop: "projectName",
          //   searchDisabled: true,
          //   searchReadonly: true,
          //   showColumn: false,
          //   searchOrder: 3,
          // },
          {
            label: "电子结算卡号码",
            prop: "settleCardNo",
            labelTip: "结算卡第一位必须为大写英文",
            search: true,
            autofocus: true,
            hide:true,
            searchLabelWidth: 134,
            showColumn: false,
            searchOrder: 2,
          },
          {
            label: "结算卡数",
            prop: "cardCount",
          },
          {
            label: "回执关联运单数",
            prop: "giveCardCnt",
          },
          {
            label: "可结算运单数",
            prop: "settleAbleCount",
          },
          {
            label: "修改运单待审核数",
            prop: "updateWaybillsCount",
          },
          {
            label: "交卡时间",
            search: true,
            prop: "giveCardDatetime",
            type: "date",
            valueFormat: "yyyy-MM-dd",
            searchRange: true,
          },
          {
            label: "交卡人",
            search: true,
            searchOrder: 2,
            prop: "giveCardUserId",
          },


          {
            label: "是否生成结算单",
            searchOrder: 3,
            search:true,
            hide:true,
            type:'select',
            searchLabelWidth: 130,
            prop: "isSettle",
            dicData: [
              {
                label: "已经生成",
                value: "1",
              },
              {
                label: "未生成",
                value: "0",
              }
            ],
          },
        ],
        lock: false,
      },
    };
  },
  created() {},
  mounted() {
    this.page = {
      total: 0, // 总页数
      currentPage: 1, // 当前页数
      pageSize: 20, // 每页显示多少条
      ascs: [], //升序字段
      descs: [], //降序字段
    };
    this.getPage(this.page);
    // this.$nextTick(() => {
    //   this.$refs.inputRef.focus();
    // });
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:garbagecustomersales:add"]
          ? true
          : false,
        delBtn: this.permissions["chain:garbagecustomersales:del"]
          ? true
          : false,
        editBtn: this.permissions["chain:garbagecustomersales:edit"]
          ? true
          : false,
        viewBtn: this.permissions["chain:garbagecustomersales:get"]
          ? true
          : false,
      };
    },
  },
  components: {
    generateCardReceiptPage,
    viewSetteleScanInfo,
  },
  methods: {
    selectionChange(e) {
      this.selectList = e;
    },
    print(row) {
      let data = [
        {
          settleCardNo: row.settleCardNo,
          giveCardUserId: row.giveCardUserId,
          giveCardCnt: row.giveCardCnt,
          giveCardDatetime: row.giveCardDatetime,
        },
      ];
      this.generateCardReceiptData = [row.giveCardNo];
      this.generateCardReceiptVisible = true;
      // this.$router.push({
      //       path: "/scan/generateCardReceipt",
      //       query: { cardInfo: data },
      //     });
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    setCard() {
      this.dialogSetCard = true;
    },
    batchSetCard() {
      this.dialogSetCard = true;
    },
    generateCardReceipt() {},
    historyCardReceipt() {},
    // 处理keyup事件
    handleKeyUp(e) {
      var shiftKey = e.shiftKey; //为true则按了shiftKey
      let keyCode = e.code;
      // console.info(keyCode);
      var key = e.key; //其他按键key
      let array = [
        "Q",
        "W",
        "E",
        "R",
        "T",
        "Y",
        "U",
        "I",
        "O",
        "P",
        "A",
        "S",
        "D",
        "F",
        "G",
        "H",
        "J",
        "K",
        "L",
        "Z",
        "X",
        "C",
        "V",
        "B",
        "N",
        "M",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "0",
        "Equal",
      ];
      let smbs = [
        {
          key: "Semicolon",
          value: ":",
        },
        {
          key: "Slash",
          value: "/",
        },
        {
          key: "Minus",
          value: "-",
        },
        {
          key: "Period",
          value: ".",
        },
        {
          key: "Equal",
          value: "=",
        },
      ];
      // 这种情况下 基本上是 中文输入法 才有出现
      if (key == "Process") {
        for (const a of array) {
          // 如果匹配到是英文
          if (keyCode == "Key" + a) {
            // 判断大小写
            if (shiftKey) {
              this.realBarcode += a;
              this.isScanningGun = true;
            } else {
              this.realBarcode += a.toLowerCase();
              this.isScanningGun = true;
            }
          }
          // 如果匹配到是数字
          else if (keyCode == "Digit" + a) {
            this.realBarcode += String(a);
            this.isScanningGun = true;
          } else if (keyCode == a) {
            console.info(keyCode);
            this.realBarcode += "=";
            this.isScanningGun = true;
          }
        }
        var n1 = this.realBarcode.length;
        var n2 = this.realBarcode.indexOf("="); //取得=号的位置
        var id = this.realBarcode.substr(n2 + 1, n1 - n2); //从=号后面的内容
        setTimeout(() => {
          this.search.cardNo = id;
          if (keyCode == "Enter") {
            console.info(this.search.cardNo);
            this.getPage();
            this.realBarcode = "";
          }
        }, 100);
      } else {
      }
    },
    cancel() {
      this.setPayeeVisible = false;
      this.tableData = [];
      this.page.currentPage = 1;
      this.getPage(this.page);
    },
    submitPayee() {
      var ids = [];
      let arr = this.selectList.map((v) => {
        ids = [...ids, ...v.ids];
      });
      let data = ids.map((item) => {
        return {
          id: item,
          payeeId: this.editForm.payeeId,
        };
      });
      console.log(data);
      this.$refs.editForm.validate((valid, done, msg) => {
        if (valid) {
          this.btnSetPayLoading = true;
          updateCarrierName(data)
            .then((res) => {
              this.btnSetPayLoading = false;
              this.$message.success("操作成功");
              this.setPayeeVisible = false;
              this.tableData = [];
              this.page.currentPage = 1;
              this.getPage(this.page);
            })
            .catch((err) => {
              this.btnSetPayLoading = false;
            });
        } else {
          this.btnSetPayLoading = false;
        }
        done();
      });
    },
    exportWaybillsByCardNoExcel() {
      let data = this.tableData
        .map((v) => {
          return v.cardNo;
        })
        .join(",");
      let url = "/chain/companysettle/exportWaybillsByCardNoExcel";
      this.btnLoading = true;
      expotOut({ cardNos: data }, url, "电子结算卡")
        .then((res) => {
          this.btnLoading = false;
        })
        .catch((err) => {
          this.btnLoading = false;
        });
      // exportWaybillsByCardNoExcel(data).then(res=>{

      //   window.open(res.data)
      // })
    },
    del(item) {
      this.$confirm(`是否删除此记录！`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.tableData = this.tableData.filter((v) => v.cardNo != item.cardNo);
      });
    },
    lookWaybill(item) {
      this.info = item;
      this.detailVisible = true;
    },
    getPage(page, params = {}) {
      // this.tableData = [];
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("giveCardDatetime")) {
          params.giveCardDatetimeStart = params.giveCardDatetime[0];
          params.giveCardDatetimeEnd = params.giveCardDatetime[1];
          delete params.giveCardDatetime;
        }
      }
      getCardHistoryReceipt(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage();
    },
    add() {
      let that = this;
      this.$confirm(`确认生成结算单？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let data = that.selectList.map((v) => {
            return {
              giveCardNo: v.giveCardNo,
              settleCardNo: v.settleCardNo,
            };
          });
          that.tableLoading = true;
          batchAddWaybillCardSettle(data)
            .then((res) => {
              that.tableLoading = false;
              that.$message.success("操作成功");
              // 后台没有返回ids就不设置
              if(that.selectList[0].ids){
                that.setPayeeVisible = true;
              }else{
                that.getPage(this.page);
              }
            })
            .catch((err) => {
              that.tableLoading = false;
            });
        })
        .catch(() => {});
    },
    changeInput(value) {
      console.log(value);
      let reg = /^[A-Z]\d{0,14}$/;
      if (reg.test(value)) {
        this.search.cardNo = this.getParam(value, "id");
      } else {
        this.search.cardNo = "";
      }
    },
    //中文输入
    onCompositionStart(e) {
      this.lock = true;
    },
    // onCompositionupdate(e){
    //   console.log(e);
    //   let reg = /^[A-Z]\d{0,14}$/
    //   if(reg.test(e.data)){
    //       console.log(e.data);
    //       e.data = this.getParam(e.data,'id')
    //     }else{
    //       e.data = ''
    //     }
    // },
    //中文输入
    onCompositionEnd(e) {
      console.log(e);
      this.search.cardNo = this.getParam(e.data, "id");
      var reg = new RegExp("(^|\\?|&)" + "id" + "=([^&]*)(\\s|&|$)", "i");

      if (reg.test(e.data)) {
        this.search.cardNo = unescape(RegExp.$2.replace(/\+/g, " "));
        this.getPage();
      } else {
        this.search.cardNo = e.data;
      }
      this.lock = false;
    },
    getParam(path, name) {
      var reg = new RegExp("(^|\\?|&)" + name + "=([^&]*)(\\s|&|$)", "i");
      if (reg.test(path)) return unescape(RegExp.$2.replace(/\+/g, " "));
      return path;
    },
    exOut() {
      this.$export.excel({
        title: "电子卡结算运单",
        columns: [
          {
            label: "运单号",
            prop: "no",
          },
          {
            label: "车牌",
            prop: "truckCode",
          },
          {
            label: "运输方式",
            prop: "tpMode",
          },
          {
            label: "土质",
            prop: "goSoilType",
          },
          {
            label: "计价类型",
            prop: "weightType",
          },
          {
            label: "重量",
            prop: "weightTons",
          },
          {
            label: "签单时间",
            prop: "goDatetime",
          },
          {
            label: "签单员",
            prop: "goStaffName",
          },
          {
            label: "司机",
            prop: "driverName",
          },
          {
            label: "车队长",
            prop: "captainName",
          },
        ],
        data: this.tableData,
      });
    },
    // updateDic() {
    //   getProjectInfoList().then((res) => {
    //     this.$refs.crud.updateDic("projectInfoId", res.data.data);
    //   });
    //   getCustomerList().then((res) => {
    //     this.$refs.crud.updateDic("garbageCustomerId", res.data.data);
    //   });
    // },
  },
};
</script>

<style lang="scss" scoped></style>
