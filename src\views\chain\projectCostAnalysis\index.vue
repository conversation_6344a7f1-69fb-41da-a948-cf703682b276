<template>
  <div class="projectCostAnalysis">
    <basic-container>
      <avue-form
        :option="option"
        ref="form"
        @submit="searchChange"
        @reset-change="resetChange"
        v-model="searchForm"
      >
      </avue-form>
    </basic-container>
    <basic-container>
      <div class="title"><span></span>经营情况</div>
    </basic-container>
    <div class="content">
      <el-row type="flex">
        <el-col
          :xs="24"
          :sm="24"
          :lg="16"
          style="height: 300px; padding: 10px; margin-right: 10px"
        >
          <tpModeBarChart
            :echartData="echartData"
            :isEmpty="isEmpty"
            :titleData="titleData"
          ></tpModeBarChart>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="8" style="height: 300px; padding: 10px">
          <gaugeChart
            :isEmpty="isEmpty"
            :echartData="echartData"
            :titleData="titleData"
            :id="searchForm.projectInfoBiPhaseId"
            type="1"
          ></gaugeChart>
        </el-col>
      </el-row>
      <el-row type="flex" style="margin-top: 10px">
        <el-col
          :xs="24"
          :sm="24"
          :lg="8"
          style="height: 300px; margin-right: 10px"
        >
          <gaugeChart
            :isEmpty="isEmpty"
            :echartData="echartData"
            :titleData="titleData"
            type="2"
          ></gaugeChart>
        </el-col>
        <el-col
          :xs="24"
          :sm="24"
          :lg="8"
          style="height: 300px; margin-right: 10px"
        >
          <pieChart
            type="1"
            :isEmpty="isEmpty"
            :titleData="titleData"
            :echartData="echartData"
          ></pieChart>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="8" style="height: 300px">
          <pieChart
            type="2"
            :isEmpty="isEmpty"
            :titleData="titleData"
            :echartData="echartData"
            :id="searchForm.projectInfoBiPhaseId"
          ></pieChart>
        </el-col>
      </el-row>
    </div>
    <basic-container>
      <div style="text-align: center">
        {{ this.echartData.projectPhaseManageSummary || "待总结" }}
      </div>
    </basic-container>
    <basic-container>
      <div class="title"><span></span>成本分析</div>
    </basic-container>
    <div class="content">
      <el-row type="flex">
        <el-col
          :xs="24"
          :sm="24"
          :lg="8"
          style="height: 500px; margin-right: 10px"
        >
          <lineChart
            subTitle="运输成本单价"
            :id="this.searchForm.projectInfoBiPhaseId"
            :isEmpty="isEmpty"
            :isSearch="loading"
            type="1"
            :tip="`单位为非车方吨${this.titleData.unitNotCorrectCount}单不参与统计\n单位为吨土质不设置单位换算${this.titleData.soilTypeNotCorrectCount}单不参与统计\n未设置核算价或预设价的${this.titleData.settlePriceNotCorrectCount}单不参与统计\n资源运单${this.titleData.tpModeResourceNotCorrectCount}单不参与统计`"
          ></lineChart>
        </el-col>
        <el-col
          :xs="24"
          :sm="24"
          :lg="8"
          style="height: 500px; margin-right: 10px"
        >
          <lineChart
            @getSummary="getSummary"
            subTitle="机械成本"
            :id="this.searchForm.projectInfoBiPhaseId"
            :isEmpty="isEmpty"
            :isSearch="loading"
            type="2"
            :tip="`单位为非车方吨${this.titleData.unitNotCorrectCount}单不参与统计\n单位为吨土质不设置单位换算${this.titleData.soilTypeNotCorrectCount}单不参与统计\n未设置挖机型号的${this.titleData.machineSetNotCorrectCount}单不参与统计\n未设置装车价${this.titleData.machineInPriceNotCorrectCount}单不参与统计\n未设置台班费用的${this.titleData.ledgerDigPriceNotCorrectCount}单不参与统计`"
          ></lineChart>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="8" style="height: 500px">
          <lineChart
            :isEmpty="isEmpty"
            :isSearch="loading"
            :id="this.searchForm.projectInfoBiPhaseId"
            subTitle="泥尾成本"
            type="3"
            :tip="`单位为非车方吨${this.titleData.unitNotCorrectCount}单不参与统计\n单位为吨土质不设置单位换算${this.titleData.soilTypeNotCorrectCount}单不参与统计\n未设置泥尾价格的${this.titleData.garbagePriceNotCorrectCount}单不参与统计`"
          ></lineChart>
        </el-col>
      </el-row>
    </div>
    <basic-container>
      <div style="text-align: center">{{ costSummary || "待总结" }}</div>
    </basic-container>
  </div>
</template>

<script>
import tpModeBarChart from "./tpModeBarChart";
import gaugeChart from "./gaugeChart";
import pieChart from "./pieChart";
import lineChart from "./lineChart";
import {
  getPhaseListByProjectInfoId,
  getBiManageVoByProjectInfoBiPhaseId,
  getTruckTpModeTitle,
} from "@/api/chain/analysis.js";
export default {
  name: "projectCostAnalysis",
  components: {
    tpModeBarChart,
    gaugeChart,
    pieChart,
    lineChart,
  },
  data() {
    return {
      isEmpty: true,
      costSummary: "",
      loading: false,
      titleData: {
        truckTotalCount: 0, // 共出车N单
        truckCorrectCount: 0, // 运输方式-N单参与统计
        squareCorrectCount: 0, // 出土情况-N单参与统计
        unitNotCorrectCount: 0, // 单位为非车方吨N单不参与统计
        soilTypeNotCorrectCount: 0, // 单位为吨土质不设置单位换算N单不参与统计
        tpModeInnerNotCorrectCount: 0, // 回填，内转回填运单N单不参与统计
        tpModeResourceNotCorrectCount: 0, // 资源运单N单不参与统计
        priceNotCorrectCount: 0, // 未设置装车价/泥尾价/核算价/预设价N单不参与统计
        ledgerDigPriceNotCorrectCount: 0, // 未设置台班费N项不参与统计
        tpModeResourceNotCorrectCount: 0,
      },
      echartData: {
        projectPhaseManageSummary: "",
        biPhaseCostTitle: {
          totalCostSum: 0, // 总支出
          transportCostSum: 0, // 运输成本
          garbageCostSum: 0, // 泥尾成本
          machineInCostSum: 0, // 机械装车成本
          machineLedgerDigCostSum: 0, // 机械台班成本
        },
        biPhaseTruckTpModeBarData: {
          tpModeName: [],
          tpModeCount: [],
        },
        biPhaseCompletionProgressData: {
          planCompletionProgress: 0,
          realCompletionProgress: 0,
        },
        biPhaseCostProgressData: {
          planCostProgress: 0, // 计划成本占比
          totalCostProgress: 0, // 总成本占比
        },
        biPhaseCostPieData: [],
        biPhaseSoilTypePieData: [],
        biPhaseSquareTitle: {
          totalSquareSum: 0, // 总方数
          avgSquare: 0, // 均方每车
        },
      },
      searchForm: {
        projectInfoId: "",
      },
      option: {
        menuSpan: 8,
        submitText: "搜索",
        submitIcon: "el-icon-search",
        labelWidth: 80,
        column: [
          {
            label: "项目",
            prop: "projectInfoId",
            type: "select",
            props: {
              label: "projectName",
              value: "id",
            },
            change: (val) => {
              this.searchForm.projectInfoBiPhaseId = "";
              if (val.value) {
                getPhaseListByProjectInfoId({ projectInfoId: val.value }).then(
                  (res) => {
                    this.$refs.form.updateDic(
                      "projectInfoBiPhaseId",
                      res.data.data
                    );
                  }
                );
              }
            },
            dicUrl: "/chain/projectinfo/list",
            filterable: true, //是否可以搜索
            span: 8,
          },
          {
            label: "阶段",
            prop: "projectInfoBiPhaseId",
            span: 8,
            type: "select",
            props: {
              label: "projectPhaseName",
              value: "id",
            },
          },
        ],
      },
    };
  },
  created() {},
  computed: {},
  activated() {
    var myEvent = new Event("resize");
    window.dispatchEvent(myEvent);
    if (this.searchForm.projectInfoId) {
      getPhaseListByProjectInfoId({
        projectInfoId: this.searchForm.projectInfoId,
      }).then((res) => {
        this.$refs.form.updateDic("projectInfoBiPhaseId", res.data.data);
      });
    }
  },
  watch: {},
  methods: {
    getSummary(val) {
      this.costSummary = val;
    },
    searchChange(form, done) {
      console.log(form);
      if (!form.projectInfoId) {
        done();
        return this.$message.error("请选择项目");
      }
      if (!form.projectInfoBiPhaseId) {
        done();
        return this.$message.error("请选择阶段");
      }
      this.isEmpty = false;

      this.loading = true;
      Promise.all([
        this.getBiManageVoByProjectInfoBiPhaseId(),
        this.getTruckTpModeTitle(),
      ])
        .then((res) => {
          this.loading = false;
          done();
        })
        .catch(() => {
          done();
        });
    },

    getBiManageVoByProjectInfoBiPhaseId() {
      return new Promise((resolve, reject) => {
        getBiManageVoByProjectInfoBiPhaseId({
          projectInfoBiPhaseId: this.searchForm.projectInfoBiPhaseId,
        })
          .then((res) => {
            this.echartData = res.data.data;
            resolve();
          })
          .catch(() => {
            reject();
          });
      });
    },
    getTruckTpModeTitle() {
      return new Promise((resolve, reject) => {
        getTruckTpModeTitle({
          projectInfoBiPhaseId: this.searchForm.projectInfoBiPhaseId,
        })
          .then((res) => {
            this.titleData = res.data.data;
            resolve();
          })
          .catch(() => {
            reject();
          });
      });
    },
    // 搜索清空
    resetChange() {},
  },
};
</script>

<style lang="scss" scoped>
.title {
  text-align: left;
  height: 20px;
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #111528;
  span {
    display: inline-block;
    height: 16px;
    width: 4px;
    background-color: #4688f7;
    margin-right: 4px;
  }
}
.content {
  padding: 0px 10px;
  /deep/ .el-row {
    background-color: #f2f2f2;
  }
  .el-col {
    background-color: #fff;
  }
}
</style>
