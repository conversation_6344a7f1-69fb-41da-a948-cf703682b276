<template>
  <div class="history">
    <el-drawer size="70%"
               title="台班详情"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <basic-container>
        <el-tabs v-model="activeName">
          <el-tab-pane label="台班信息"
                       name="1">
            <avue-form :option="option"
                       v-model="form"></avue-form>
          </el-tab-pane>
          <el-tab-pane label="审核流程"
                       name="2">
            <div class="info"
                 style="width:400px;margin-left: 20px;margin-top: 20px;"
                 v-if="flowList&&flowList.length>0">
              <!-- color="#409eff" -->
              <div class="approvalFlow">
                <el-timeline>
                  <el-timeline-item :timestamp="item.companyPositionName"
                                    placement="top"
                                    :class="item.currentAuditFlag==1?'myActive':''"
                                    :color="item.currentAuditFlag==1?'#409eff':''"
                                    v-for="(item,index) in flowList"
                                    :key="index">
                    <i :class="item.isShow?'el-icon-arrow-up':'el-icon-arrow-down'"
                       @click="showMore(item)"
                       v-if="item.approveStatus==1"
                       style="cursor: pointer;position: absolute;right: -20px;top: 0px;font-size: 18px;"></i>
                    <div style="display:flex;align-items: center;justify-content: space-between;margin-bottom:10px">
                      <h4>{{item.approveUsername}}</h4><span>{{item.approveDatetime}}</span>
                    </div>
                    <el-input type="textarea"
                              v-if="item.approveRemark"
                              v-model="item.approveRemark"
                              :autosize="{ minRows: 3, maxRows: 8}"
                              disabled></el-input>
                    <div v-if="item.isShow">{{item.positionStaffName}}</div>
                    <div v-if="item.approvePictureUrl">
                      <el-image v-for="(item2,idx) in item.approvePictureUrl.split(',')" :key="idx" style="width: 70px; height: 70px;margin-right: 8px;margin-top: 8px;" :preview-src-list="item.approvePictureUrl.split(',')" :src="item2"  >
                      </el-image>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </div>
            <el-empty v-else></el-empty>
          </el-tab-pane>
        </el-tabs>

      </basic-container>

    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getFlowNode } from '@/api/chain/ledgerDig2'
// import poPerson from "@/views/chain/waybillProcessSettings/poPerson"
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {},
  data () {
    return {
      flowList: [],
      activeName: "1",
      form: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 120,
        detail: true,
        column: [
          {
            label: "项目",
            prop: "projectName",
          },
          {
            label: "台班编号",
            prop: "ledgerNo",
          },
          {
            label: "挖机车主姓名",
            prop: "ownerName",
          },
          {
            label: "挖机车主手机号",
            prop: "ownerMobile",
          },
          {
            label: "挖机员",
            prop: "staffName",
          },
          {
            label: "作业类型",
            prop: "ledgerType",
          },
          {
            label: "挖机班次",
            prop: "inShiftTypeName",
          },
          {
            label: "台班状态",
            prop: "auditStatus",
            type: "select", // 下拉选择
            dicData: [
              {
                label: "提交",
                value: "0",
              },
              {
                label: "审批中",
                value: "1",
              },
              {
                label: "已审批",
                value: "2",
              },
              {
                label: "已驳回",
                value: "3",
              },
              {
                label: "已删除",
                value: "7",
              },
            ],
          },
          // {
          //   label: "审核状态",
          //   prop: "approveStatus",
          //   type: "select", // 下拉选择
          //   dicData: [
          //     {
          //       label: "待审核",
          //       value: "1",
          //     },
          //     {
          //       label: "已审核",
          //       value: "2",
          //     },
          //     {
          //       label: "已驳回",
          //       value: "3",
          //     },
          //   ],
          // },
          {
            label: "挖机类型",
            prop: "inType",
          },
          {
            label: "机械型号",
            prop: "machineCode",
          },
          {
            label: "完成量",
            prop: "scheduleWork",
          },
          {
            label: "单位",
            prop: "unitWork",
          },
          {
            label: "地块",
            prop: "landName",
          },
          {
            label: "作业地点",
            prop: "address",
          },
          {
            label: "开始时间",
            prop: "startDatetime",
          },
          {
            label: "结束时间",
            prop: "endDatetime",
          },
          {
            label: "时长",
            prop: "workTime",
          },
          {
            label: "转换时长",
            prop: "workHour",
          },
          {
            label: "是否删除",
            prop: "isDel",
            type: "select", // 下拉选择
            dicData: [
              {
                label: "是",
                value: "1",
              },
              {
                label: "否",
                value: "0",
              },
            ],
          },
          {
            label: "是否签证",
            prop: "isVisa",
            type: "select", // 下拉选择
            dicData: [
              {
                label: "是",
                value: "1",
              },
              {
                label: "否",
                value: "0",
              },
            ],
          },
          {
            label: "备注",
            prop: "ledgerRemark",
          },
          {
            label: "照片凭证",
            prop: "pictureUrl",
            type: 'upload',
            listType: "picture-img",
            dataType: 'string',
          },
          {
            label: "开始码表拍照",
            prop: "beginClockUrl",
            type: 'upload',
            listType: "picture-img",
            dataType: 'string',
          },
          {
            label: "结束码表拍照",
            prop: "endClockUrl",
            type: 'upload',
            listType: "picture-img",
            width:50,
            height:50,
            dataType: 'string',
          },
          {
            label: "开始码表",
            prop: "beginClock",
          },
          {
            label: "结束码表",
            prop: "endClock",
          },
          {
            label: "加油升数",
            prop: "refuelingLitres",
          },
          {
            label: "费用(元)",
            prop: "cost",
          },
          {
            label: "是否餐补",
            prop: "isSubsidizedMeals",
          },
          {
            label: "加班时长(小时)",
            prop: "overtimeHours",
          },
          // {
          //   label: "创建日期",
          //   prop: "searchDate",
          //   valueFormat: 'yyyy-MM-dd',
          // },
          // {
          //   label: "班次日期",
          //   prop: "inShiftTime",
          //   valueFormat: 'yyyy-MM-dd',
          // },
          {
            label: "PC后台备注",
            prop: "pcLedgerRemark",
          },
          {
            label: "台班价",
            prop: "preparePrice",
          },

        ],
      },
    };
  },
  created () { },
  mounted () {
    this.form = Object.assign({},this.info)
    this.getData()
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    getData () {
      getFlowNode(this.info.ledgerType == "签证" ? 3 : 2, this.info.id).then(res => {
        this.flowList = res.data.data
      })
    },
    showMore (item) {
      console.log(item);
      this.$set(item, 'isShow', !item.isShow)
      // item.isShow = !item.isShow
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
/deep/ .myActive .el-timeline-item__tail {
  border-left-color: #409eff;
}
</style>
