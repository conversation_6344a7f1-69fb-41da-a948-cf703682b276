import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companyoutputsummary/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companyoutputsummary',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companyoutputsummary/detail/' + id,
        method: 'get'
    })
}
