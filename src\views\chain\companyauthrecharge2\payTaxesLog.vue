<template>
  <div class="payTaxesLog">
    <el-dialog title="税费付款记录"
               width="70%"
               :visible.sync="visible"
               :before-close="cancelModal">
      <avue-crud ref="crud2"
                  style="margin-bottom:20px"
                 :page="page"
                 :data="tableData"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 @on-load="getPage"
                 v-model="form">
      </avue-crud>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getPage } from '@/api/chain/companynsrsbhwallet'
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  components: {
  },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        border: true,
        stripe: true,
        columnBtn: false,
        // showHeader:false,
        index: false,
        size: "mini",
        selection: false,
        addBtn: false,
        refreshBtn: false,
        menu: false,
        // page:this.showPage,
        align: "center",
        menuAlign: "center",
        // menuType:this.menuType,
        // menuBtnTitle:'自定义名称',
        defaultSort: {
          prop: 'paymentDatetime',
          order: 'descending'
        },
        header: false,
        column: [
          {
            label: "交易时间",
            prop: "transactionDatetime",
            sortable: "custom",
            minWidth: 140,
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            overHidden: true,
          },
          {
            label: "业务流水号",
            prop: "businessSerialNo",
            sortable: "custom",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "交易类型",
            prop: "transactionType",
            type: "select", // 下拉选择
            props: {
              label: "itemName",
              value: "itemValue",
            },
            dicUrl:
              "/chain/systemdictionaryitem/listDictionaryItem?dictionary=transaction_type",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "交易金额",
            prop: "transactionAmount",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "变动前余额",
            prop: "balanceBalance",
            minWidth: 96,
            overHidden: true,
          },
          {
            label: "变动后余额",
            prop: "adjustedBalance",
            minWidth: 96,
            overHidden: true,
          },
          {
            label: "益路银行企业",
            prop: "nsrmc",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "收款人",
            prop: "accountName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "收款卡号",
            prop: "bankAccount",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "联系电话",
            prop: "mobile",
            minWidth: 100,
            overHidden: true,
          },
        ],
      },
    };
  },
  created () {
  },
  mounted () {
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    getPage (page, params) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: page.descs,
            ascs: page.ascs,
            sourceId: this.info.id,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    cancelModal () {
      this.$emit("update:visible", false);
    },

  },
};
</script>

<style lang="scss" scoped>
</style>
