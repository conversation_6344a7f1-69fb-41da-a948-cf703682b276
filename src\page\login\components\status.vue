<template>
  <div class="r">
    {{msg}}
    <el-result icon="error"  :subTitle="msg" v-if="msg">
      <template slot="extra">
        <el-button type="primary" size="medium" @click="$emit('rewrite')">重新填写</el-button>
      </template>
    </el-result>
    <el-result icon="info"  subTitle="后台审核中，审核结果会通过站内消息发送，请耐心等待" v-else>
    </el-result>
  </div>
</template>

<script>
export default {
    props:{
        msg:{
            type:String,
            default:''
        }
    }
};
</script>

<style>
.r{
    margin-top: 80px;
}
</style>
