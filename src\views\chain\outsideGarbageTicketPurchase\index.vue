<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 :row-class-name="isShowIcon"
                 v-model="form"
                 @on-load="getPage"
                 @row-save="handleSave"
                 @refresh-change="refreshChange"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="menuLeft"
                  slot-scope="{ row }">
          <el-button icon="el-icon-document-add"
                     v-if="permissions['chain:outsideGarbageTicketPurchase:buy']"
                     size="small"
                     type="primary"
                     @click="add(1)">
            购票
          </el-button>
          <el-button icon="el-icon-document-remove"
                     v-if="permissions['chain:outsideGarbageTicketPurchase:refund']"
                     size="small"
                     type="primary"
                     @click="add(2)">
            退票
          </el-button>
          <el-button icon="el-icon-download"
                     v-if="permissions['chain:outsideGarbageTicketPurchase:excel']"
                     size="small"
                     type="primary"
                     @click="exOut">
            导出
          </el-button>
        </template>
        <template slot-scope="{ row }" slot="qty">
          <span :class="row.type==2?'blue':''" @click="qtyClick(row)">{{row.qty}}</span>
        </template>
        <template slot-scope="{disabled,size}"
                  slot="qtyLabel">
          <span>{{type==2?'退票数':'采购数'}}</span>
        </template>
        <template slot-scope="{disabled,size}"
                  slot="purchaseManLabel">
          <span>{{type==2?'退票人':'采购人'}}</span>
        </template>
        <template slot-scope="{disabled,size}"
                  slot="purchaseDateLabel">
          <span>{{type==2?'退票日期':'采购日期'}}</span>
        </template>
        <template slot-scope="{disabled,size}"
                  slot="lineForm">
          -
        </template>
        <template slot="refundTicketNoForm"
                  slot-scope="scope">
          <div>
            <div style="
                  border: 1px solid #d9d9d9;
                  padding: 0px 10px;
                  border-radius: 4px;
                  line-height: 30px;
                  min-height: 32px;
                  max-height: 130px;
                  overflow-y: auto;
                  cursor: pointer;
                ">
              <span style="color:#b4bccc"
                    v-if="ticketList.length==0">
                请选择泥尾票号
              </span>
              <span v-else>
                <el-tag closable
                        v-for="(item, index) in ticketList"
                        size="mini"
                        type='info'
                        @close="preventDefault(item,index)"
                        :key="index"
                        style="margin-left:4px">
                  {{ item.ticketNo }}
                </el-tag>
              </span>
              <el-button size="small"
                         type="text"
                         style="position: absolute;right: 8px;bottom:0px;cursor: pointer;"
                         @click.stop.prevent="changeTicket">选择</el-button>
            </div>
          </div>
        </template>
      </avue-crud>
    </basic-container>
    <!-- 选择票号 -->
    <selectTicket v-if="visible3"
                  :visible.sync="visible3"
                  :list="ticketList"
                  :info="info"
                  @submit="changeTicketList"></selectTicket>
    <!-- 退票的票号 -->
    <expandTicket v-if="visible2"
                  :visible.sync="visible2"
                  :info="info"></expandTicket>
  </div>
</template>

<script>
import { getPage, addObj, queryInventoryByGarbageId, querySoilTypeByGarbage } from '@/api/chain/outsideGarbageTicketPurchase'
import { tableOption } from '@/const/crud/chain/outsideGarbageTicketPurchase'
import { mapGetters } from 'vuex'
import { expotOut } from "@/util/down.js";
import selectTicket from './selectTicket';
import expandTicket from './expandTicket';
export default {
  name: 'outsideGarbageTicketPurchase',
  components: {
    selectTicket,
    expandTicket,
  },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: 'purchase_date'//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      type: 1,
      tableOption: tableOption(this),
      ticketList: [],
      visible3: false,
      visible2: false,
      info: {},
    }
  },
  created () {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList () {
      return {
        addBtn: this.permissions['chain:outsideGarbageTicketPurchase:add'] ? true : false,
        delBtn: this.permissions['chain:outsideGarbageTicketPurchase:del'] ? true : false,
        editBtn: this.permissions['chain:outsideGarbageTicketPurchase:edit'] ? true : false,
        viewBtn: this.permissions['chain:outsideGarbageTicketPurchase:get'] ? true : false,
        excelBtn: this.permissions['chain:outsideGarbageTicketPurchase:excel'] ? true : false,
      };
    }
  },
  methods: {
    isShowIcon (row, index) {
      return row.row.type == 1 ? 'hiderow' : ''
    },
    searchChange (params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage (page, params) {
      this.tableLoading = true
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
        if (params.hasOwnProperty("soilTypeName")) {
          params.soilType = params.soilTypeName;
          delete params.soilTypeName;
        }

      }
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        type: '1,2'
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
       * @title 数据添加
       * @param row 为当前的数据
       * @param done 为表单关闭函数
       *
       **/
    handleSave: function (row, done, loading) {
      console.log(row);
      row.type = this.type
      if (this.type == 2 && row.qty > row.num) {
        this.$message.error('退票数不能大于剩余数')
        loading()
        return false
      }
      addObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '操作成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page)
    },
    add (val) {
      this.type = val
      this.$refs.crud.tableOption.column.forEach(element => {
        if (element.prop == 'num' || element.prop == 'refundTicketNo') {
          element.display = val == 2
        }
        if (element.prop == 'startPrefix' || element.prop == 'startNo' || element.prop == 'line' || element.prop == 'endNo' || element.prop == "price") {
          element.display = val != 2
        }
        // if (element.prop == 'qty') {
        //  element.disabled = val == 2
        //}
      });
      this.ticketList = []
      if (val == 2) {
        this.form.qty = this.ticketList.length
      }
      this.$set(this.tableOption, 'addTitle', val == 2 ? '退票' : '购票')

      this.$refs.crud.rowAdd()
    },
    exOut () {
      let params = Object.assign({}, { type: '1,2' }, this.paramsSearch)
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
      }
      let url = '/chain/companyticketpurchase/purchasePageExportExcel'
      expotOut(params, url, '外部泥尾票采购');
    },
    getNum (value) {
      this.form.num = ''
      this.form.refundTicketNo = ""
      queryInventoryByGarbageId({ garbageId: this.form.garbageId, soilType: value }).then(res => {
        console.log(res);
        this.form.num = res.data.data || 0
      })
    },
    getSoilType (garbageId) {
      this.form.soilType = ''
      this.$refs.crud.updateDic("soilType", [])
      // this.$refs.crud.tableOption.column[3].dicData= []
      querySoilTypeByGarbage({ garbageId }, this.type).then(res => {
        console.log(res);
        this.$refs.crud.updateDic("soilType", res.data.data)
        // this.$refs.crud.tableOption.column[3].dicData= res.data.data
        console.log(this.$refs.crud);
      })
    },
    changeTicket () {
      this.info = Object.assign({}, this.form)
      if (this.info.garbageId && this.info.soilType) {
        console.log(1111);
        this.visible3 = true
      } else {
        this.$message.error('请先选择泥尾及土质')
      }
    },
    changeTicketList (value) {
      let arr = JSON.parse(value);
      this.ticketList = arr;
      this.visible3 = false
      this.form.qty = this.ticketList.length
      this.form.refundTicketNo = this.ticketList && this.ticketList.map(item => item.ticketNo).join(",") || ''
    },
    preventDefault (item, index) {
      this.ticketList.splice(index, 1);
      this.form.qty = this.ticketList.length
      this.form.refundTicketNo = this.ticketList && this.ticketList.map(item => item.ticketNo).join(",") || ''
    },
    qtyClick(row){
      if(row.type==2){
        this.visible2 = true
        this.info = Object.assign({},row)
      }
      console.log(row)
    },
  }
}
</script>

<style lang="scss" scoped>
/deep/ .hiderow .el-table__expand-column .el-table__expand-icon {
  visibility: hidden;
}
.blue{
  color: #409eff;
  cursor: pointer;
}
</style>
