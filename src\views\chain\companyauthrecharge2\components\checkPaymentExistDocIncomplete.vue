<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%" title="证书检查" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <avue-crud ref="crud" :data="tableData"  :table-loading="tableLoading" :option="tableOption">
          <template slot="roadStatus" slot-scope="{ row }">
              <div  v-for="(item,index) in row.roadStatus" :key="index">
                <span :style="{color:item=='完整'?'green':'red'}">{{item}}</span>
              </div>
          </template>
          <template slot="truckStatus" slot-scope="{ row }">
              <div  v-for="(item,index) in row.truckStatus" :key="index">
                <span :style="{color:item=='完整'?'green':'red'}">{{item}}</span>
              </div>
          </template>
          <template slot="menuLeft" slot-scope="{ row }">
              <el-button
                  size="mini"
                  icon="el-icon-download"
                  type="primary"
                  :disabled="tableData.length<1"
                  @click="exOut"
                >
                  导出
              </el-button>

              <span class="tip" >{{tip}}</span>
          </template>
        </avue-crud>
        <div class="btns">
          <el-button type="primary" @click="continuePayment">{{submitBtnText}}</el-button>
          <el-button type="primary" @click="cancelModal">{{cancelBtnText}}</el-button>
        </div>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: ()=>{
        return []
      }
    },
    submitBtnText: {
      type: String,
      default: ()=>{
        return '继续付款'
      }
    },
    cancelBtnText: {
      type: String,
      default: ()=>{
        return '停止付款'
      }
    },
    tip: {
      type: String
    },

  },
  data() {
    return {
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: false,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: false,
        menu:false,
        refreshBtn:false,
        column: [
          {
            label: "司机姓名",
            prop: "userName",
            overHidden:true,
          },
          {
            label: "司机手机号码",
            prop: "userMobile",
            overHidden:true,
          },
          {
            label: "身份证",
            prop: "cardStatus",
            formatter: (val) => {
              if(val.cardStatus =="完整"){
                return `<span style='color:green'>${val.cardStatus}</span>`
              }else{
                return `<span style='color:red'>${val.cardStatus}</span>`
              }
            },
            overHidden:true,
          },
          {
            label: "驾驶证",
            prop: "archivesStatus",
            formatter: (val) => {
              if(val.archivesStatus =="完整"){
                return `<span style='color:green'>${val.archivesStatus}</span>`
              }else{
                return `<span style='color:red'>${val.archivesStatus}</span>`
              }
            },
            overHidden:true,
          },
          {
            label: "从业资格证",
            prop: "qualificationsStatus",
            formatter: (val) => {
              if(val.qualificationsStatus =="完整"){
                return `<span style='color:green'>${val.qualificationsStatus}</span>`
              }else{
                return `<span style='color:red'>${val.qualificationsStatus}</span>`
              }
            },
            overHidden:true,
          },
          {
            label: "行驶证",
            prop: "truckStatus",
            overHidden:true,
          },
          {
            label: "运输许可证",
            prop: "roadStatus",
            overHidden:true,
          },
        ],
      },
    }
  },
  created() {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    continuePayment(){
      this.$emit('continuePayment')
    },
    exOut(){
      this.$emit('exOut')
    },
  }
};
</script>

<style lang="scss" scoped>
/deep/ #el-drawer__title span{
  color: red;
}
.btns{
  text-align: center;
  margin-top: 10px;
}
.tip{
  color: red;
  margin-left: 15px;
}
</style>
