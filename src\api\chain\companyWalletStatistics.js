import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companyprojectwalletapply/getList',
        method: 'get',
        params: query
    })
}
export function getDetail(query) {
    return request({
        url: '/chain/companyprojectwalletapply/getDetail',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/platformservicecharge',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/platformservicecharge/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/platformservicecharge/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/platformservicecharge',
        method: 'put',
        data: obj
    })
}
