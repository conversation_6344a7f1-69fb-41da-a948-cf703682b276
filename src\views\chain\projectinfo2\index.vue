<template>
  <div class="projectInfo">
    <basic-container>
      <my-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        :before-open="beforeOpen"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="handleDel"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <!-- <template slot="soilTypeTipForm" slot-scope="scope">
          <div style="color: #f56c6c; font-size: 12px; padding-bottom: 20px">
            默认不选择，表示不限制选择，为空表示选择全部
          </div>
        </template> -->
        <!-- <template slot="muTailTipForm" slot-scope="scope">
          <div style="color: #f56c6c; font-size: 12px; padding-bottom: 20px">
            默认不选择，表示不限制选择，为空表示选择全部
          </div>
        </template> -->
        <template slot="leadingIds" slot-scope="scope">
          <span>{{ scope.row.leadingNames }}</span>
        </template>
        <template slot="memberIdsForm" slot-scope="scope">
          <div>
            <div
                  style="
                  border: 1px solid #d9d9d9;
                  padding: 0px 10px;
                  border-radius: 4px;
                  line-height: 30px;
                  min-height: 32px;
                  margin-top:5px;
                  cursor: pointer;
                "
                >
                <span style="color:#b4bccc" v-if="userList.length==0">
                  请选择项目成员
                </span>
                <span v-else>
                  <el-tag
                    closable
                    v-for="(item, index) in userList"
                    size="mini"
                    type='info'
                    @close="preventDefault(item,index)"
                    :key="item.id"
                    style="margin-left:4px"
                  >
                    {{ item.deptName }}
                  </el-tag>
                </span>
                 <el-button
                    size="small"
                    type="text"
                    style="position: absolute;right: 4px;cursor: pointer;"
                    @click.stop="visible3 = true"
                    >选择</el-button>
                </div>
          </div>
        </template>
        <!-- <template slot="weightTypeForm" slot-scope="scope">
          <el-select
            v-model="form.weightType"
            placeholder="请选择"
            @change="changeWeighType"
          >
            <el-option
              v-for="item in weighTypeList"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template> -->
        <template slot="isTicketForm" slot-scope="scope">
          <el-checkbox
            :checked="form.ticketEnable == 1"
            v-model="form.ticketEnable"
            true-label="1"
            false-label="0"
            >工地清点票数</el-checkbox
          >
          <el-checkbox
            :checked="form.isTicketInventory == 1"
            v-model="form.isTicketInventory"
            @change="changeBox"
            true-label="1"
            false-label="0"
            >泥尾票库存管理</el-checkbox
          >
          <div v-if="form.isTicketInventory == 1" style="margin-left: 136px;margin-top: 6px;">
            <el-switch
              active-value="1"
              inactive-value="0"
              active-text="出场签单是否对票号精细化管理"
              v-model="form.manualSelectTicket">
            </el-switch>
          </div>
        </template>
        <template slot="isEntranceWaybillForm" slot-scope="scope">
          <el-checkbox
            :checked="form.isEntranceWaybill == 1"
            v-model="form.isEntranceWaybill"
            true-label="1"
            false-label="0"
            >空车签单</el-checkbox
          >
          <el-checkbox
            :checked="form.isEntranceHeavyWaybill == 1"
            v-model="form.isEntranceHeavyWaybill"
            true-label="1"
            false-label="0"
            >重车签单</el-checkbox
          >
        </template>
        <template slot="annotationForm" slot-scope="scope">
          <ul
            style="
              background: #f4f3f3;
              line-height: 20px;
              padding: 8px 15px;
              font-size: 12px;
              color: red;
            "
          >
            <li>此模式是司机使用电子卡签单及APP直付，存在两种情况：</li>
            <li>
              某个司机设置直付价，司机使用APP出车、出场、卸土、收车操作后付款到司机APP钱包；
            </li>
            <li>某个司机未设置直付价，使用电子卡结算。</li>
          </ul>
        </template>
        <template slot="smsNoticeMobileForm" slot-scope="scope">
          <el-form
            label-width="180px"
            :model="mobileForm"
            ref="mobileForm"
            :inline="true"
          >
            <div
              v-for="(item, index) in mobileForm.smsNoticeMobile"
              :key="index"
            >
              <el-form-item
                style="margin-bottom: 0px"
                :style="index != 0 ? 'padding-left:136px' : ''"
                :label="index == 0 ? 'APP支付手机号码:' : ''"
                :prop="'smsNoticeMobile.' + index + '.mobile'"
                label-width="'128px'"
                :rules="[
                  { required: true, message: '请输入', trigger: 'blur' },
                  {
                    validator: isMobileNumber,
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input
                  v-model="item.mobile"
                  :style="index != 0 ? 'margin-top: 14px' : ''"
                  maxlength="11"
                  size="small"
                  placeholder="请输入手机号码"
                ></el-input>
              </el-form-item>
              <el-button
                style="margin-left: 8px; margin-top: 3px"
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="add"
                v-if="index == 0"
                >添加</el-button
              >
              <el-button
                style="margin-left: 8px; margin-top: 15px"
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="del(item, index)"
                v-else
                >删除</el-button
              >
            </div>
          </el-form>
        </template>

        <template slot="isInForm" slot-scope="scope">
          <el-checkbox
            :checked="form.isIn == 1"
            v-model="form.isIn"
            @change="changeIsIn"
            true-label="1"
            false-label="0"
            >需要签单</el-checkbox
          >
          <el-checkbox
            :checked="form.weightType == 2"
            v-model="form.weightType"
            @change="changeValue($event, 'weightType')"
            true-label="2"
            false-label="1"
            >挖机称重</el-checkbox
          >
          <el-checkbox
            :checked="form.isInSoilType == 1"
            v-model="form.isInSoilType"
            @change="changeValue($event, 'isInSoilType')"
            true-label="1"
            false-label="0"
            >挖机土质</el-checkbox
          >
          <el-checkbox
            :checked="form.isInPic == 1"
            v-model="form.isInPic"
            @change="changeValue($event, 'isInPic')"
            true-label="1"
            false-label="0"
            >挖机拍照</el-checkbox
          >
        </template>
        <template slot="manHourTypeForm" slot-scope="scope">
          <div>
            <el-tag
              :key="index"
              v-for="(tag,index) in dynamicTags"
              closable
              :disable-transitions="false"
              style="margin-right:4px"
              @close="handleClose('dynamicTags',tag)">
              {{tag}}
            </el-tag>
            <div>
              <el-input
                class="input-new-tag"
                v-model="tagName"
                ref="saveTagInput"
                size="small"
                style="width:200px;margin-left:0;margin-top:10px"
              >
              <el-button slot="suffix" @click="addTag('dynamicTags','tagName')" type="text">添加</el-button>
              </el-input>
            </div>
          </div>
        </template>
        <template slot="inJobAddressForm" slot-scope="scope">
          <div>
            <el-tag
              :key="index"
              v-for="(tag,index) in inJobAddressTags"
              closable
              :disable-transitions="false"
              style="margin-right:4px"
              @close="handleClose('inJobAddressTags',tag)">
              {{tag}}
            </el-tag>
            <div>
              <el-input
                class="input-new-tag"
                v-model="inJobAddressName"
                ref="saveTagInput"
                size="small"
                style="width:200px;margin-left:0;margin-top:10px"
              >
              <el-button slot="suffix" @click="addTag('inJobAddressTags','inJobAddressName')" type="text">添加</el-button>
              </el-input>
            </div>
          </div>
        </template>
        <template slot="inUnitWorkForm" slot-scope="scope">
          <div>
            <el-tag
              :key="index"
              v-for="(tag,index) in inUnitWorkTags"
              closable
              :disable-transitions="false"
              style="margin-right:4px"
              @close="handleClose('inUnitWorkTags',tag)">
              {{tag}}
            </el-tag>
            <div>
              <el-input
                class="input-new-tag"
                v-model="inUnitWorkName"
                ref="saveTagInput"
                size="small"
                style="width:200px;margin-left:0;margin-top:10px"
              >
              <el-button slot="suffix" @click="addTag('inUnitWorkTags','inUnitWorkName')" type="text">添加</el-button>
              </el-input>
            </div>
          </div>
        </template>
        <!-- 班次设置 -->
        <template slot="projectShiftsForm" slot-scope="scope">
          <div>
            <el-tag
              :key="index"
              v-for="(tag,index) in projectShiftsTags"
              closable
              :disable-transitions="false"
              style="margin-right:4px"
              @close="handleClose('projectShiftsTags',tag)">
              班次：{{tag.shiftName }}    &nbsp;&nbsp;时间：{{tag.shiftTimeStart}} - {{tag.shiftTimeEnd}}
            </el-tag>
            <div style="display:flex;align-items: center;margin-top:10px">
              <label>班次：</label>
              <el-input
                class="input-new-tag"
                v-model="shiftName"
                ref="saveTagInput"
                size="small"
                style="width:200px;margin-right:10px"
              >
              </el-input>
              <label>时间：</label>
              <el-time-picker
                style="width:150px !important;margin-right:10px"
                v-model="shiftTimeStart"
                value-format="HH:mm:ss"
                placeholder="开始时间">
              </el-time-picker>
              <label>-</label>
              <el-time-picker
                style="width:150px !important;margin-right:10px"
                v-model="shiftTimeEnd"
                value-format="HH:mm:ss"
                placeholder="结算时间">
              </el-time-picker>
              <el-button  @click="addShifts">添加</el-button>
            </div>
          </div>
        </template>
        <template slot="isGoForm" slot-scope="scope">
          <el-checkbox :checked="true" disabled>需要签单</el-checkbox>
        </template>

        <template slot="menu" slot-scope="scope">
          <template v-if="scope.row.projectStatus != 9">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              v-if="permissions['chain:projectinfo:edit']"
              @click="handleEdit(scope.row, scope.index)"
              >设置
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-check"
              v-if="permissions['chain:projectinfo:authorizer']"
              @click="handleComp(scope.row, scope.index)"
              >项目授权
            </el-button>
            <el-dropdown size="mini" style="margin-left:4px" v-if="permissions['chain:projectinfo:complete']||permissions['chain:projectinfo:priceApply']&&scope.row.settleType==4||
            permissions['chain:projectinfo:setSecondaryPlots']||permissions['chain:projectinfo:phaseSettings']||permissions['chain:projectinfo:fleetName']||permissions['chain:projectinfo:excavatorModel']||permissions['chain:projectinfo:personEditLog']">
              <el-button type="text"  size="mini">更多功能<i class="el-icon-arrow-down el-icon--right"></i></el-button>
                <el-dropdown-menu slot="dropdown">
                  <div class="menuList">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-check"
                      v-if="permissions['chain:projectinfo:complete']"
                      @click="handleComplete(scope.row, scope.index)"
                      >项目完成
                    </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-check"
                      v-if="permissions['chain:projectinfo:priceApply']&&scope.row.settleType==4"
                      @click="priceSet(scope.row, scope.index)"
                      >直付价申请
                    </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-setting"
                      v-if="permissions['chain:projectinfo:setSecondaryPlots']"
                      @click="plotsSet(scope.row, scope.index)"
                      >设置二级地块
                    </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-check"
                      v-if="permissions['chain:projectinfo:phaseSettings']"
                      @click="projectSet(scope.row,'phaseVisible')"
                      >项目阶段设置
                    </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-truck"
                      v-if="permissions['chain:projectinfo:fleetName']"
                      @click="projectSet(scope.row,'fleetNameVisible')"
                      >车队名称
                    </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-refrigerator"
                      v-if="permissions['chain:projectinfo:excavatorModel']"
                      @click="projectSet(scope.row,'excavatorModelVisible')"
                      >挖机型号
                    </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-document"
                      v-if="permissions['chain:projectinfo:personEditLog']"
                      @click="projectSet(scope.row,'personEditLogVisible')"
                      >人员修改记录
                    </el-button>
                    <!-- <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-attract"
                      v-if="permissions['chain:projectinfo:matchingSetting']"
                      @click="projectSet(scope.row,'matchingVisible')"
                      >撮合设置
                    </el-button> -->
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-attract"
                      v-if="permissions['chain:projectinfo:setLoadingStartingPoint']"
                      @click="projectSet(scope.row,'startPointVisible')"
                      >设置装货起点
                    </el-button>
                  </div>
                </el-dropdown-menu>
            </el-dropdown>
          </template>
        </template>
        <template slot="menuLeft" slot-scope="scope">
            <el-button
            type="primary"
            icon="el-icon-download"
            v-if="permissions['chain:projectinfo:excel']"
            size="small"
            :loading="tableLoading"
            @click="exOut"
            >导出</el-button>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-circle-plus-outline"
            v-if="permissions['chain:projectinfo:batchadd']"
            @click="handle(1)"
            >批量添加成员
          </el-button>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-remove-outline"
            v-if="permissions['chain:projectinfo:batchdel']"
            @click="handle(2)"
            >批量删除成员
          </el-button>
        </template>

        <template slot="electronicRangeForm" slot-scope="scope">
          <div style="margin-bottom: 20px; width: 50vw">
            <!-- <el-input
              v-model="form.projectAddress"
              placeholder="请在地图选择定位"
              :disabled="true"
              style="width: 40%"
            ></el-input> -->
            <!-- <span style="padding: 0 20px">范围：</span> -->
            <el-input-number
              v-model="form.electronicRange"
              :min="100"
              label="请输入"
            ></el-input-number>
          </div>

          <div style="padding-bottom: 20px">
            <el-amap-search-box
              class="search-box"
              :search-option="searchOption"
              :on-search-result="onSearchResult"
            >
            </el-amap-search-box>
          </div>
          <el-amap
            vid="amapDemo"
            :center="mapInfo.lnglat"
            :amap-manager="amapManager"
            :zoom="zoom"
            :events="events"
            class="amap-demo"
            style="height: 300px; width: 50vw"
          >
            <el-amap-marker
              ref="marker"
              vid="component-marker"
              :draggable="true"
              :events="markerEvent"
              :position="mapInfo.lnglat"
            ></el-amap-marker>
            <el-amap-circle
              :center="mapInfo.lnglat"
              :radius="form.electronicRange"
              :fillOpacity="0.2"
              :events="events"
            ></el-amap-circle>
            <!-- fillColor="#ee2200"
              strokeColor="#ee2200"
              :strokeOpacity="0.35"
              :strokeWeight="0.35" -->
            <el-amap-text
              :text="projectAddressLabel.content"
              :position="mapInfo.lnglat"
              :offset="[0, -50]"
            ></el-amap-text>
          </el-amap>
        </template>
      </my-crud>

      <el-dialog title="项目授权" :visible.sync="dialogVisible" width="30%">
        <div style="text-align: center">
          <el-select
            v-model="selectPartnersData"
            multiple
            filterable
            placeholder="请选择"
            style="width: 100%"
            value-key="id"
          >
            <el-option
              v-for="item in partnersData"
              :key="item.id"
              :label="item.agentName"
              :value="item"
            >
            </el-option>
          </el-select>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="doSubmit">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 批量添加/删除成员 -->
      <batchHandle
        v-if="batchVisible"
        v-on:getPage="changeRefesh"
        :mode="mode"
        :visible.sync="batchVisible"
      ></batchHandle>
      <!-- 选择人员框，后期调接口封装 -->
      <select-personnel
        v-if="visible3"
        :visible.sync="visible3"
        :list="userList"
        @changeUserList="changeUserList"
      ></select-personnel>
      <!-- 设置二级地块 -->
      <setSecondaryPlots
        v-if="plotsVisible"
        v-on:getPage="changeRefesh"
        :info="info"
        :visible.sync="plotsVisible"
      ></setSecondaryPlots>
      <!-- 项目阶段设置 -->
      <phaseSettings
        v-if="phaseVisible"
        :info="info"
        :visible.sync="phaseVisible"
      ></phaseSettings>
      <!-- 设置车队名称 -->
      <setFleetName
        v-if="fleetNameVisible"
        :info="info"
        :visible.sync="fleetNameVisible"
      ></setFleetName>
      <!-- 设置挖机车型 -->
      <setExcavatorModel
        v-if="excavatorModelVisible"
        :info="info"
        :visible.sync="excavatorModelVisible"
      ></setExcavatorModel>
      <!-- 人员修改记录 -->
      <personEditLog
        v-if="personEditLogVisible"
        :info="info"
        :visible.sync="personEditLogVisible"
      ></personEditLog>
      <!-- 撮合设置 -->
      <!-- <matchingSetting
        v-if="matchingVisible"
        :info="info"
        :visible.sync="matchingVisible"
      ></matchingSetting> -->
      <!-- 设置装货起点 -->
      <setStartPoint
        v-if="startPointVisible"
        :info="info"
        @refreshChange="refreshChange"
        :visible.sync="startPointVisible"
      ></setStartPoint>
    </basic-container>
  </div>
</template>

<script>
import {
  getPage,
  getObj,
  addObj,
  putObj,
  delObj,
  projectUpdate,
  listDictionaryItem,
} from "@/api/chain/projectinfo";

import {
  getLists3 as getPartnersData,
  workOffByProjectInfoId,
} from "@/api/chain/agentinfo";
import { tableOption } from "@/const/crud/chain/projectinfo2";
import { mapGetters } from "vuex";
import { AMapManager } from "vue-amap";
const amapManager = new AMapManager();
import location from "./positionLocation.js";
import batchHandle from "./batchHandle.vue";
import setSecondaryPlots from "./setSecondaryPlots.vue";
import { isMobileNumber } from "@/util/validate";
import selectPersonnel from "./components/selectPersonnel"; //选择人员
import { expotOut } from "@/util/down.js";
import phaseSettings from "./phaseSettings.vue";
import setFleetName from "./setFleetName.vue";
import setExcavatorModel from "./setExcavatorModel.vue";
import personEditLog from "./personEditLog.vue";
import setStartPoint from "./setStartPoint.vue";

export default {
  name: "projectinfo2",
  components: {
    batchHandle,
    selectPersonnel,
    setSecondaryPlots,
    phaseSettings,
    setFleetName,
    setExcavatorModel,
    personEditLog,
    setStartPoint,
  },
  data() {
    return {
      type: [],
      nowRow: {},
      dialogVisible: false,
      amapManager,
      events: {
        click: (e) => {
          console.log("jfkdjdfjd");
          this.mapInfo.lng = e.lnglat.lng;
          this.mapInfo.lat = e.lnglat.lat;
          this.mapInfo.lnglat = [e.lnglat.lng, e.lnglat.lat];
          this.form.electronicFence = e.lnglat.lng + "," + e.lnglat.lat;
          this.getFormattedAddress();
        },
      },
      markerEvent:{
        dragend: (e) => {
          console.log("dddddd",e);
          let lnglat = e.target.getPosition();
          this.mapInfo.lng = lnglat.lng;
          this.mapInfo.lat = lnglat.lat;
          this.mapInfo.lnglat = [lnglat.lng, lnglat.lat];
          this.form.electronicFence = lnglat.lng + "," + lnglat.lat;
          this.getFormattedAddress();
        }
      },
      searchOption: {
        // 限制搜索城市的范围
        citylimit: false,
      },
      partnersData: [],
      selectPartnersData: [],
      params: {},
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      // oldMapInfo: {},
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption(this),
      weighTypeList: [
        {
          id: 0,
          label: "不需要称重",
          value: "1",
        },
        {
          id: 1,
          label: "挖机称重",
          value: "2",
        },
        {
          id: 2,
          label: "出口称重",
          value: "3",
        },
      ],
      batchVisible: false,
      mode: 1,
      mapInfo: {
        lng: 114.061401,
        lat: 22.540293,
        lnglat: [114.061401, 22.540293],
      },
      mobileForm: {
        smsNoticeMobile: [
          {
            mobile: "",
          },
        ],
      },
      dynamicTags:[],
      inJobAddressTags:[],
      inUnitWorkTags:[],
      projectShiftsTags:[],
      tagName:'',
      inJobAddressName:'',
      inUnitWorkName:'',
      shiftName:"", //班次
      shiftTimeStart:null,    //班次时间
      shiftTimeEnd:null,    //班次时间
      userList:[],
      visible3:false,
      info:{},
      plotsVisible:false,
      phaseVisible:false,
      beforeMemberIds:[],
      fleetNameVisible:false,
      excavatorModelVisible:false,
      personEditLogVisible:false,
      startPointVisible:false,
    };
  },
  created() {
    console.log(this.$store.getters.userInfo);
    // 调用获取地理位置
    // this.getLocation();
  },
  activated() {
    // this.getPartnersData();
  },
  mounted: function () {
    this.getPartnersData();
    this.getCompanyLedgerType()
    this.getInUnitWork()
  },
  computed: {
    ...mapGetters(["permissions","tagList"]),
    zoom() {
      if (this.form.electronicRange <= 100) {
        return 16;
      }
      if (this.form.electronicRange > 100 && this.form.electronicRange < 700) {
        return 15;
      }
      if (this.form.electronicRange >= 700) {
        return 14;
      }
      return 13;
    },
    // mapInfo() {
    //   if (this.form.electronicFence) {
    //     console.log(111);
    //     let arr = this.form.electronicFence.split(",");
    //     console.log(arr);
    //     let center = {
    //       address: "北京市东城区东华门街道天安门",
    //       lng: arr[0],
    //       lat: arr[1],
    //       lnglat: [arr[0], arr[1]],
    //     };
    //     return center;
    //   } else {
    //     console.log(222);
    //     return {
    //       address: "广东省深圳市福田区莲花街道深南大道市民广场",
    //       lng: 114.061401,
    //       lat: 22.540293,
    //       lnglat: [114.061401, 22.540293],
    //     };
    //   }
    // },
    projectAddressLabel() {
      if (this.form.projectAddress) {
        return {
          content: this.form.projectAddress,
          textAlign: "center",
        };
      } else {
        return {
          content: "",
          offset: [20, -20],
          textAlign: "center",
        };
      }
    },
    permissionList() {
      return {
        addBtn: this.permissions["chain:projectinfo:add"] ? true : false,
        delBtn: this.permissions["chain:projectinfo:del"] ? true : false,
        editBtn: this.permissions["chain:projectinfo:edit"] ? true : false,
        viewBtn: this.permissions["chain:projectinfo:get"] ? true : false,
      };
    },
  },
  watch: {
    forms(val) {
      console.log(val);
    },
  },
  methods: {
    getCompanyLedgerType(){
      listDictionaryItem({dictionary:'company_ledger_type'}).then(res=>{
        this.dynamicTags = res.data.data.map(item=>item.itemName)
      })
    },
    getInUnitWork(){
      listDictionaryItem({dictionary:'in_unit_work'}).then(res=>{
        this.inUnitWorkTags = res.data.data.map(item=>item.itemName)
      })
    },
    isMobileNumber,
    changeIsIn(e) {
      console.log(e);
      if (e != 1) {
        this.$confirm('如取消挖机签单，所有上班挖机签单人员强制下班', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (this.form.weightType == "2") {
            this.form.weightType = "1";
          }
          this.form.isInPic = "0";
          this.form.isInSoilType = "0";
        }).catch(() => {
          this.form.isIn = '1'
        });

      }
    },
    changeValue(val, label) {
      if (
        ((label == "weightType" && val == "2") ||
          ((label == "isInPic" || label == "isInSoilType") && val == "1")) &&
        this.form.isIn == "0"
      ) {
        this.form.isIn = "1";
      }
    },
    onSearchResult(e) {
      console.log("onSearchResult");
      this.form.electronicFence = e[0].location.lng + "," + e[0].location.lat;
      this.mapInfo = {
        lng: e[0].location.lng,
        lat: e[0].location.lat,
        lnglat: [e[0].location.lng, e[0].location.lat],
      };
      this.form.projectAddress = e[0].address;
    },
    doSubmit() {
      if (this.selectPartnersData.length == 0) {
        this.$message({
          showClose: true,
          message: "请选择合作伙伴",
          type: "error",
        });
        return;
      }
      let agentInfoIds = [];
      let agentInfoNames = [];
      this.selectPartnersData.forEach((item) => {
        console.log(item);
        agentInfoIds.push(item.id);
        agentInfoNames.push(item.agentName);
      });
      let data = {
        id: this.nowRow.id,
        agentInfoIds: agentInfoIds.join(","),
        agentInfoNames: agentInfoNames.join(","),
      };
      let myAgentInfoIds =
        (this.nowRow.agentInfoIds == ""||this.nowRow.agentInfoIds ==undefined)
          ? []
          : this.nowRow.agentInfoIds.split(",");
      console.log(myAgentInfoIds);
      console.log(agentInfoIds);
      let diff = [];
      if (myAgentInfoIds && myAgentInfoIds.length > 0) {
        let set = new Set(agentInfoIds);
        diff = myAgentInfoIds.filter((v) => !set.has(v));
        console.log('diff',diff);
      }
      let add = []
      if (agentInfoIds && agentInfoIds.length > 0) {
        let set = new Set(myAgentInfoIds);
        add = agentInfoIds.filter((v) => !set.has(v));
        console.log('add',add);
      }
      //删除了之前存在的某个 需要提示
      if (diff && diff.length > 0||add&&add.length>0) {
        this.$confirm(
          `本次修改新增${add.length}人，减少${diff.length}人`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then((res) => {
            projectUpdate(data)
              .then((res) => {
                this.dialogVisible = false;
                this.selectPartnersData = [];
                this.$message({
                  showClose: true,
                  message: "授权成功",
                  type: "success",
                });
                this.getPage(this.page);
              })
              .catch((err) => {
                this.dialogVisible = false;
              });
          })
          .catch((err) => {});
      } else {
        projectUpdate(data)
          .then((res) => {
            this.dialogVisible = false;
            this.selectPartnersData = [];
            this.$message({
              showClose: true,
              message: "授权成功",
              type: "success",
            });
            this.getPage(this.page);
          })
          .catch((err) => {
            this.dialogVisible = false;
          });
      }
    },
    getPartnersData() {
      getPartnersData().then((res) => {
        console.log(res);
        this.partnersData = res.data.data;
      });
    },
    handleComp(row, index) {
      console.log(row);
      this.selectPartnersData = [];
      if (row.agentInfoIds) {
        let agentInfoIds = row.agentInfoIds.split(",");
        let agentInfoNames = row.agentInfoNames.split(",");
        let arrs = [];
        agentInfoIds.forEach((item, index) => {
          arrs.push({
            agentName: agentInfoNames[index],
            id: item,
          });
        });
        this.selectPartnersData = arrs;
      }

      this.nowRow = row;
      this.dialogVisible = true;
    },

    beforeOpen(done, type) {
      if (type == "add") {
        // setTimeout(() => {
          this.form = {
            projectName: "",
            leadingIds: [],
            memberIds: [],
            listSoilType: [],
            garbageIds: [],
            weightType: "1",
            // weightTruckTypeIds: [],
            isGo: "1",
            isIn: "1",
            isEntranceWaybill: "0",
            projectAddress: "",
            electronicRange: 500,
            electronicFence: "",
            isInPic: "1",
            isInSoilType: "1",
            ticketEnable: "0",
            isCubicPrice: "0",
            isTicketInventory: "0",
            manualSelectTicket: "0",
            overtimePeriod: 72,
            truckCodePeriod: 7,
            goInterval: 10,
            smsBalance:0,
            stopPayBalance:0,
            isInPictureUploadOffline:"0",
            isGoPictureUploadOffline:"0",
            companyWithdrawPeriod:24,
            isEntranceHeavyWaybill:'0',
            isGoVehicleType:'0',
            isGoPayDirect:'0',
            maxDirectPay:1500,
            directTriggerPoint:"1",
            directChannel:"1",
            inWaybillSaveTime: 30,  //挖机运单保留时间(分钟):
            goWaybillTipCheck:'0',
          };
          this.mobileForm = {
            smsNoticeMobile: [
              {
                mobile: "",
              },
            ],
          };
          this.mapInfo = {
            lng: 114.061401,
            lat: 22.540293,
            lnglat: [114.061401, 22.540293],
          };
          this.getCompanyLedgerType()
          this.getInUnitWork()
          this.inJobAddressTags = []
          this.tagName = ""
          this.inJobAddressName = ""
          this.inUnitWorkName = ""
          this.userList = []
          this.projectShiftsTags = [
            {
              shiftName:"白班",
              shiftTimeStart:"06:00:00",
              shiftTimeEnd:"18:00:00",
            },
            {
              shiftName:"晚班",
              shiftTimeStart:"18:00:00",
              shiftTimeEnd:"06:00:00",
            },
          ]
          this.shiftTimeStart = null
          this.shiftTimeEnd = null
          this.shiftName = ""
        // }, 100);
      } else {
        if (this.form.electronicFence) {
          let arr = this.form.electronicFence.split(",");
          this.mapInfo = {
            lng: arr[0],
            lat: arr[1],
            lnglat: [arr[0], arr[1]],
          };
        } else {
          this.$message.error("没有找到项目GPS信息");
          this.mapInfo = {
            lng: 114.061401,
            lat: 22.540293,
            lnglat: [114.061401, 22.540293],
          };
        }
        //作业类型
        if(this.form.manHourType){
          this.dynamicTags = this.form.manHourType.split(",")
          this.tagName = ""
        }
        //作业地点
        if(this.form.inJobAddress){
          this.inJobAddressTags = this.form.inJobAddress.split(",")
          this.inJobAddressName = ""
        }
        //工时单位
        if(this.form.inUnitWork){
          this.inUnitWorkTags = this.form.inUnitWork.split(",")
          this.inUnitWorkName = ""
        }
        //班次
        if(this.form.projectShifts){
          this.projectShiftsTags = this.form.projectShifts
          this.shiftTimeStart = null
          this.shiftTimeEnd = null
          this.shiftName = ""
        }
        if (this.form.smsNoticeMobile) {
          this.mobileForm.smsNoticeMobile = this.form.smsNoticeMobile
            .split(",")
            .map((mobile) => {
              return {
                mobile,
              };
            });
        }
        this.userList = []
        if(this.form.memberNames&&this.form.memberIds&&this.form.memberIds.length>0){
          let arr = this.form.memberIds
          let arr1 = this.form.memberNames.split(",")
          this.userList = arr.map((item,index)=>{
            return {
              id:item,
              deptName:arr1[index]
            }
          })
        }
        this.beforeMemberIds = this.form.memberIds||[]
      }
      console.log(this.beforeMemberIds);
      done();
    },
    // getLocation() {
    //   console.log('getLocation');
    //   let geolocation = location.initMap("amapDemo"); // 定位
    //   AMap.event.addListener(geolocation, "complete", (result) => {
    //     console.log("results", result);
    //     this.oldMapInfo = {
    //       formattedAddress: result.formattedAddress,
    //       electronicFence: result.position.lng + "," + result.position.lat,
    //     };
    //     console.log("this.oldMapInfo", this.oldMapInfo);
    //     this.form.projectAddress = result.formattedAddress;
    //     this.form.electronicFence =
    //       result.position.lng + "," + result.position.lat;
    //     console.log(this.form.electronicFence);
    //   });
    // },
    getFormattedAddress() {
      AMap.plugin("AMap.Geocoder", () => {
        let GeocoderOptions = {
          city: "全国",
        };
        let geocoder = new AMap.Geocoder(GeocoderOptions);
        console.log("geocoder", geocoder);
        geocoder.getAddress(this.mapInfo.lnglat, (status, result) => {
          console.log("通过经纬度拿到的地址", result);
          if (status === "complete" && result.info === "OK") {
            this.form.projectAddress = result.regeocode.formattedAddress;
          } else {
            this.mapInfo.address = "无法获取地址";
          }
        });
      });
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      let descs = this.page.descs;

      if (params) {
        if (params.hasOwnProperty("finishDate")) {
          params.finishDateStart = params.finishDate[0];
          params.finishDateEnd = params.finishDate[1];
          delete params.finishDate;
        }
        if (params.hasOwnProperty("startDate")) {
          params.startDateStart = params.startDate[0];
          params.startDateEnd = params.startDate[1];
          delete params.startDate;
        }
      }

      // return;
      if (descs.length == 0) {
        descs = "create_datetime";
      }

      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          response.data.data.records.map((item) => {
            // console.log(item.weightTruckTypeIds);
            // item.weightTruckTypeIds =
            //   item.weightTruckTypeIds && item.weightTruckTypeIds.length > 0
            //     ? item.weightTruckTypeIds.split(",")
            //     : [];
            let labelList = [
              "leadingIds",
              "memberIds",
              "listSoilType",
              "garbageIds",
            ]; //多选转格式
            labelList.forEach((label) => {
              if (item[label] && item[label].length > 0) {
                let arr = item[label].split(",");
                let arr2 = [];
                if (arr.length > 1) {
                  arr.forEach((item2) => {
                    arr2.push(item2);
                  });
                } else {
                  arr2 = arr;
                }
                item[label] = arr2;
              } else {
                item[label] = [];
              }
            });
            return item;
          });
          this.tableData = response.data.data.records;
          console.log(this.tableData);
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },

    handleComplete(row, index) {
      let _this = this;
      this.$confirm("是否确认完成此项目", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          console.log("row", row);
          let data = {
            id: row.id,
            projectStatus: 9,
          };

          return putObj(data);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "确认成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      if(row.settleType==4&&row.isGoPayDirect==1&&row.weightType!=3&&row.isCubicPrice!=1){
        this.$message.error("出场签单价格为直付价,请选择出口签单的出场数量或出场价格！")
        loading()
        return false
      }
      if(row.isGoPayDirect!=1){
        row.maxDirectPay = undefined
      }
      console.log("row.memberIds", row);
      row.memberIds = this.userList&&this.userList.length>0?this.userList.map(item=>item.id).join(","):"";
      row.memberNames = this.userList&&this.userList.length>0?this.userList.map(item=>item.deptName).join(","):"";
      row.leadingIds =row.leadingIds&&row.leadingIds.length > 0 ? row.leadingIds.join(",") : "";
      row.listSoilType =row.listSoilType&&row.listSoilType.length > 0 ? row.listSoilType.join(",") : "";
      row.garbageIds =row.garbageIds&&row.garbageIds.length > 0 ? row.garbageIds.join(",") : "";
      row.manHourType = this.dynamicTags.length > 0 ? this.dynamicTags.join(",") : "";
      row.inJobAddress = this.inJobAddressTags.length > 0 ? this.inJobAddressTags.join(",") : "";
      row.inUnitWork = this.inUnitWorkTags.length > 0 ? this.inUnitWorkTags.join(",") : "";
      row.projectShifts = this.projectShiftsTags
      // row.weightTruckTypeIds =
      //   row.weightTruckTypeIds.length > 0
      //     ? row.weightTruckTypeIds.join(",")
      //     : "";
      // row.weightTruckTypeNames =
      //   row.$weightTruckTypeIds.length > 0
      //     ? row.$weightTruckTypeIds.replace(" | ", ",")
      //     : "";

      row.leadingNames = row.$leadingIds
        ? row.$leadingIds.replace(/\s+\|\s+/g, ",")
        : "";
      row.garbageNames = row.$garbageIds
        ? row.$garbageIds.replace(/\s+\|\s+/g, ",")
        : "";
      if (row.settleType == 4) {
        let flag = false;
        this.$refs.mobileForm.validate((valid) => {
          if (!valid) {
            flag = true;
            this.$message.error("请输入APP支付手机号码");
          } else {
            row.smsNoticeMobile = this.mobileForm.smsNoticeMobile
              .map((item) => item.mobile)
              .join();
          }
        });
        if (flag) {
          loading()
          return;
        }
      }
      //判断新增多少人 减少多少人
      console.log();
      let memberIds = row.memberIds&&row.memberIds.split(",")||[]
      console.log(memberIds);
       let diff = [];
      if (this.beforeMemberIds && this.beforeMemberIds.length > 0) {
        let set = new Set(memberIds);
        diff = this.beforeMemberIds.filter((v) => !set.has(v));
        console.log('diff',diff);
      }
      let add = []
      if (memberIds && memberIds.length > 0) {
        let set = new Set(this.beforeMemberIds);
        add = memberIds.filter((v) => !set.has(v));
        console.log('add',add);
      }
      if (diff && diff.length > 0||add&&add.length>0) {
      this.$confirm(
          `本次修改项目成员新增${add.length}人，减少${diff.length}人`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).then(()=>{
          putObj(row).then((response) => {
              if (response.data.code == 789) {
                let _this = this;
                this.$confirm(response.data.msg, "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                }).then(() => {
                  workOffByProjectInfoId({
                    projectInfoId: row.id,
                    staffId: response.data.data,
                  })
                    .then(() => {
                      // _this.$message({
                      //   showClose: true,
                      //   message: "该员工已下班成功，再次点击确认移除该员工",
                      //   type: "success",
                      // });
                      console.log(row);
                      putObj(row)
                        .then((res) => {
                          this.$message({
                            showClose: true,
                            message: "修改成功",
                            type: "success",
                          });
                          this.getPage(this.page);
                          done();
                        })
                        .catch(() => {
                          console.log("err");
                          loading();
                        });
                      // loading();
                    })
                    .catch(() => {
                      console.log("catch");
                      loading();
                    });
                }).catch(() => {
                      loading();
                    });
              } else {
                this.$message({
                  showClose: true,
                  message: "修改成功",
                  type: "success",
                });
                this.getPage(this.page);
                done();
              }
            })
            .catch(() => {
              console.log("err");
              loading();
            });
        }).catch((err) => {
          loading();
        });
      }else{
        putObj(row).then((response) => {
              if (response.data.code == 789) {
                let _this = this;
                this.$confirm(response.data.msg, "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                }).then(() => {
                  workOffByProjectInfoId({
                    projectInfoId: row.id,
                    staffId: response.data.data,
                  })
                    .then(() => {
                      // _this.$message({
                      //   showClose: true,
                      //   message: "该员工已下班成功，再次点击确认移除该员工",
                      //   type: "success",
                      // });
                      console.log(row);
                      putObj(row)
                        .then((res) => {
                          this.$message({
                            showClose: true,
                            message: "修改成功",
                            type: "success",
                          });
                          this.getPage(this.page);
                          done();
                        })
                        .catch(() => {
                          console.log("err");
                          loading();
                        });
                      // loading();
                    })
                    .catch(() => {
                      console.log("catch");
                      loading();
                    });
                }).catch(() => {
                      loading();
                    });
              } else {
                this.$message({
                  showClose: true,
                  message: "修改成功",
                  type: "success",
                });
                this.getPage(this.page);
                done();
              }
            })
            .catch(() => {
              console.log("err");
              loading();
            });
      }
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      console.log(row, "row");
      if(row.settleType==4&&row.isGoPayDirect==1&&row.weightType!=3&&row.isCubicPrice!=1){
        this.$message.error("出场签单价格为直付价,请选择出口签单的出场数量或出场价格！")
        loading()
        return false
      }
      if(row.isGoPayDirect!=1){
        row.maxDirectPay = undefined
      }
      row.memberIds = this.userList&&this.userList.length>0?this.userList.map(item=>item.id).join(","):"";
      row.memberNames = this.userList&&this.userList.length>0?this.userList.map(item=>item.deptName).join(","):"";
      row.leadingIds =row.leadingIds&&row.leadingIds.length > 0 ? row.leadingIds.join(",") : "";
      row.listSoilType =row.listSoilType&&row.listSoilType.length > 0 ? row.listSoilType.join(",") : "";
      row.garbageIds =row.garbageIds&&row.garbageIds.length > 0 ? row.garbageIds.join(",") : "";
      row.manHourType = this.dynamicTags.length > 0 ? this.dynamicTags.join(",") : "";
      row.inJobAddress = this.inJobAddressTags.length > 0 ? this.inJobAddressTags.join(",") : "";
      row.inUnitWork = this.inUnitWorkTags.length > 0 ? this.inUnitWorkTags.join(",") : "";
      row.projectShifts = this.projectShiftsTags

      // row.weightTruckTypeIds =
      //   row.weightTruckTypeIds.length > 0
      //     ? row.weightTruckTypeIds.join(",")
      //     : "";
      // row.weightTruckTypeNames =
      //   row.$weightTruckTypeIds.length > 0
      //     ? row.$weightTruckTypeIds.replace(" | ", ",")
      //     : "";
      row.leadingNames = row.$leadingIds
        ? row.$leadingIds.replace(/\s+\|\s+/g, ",")
        : "";
      row.garbageNames = row.$garbageIds
        ? row.$garbageIds.replace(/\s+\|\s+/g, ",")
        : " ";
      row.projectStatus = 1;
      if (row.settleType == 4) {
        let flag = false;
        this.$refs.mobileForm.validate((valid) => {
          if (!valid) {
            flag = true;
            this.$message.error("请输入APP支付手机号码");
            // return false;
          } else {
            row.smsNoticeMobile = this.mobileForm.smsNoticeMobile
              .map((item) => item.mobile)
              .join();
          }
        });
        if (flag) {
          loading()
          return;
        }
      }
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    /**
     * 新增删除人员回调
     */
    changeRefesh(page) {
      this.$message({
        showClose: true,
        message: "操作成功",
        type: "success",
      });
      this.getPage(this.page);
    },
    // handleEdit 是自己自定义的编辑按钮方法
    handleEdit(row, index) {
      this.$refs.crud.rowEdit(row, index);
    },
    // changeWeighType(val) {
    //   console.log(val);
    //   var column = this.findObject(this.tableOption.column,'weightTruckTypeIds');
    //   console.log(column);
    //   if (val != 1) {
    //     column.display = true
    //     // this.$refs.crud.tableOption.column[9].display = true;
    //   } else {
    //     // this.$refs.crud.tableOption.column[9].display = false;
    //     column.display = false
    //   }
    // },
    handle(value) {
      this.batchVisible = true;
      this.mode = value;
    },
    add() {
      this.mobileForm.smsNoticeMobile.push({
        mobile: "",
      });
    },
    del(item, index) {
      this.mobileForm.smsNoticeMobile.splice(index, 1);
    },
    priceSet(row){
      let info = row
      console.log(this.tagList)
      let tag = this.findTag('/directPayPriceSet/index').tag;
      if(tag) this.$store.commit("DEL_TAG", tag);
      this.$router.push({path:'/directPayPriceSet/index',query:{info:JSON.stringify(info)}})
    },
    plotsSet(row){
      this.info = JSON.parse(JSON.stringify(row))
      //地块
      this.info.landParcel = row.landParcel?row.landParcel.split(","):[]
      this.plotsVisible = true
    },
    projectSet(row,dialog){
      this.info = JSON.parse(JSON.stringify(row))
      //地块 车队名称 挖机型号
      this[dialog] = true
      console.log(111);
    },
    findTag(url) {
      let tag, key;
      this.tagList.map((item, index) => {
        if (item.value.includes(url)) {
          tag = item;
          key = index;
        }
      });
      return { tag: tag, key: key };
    },
    handleClose(list,tag) {
      this[list].splice(this[list].indexOf(tag), 1);
    },
    addTag(list,name){
      if(!this[name]){
        this.$message.error("请输入内容后再添加")
        return false
      }
      if(this[list].includes(this[name])){
        this.$message.error("已存在，请勿重复添加")
      }else{
        this[list].push(this[name])
      }
    },
    addShifts(){
      if(!this.shiftName||!this.shiftTimeStart||!this.shiftTimeEnd){
        this.$message.error("请输入班次跟时间")
        return false
      }

      // 验证班次不能重名，中间时间不允许重叠和首尾时间可以
      let isTrue = false
      this.projectShiftsTags&&this.projectShiftsTags.length>0&&this.projectShiftsTags.forEach(item=>{
        if(item.shiftName ==this.shiftName){
          this.$message.error("班次名称不能重复")
          isTrue = true
          return false
        }
        // else if(this.shiftTimeStart>=item.shiftTimeStart&&this.shiftTimeStart<item.shiftTimeEnd){
        //   this.$message.error("班次时间不能重叠")
        //   isTrue = true
        //   return false
        // }else if(this.shiftTimeEnd>item.shiftTimeStart&&this.shiftTimeEnd<=item.shiftTimeEnd){
        //   this.$message.error("班次时间不能重叠")
        //   isTrue = true
        //   return false
        // }else if(this.shiftTimeStart<item.shiftTimeStart&&this.shiftTimeEnd>=item.shiftTimeEnd){
        //   this.$message.error("班次时间不能重叠")
        //   isTrue = true
        //   return false
        // }
      })
      if(isTrue) return false
      this.projectShiftsTags.push({
        shiftName:this.shiftName,
        shiftTimeStart:this.shiftTimeStart,
        shiftTimeEnd:this.shiftTimeEnd
      })
    },
    changeUserList(value) {
      let arr = JSON.parse(value);
      this.userList = arr;
    },
     preventDefault(item, index) {
      this.userList.splice(index, 1);
    },
    exOut() {
      let params = Object.assign({},this.paramsSearch)
      if (params) {
        if (params.hasOwnProperty("finishDate")) {
          params.finishDateStart = params.finishDate[0];
          params.finishDateEnd = params.finishDate[1];
          delete params.finishDate;
        }
        if (params.hasOwnProperty("startDate")) {
          params.startDateStart = params.startDate[0];
          params.startDateEnd = params.startDate[1];
          delete params.startDate;
        }
      }
      this.tableLoading=true
      let url = "/chain/projectinfo/exportExcel";
      // params.id = this.info.id
      expotOut(params, url, "项目管理").then(()=>{
        this.tableLoading=false
      }).catch(()=>{
        this.tableLoading=false
      });
    },
    changeBox(val){
      if(val==0){
        this.form.manualSelectTicket = '0'
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.amap-overlay-text-container {
  font-size: 9px;
}
/deep/ .avue-crud__dialog .el-dialog .el-dialog__body {
  padding-left: 20px;
}
.tip {
  font-size: 12px;
  color: red;
  line-height: 40px;
  margin-left: 6px;
}
.menuList {
  width:120px;
  padding:2px 20px 2px 0px;
  line-height:34px;
  :hover{
      background-color:#f3f4f5;
    }
    >.el-button{
      margin-left: 10px;
    }
}
</style>
<style lang="scss">
.customProjectDialog .avue-form .el-row .avue-group:nth-child(3) {
  .avue-group__header {
    margin-bottom: 4px;
  }
}
.customProjectDialog .red{
    .el-input__inner{
      font-size: 12px;
      color: red !important;
      padding-left: 0;
    }
}
</style>
