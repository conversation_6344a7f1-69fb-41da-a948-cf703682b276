<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="70%"
      title="服务费用"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <header style="text-align:center;font-size:18px;margin-bottom:20px;color:#000;">{{info.companyName+$moment(info.presentDate).format("YYYY年MM月")}}服务费用</header>
        <avue-crud
          ref="crud"
          :data="tableData"
          :table-loading="tableLoading"
          :option="tableOption"
          v-model="form"
          @on-load="getPage"
        >
        </avue-crud>
        <ul class="count">
          <li>放飞：{{countForm.waybillFlyingCount}}单</li>
          <li>运费：{{countForm.waybillFreightCount}}单</li>
          <li>资源：{{countForm.waybillResourcesCount}}单</li>
          <li>回填：{{countForm.waybillBackFillCount}}单</li>
          <li>内运：{{countForm.waybillInternalTransportCount}}单</li>
        </ul>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getListByCompanyNameAndPresentDate } from "@/api/chain/platformservicecharge";
import { expotOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  data() {
    return {
      form: {},
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 12,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchMenuSpan: 6,
        index: true,
        refreshBtn: false,
        menu: false,
        searchShowBtn: false,
        header: false,
        showSummary: true,
        sumColumnList: [
            {
              name: 'waybillTotal',
              type: 'sum'
            },
            {
              name: 'khyWaybillTotal',
              type: 'sum'
            },
            {
              name: 'chargeWaybillTotal',
              type: 'sum'
            },
            {
              name: 'chargeTotal',
              type: 'sum'
            },
          ],
        column: [
          {
            label: "项目名称",
            prop: "projectName",
          },
          {
            label: "运单总数",
            prop: "waybillTotal",
          },
          {
            label: "开票运单数",
            prop: "khyWaybillTotal",
          },
          {
            label: "收费运单数",
            prop: "chargeWaybillTotal",
          },
          {
            label: "单价(单/元)",
            prop: "platformFee",
          },
          {
            label: "金额",
            prop: "chargeTotal",
          },
        ],
      },
      countForm:{}
    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    getPage(params = {}) {
      this.tableLoading = true;
      getListByCompanyNameAndPresentDate(
        Object.assign(
          {
            presentDate: this.info.presentDate,
            companyAuthId: this.info.companyAuthId,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.platformServiceChargeSettleDetailVo;
          this.countForm = response.data.data;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>

/deep/ .avue-crud__pagination{
  display: none;
}
.count{
  display: flex;
  margin-top: 20px;
  color: #000;
  li{
    margin-right: 50px;
  }
}
</style>
