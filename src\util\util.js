import {validatenull} from './validate'
import request from '@/router/axios'
import * as CryptoJS from'crypto-js'
import printJS from 'print-js';
import html2canvas from 'html2canvas';
// 表单序列化
export const serialize = data => {
  let list = []
  Object.keys(data).forEach(ele => {
    list.push(`${ele}=${data[ele]}`)
  })
  return list.join('&')
}
export const getObjType = obj => {
  var toString = Object.prototype.toString
  var map = {
    '[object Boolean]': 'boolean',
    '[object Number]': 'number',
    '[object String]': 'string',
    '[object Function]': 'function',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object RegExp]': 'regExp',
    '[object Undefined]': 'undefined',
    '[object Null]': 'null',
    '[object Object]': 'object'
  }
  if (obj instanceof Element) {
    return 'element'
  }
  return map[toString.call(obj)]
}
/**
 * 对象深拷贝
 */
export const deepClone = data => {
  var type = getObjType(data)
  var obj
  if (type === 'array') {
    obj = []
  } else if (type === 'object') {
    obj = {}
  } else {
    // 不再具有下一层次
    return data
  }
  if (type === 'array') {
    for (var i = 0, len = data.length; i < len; i++) {
      obj.push(deepClone(data[i]))
    }
  } else if (type === 'object') {
    for (var key in data) {
      obj[key] = deepClone(data[key])
    }
  }
  return obj
}
/**
 * 判断路由是否相等
 */
export const diff = (obj1, obj2) => {
  delete obj1.close
  var o1 = obj1 instanceof Object
  var o2 = obj2 instanceof Object
  if (!o1 || !o2) { /*  判断不是对象  */
    return obj1 === obj2
  }

  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false
    // Object.keys() 返回一个由对象的自身可枚举属性(key值)组成的数组,例如：数组返回下表：let arr = ["a", "b", "c"];console.log(Object.keys(arr))->0,1,2;
  }

  for (var attr in obj1) {
    var t1 = obj1[attr] instanceof Object
    var t2 = obj2[attr] instanceof Object
    if (t1 && t2) {
      return diff(obj1[attr], obj2[attr])
    } else if (obj1[attr] !== obj2[attr]) {
      return false
    }
  }
  return true
}
/**
 * 设置灰度模式
 */
export const toggleGrayMode = (status) => {
  if (status) {
    document.body.className = document.body.className + ' grayMode'
  } else {
    document.body.className = document.body.className.replace(' grayMode', '')
  }
}
/**
 * 设置主题
 */
export const setTheme = (name) => {
  document.body.className = name
}

/**
 *加密处理
 */
export const encryption = (params) => {
  let {
    data,
    type,
    param,
    key
  } = params
  const result = JSON.parse(JSON.stringify(data))
  if (type === 'Base64') {
    param.forEach(ele => {
      result[ele] = btoa(result[ele])
    })
  } else {
    param.forEach(ele => {
      var data = result[ele]
      key = CryptoJS.enc.Latin1.parse(key)
      var iv = key
      // 加密
      var encrypted = CryptoJS.AES.encrypt(
        data,
        key, {
          iv: iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.ZeroPadding
        })
      result[ele] = encrypted.toString()
    })
  }
  return result
}

/**
 * 浏览器判断是否全屏
 */
export const fullscreenToggel = () => {
  if (fullscreenEnable()) {
    exitFullScreen()
  } else {
    reqFullScreen()
  }
}
/**
 * esc监听全屏
 */
export const listenfullscreen = (callback) => {
  function listen() {
    callback()
  }

  document.addEventListener('fullscreenchange', function() {
    listen()
  })
  document.addEventListener('mozfullscreenchange', function() {
    listen()
  })
  document.addEventListener('webkitfullscreenchange', function() {
    listen()
  })
  document.addEventListener('msfullscreenchange', function() {
    listen()
  })
}
/**
 * 浏览器判断是否全屏
 */
export const fullscreenEnable = () => {
  return document.isFullScreen || document.mozIsFullScreen || document.webkitIsFullScreen
}

/**
 * 浏览器全屏
 */
export const reqFullScreen = () => {
  if (document.documentElement.requestFullScreen) {
    document.documentElement.requestFullScreen()
  } else if (document.documentElement.webkitRequestFullScreen) {
    document.documentElement.webkitRequestFullScreen()
  } else if (document.documentElement.mozRequestFullScreen) {
    document.documentElement.mozRequestFullScreen()
  }
}
/**
 * 浏览器退出全屏
 */
export const exitFullScreen = () => {
  if (document.documentElement.requestFullScreen) {
    document.exitFullScreen()
  } else if (document.documentElement.webkitRequestFullScreen) {
    document.webkitCancelFullScreen()
  } else if (document.documentElement.mozRequestFullScreen) {
    document.mozCancelFullScreen()
  }
}
/**
 * 递归寻找子类的父类
 */

export const findParent = (menu, id) => {
  for (let i = 0; i < menu.length; i++) {
    if (menu[i].children.length !== 0) {
      for (let j = 0; j < menu[i].children.length; j++) {
        if (menu[i].children[j].id === id) {
          return menu[i]
        } else {
          if (menu[i].children[j].children.length !== 0) {
            return findParent(menu[i].children[j].children, id)
          }
        }
      }
    }
  }
}

/**
 * 动态插入css
 */

export const loadStyle = url => {
  const link = document.createElement('link')
  link.type = 'text/css'
  link.rel = 'stylesheet'
  link.href = url
  const head = document.getElementsByTagName('head')[0]
  head.appendChild(link)
}
/**
 * 判断路由是否相等
 */
export const isObjectValueEqual = (a, b) => {
  let result = true
  Object.keys(a).forEach(ele => {
    const type = typeof (a[ele])
    if (type === 'string' && a[ele] !== b[ele]) result = false
    else if (type === 'object' && JSON.stringify(a[ele]) !== JSON.stringify(b[ele])) result = false
  })
  return result
}
/**
 * 根据字典的value显示label
 */
export const findByvalue = (dic, value) => {
  let result = ''
  if (validatenull(dic)) return value
  if (typeof (value) === 'string' || typeof (value) === 'number' || typeof (value) === 'boolean') {
    let index = 0
    index = findArray(dic, value)
    if (index !== -1) {
      result = dic[index].label
    } else {
      result = value
    }
  } else if (value instanceof Array) {
    result = []
    let index = 0
    value.forEach(ele => {
      index = findArray(dic, ele)
      if (index !== -1) {
        result.push(dic[index].label)
      } else {
        result.push(value)
      }
    })
    result = result.toString()
  }
  return result
}
/**
 * 根据字典的value查找对应的index
 */
export const findArray = (dic, value) => {
  for (let i = 0; i < dic.length; i++) {
    if (dic[i].value === value) {
      return i
    }
  }
  return -1
}
/**
 * 生成随机len位数字
 */
export const randomLenNum = (len, date) => {
  let random = ''
  random = Math.ceil(Math.random() * 100000000000000).toString().substr(0, len || 4)
  if (date) random = random + Date.now()
  return random
}
/**
 * 打开小窗口
 */
export const openWindow = (url, title, w, h) => {
  // Fixes dual-screen position                            Most browsers       Firefox
  const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : screen.left
  const dualScreenTop = window.screenTop !== undefined ? window.screenTop : screen.top

  const width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width
  const height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height

  const left = ((width / 2) - (w / 2)) + dualScreenLeft
  const top = ((height / 2) - (h / 2)) + dualScreenTop
  const newWindow = window.open(url, title, 'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=yes, copyhistory=no, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left)

  // Puts focus on the newWindow
  if (window.focus) {
    newWindow.focus()
  }
}

export const arraySort = (list = [], prop, callback) => {
  return list.filter(ele => !validatenull(ele[prop])).sort((a, b) => callback(a, b)).concat(list.filter(ele => validatenull(ele[prop])));
}
/**
 *  <img> <a> src 处理
 * @returns {PromiseLike<T | never> | Promise<T | never>}
 */
export function handleImg(fileName, id) {
  return validatenull(fileName) ? null : request({
    url: '/upms/file/' + fileName,
    method: 'get',
    responseType: 'blob'
  }).then((response) => { // 处理返回的文件流
    let blob = response.data;
    let img = document.getElementById(id);
    img.src = URL.createObjectURL(blob);
    window.setTimeout(function () {
      window.URL.revokeObjectURL(blob)
    }, 0)
  })
}

export const filterForm = (form) => {
  let obj = {};
  Object.keys(form).forEach(ele => {
    if (!validatenull(form[ele])) {
      obj[ele] = form[ele]
    }
  });
  return obj;
}

export const vaildData = (val, dafult) => {
  if (typeof val === 'boolean') {
    return val;
  }
  return !validatenull(val) ? val : dafult;
};

//控制只能输入小数点后2位
export const clearNoNum = (value)=>{
  value = value+''
  value = value.replace(/[^\d.\-]/g, "");  //清除“数字”和“.”以外的字符
  value = value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
  value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  value = value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数
  if (value.indexOf(".") < 0 && value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
    value = parseFloat(value);
  }
  return Number(value);
}
/**
 * 字符串数据类型转化
 */
export const detailDataType = (value, type) => {
  if (validatenull(value)) return value
  if (type === 'number') {
    return Number(value);
  } else if (type === 'string') {
    return value + '';
  } else {
    return value;
  }
};
export function getAsVal (obj, bind = '') {
  let result = deepClone(obj);
  if (validatenull(bind)) return result;
  bind.split('.').forEach(ele => {
    result = !validatenull(result[ele]) ? result[ele] : '';
  });
  return result;
}
export function setAsVal (obj, bind = '', value) {
  let result;
  let type = getObjType(value)
  if (validatenull(value)) {
    if (type === 'array') {
      result = `obj.${bind}=[]`
    } else if (type === 'object') {
      result = `obj.${bind}={}`
    } else if (['number', 'boolean'].includes(type)) {
      result = `obj.${bind}=undefined`
    } else {
      result = `obj.${bind}=''`
    }
  } else {
    if (type == 'string') {
      result = `obj.${bind}='${value}'`;
    } else {
      result = `obj.${bind}=${value}`;
    }
  }
  eval(result);
  return obj;
}
/**
 * 设置px像素
 */
export const setPx = (val, defval = '') => {
  if (validatenull(val)) val = defval;
  if (validatenull(val)) return '';
  val = val + '';
  if (val.indexOf('%') === -1) {
    val = val + 'px';
  }
  return val;
};

/**
 * 过滤字典翻译字段和空字段
 */
export const filterParams = (form, list = ['', '$'], deep = true) => {
  let data = deep ? deepClone(form) : form
  for (let o in data) {
    if (list.includes('')) {
      if (validatenull(data[o])) delete data[o];
    }
    if (list.includes('$')) {
      if (o.indexOf('$') !== -1) delete data[o];
    }

  }
  return data
};

/**
 * 过滤字典翻译字段和空字段
 */
export const filterDefaultParams = (form, translate = true) => {
  let data = deepClone(form);
  if (translate) return data;
  for (let o in data) {
    if (o.indexOf('$') !== -1 || validatenull(data[o])) {
      delete data[o];
    }
  }
  return data;
};
/**
 * 数组的数据类型转化
 */
export const detailDic = (list = [], props = {}, type) => {
  let valueKey = props.value || "value";
  let childrenKey = props.children || "children";
  list.forEach(ele => {
    ele[valueKey] = detailDataType(ele[valueKey], type);
    if (ele[childrenKey]) detailDic(ele[childrenKey], props, type);
  });
  return list;
};
/**
 * 获取多层data
 */
export const getDeepData = (res) => {
  return (Array.isArray(res) ? res : res.data) || [];
};
export const getObjValue = (data, params = '', type) => {
  const list = params.split('.');
  let result = data;
  if (list[0] === '' && type !== 'object') {
    return getDeepData(data);
  } else if (list[0] !== '') {
    list.forEach(ele => {
      result = result[ele];
    });
  }
  return result;
};

export const isMediaType = (url, type) => {
  const typeList = {
    img: /\.(gif|jpg|jpeg|png|webp|svg|GIF|JPG|JPEG|PNG|WEBP|SVG)/,
    video: /\.(swf|avi|flv|mpg|rm|mov|wav|asf|3gp|mkv|rmvb|ogg|mp4)/,
    audio: /\.(mp3|wav|MP3|WAV)/,
  };
  if (validatenull(url)) return
  if (typeList.audio.test(url) || type == 'audio') {
    return 'audio'
  } else if (typeList.video.test(url) || type == 'video') {
    return 'video'
  } else if (typeList.img.test(url) || type == 'img') {
    return 'img'
  }
  return
}
/**
 * 饼状图百分比算法--最大余额法
 * @param valueList 数值数组
 * @param idx 索引下标
 * @param precision 精确度
 */
export const getPercentWithPrecision = (
  valueList,
  idx,
  precision=2
) => {
  if (!valueList[idx]) {
    return 0
  }

  const sum = valueList.reduce((acc, val) => {
    return acc + (isNaN(val) ? 0 : val)
  }, 0)
  if (sum === 0) {
    return 0
  }

  const digits = Math.pow(10, precision)
  const votesPerQuota = valueList.map(val => {
    return ((isNaN(val) ? 0 : val) / sum) * digits * 100
  })
  const targetSeats = digits * 100

  const seats = votesPerQuota.map(votes => {
    // Assign automatic seats.
    return Math.floor(votes)
  })
  let currentSum = seats.reduce((acc, val) => {
    return acc + val
  }, 0)

  const remainder = votesPerQuota.map((votes, idx) => {
    return votes - seats[idx]
  })

  // Has remainding votes.
  while (currentSum < targetSeats) {
    // Find next largest remainder.
    let max = Number.NEGATIVE_INFINITY
    let maxId = null
    for (let i = 0, len = remainder.length; i < len; ++i) {
      if (remainder[i] > max) {
        max = remainder[i]
        maxId = i
      }
    }

    // Add a vote to max remainder.
    ++seats[maxId]
    remainder[maxId] = 0
    ++currentSum
  }

  return seats[idx] / digits
}


export const print = (dom,style) => {
  const printContent = dom
  const width = printContent.clientWidth;
  const height = printContent.clientHeight;
  // 创建一个canvas节点
  const canvas = document.createElement('canvas');

  const scale = 4; // 定义任意放大倍数，支持小数；越大，图片清晰度越高，生成图片越慢。
  canvas.width = width * scale; // 定义canvas 宽度 * 缩放
  canvas.height = height * scale; // 定义canvas高度 *缩放
  canvas.style.width = width * scale + 'px';
  canvas.style.height = height * scale + 'px';
  canvas.getContext('2d').scale(scale, scale); // 获取context,设置scale

  console.log(height);
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop; // 获取滚动轴滚动的长度
  const scrollLeft = document.documentElement.scrollLeft || document.body.scrollLeft; // 获取水平滚动轴的长度
  html2canvas(printContent, {
    canvas,
    backgroundColor: null,
    useCORS: true,
    windowHeight: document.body.scrollHeight,
    scrollX: -scrollLeft, // 解决水平偏移问题，防止打印的内容不全
    scrollY: -scrollTop
  }).then((canvas) => {
    const url = canvas.toDataURL('image/png')
    console.log(url);
    printJS({
    printable: url,
    type: 'image',
    base64:true,
    documentTitle: '', // 标题
    style: style?style:'@media print { @page {size: auto; margin: 0; } body{margin:0 5px}}' // 去除页眉页脚
    // style: '@page{size:auto;margin: 0cm 1cm 0cm 1cm;border:1px solid white}@media print {html {  height: auto;margin: 0px;}body {}}' // 去除页眉页脚
    })
  }).catch(err=>{
    console.error(err);
  })
};


export const downloadPDF = (dom) => {
  return new Promise((resolve, reject) => {
    const printContent = dom
    const width = printContent.clientWidth;
    const height = printContent.clientHeight;
    // 创建一个canvas节点
    const canvas = document.createElement('canvas');

    const scale = 4; // 定义任意放大倍数，支持小数；越大，图片清晰度越高，生成图片越慢。
    canvas.width = width * scale; // 定义canvas 宽度 * 缩放
    canvas.height = height * scale; // 定义canvas高度 *缩放
    canvas.style.width = width * scale + 'px';
    canvas.style.height = height * scale + 'px';
    canvas.getContext('2d').scale(scale, scale); // 获取context,设置scale

    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop; // 获取滚动轴滚动的长度
    const scrollLeft = document.documentElement.scrollLeft || document.body.scrollLeft; // 获取水平滚动轴的长度

    html2canvas(printContent, {
      canvas,
      backgroundColor: '#fff',
      useCORS: true,
      windowHeight: document.body.scrollHeight,
      scrollX: -scrollLeft, // 解决水平偏移问题，防止打印的内容不全
      scrollY: -scrollTop,
    }).then((canvas) => {
      let contentWidth = canvas.width;

      let contentHeight = canvas.height;

      //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高

      let imgWidth = 595.28;

      let imgHeight = 592.28 / contentWidth * contentHeight;

      //let imgHeight = 700/contentWidth * contentHeight;

      //一页pdf显示html页面生成的canvas高度;

      var pageHeight = contentWidth / 592.28 * 841.89;

      let totalHeight = contentHeight;

      // 第一个参数： l：横向  p：纵向

      // 第二个参数：测量单位（"pt"，"mm", "cm", "m", "in" or "px"）

      let pdf = new jsPDF("p", "pt");

      let position = 0;

      if (totalHeight < pageHeight) {

          pdf.addImage( canvas.toDataURL("image/jpeg", 1.0), 'JPEG', 0, 0, imgWidth, imgHeight);

      } else {

          while (totalHeight > 0) {

              pdf.addImage( canvas.toDataURL("image/jpeg", 1.0), 'JPEG', 0, position, imgWidth, imgHeight)

              totalHeight -= pageHeight;

              position -= 858.89;

              //避免添加空白页

              if (totalHeight > 0) {

                  pdf.addPage();

              }

          }

      }
      resolve(true)
      pdf.save("PDF.pdf");
    }).catch(err=>{
      reject(err)
      console.error(err);
    })
  })

};
/**
 * 防抖
 */
export const debounce =(func, interval)=>{
  let timer;
  return function() {
    timer && clearTimeout(timer)
    let args = arguments;
    timer = setTimeout(() => {
      func.apply(this, args)
      timer = null;
    }, interval)
  }
}
/**
 * 节流1 使用时间戳实现的节流函数会在第一次触发事件时立即执行，以后每过 delay 秒之后才执行一次，并且最后一次触发事件不会被执行
 */
export const throttle = (func, interval)=>{
  // last为上一次触发回调的时间
  let last = 0;
  // 将throttle处理结果当作函数返回
  return function() {
    // 保留调用时的this上下文
    let context = this;
    // 保留调用时传入的参数
    let args = arguments;
    // 记录本次触发回调的时间
    let now = Date.now();
    // 判断上次触发的时间和本次触发的时间差是否小于时间间隔的阈值
    if (now - last >= interval) {
      // 如果时间间隔大于我们设定的时间间隔阈值，则执行回调
      last = now
      func.apply(this, args);
    }
  }
}
/**
 * 节流2 定时器实现的节流函数在第一次触发时不会执行，而是在 interval秒之后才执行，当最后一次停止触发后，还会再执行一次函数
 */
export const throttle1 =(func, interval)=>{
  let sign = true;
  return function() {
    // 在函数开头判断标志是否为 true，不为 true 则中断函数
    if (!sign) return;
    //  sign 设置为 false，防止执行之前再被执行
    sign = false;
    setTimeout(() => {
      func.apply(this, arguments)
      // 执行完事件之后，重新将这个标志设置为 true
      sign = true;
    }, interval)
  }
}
