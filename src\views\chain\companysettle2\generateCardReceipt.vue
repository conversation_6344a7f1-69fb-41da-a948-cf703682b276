<template>
  <div class="execution">
    <el-drawer
      size="920px"
      title="交卡回执单"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
      :wrapperClosable="false"
      center
    >
      <basic-container>
        <el-collapse
          v-model="activeNames"
          v-for="(item, index) in lists"
          :key="index"
        >
          <el-collapse-item :title="'交卡回执：' + item.receipt" :name="index">
            <div class="dialogContent" :ref="'print' + (index + 1)">
              <div class="dialogTitle">
                <span  style="border-bottom:1px solid #9a9a9a;padding:0px 20px 4px;"> <span >{{ item.companyAuthName }}</span></span>
                结算卡交卡回执单
              </div>

              <div class="table">
                <!-- <div class="tableTitle">
                <span>承运人:</span><span class="red">共计车，元</span>
              </div> -->

                <div class="inner">
                  <div class="tableTitle ">
                    <span>交卡回执单号:{{ item.giveCardNo }}</span
                    ><span>交卡人:{{ item.giveCardUserId }}</span>
                  </div>
                  <el-table :data="item.list" border class="table">
                    <el-table-column
                      property="cardType"
                      label="卡类型"
                    ></el-table-column>
                    <el-table-column
                      property="settleCardNo"
                      label="结算卡号"
                    ></el-table-column>
                    <el-table-column
                      property="projectName"
                      label="项目名称"
                    ></el-table-column>
                    <!-- <el-table-column
                    property="perPrice"
                    label="单价"
                  ></el-table-column> -->
                    <el-table-column
                      property="giveCardCnt"
                      label="运单总数"
                    ></el-table-column>
                    <el-table-column
                      property="giveCardDatetime"
                      label="交卡日期"
                    ></el-table-column>
                  </el-table>
                  <div class="tableTitle bottom">
                    <span>合计:</span
                    ><span> {{ getTotal(item.list)}}</span>
                  </div>
                </div>
              </div>
              <ul class="info" style="margin-top: 30px">
              <li>
                <label>甲方签名：</label>
                <!-- <span>{{accountForm.id}}</span> -->
              </li>
              <li>
                <label>乙方签名：</label>
                <!-- <span>{{accountForm.name}}</span> -->
              </li>
              <li>
                <label>日期：</label>
                <!-- <span>{{accountForm.projectName}}</span> -->
              </li>
              <li>
                <label>日期：</label>
                <!-- <span>{{accountForm.num}}</span> -->
              </li>
            </ul>
            </div>

            <div class="btn">
              <el-button
                size="small"
                type="primary"
                @click="downExport(item.giveCardNo)"
                >导出</el-button
              >
              <el-button size="small" :loading="btnLoading" @click="print(item.giveCardNo)">下载PDF打印</el-button>
            </div>

          </el-collapse-item>
        </el-collapse>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { expotOut } from "@/util/down.js";
import { getCardReceipt,downloadReceiptPdf } from "@/api/chain/companysettle";
export default {
  name: "generateCardReceipt",
  components: {},
  props: {
    cardReceiptList: {
      type: Array,
    },
    visible: {
      type: Boolean,
    },
  },
  data() {
    return {
      lists: [],
      // cardReceiptList: [],
      selectGenerateCardReceiptList: [],
      activeNames: [0],
      accountForm: {},
      btnLoading:false
    };
  },
  created() {},
  mounted: function () {
    // this.cardReceiptList = this.$route.query.cardInfo;
    this.getData();
  },
  computed: {},
  methods: {
    getTotal(list){
      return list.reduce((pre,cur)=>{
            return pre+cur.giveCardCnt
        },0)
    },
    cancelModal() {
      this.$emit("update:visible", false);
    },
    getData() {
      getCardReceipt(this.cardReceiptList).then((res) => {
        this.lists = res.data.data;
      });
    },
    //导出
    downExport(giveCardNo) {
      let params = {
        giveCardNo: giveCardNo,
        code: "CompanyCardGiverExcelExport",
      };
      let url = "/chain/excelExport/createByCode";
      expotOut(params, url, "交卡回执单");
    },
    print(giveCardNo) {
      this.btnLoading = true
      downloadReceiptPdf(this.cardReceiptList).then(res=>{
        console.log(res);
        const url = window.URL.createObjectURL(new Blob([res.data]));
		    const link = document.createElement('a');
        link.href = url;
        let fileName = "交卡回执单.pdf" //
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        this.btnLoading = false
      }).catch(err=>{
        this.btnLoading = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.info{
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  // border-bottom: 1px solid #ccc;
  padding-bottom: 10px;
  li{
    text-align: center;

    width: 50%;
    line-height: 36px;
    color: #333;
    label{
      display: inline-block;
      width: 96px;
    }
    span{
      color: #666;
    }
  }
}
.tableTitle {
  border: 1px solid #9a9a9a;
  margin-left: 1px;
  border-bottom: none;
  line-height: 32px;
  padding: 0px 10px;
  // color: #606266;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &.bottom{
    border-top: none;
    border-bottom: 1px solid #9a9a9a;
  }
}
.red {
  color: red;
}
/deep/ .el-table {
  border-bottom: 1px solid #9a9a9a;
  border-right: 1px solid #9a9a9a;
  tr {
    .el-table__cell {
      border-left: 1px solid #9a9a9a;
      border-top: 1px solid #9a9a9a;
    }
    border-right: 1px solid #9a9a9a;
  }
  .el-table__row {
    td {
      padding: 4px 0px;
    }
  }
  .el-table__header {
    th {
      padding: 4px 0px;
    }
  }
}
.dialogContent {
  padding: 20px;
  // border: 1px solid #ccc;
  // margin-bottom: 30px;
  .dialogTitle {
    font-size: 18px;
    font-weight: 700;
    text-align: center;
    line-height: 40px;
    // border-bottom: 1px solid #ccc;
    // margin-bottom: 20px;
  }
  .myTable {
    padding: 0 10px;
  }
}
.total {
  margin-top: 40px;
  background-color: #f2f2f2;
  color: #333;
  font-weight: 700;
  line-height: 40px;
  text-align: center;
}
.btn {
  margin-top: 30px;
  text-align: center;
}
</style>
