export const option = {
    labelWidth: 120,
    emptyBtn:false,
    group: [
        //弹窗区分
        {
            label: "项目设置",
            arrow: false,
            prop: "group1",
            column: [
                {
                    label: "总方量(方)",
                    prop: "totalSquare",
                    type: "number",
                    controls: false,
                    minRows: 0,
                    maxRows: 999999999.99,
                    precision: 2,
                    append: '方',
                    rules: [
                        {
                            required: true,
                            message: "请输入",
                            trigger: "blur",
                        },
                    ],
                },
                {
                    label: "总运输成本(元)",
                    labelWidth: 120, //in pixels, optional.  defaults to 120 if omitted.  it's not a good idea to set this value.  It
                    prop: "totalTransportCost",
                    type: "number",
                    controls: false,
                    minRows: 0,
                    maxRows: 999999999.99,
                    precision: 2,
                    append: '元',
                    rules: [
                        {
                            required: true,
                            message: "请输入",
                            trigger: "blur",
                        },
                    ],
                },
                {
                    label: "默认一车(方)",
                    prop: "defaultTruckCapacity",
                    type: "number",
                    append: '方',
                    controls: false,
                    minRows: 0,
                    maxRows: 999999999.99,
                    precision: 2,
                    value: 11.6,
                    rules: [
                        {
                            required: true,
                            message: "请输入",
                            trigger: "blur",
                        },
                    ],
                },
                {
                    label: "",
                    prop: "tabs",
                    formslot: true,
                    labelWidth: 0,
                    span: 24, //in pixels, optional.  defaults to 24 if omitted.  it's not a good idea to set this value.  It's
                }
            ]
        }
    ]

}

export const phaseOption = {
    submitBtn: false,
    emptyBtn: false,
    labelWidth: 120,
    group: [
        //弹窗区分
        {
            label: "",
            arrow: false,
            prop: "group1",
            column: [
                {
                    label: "时间",
                    prop: "projectPhaseDate",
                    type: "daterange",
                    format: 'yyyy-MM-dd',
                    valueFormat: 'yyyy-MM-dd',
                    startPlaceholder: '开始日期',
                    endPlaceholder: '结束日期',
                    rules: [
                        {
                            required: true,
                            message: "请选择",
                            trigger: "change",
                        },
                    ],
                },
                {
                    label: "预期运输成本(元)",
                    labelWidth: 180,
                    prop: "projectPhaseTransportCost",
                    type: "number",
                    formslot:true,
                    append: '方',
                    controls: false,
                    minRows: 0,
                    maxRows: 999999999.99,
                    precision: 2,
                    rules: [
                        {
                            required: true,
                            message: "请输入",
                            trigger: "blur",
                        },
                    ],
                },
                {
                    label: "计划出土(方)",
                    prop: "projectPhaseSquareCost",
                    type: "number",
                    append: '方',
                    formslot:true,
                    controls: false,
                    minRows: 0,
                    maxRows: 999999999.99,
                    precision: 2,
                    rules: [
                        {
                            required: true,
                            message: "请输入",
                            trigger: "blur",
                        },
                    ],
                },
                {
                    label: "预期运输单位成本(元/方)",
                    disabled: true,
                    labelWidth: 180,
                    prop: "projectPhaseSquarePrice",
                    type: "number",
                    append: '方',
                    controls: false,
                    minRows: 0,
                    maxRows: 999999999.99,
                    precision: 2,
                    rules: [
                        {
                            required: true,
                            message: "请输入",
                            trigger: "blur",
                        },
                    ],
                },
                {
                    label: "勘测出土(方)",
                    prop: "projectPhaseRealSquareCost",
                    type: "number",
                    append: '方',
                    controls: false,
                    minRows: 0,
                    maxRows: 999999999.99,
                    precision: 2,
                },
                {
                    label: "",
                    prop: "soilsTable",
                    formslot: true,
                    labelWidth: 0,
                    span: 24, //in pixels, optional.  defaults to 24 if omitted.  it's not a good idea to set this value.  It's
                }
            ]
        },
        //弹窗区分
        {
            label: "总结",
            arrow: false,
            prop: "group2",
            column: [
                {
                    label:'经营情况阶段',
                    prop:'projectPhaseManageSummary',
                    type:'textarea',
                    rows:5
                 },
                 {
                    label:'成本分析阶段',
                    prop:'projectPhaseCostSummary',
                    type:'textarea',
                    rows:5
                 }
            ]
        },

    ]
}

export const tableOption = {
    delBtn:false,
    emptyBtn:false, 
    addBtn: false,
    searchShowBtn: false,
    refreshBtn: false,
    columnBtn: false,
    searchMenu: false,
    header: true,
    border: true,
    align: 'center',
    editBtn: false,
    selection: true,
    menuAlign: 'center',
    column: [
        {
            label: '土质',
            prop: 'soilType'
        }, {
            label: '1吨/方',
            prop: 'convertFactor',
            cell:true
        }
    ]
}