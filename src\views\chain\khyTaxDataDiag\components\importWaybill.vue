<template>
  <div>
    <avue-crud ref="crud" :page.sync="khyHome.page" :data="tableData" :table-loading="tableLoading"
      :option="tableOption" v-model="form"  @on-load="getPage"
      @refresh-change="refreshChange" @sort-change="sortChange" @search-change="searchChange">
      <template slot="menuLeft" slot-scope="{ row }">
        <el-button type="primary" size="small" icon="el-icon-upload2" @click="uploadVisible=true">导入</el-button>
        <el-button type="primary" size="small" icon="el-icon-view" @click="errorInfoVisible = true">查看最近一次导入</el-button>
      </template>
    </avue-crud>
    <uploadImport ref="uploadImport" v-if="uploadVisible" :visible.sync="uploadVisible" @imtExcel="imtExcel" :templateUrl="templateUrl"></uploadImport>
    <!-- 最近一次导入信息查询 -->
    <importErrInfo v-if="errorInfoVisible" :visible.sync="errorInfoVisible">
    </importErrInfo>
  </div>
</template>

<script>
import {
  mapGetters
} from "vuex";
import {importThirdWaybillExcel} from "@/api/chain/companywaybill";
import uploadImport from "@/components/uploadImport";
import importErrInfo from "./importErrInfo.vue";
export default {
  name: "khyTaxDataDiag",
  components: {
    uploadImport,
    importErrInfo,
  },
  inject: ["khyHome"],
  props: {
    tableData: {
      type: Array,
      default () {
        return [];
      },
    },
    btnsLoading: {
      type: Boolean,
      default () {
        return false;
      },
    },
  },
  data () {
    return {
      form: {},
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: true,
        border: true,
        indexLabel: "序号",
        stripe: true,
        menuAlign: "center",
        align: "center",
        menuType: "text",
        searchShow: false,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        searchMenuSpan: 6,
        labelWidth: 120,
        menu: false,
        selection: false,
        column: [{
            label: "批次号",
            prop: "batchNo",
          },
          {
            label: "导入运单数",
            prop: "importCount",
          },
          {
            label: "操作人",
            prop: "createName",
          },
          {
            label: "导入时间",
            prop: "createDatetime",
          },
        ],
      },
      selectList: [],
      btnLoading: false,
      uploadVisible: false,
      errorInfoVisible: false,
      templateUrl:"https://jyb-app.obs.cn-south-1.myhuaweicloud.com:443/1%2F92ffca52-06d5-4f22-8745-2f37828eab27.xlsx"
    };
  },
  created () { },
  mounted () { },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList () {
      return {
        addBtn: this.permissions["chain:khyTaxDataDiag:add"] ? true : false,
        delBtn: this.permissions["chain:khyTaxDataDiag:del"] ? true : false,
        editBtn: this.permissions["chain:khyTaxDataDiag:edit"] ? true : false,
        viewBtn: this.permissions["chain:khyTaxDataDiag:get"] ? true : false,
        excelBtn: this.permissions["chain:khyTaxDataDiag:excel"] ? true : false,
      };
    },
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.khyHome.page.currentPage = 1;
      this.getPage(this.khyHome.page, params);
      done();
    },
    sortChange (val) {
      this.$emit("sortChange", val);
    },
    getPage (page, params) {
      this.$emit("getPage", page);
    },

    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.khyHome.page);
    },
    //上传表格
    imtExcel (formData) {
      importThirdWaybillExcel(formData)
        .then((res) => {
          this.uploadVisible = false;
          this.$refs.uploadImport.init();
          this.errorInfoVisible = true;
        })
        .catch((err) => {
          this.$refs.uploadImport.btnLoading = false;
          this.$message.error(err.data.msg || "导入失败");
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
