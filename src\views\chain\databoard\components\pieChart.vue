<template>
  <div class="Echarts">
    <div ref="pieChart"
         style="width: 100%;height: 100%;"
         ></div>
    <!-- <div class="empty"
         v-else>
      <el-empty description="暂无数据"></el-empty>
    </div> -->
  </div>
</template>

<script>
export default {
  name: "pieChart",
  props: {
    option: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
  },
  data () {
    return {
      myChart: null,
    };
  },
  watch: {
    option: {
      handler(val) {
        // console.log('更新',val);
        if (this.myChart) {
          let option = val
          this.myChart.clear()
          this.myChart.setOption(option);
          // this.resizeHanlder()
        }
      },
      deep: true,
    },
  },
  mounted () {
    this.myEcharts();
    window.addEventListener("resize", this.resizeHanlder);
  },
  activated(){
    this.resizeHanlder()
  },
  methods: {
    myEcharts () {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(this.$refs.pieChart);
      // 使用刚指定的配置项和数据显示图表。
      console.log(this.option);
      this.myChart.setOption(this.option);
    },
    resizeHanlder () {
      if (this.myChart) {
        this.myChart.resize();
      }
    },
  },
  beforeDestroy () {
    if (!this.myChart) {
      return;
    }
    window.removeEventListener("resize", this.resizeHanlder);
    this.myChart.dispose();
    this.myChart = null;
  },
};
</script>

<style lang="scss" scoped>
.Echarts{
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
