<template>
  <div class="setPayee">
    <el-dialog width="800px"
               title="生成资金支付计划"
               center
               :visible.sync="visible"
               :before-close="cancelModal"
               :close-on-click-modal="false">
               <el-row>
                <el-col :span="12">
                  <div class="flex flex-items-center" style="margin-bottom:20px">
                    <span style="width:96px;">
                      <span style="color:red">*</span>计划名称：
                    </span>
                    <el-input  v-model.trim="paymentPlanName" size="small" clearable placeholder="请输入计划名称"></el-input>
                  </div>
                </el-col>
               </el-row>
      <avue-crud  :option="editOption" :data="tableData"></avue-crud>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="cancelModal">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitPayee">确认生成</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    tableData:{
      type: Array,
      default: ()=>{
        return []
      },
    },
  },
  data () {
    return {
      tableLoading: false,
      editOption: {
        menu:false,
        header:false,
        page:false,
        column: [
          {
            label: "承运人",
            prop: "payeeName",
            minWidth:70,
            overHidden:true,
          },
          {
            label: "手机号码",
            prop: "payeeMobile",
            minWidth:100,
            overHidden:true,
          },
          {
            label: "银行卡号",
            prop: "bindingBankNo",
            minWidth:160,
            overHidden:true,
          },
          {
            label: "核算运单数",
            prop: "settleCount",
            minWidth:100,
            overHidden:true,
          },
          {
            label: "核算金额",
            prop: "settlePrice",
            minWidth:80,
            overHidden:true,
          },
          {
            label: "核算驳回运单数",
            prop: "rejectCount",
            minWidth:90,
            overHidden:true,
          },
          {
            label: "核算驳回金额",
            prop: "rejectPrice",
            minWidth:90,
            overHidden:true,
          },
        ],
        labelWidth: 120,
        submitBtn: false,
        position: "left",
        emptyBtn: false,
        cancelBtn: true,
      },
      loading: false,
      paymentPlanName:"",
    };
  },
  created () { },
  mounted: function () {},
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    changLoading () {
      this.loading = false
    },
    submitPayee () {
      if(!this.paymentPlanName){
        this.$message.error('请输入计划名称')
        return false
      }
      this.loading = true
      this.$emit("submit", this.paymentPlanName, this.changLoading);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
