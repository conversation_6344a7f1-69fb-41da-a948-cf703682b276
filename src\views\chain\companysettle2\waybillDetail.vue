<template>
  <div class="merchantDetail">
    <el-drawer
      size="800px"
      title="运单详情"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
      center
    >
      <el-descriptions title="运单项目信息">
        <el-descriptions-item label="运单号">{{ billForm.no}}</el-descriptions-item>
        <el-descriptions-item label="结算价">{{billForm.settlePrice}}</el-descriptions-item>
        <el-descriptions-item label="异常金额">{{billForm.exAmt}}</el-descriptions-item>
        <el-descriptions-item label="增减值">{{billForm.adjustAmt}}</el-descriptions-item>
        <el-descriptions-item label="容量">{{billForm.load}}</el-descriptions-item>
        <!-- <el-descriptions-item label="备注">{{billForm.remark}}</el-descriptions-item> -->
        <el-descriptions-item label="AI诊断">{{billForm.aiIssue}}</el-descriptions-item>
        <el-descriptions-item label="泥尾">{{billForm.names}}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{billForm.completeDatetime}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="项目信息">
        <el-descriptions-item label="项目合作方">{{billForm.agentName}}</el-descriptions-item>
        <el-descriptions-item label="项目负责人">{{billForm.leadingNames}}</el-descriptions-item>
        <el-descriptions-item label="项目名称">{{billForm.projectName}}</el-descriptions-item>
        <el-descriptions-item label="项目地址">{{billForm.projectAddress}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="挖机签单信息">
        <el-descriptions-item label="挖机签单员">{{billForm.inName}}</el-descriptions-item>
        <el-descriptions-item label="挖机签单时间">{{billForm.inDatetime}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="出场签单信息">
        <el-descriptions-item label="出场签单员">{{billForm.goName}}</el-descriptions-item>
        <el-descriptions-item label="出场签单时间">{{billForm.goDatetime}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="运输信息">
        <el-descriptions-item label="车队长">{{billForm.captainName}}</el-descriptions-item>
        <el-descriptions-item label="司机">{{billForm.driverName}}</el-descriptions-item>
        <el-descriptions-item label="车牌号">{{billForm.truckCode}}</el-descriptions-item>
        <el-descriptions-item label="运输方式">{{filterTpMode(billForm.tpMode)}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="图片信息" :column="1">
        <el-descriptions-item label="挖机签单图片" >
          <span v-if="billForm.goPicture">
            <el-tooltip class="item" effect="dark" content="此处只显示一张，有多张请点击图片预览" placement="top-start">
              <el-image
                style="width: 80px; height: 80px"
                :src="filterImg(billForm.goPicture)"
                :preview-src-list="filterImgs(billForm.goPicture)">
              </el-image>
            </el-tooltip>
            <span v-if="filterImgs(billForm.goPicture).length>1">多张图片点击图片进行预览</span>
          </span>
          <span v-else>暂无图片信息</span>
        </el-descriptions-item>
        <el-descriptions-item label="出口签单图片" >
          <span v-if="billForm.inPicture">
            <el-tooltip class="item" effect="dark" content="此处只显示一张，有多张请点击图片预览" placement="top-start">
              <el-image
                style="width: 80px; height: 80px"
                :src="filterImg(billForm.inPicture)"
                :preview-src-list="filterImgs(billForm.inPicture)">
              </el-image>
            </el-tooltip>
            <span v-if="filterImgs(billForm.inPicture).length>1">多张图片点击图片进行预览</span>
          </span>
          <span v-else>暂无图片信息</span>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="轨迹信息" :colon="false">
        <el-descriptions-item>
          <div v-if="lineArr&&lineArr.length>0" style="position:relative">
            <div id="maps" style="height:500px;width:700px"></div>
              <div class="input-card">
                <h4>轨迹回放控制</h4><span><el-slider v-model="speed" :min="30" :max="2000"></el-slider></span>
                <div class="input-item">
                  <el-row class="my-row" :gutter="12">
                    <el-col :span="12">
                      <el-button type="primary" size="small" @click="startAnimation">开始动画</el-button>
                    </el-col>
                    <el-col :span="12">
                      <el-button type="primary" size="small" @click="pauseAnimation">暂停动画</el-button>
                    </el-col>
                  </el-row>
                  <el-row class="my-row" :gutter="12">
                    <el-col :span="12">
                      <el-button type="primary" size="small" @click="resumeAnimation">继续动画</el-button>
                    </el-col>
                    <el-col :span="12">
                      <el-button type="primary" size="small" @click="stopAnimation">停止动画</el-button>
                    </el-col>
                  </el-row>
                </div>
            </div>
          </div>
          <div v-else>
            暂无轨迹信息
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script>
import { clearNoNum } from "@/util/util.js";
import { printWaybillDetail } from "@/api/chain/companysettle";
import {getGpsList} from "@/api/chain/companywaybill";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    //数据
    billForm: {},
  },
  data() {
    return {
      marker:null,
      map:null,
      polyline:null,
      speed:50,
      firstArr:[113.98074, 22.55251],
      lineArr:[[121.5389385, 31.21515044], [121.5389385, 31.29615044], [121.5273285, 31.21515044]]
    };
  },
  mounted() {
    this.track()
  },
  computed: {
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    filterImg(imgUrl) {
      console.log(imgUrl);
      let url = null
      if(!!imgUrl&&imgUrl!=''){
        url = imgUrl.split(',')
      }
      console.log(url);
      return url==null?null:url[0]
    },
    filterImgs(imgUrl) {
      console.log(imgUrl);
      let url = null
      if(!!imgUrl&&imgUrl!=''){
        url = imgUrl.split(',')
      }
      console.log(url);
      return url==null?[]:url
    },
    track() {
         this.lineArr = []
         this.firstArr = []
         this.marker = null
       getGpsList({companyWaybillId:this.billForm.companyWaybillId}).then(res=>{
        if(res.data.data&&res.data.data.length>0){
          res.data.data.forEach(item=>{
            if(item.gps){
              let arr = item.gps.split(',')
              this.lineArr.push(arr)
            }
          })
          this.firstArr = this.lineArr[0]
          this.trackVisible = true;
            console.log(this.lineArr);
           setTimeout(() => {
              this.initMap();
              this.initroad();
            }, 500);
        }
      })

    },
    //初始化地图
    initMap() {
      this.map = new AMap.Map("maps", {
        resizeEnable: true, //窗口大小调整
        center: this.firstArr, //中心 firstArr: [116.478935, 39.997761],
        zoom: 17,
      });
           // 创建一个车 Icon
    var car = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(25, 34),
        // 图标的取图地址
        image: 'https://webapi.amap.com/images/car.png',
        // 图标所用图片大小
        imageSize: new AMap.Size(25, 15),
    });
      this.marker = new AMap.Marker({
        map: this.map,
        position: this.firstArr,
        icon: car,
        offset: new AMap.Pixel(-26, -13),
        autoRotation: true,
        angle:-90,
      });
         // 创建一个起点 Icon
    var startIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(25, 34),
        // 图标的取图地址
        image: '//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png',
        // 图标所用图片大小
        imageSize: new AMap.Size(135, 40),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(-9, -3)
    });
      let startMarker = new AMap.Marker({
        map: this.map,
        position: this.firstArr,
        icon: startIcon,
        offset: new AMap.Pixel(-13, -30)
      });
         // 创建一个终点 Icon
    var endIcon = new AMap.Icon({
        size: new AMap.Size(25, 34),
        image: '//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png',
        imageSize: new AMap.Size(135, 40),
        imageOffset: new AMap.Pixel(-95, -3)
    });
      let endMarker = new AMap.Marker({
        map: this.map,
        position: this.lineArr[this.lineArr.length-1],
        icon: endIcon,
        offset: new AMap.Pixel(-13, -30)
      });
    },
    //初始化轨迹
    initroad(row) {
      //绘制还未经过的路线
      this.polyline = new AMap.Polyline({
        map: this.map,
        path: this.lineArr,
        showDir: true,
        strokeColor: "#28F", //线颜色--蓝色
        // strokeOpacity: 1,     //线透明度
        strokeWeight: 6, //线宽
        // strokeStyle: "solid"  //线样式
      });
      // // 绘制路过了的轨迹
      var passedPolyline = new AMap.Polyline({
        map: this.map,
        strokeColor: "#AF5", //线颜色-绿色
        //path: this.lineArr,
        // strokeOpacity: 1,     //线透明度
        strokeWeight: 6, //线宽
        // strokeStyle: "solid"  //线样式
      });
      this.marker.on("moving", (e) => {
        passedPolyline.setPath(e.passedPath);
      });
      this.map.setFitView(); //合适的视口
    },
    startAnimation() {
      this.marker.moveAlong(this.lineArr, this.speed);
    },
    pauseAnimation() {
      this.marker.pauseMove();
    },
    resumeAnimation() {
      this.marker.resumeMove();
    },
    stopAnimation() {
      this.marker.stopMove();
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header > :first-child {
  text-align: center;
}
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
.el-descriptions{
  margin-bottom: 20px;
}
/deep/ .el-descriptions__title{
  padding-left: 8px;
  text-align: left;
  height: 16px;
  line-height: 16px;
  font-size: 16px;
  color: #111528;
  border-left: 4px solid #4688f7;
}
/deep/ .el-descriptions__body{
  padding-left: 20px;
}
.input-card{
  position: absolute;
  right: 40px;
  bottom: 40px;
  background-color: #fff;
  padding:10px;
  .my-row{
    margin-top: 15px;
  }
}
</style>
