import request from '@/router/axios'

export function getBoard(query) {
    return request({
        url: '/chain/companystaff/statistics',
        method: 'get',
        params: query
    })
}


export function queryMachineOwnerPcIndexStat(query) {
    return request({
        url: '/chain/companymachineowner/queryMachineOwnerPcIndexStat',
        method: 'get',
        params: query
    })
}
export function achievementStatics(data) {
    return request({
        url: '/chain/captainStatics/achievementStatics',
        method: 'post',
        data
    })
}
export function garbageManagerStatistics(params) {
    return request({
        url: '/chain/companystaff/garbageManagerStatistics',
        method: 'get',
        params
    })
}
//白板出车运单总数获取
export function getWaybillCount(params) {
  return request({
      url: '/chain/statisticsboard/getWaybillCount',
      method: 'get',
      params
  })
}
//白板车队长总数获取
export function getDriverCaptainCount(params) {
  return request({
      url: '/chain/statisticsboard/getDriverCaptainCount',
      method: 'get',
      params
  })
}
//白板泥尾总数获取
export function getGarbageCount(params) {
  return request({
      url: '/chain/statisticsboard/getGarbageCount',
      method: 'get',
      params
  })
}
//白板挖机装车数台班数获取
export function getExcavatorLoadingAndDigCount(params) {
  return request({
      url: '/chain/statisticsboard/getExcavatorLoadingAndDigCount',
      method: 'get',
      params
  })
}
//运单费运单数获取
export function getWaybillAmountCount(data) {
    return request({
        url: '/chain/companynsrsbhwallet/getWaybillAmountCount',
        method: 'post',
        data
    })
}
//白板总出土量获取
export function getExcavatedNumber(data) {
    return request({
        url: '/chain/statisticsboard/getExcavatedNumber',
        method: 'post',
        data
    })
}
//数据看板运单分析-统计
export function getWaybillAnalyzeCount(data) {
    return request({
        url: 'chain/companywaybill/getWaybillAnalyzeCount',
        method: 'post',
        data
    })
}
//异常运单诊断饼图获取
export function getCompanyWaybillUpdateHitTargetCount(data) {
    return request({
        url: '/chain/companywaybillupdatehittarget/getCompanyWaybillUpdateHitTargetCount',
        method: 'post',
        data
    })
}
//出土分析柱状图
export function outputAnalysis(data) {
    return request({
        url: '/chain/companydashboardstatistic/outputAnalysis',
        method: 'post',
        data
    })
}
//车队长增长趋势
export function captainIncreaseTrend(data) {
    return request({
        url: '/chain/companydashboardstatistic/captainIncreaseTrend',
        method: 'post',
        data
    })
}
//挖机台班分析
export function digAnalysis(data) {
    return request({
        url: '/chain/companydashboardstatistic/digAnalysis',
        method: 'post',
        data
    })
}
//付款分析
export function paymentAnalysis(data) {
    return request({
        url: '/chain/companydashboardstatistic/paymentAnalysis',
        method: 'post',
        data
    })
}
//泥尾分析饼图
export function garbageAnalysis(data) {
    return request({
        url: '/chain/companydashboardstatistic/garbageAnalysis',
        method: 'post',
        data
    })
}
//获取项目列表
export function getProjectList(data) {
    return request({
        url: '/chain/statisticsboard/getProjectDynamicInfo',
        method: 'post',
        data
    })
}
//获取运单列表
export function getWaybillPage(params) {
    return request({
        url: '/chain/statisticsboard/getWaybillPage',
        method: 'get',
        params
    })
}
// //获取车队长数量分页
export function getDriverCaptainPage(params) {
    return request({
        url: '/chain/statisticsboard/getDriverCaptainPage',
        method: 'get',
        params
    })
}
// //获取总泥尾项目列表
export function activityProjectGarbage(data) {
    return request({
        url: '/chain/companydashboardstatistic/activityProjectGarbage',
        method: 'post',
        data
    })
}
// //获取泥尾数量分页
export function garbageWaybillAnalysisList(params) {
    return request({
        url: '/chain/companydashboardstatistic/garbageWaybillAnalysisList',
        method: 'get',
        params
    })
}
// //获取出土总量的项目信息
export function getProjectDynamicInfoByExcavatedNumber(data) {
    return request({
        url: '/chain/statisticsboard/getProjectDynamicInfoByExcavatedNumber',
        method: 'post',
        data
    })
}
// //获取出土总量的分页
export function getExcavatedNumberPage(params) {
    return request({
        url: '/chain/statisticsboard/getExcavatedNumberPage',
        method: 'get',
        params
    })
}
// //获取异常运单的项目信息
export function getWaybillUpdateHitTargetHeadCount(data) {
    return request({
        url: '/chain/companywaybillupdatehittarget/getWaybillUpdateHitTargetHeadCount',
        method: 'post',
        data
    })
}
// //获取异常运单分页
export function getWaybillUpdateHitTargetList(params) {
    return request({
        url: '/chain/companywaybillupdatehittarget/getWaybillUpdateHitTargetList',
        method: 'get',
        params
    })
}
// //获取泥尾分析列表
export function garbageAnalysisList(params) {
    return request({
        url: '/chain/companydashboardstatistic/garbageAnalysisList',
        method: 'get',
        params
    })
}
// //运单分析土质类型获取项目
export function getWaybillAnalyzeProjectList(data) {
    return request({
        url: '/chain/companywaybill/getWaybillAnalyzeProjectList',
        method: 'post',
        data
    })
}
// //运单分析列表
export function getWaybillAnalyzeList(params) {
    return request({
        url: '/chain/companywaybill/getWaybillAnalyzeList',
        method: 'get',
        params
    })
}
// //运单分析土质 泥尾点获取
export function getWaybillAnalyzeGarbageCount(data) {
    return request({
        url: '/chain/companywaybill/getWaybillAnalyzeGarbageCount',
        method: 'post',
        data
    })
}

//数据看板运费和运单数量
export function getCompanyPaymentWaybillProjectInfo(data) {
    return request({
        url: '/chain/companynsrsbhwallet/getCompanyPaymentWaybillProjectInfo',
        method: 'post',
        data
    })
}

//数据看板运费和运单数量项目-二级页面
export function getCompanyPaymentProjectList(params) {
    return request({
        url: '/chain/companynsrsbhwallet/getCompanyPaymentProjectList',
        method: 'get',
        params
    })
}

//获取产值统计列表头部统计导出
export function companyPaymentProjectListExport(data) {
    return request({
        url: '/chain/companynsrsbhwallet/companyPaymentProjectListExport',
        method: 'post ',
        data
    })
}

//班次列表
export function getShiftOfProjectByCompanyAuthId(params) {
    return request({
        url: '/chain/projectinfo/getShiftOfProjectByCompanyAuthId',
        method: 'get',
        params
    })
}


//项目列表
export function getProjectDynamicInfoByExcavatorLoading(data) {
    return request({
        url: '/chain/statisticsboard/getProjectDynamicInfoByExcavatorLoading',
        method: 'post',
        data
    })
}


//列表
export function getExcavatorLoadingPage(params) {
    return request({
        url: '/chain/statisticsboard/getExcavatorLoadingPage',
        method: 'get',
        params
    })
}

//列表
export function getCompanyLedgerDigPage(params) {
    return request({
        url: '/chain/statisticsboard/getCompanyLedgerDigPage',
        method: 'get',
        params
    })
}

//作业类型
export function getCompanyLedgerTypeByCompanyId(params) {
    return request({
        url: '/chain/projectinfoext/getCompanyLedgerTypeByCompanyId',
        method: 'get',
        params
    })
}


//项目列表
export function getProjectDynamicInfoByDig(data) {
    return request({
        url: '/chain/statisticsboard/getProjectDynamicInfoByDig',
        method: 'post',
        data
    })
}

