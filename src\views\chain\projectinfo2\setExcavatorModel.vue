<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="80%"
      title="管理项目"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
         <el-tabs v-model="activeName">
          <el-tab-pane label="挖机型号配置" name="first">
              <avue-crud
                ref="crud"
                :page.sync="page"
                :data="tableData"
                :table-loading="tableLoading"
                :option="tableOption"
                @on-load="getPage"
                @refresh-change="refreshChange"
                @sort-change="sortChange"
                @search-change="searchChange"
                @selection-change="selectionChange"
              >
                <template slot="platformBranchNsrmcSearch" slot-scope="scope">
                  <el-input disabled :value="info.platformBranchNsrmc"></el-input>
                </template>
                <template slot="menuLeft" slot-scope="{ size }">
                  <el-radio-group v-model="tabPosition" @change="changeTab" size="small">
                    <el-radio-button label="2">未添加挖机型号</el-radio-button>
                    <el-radio-button label="1">已添加挖机型号</el-radio-button>
                  </el-radio-group>
                </template>
                <template slot="menu" slot-scope="scope">
                  <el-button
                    v-if="tabPosition == 2"
                    type="text"
                    icon="el-icon-plus"
                    size="small"
                    plain
                    @click="add(scope.row, scope.index)"
                  >
                    添加
                  </el-button>
                  <el-button
                    type="text"
                    v-if="tabPosition == 1"
                    icon="el-icon-delete"
                    size="small"
                    plain
                    @click="del(scope.row, scope.index)"
                  >
                    删除
                  </el-button>
                </template>
                <template slot="header" slot-scope="scope">
                  <el-button
                    style="vertical-align: top; margin-top: 8px; margin-left: 6px"
                    type="primary"
                    v-if="tabPosition == 2"
                    :disabled="selectList.length == 0"
                    icon="el-icon-plus"
                    size="mini"
                    @click="batchAdd(scope.row, scope.index)"
                  >
                    批量添加
                  </el-button>
                  <el-button
                    style="vertical-align: top; margin-top: 8px; margin-left: 6px"
                    v-if="tabPosition == 1"
                    :disabled="selectList.length == 0"
                    type="primary"
                    size="mini"
                    icon="el-icon-delete"
                    @click="batchDel(scope.row, scope.index)"
                  >
                    批量删除
                  </el-button>
                </template>
              </avue-crud>
          </el-tab-pane>
          <el-tab-pane label="挖机型号模式" name="second">
            <div style="display:flex;align-items:center;margin-bottom:30px;margin-top:10px">
              <el-checkbox-group v-model="checkList">
                <el-checkbox label="1">可输入</el-checkbox>
                <el-checkbox label="2">可选择</el-checkbox>
              </el-checkbox-group>
            </div>
            <el-button type="primary" size="small" style="margin-left:30px;" @click="saveInModel" :loading="btnLoading">保 存</el-button>
          </el-tab-pane>
        </el-tabs>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getCompanymachine,batchAddProInModel,batchDelProInModel,updProInModelType,getProjectInfoExt } from "@/api/chain/projectinfo";
import { expotOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  data() {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: true,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 6,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchMenuSpan: 6,
        menu: true,
        selection: true,
        menuWidth: 100,
        column: [
          {
            label: "挖机车主",
            prop: "ownerName",
            search: true,
            overHidden: true,
            searchLabelWidth: 74,
          },
          {
            label: "挖机车主手机",
            prop: "ownerMobile",
            search: true,
            overHidden: true,
            searchLabelWidth: 100,
          },
          {
            label: "机械型号",
            prop: "machineCode",
            search: true,
            overHidden: true,
            searchLabelWidth: 74,
          },
        ],
      },
      tabPosition: "2",
      selectList: [],
      activeName: 'first',
      checkList:['1'],
      btnLoading:false,
    };
  },
  created() {},
  mounted() {
    this.getProjectInfoExt()
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    getProjectInfoExt(){
      getProjectInfoExt(this.info.id).then(res=>{
        if(res.data.data){
          this.checkList = res.data.data.inModelType=="0"?["1","2"]:res.data.data.inModelType=="2"?["2"]:["1"]
        }
      })
    },
    cancelModal() {
      this.$emit("update:visible", false);
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.getPage(this.page,params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page,params = {}) {
      console.log(params);
      console.log(this.paramsSearch);

      let data = Object.assign(params,this.paramsSearch, {
            current: page.currentPage,
            size: page.pageSize,
            descs:this.page.ascs,
            ascs: this.page.ascs,
          },)
      if(this.tabPosition==1){
        data.inProjectIds = this.info.id
        data.notProjectIds = undefined
      }else{
        data.inProjectIds = undefined
        data.notProjectIds = this.info.id
      }
      data.id = this.info.id;
      this.tableLoading = true;
      getCompanymachine(data)
        .then((response) => {
          console.log("response", response);
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          console.log(this.page);
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange() {
      this.getPage(this.page);
    },
    selectionChange(e) {
      this.selectList = e;
    },
    add(row) {
      // this.$confirm(`是否添加${row.projectName}？`, "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // }).then(res=>{
        this.batchAddProInModel({projectInfoId:this.info.id,inModel:row.id})
      // }).catch(()=>{})
    },
    del(row) {
      this.$confirm(`确定删除？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res=>{
        this.batchDelProInModel({projectInfoId:this.info.id,inModel:row.id})
      }).catch(()=>{})
    },
    batchAdd() {
    let ids = this.selectList.map(item=>item.id)
    // this.$confirm(`确定批量添加项目？`, "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning",
    //   }).then(res=>{
        this.batchAddProInModel({projectInfoId:this.info.id,inModel:ids.join(",")})
      // }).catch(()=>{})
    },
    batchDel() {
      let ids = this.selectList.map(item=>item.id)
      this.$confirm(`确定删除？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res=>{
        this.batchDelProInModel({projectInfoId:this.info.id,inModel:ids.join(",")})
      }).catch(()=>{})
    },
    changeTab() {
      this.page.currentPage = 1
      this.tableData = [];
      this.getPage(this.page);
    },
    batchAddProInModel(data){
      this.tableLoading = true
      batchAddProInModel(data).then(res=>{
        this.tableLoading = false
        this.getPage(this.page);
      }).catch(()=>{
        this.tableLoading = false
      })
    },
    batchDelProInModel(data){
      this.tableLoading = true
      batchDelProInModel(data).then(res=>{
        this.tableLoading = false
        this.getPage(this.page);
      }).catch(()=>{
        this.tableLoading = false
      })
    },
    saveInModel(){
      if(this.checkList.length==0){
        this.$message.error("请选择模式")
        return false
      }
      this.btnLoading = true;
      updProInModelType({
        projectInfoId: this.info.id,
        inModelType:this.checkList.length==2?0:this.checkList.includes("1")?1:this.checkList.includes("2")?2:"",
      })
        .then((res) => {
          this.btnLoading = false;
          this.$message.success("操作成功")
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
