<template>
  <div class="execution">
    <basic-container>
      <el-form class="filtrationBox" :model="searchForm1" :inline="true">
        <div style="font-size: 16px; font-weight: 600">设置小票过滤条件</div>
        <el-form-item label="项目名称">
          <el-select
            filterable
            v-model="searchForm1.projectInfoId"
            placeholder="请选择项目名称"
          >
            <el-option
              v-for="(item, index) in projectList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="小票联数">
          <el-select v-model="searchForm1.union" placeholder="请选择小票联数">
            <el-option
              v-for="(item, index) in unionList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="打印次数">
          <el-select
            v-model="searchForm1.printCount"
            placeholder="请选择小票联数"
          >
            <el-option
              v-for="(item, index) in printCountList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="结算状态">
          <el-select
            v-model="searchForm1.agentStatus"
            placeholder="请选择小票联数"
          >
            <el-option
              v-for="(item, index) in settleStatusList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="出场时间">
          <el-date-picker
            v-model="searchForm1.time"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-value="new Date(2010, 9, 1)"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="editPrint"
            >设置小票过滤条件
          </el-button>
        </el-form-item>
      </el-form>
      <el-form
        class="filtrationBox"
        :model="searchForm"
        label-width="auto"
        :inline="true"
      >
        <el-form-item label="运单号码：">
          <el-input
            v-model="searchForm.companyNo"
            clearable
            style="width: 600px"
            @keydown.native="handleKeyUp"
            :autofocus="true"
          />
        </el-form-item>
        <el-form-item label="过滤条件">
          <el-select
            v-model="searchForm.isMeet"
            clearable
            placeholder="请选择过滤条件"
            @change="isMeetChange"
          >
            <el-option
              v-for="(item, index) in isMeetList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
      <avue-crud
        ref="crud"
        :page="page"
        :data="showTable"
        :option="tableOption"
        @selection-change="selectionChange"
      >
        <template slot="menu" slot-scope="{ row }">
          <el-button
            type="text"
            icon="el-icon-delete"
            size="small"
            plain
            @click="handleRemove(row)"
          >
            移除
          </el-button>
        </template>
        <template slot="menuLeft" slot-scope="{ row }">
          <el-button
            icon="el-icon-download"
            size="small"
            type="primary"
            @click="batchRemove(true)"
          >
            批量移除记录
          </el-button>
          <el-button
            icon="el-icon-download"
            size="small"
            type="primary"
            @click="batchSettlementCheck()"
          >
            批量生成结算单
          </el-button>
        </template>

         <template #companyNo="{ row }">
            <span
              style="color: #409eff; cursor: pointer"
              @click="viewInfo(row)"
              >{{ row.companyNo }}</span
            >
         </template>
        <template #projectName="{ row }">
          <span :style="{ color: row.projectNameFlag == 1 ? 'red' : '' }">{{
            row.projectName
          }}</span>
        </template>
        <template #union="{ row }">
          <span :style="{ color: row.unionFlag == 1 ? 'red' : '' }">{{
            row.union
          }}</span>
        </template>
        <template #printCount="{ row }">
          <span :style="{ color: row.printCountFlag == 1 ? 'red' : '' }">{{
            row.printCount
          }}</span>
        </template>
        <template #printTime="{ row }">
          <span :style="{ color: row.printTimeFlag == 1 ? 'red' : '' }">{{
            row.printTime
          }}</span>
        </template>
        <template #agentStatus="{ row }">
          <el-tooltip v-if="row.agentStatusFlag == 1" class="item" effect="dark" :content="'核算人：' + row.settleName +'    核算时间：' + row.settleDatetime" placement="bottom-end">
          <span :style="{ color: row.agentStatusFlag == 1 ? 'red' : '' }">{{
            getagentStatus(row.agentStatus)
          }}</span>
          </el-tooltip>
          <span v-else :style="{ color: '' }">{{ getagentStatus(row.agentStatus) }}</span>
        </template>
        <template #isMeet="{ row }">
          <span :style="{ color: row.isMeet ? 'green' : 'red' }">{{
            row.isMeet ? "符合" : "不符合"
          }}</span>
        </template>
      </avue-crud>
    </basic-container>
    <el-dialog
          title="证件不齐全车辆信息"
          :visible.sync="centerDialogVisible"
          width="60%"
          center>
          <span style="color: red">如需在本平台开具发票，请完善相关证件信息</span>
                <el-table
                  :data="dialogObj.dialogData"
                  style="width: 100%; color: red;">
                <el-table-column
                prop="projectName"
                label="项目名称"
                width="150">
                </el-table-column>
                <el-table-column
                prop="truckCode"
                label="车牌"
                width="130">
                </el-table-column>
                <el-table-column
                prop="driverName"
                label="司机"
                width="130">
                </el-table-column>
                 <el-table-column
                prop="captainName"
                label="车队长"
                width="130">
                </el-table-column>
                 <el-table-column
                prop="positiveImgUrl"
                label="行驶证正面"
                width="180">
                <template slot-scope="scope">
                <img :src="scope.row.positiveImgUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                 <el-table-column
                prop="negativeImgUrl"
                label="行驶证反面"
                width="180">
                <template slot-scope="scope">
                <img :src="scope.row.negativeImgUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                 <el-table-column
                
                prop="roadLicencePositiveUrl"
                label="道路运输证正面">
                <!-- 图片的显示 -->
                <template slot-scope="scope">
                <img :src="scope.row.roadLicencePositiveUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                 <el-table-column
                prop="roadLicenceNegativeUrl"
                label="道路运输证反面">
                <template slot-scope="scope">
                <img :src="scope.row.roadLicenceNegativeUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                </el-table>
          <span slot="footer" class="dialog-footer">
            <el-button @click="centerDialogVisible = false,dialogPass = false">取 消</el-button>
            <el-button type="primary" @click="centerDialogVisible = false,batchSettlement()">生成结算单</el-button>
          </span>
        </el-dialog>
        <!-- 运单详情 -->
    <waybill-detail
      v-if="detailVisible"
      :detailForm="detailForm"
      :option="detailOption"
      :visible.sync="detailVisible"
    ></waybill-detail>
  </div>
</template>

<script>
import {
  getProjectList,
  listDictionaryItem,
  receiptSettlement,
  receiptBatchAddWaybillSettlePre,
  cardGiveCheck,
} from "@/api/chain/companysettle";
import {getObj,} from "@/api/chain/waybillPreset";
import WaybillDetail from "@/views/chain/companywaybill2/detail.vue";
export default {
  name: "scanQrCode",
  components: {
    WaybillDetail,
  },
  data() {
    return {
      searchForm1: {
        companyNo: "", //运单号
        projectInfoId: "", //项目id
        union: "", //小票联数
        printCount: "", //打印次数
        agentStatus: "", //结算状态
        time: "", //出场时间
        isMeet: "", //过滤条件
      },
      searchForm: {
        companyNo: "", //运单号
        projectInfoId: "", //项目id
        union: "", //小票联数
        printCount: "", //打印次数
        agentStatus: "", //结算状态
        time: "", //出场时间
        isMeet: "", //过滤条件
      },
      projectList: [], //项目列表
      unionList: [], //小票联数列表
      printCountList: [], //打印次数列表
      settleStatusList: [], //结算状态列表
      isMeetList: [], //过滤条件列表
      showTable: [], //是否显示表格
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      tableOption: {
        selection: true,
        addBtn: false,
        // menu: true,
        // menuSlot:true,
        border: true,
        refreshBtn: false,
        columnBtn: false,
        delBtn: false,
        editBtn: false, // 隐藏行编辑按钮
        // header: false,
        align: "center",
        searchSpan: 8,
        searchBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "运单号码",
            prop: "companyNo",
          },
          {
            label: "项目名称",
            prop: "projectName",
            slot: true,
          },
          {
            label: "车牌号码",
            prop: "truckCode",
          },
          {
            label: "车队长",
            prop: "payFleetCaptainName",
          },
          {
            label: "运单时间",
            prop: "goShiftTime",
          },
          {
            label: "第几联",
            prop: "union",
            slot: true,
          },
          {
            label: "第几次打印",
            prop: "printCount",
            slot: true,
          },
          {
            label: "打印时间",
            prop: "printTime",
            slot: true,
          },
          {
            label: "是否结算",
            prop: "agentStatus",
            slot: true,
          },
          {
            label: "过滤条件",
            prop: "isMeet",
            slot: true,
          },
        ],
        lock: false,
      },
      selectList: [], // 选中的行数据
      accordionList: [], // 符合条件的运单列表
      noticeList: [], // 不符合条件的运单列表
      centerDialogVisible: false,
      dialogPass: true,
      dialogObj :{
        dialogData: [
          {
            label: '项目名称',
            prop: 'projectName',
            value: 'small',
            isImg: false
          },
          {
            label: '车牌',
            prop: 'truckCode',
            value: 'small',
            isImg: false
          },
          {
            label: '司机',
            prop: 'driverName',
            value: 'small',
            isImg: false
          },
          {
            label: '车队长',
            prop: 'captainName',
            value: 'small',
            isImg: false
          },
          {
            label: '行驶证正面',
            prop: 'positiveImgUrl',
            value: 'small',
            isImg: true
          },
          {
            label: '行驶证反面',
            prop: 'negativeImgUrl',
            value: 'small',
            isImg: true
          },
          {
            label: '道路运输证正面',
            prop: 'roadLicencePositiveUrl',
            value: 'small',
            isImg: true
          },
          {
            label: '道路运输证反面',
            prop: 'roadLicenceNegativeUrl',
            value: 'small',
            isImg: true
          },
        ]
      },
      detailVisible: false, //详情弹窗
      detailForm: {},
      detailOption: {
        detail: true,
        labelWidth: 114,
        group: [
          {
            label: "基本信息",
            prop: "group",
            column: [
              {
                label: "运单号",
                prop: "no",
                span: 12,
                placeholder: " ",
              },
              {
                label: "项目名称",
                prop: "projectName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "项目合作方",
                prop: "agentName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "状态",
                prop: "statusName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "地块",
                prop: "landParcel",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: '入场信息',
            prop: 'group',
            column: [
              {
                label: '入场拍照凭证',
                prop: 'iotInPicture',
                placeholder:" ",
                type:'upload',
                listType: "picture-card",
                dataType: 'string',
                span:24,
                placeholder:" ",
              }
            ]
          },
          {
            label: "挖机签单信息",
            prop: "group1",
            column: [
              {
                label: "挖机签单员",
                prop: "inStaffName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机土质",
                prop: "inSoilType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机车牌",
                prop: "inDriverTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机签单时间",
                prop: "inDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机班次",
                prop: "inShiftTypeName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机备注",
                prop: "inRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "凭证类型",
                prop: "inWeightUnit",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机拍照凭证",
                prop: "inPicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "称重仪器照片",
                prop: "inWeighInstrumentPicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "挖机签单地址",
                prop: "inAddr",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: "出场签单信息",
            prop: "group2",
            column: [
              {
                label: "运输类型",
                prop: "tpModeName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾",
                prop: "garbageName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场签单员",
                prop: "goStaffName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场土质",
                prop: "goSoilType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场车牌",
                prop: "goDriverTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场签单时间",
                prop: "goDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场班次",
                prop: "goShiftTypeName",
                span: 12,
                placeholder: " ",
              },
              {
                label: '入场重量',
                prop: 'inWeight',
                span:12,
                placeholder:" ",
              },
              {
                label: '出场重量',
                prop: 'outWeight',
                span:12,
                placeholder:" ",
              },
              {
                label: "单位",
                prop: "weightUnit",
                span: 12,
                placeholder: " ",
              },
              {
                label: "单价",
                prop: "unitPrice",
                span: 12,
                placeholder: " ",
              },
              {
                label: "数量",
                prop: "weightTons",
                span: 12,
                placeholder: " ",
              },
              {
                label: "价格",
                prop: "payeePrice",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾票土质",
                prop: "goTicketSoilType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾票号",
                prop: "ticketNo",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾票单价",
                prop: "ticketPrice",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场拍照凭证",
                prop: "goPicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: " 磅单票据",
                prop: "poundbillUrl",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "出场拍照泥尾票",
                prop: "ticketImg",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "出场备注",
                prop: "goRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "电子结算卡",
                prop: "settleCardNo",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出口签单车型",
                prop: "goVehicleType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场签单地址",
                prop: "goAddr",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: "司机信息",
            prop: "group3",
            column: [
              {
                label: "司机",
                prop: "driverName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "车牌",
                prop: "goDriverTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机出场时间",
                prop: "confirmGoDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机卸土时间",
                prop: "completeDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机卸土地址",
                prop: "completeAddr",
                span: 12,
                placeholder: " ",
              },
              {
                label: "直付类型",
                prop: "isPlatformDirectPayName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机联系方式",
                prop: "driverMobile",
                span: 12,
                placeholder: " ",
              },
              {
                label: "行驶证车辆车型",
                prop: "brandType",
                span: 12,
                placeholder: " ",
              },
            ],
          },
          {
            label: "空车入场签单",
            prop: "group4",
            column: [
              {
                label: "入场车牌",
                prop: "entranceTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "入场签单人",
                prop: "entranceStaffName",
                span: 12,
                placeholder: " ",
                overHidden: true,
              },
              {
                label: "入场签单时间",
                prop: "entranceDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "入场备注",
                prop: "entranceRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "入场拍照",
                prop: "entrancePicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: "回填入场签单",
            prop: "group5",
            column: [
              {
                label: "回填项目",
                prop: "backfillProjectName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "回填车牌",
                prop: "backfillTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "票号",
                prop: "backfillTicketNo",
                span: 12,
                placeholder: " ",
                overHidden: true,
              },
              {
                label: "备注",
                prop: "backfillRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "回填签单人",
                prop: "backfillSignName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "回填签单时间",
                prop: "backfillSignDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "拍照车辆",
                prop: "backfillPicUrl",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "拍照泥尾票",
                prop: "backfillTicketNoUrl",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
            ],
          },
        ],
      },
    };
  },
 
  created() {},
  mounted() {
    this.getProjectList();
    this.listDictionaryItem();
    this.listDictionaryItem2();
    this.listDictionaryItem3();
    this.listDictionaryItem4();
    this.searchForm=this.searchForm1;
   
  },
  methods: {

    viewInfo(row) {
      this.tableLoading = true;
      getObj(row.companyWaybillId)
        .then((res) => {
          this.tableLoading = false;
          this.detailForm = res.data.data;
          this.detailForm.ticketNo =
            this.detailForm.ticketNo &&
            this.detailForm.manualSelectTicket == "0"
              ? `${this.detailForm.ticketNo}(系统分配)`
              : this.detailForm.ticketNo || "";
          this.detailVisible = true;
        })
        .catch((err) => {
          this.tableLoading = false;
        });
    },
    getagentStatus(value) {
      // 在 settleStatusList 中查找匹配的 label
      const match = this.settleStatusList.find(item => item.value === value);
      return match ? match.label : "";
    },
    // 设置小票过滤条件弹框
    editPrint() {
      console.log(this.searchForm1);
      this.$confirm("确认设置小票过滤条件后，下方列表将清空", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.searchForm=this.searchForm1;
          this.tableData = [];
          this.$nextTick(() => {
            this.filtrate();
            if (this.searchForm.isMeet == 1) {
            } else if (this.searchForm.isMeet == 2) {
              this.showTable = this.noticeList;
            } else {
              this.showTable = this.tableData;
            }
          });
        })
        
    },
    // 处理keyup事件
    handleKeyUp(e) {
      console.log(e);
      let keyCode = e.code;
      var key = e.key;
      if (keyCode == "Enter" || key == "Enter") {
        setTimeout(() => {
          this.search();
        }, 500);
      }
    },
    selectionChange(e) {
      this.selectList = e;

      console.log(this.selectList);
    },
    handleRemove(row) {
      let index = this.tableData.indexOf(row);
      this.tableData.splice(index, 1);
    },
    showDialog() {
      this.centerDialogVisible = true; 
    },
    batchSettlementCheck(){
      let params = [];
      for (let i = 0; i < this.selectList.length; i++) {
        params.push(this.selectList[i].companyWaybillId);
      }
      let checkParams = {
        ids: params
      }
      cardGiveCheck(checkParams).then((res)=>{
        if(res.data.data.length>0){
            this.dialogObj.dialogData = res.data.data;
            //打开弹窗
            this.dialogPass = false;
            this.showDialog();
          }else{
            this.batchSettlement();
          }
         });
    },
    batchSettlement() {
      let that = this;
      let params = [];
      for (let i = 0; i < this.selectList.length; i++) {
        params.push(this.selectList[i].companyWaybillId);
      }
      receiptBatchAddWaybillSettlePre(params).then((res) => {
            that.$message({
              message: "批量生成结算单成功",
              type: "success",
            });
            that.batchRemove(false);
            console.log(res);
          });
    },
    
    batchRemove(isbl) {
      const commonIds = new Set(
        this.tableData
          .map((a) => a.id)
          .filter((id) => this.selectList.some((b) => b.id === id))
      );
      this.tableData = this.tableData.filter((a) => !commonIds.has(a.id));
      this.selectList = this.selectList.filter((b) => !commonIds.has(b.id));
      this.$nextTick(() => {
        this.filtrate();
        if (this.searchForm.isMeet == 1) {
        } else if (this.searchForm.isMeet == 2) {
          this.showTable = this.noticeList;
        } else {
          this.showTable = this.tableData;
        }
      });

      if (isbl) {
        this.$message({
          message: "批量移除成功",
          type: "success",
        });
      }
    },
    filtrate() {
      this.accordionList = [];
      this.noticeList = [];
      for (let i = 0; i < this.tableData.length; i++) {
        if (this.tableData[i].isMeet) {
          this.accordionList.push(this.tableData[i]);
        } else {
          this.noticeList.push(this.tableData[i]);
        }
      }
    },
    isMeetChange(e) {
      console.log(e);
      if (e == 1) {
        this.showTable = this.accordionList;
      } else if (e == 2) {
        this.showTable = this.noticeList;
      } else {
        this.showTable = this.tableData;
      }
    },
    search() {
      let that = this;
      let params = {
        companyNo: this.searchForm.companyNo,
        projectInfoId: this.searchForm.projectInfoId,
        union: this.searchForm.union,
        printCount: this.searchForm.printCount,
        agentStatus: this.searchForm.agentStatus,
        isMeet: this.searchForm.isMeet,
        goShiftTimeStart: this.searchForm.time ? this.searchForm.time[0] : "",
        goShiftTimeEnd: this.searchForm.time ? this.searchForm.time[1] : "",
      };
      console.log(params);

      receiptSettlement(params).then((res) => {
        this.upsertById(this.tableData, res.data.data);
        this.filtrate();
        this.showTable = this.tableData;
        that.searchForm.companyNo = "";
      }).catch((err) => {
        console.log(err);
        that.searchForm.companyNo = "";
        this.$message({
          message: "运单号不存在",
          type: "warning",
        });
      });
    },
    upsertById(arr, item) {
      const idx = arr.findIndex((x) => x.id === item.id);
      if (idx > -1) {
        // 已有相同 id，替换整个对象
        arr.splice(idx, 1, item);
        this.$message({
          message: "该运单已存在",
          type: "warning",
        });
      } else {
        // 没有则插入
        arr.unshift(item);
      }
    },
    getProjectList() {
      getProjectList(this.searchForm).then((res) => {
        this.projectList = res.data.data.map((item) => {
          return {
            label: item.projectName,
            value: item.id,
          };
        });
        // console.log(this.projectList);
      });
    },
    listDictionaryItem() {
      listDictionaryItem({ dictionary: "receipt_settle_print_union" }).then(
        (res) => {
          this.unionList = res.data.data.map((item) => {
            return {
              label: item.itemName,
              value: item.itemValue,
            };
          });
          this.searchForm1.union = this.unionList[1].value;
          console.log(this.unionList);
        }
      );
    },
    listDictionaryItem2() {
      listDictionaryItem({ dictionary: "receipt_print_times" }).then((res) => {
        this.printCountList = res.data.data.map((item) => {
          return {
            label: item.itemName,
            value: item.itemValue,
          };
        });
        this.searchForm1.printCount = this.printCountList[1].value;
      });
    },
    listDictionaryItem3() {
      listDictionaryItem({ dictionary: "receipt_settle_status" }).then(
        (res) => {
          this.settleStatusList = res.data.data.map((item) => {
            return {
              label: item.itemName,
              value: item.itemValue,
            };
          });
          this.searchForm1.agentStatus = this.settleStatusList[1].value;
        }
      );
    },
    listDictionaryItem4() {
      listDictionaryItem({
        dictionary: "receipt_settle_filter_condition",
      }).then((res) => {
        this.isMeetList = res.data.data.map((item) => {
          return {
            label: item.itemName,
            value: item.itemValue,
          };
        });

        this.searchForm1.isMeet = this.isMeetList[0].value;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.filtrationBox {
  box-sizing: border-box;
  width: 100%;
  padding: 10px 10px 0;
  border: 1px solid #e4e7ed;
  margin-bottom: 12px;
}
</style>
