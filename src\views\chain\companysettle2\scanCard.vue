<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        :search.sync="search"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @selection-change="selectionChange"
      >
        <template slot="menu" slot-scope="{ row }">
          <el-button
            type="text"
            icon="el-icon-delete"
            size="small"
            plain
            @click="setCard(row)"
          >
            设置交卡人
          </el-button>
          <el-button
            type="text"
            icon="el-icon-view"
            size="small"
            plain
            @click="lookWaybill(row)"
          >
            查看运单</el-button
          >
          <el-button
            type="text"
            icon="el-icon-delete"
            size="small"
            plain
            @click="del(row)"
          >
            删除</el-button
          >
        </template>
        <template slot="menuLeft" slot-scope="{ row }">
          <el-button
            icon="el-icon-download"
            size="small"
            type="primary"
            :disabled="selectList.length == 0"
            @click="batchSetCard"
          >
            批量设置交卡人
          </el-button>
          <el-button
            icon="el-icon-download"
            size="small"
            :loading="generateCardLoading"
            type="primary"
            :disabled="selectGenerateCardReceiptList.length == 0"
            @click="generateCardReceipt"
          >
            生成交卡回执
          </el-button>
          <el-button
            icon="el-icon-download"
            size="small"
            type="primary"
            @click="historyCardReceipt"
          >
            历史交卡回执
          </el-button>
          <!-- <el-button
            icon="el-icon-document-add"
            size="small"
            type="primary"
            :loading="tableLoading"
            :disabled="selectSettlementList.length == 0"
            @click="add"
          >
            生成结算单
          </el-button> -->
          <el-button
            icon="el-icon-download"
            size="small"
            type="primary"
            :disabled="selectList.length == 0"
            @click="exportWaybillsByCardNoExcel"
          >
            导出
          </el-button>
        </template>
        <template slot="cardNoSearch" slot-scope="{ disabled, size }">
          <el-input
            type="text"
            placeholder="请输入 电子结算卡号码 如B123456789"
            :autofocus="true"
            ref="inputRef"
            :disabled="disScan"
            @keydown.native="handleKeyUp"
            v-model="search.cardNo"
          >
          </el-input>
        </template>
        <template slot="cardTypeSearch" slot-scope="{ disabled, size }">
          <el-input
            type="text"
            placeholder="请输入"
            disabled
            v-model="search.cardType"
          >
          </el-input>
        </template>
        <template slot-scope="{ column }" slot="waybillsCountHeader">
            <el-tooltip class="item" effect="dark" content="运单总数=可结算运单数+修改运单待审核数" placement="top">
              <span>{{(column || {}).label}} <i class="el-icon-question"></i></span>
            </el-tooltip>
        </template>
        <template slot-scope="{ column }" slot="settleAbleCountHeader">
            <el-tooltip class="item" effect="dark" content="无待审核运单修改申请且运单状态为已卸土的运单" placement="top">
              <span>{{(column || {}).label}} <i class="el-icon-question"></i></span>
            </el-tooltip>
        </template>
        <template slot-scope="{ column }" slot="updateWaybillsCountHeader">
            <el-tooltip class="item" effect="dark" content="运单存在待审核修改申请，可前往“运单修改申请管理”审核完成后，在当前表格删除对应卡号重新扫描更新数据" placement="top">
              <span>{{(column || {}).label}} <i class="el-icon-question"></i></span>
            </el-tooltip>
        </template>
      </avue-crud>
      <waybill
        v-if="detailVisible"
        :cardNo="curCardNo"
        :visible.sync="detailVisible"
      ></waybill>
      <!-- 任务弹窗 -->
      <el-dialog
        width="500px"
        title="设置收款人"
        center
        :visible.sync="setPayeeVisible"
        :close-on-click-modal="false"
      >
        <avue-form ref="editForm" v-model="editForm" :option="editOption">
          <template slot-scope="{ disabled, size }" slot="mobile">
            <span>{{ editForm.mobile }}</span>
          </template>
          <template slot-scope="{ disabled, size }" slot="bindingBankNo">
            <span>{{ editForm.bindingBankNo }}</span>
          </template>
          <template slot-scope="{ disabled, size }" slot="bindingBankName">
            <span>{{ editForm.bindingBankName }}</span>
          </template>
          <template slot-scope="{ disabled, size }" slot="status">
            <span>{{ editForm.status }}</span>
          </template>
        </avue-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancel">取消</el-button>
          <el-button
            type="primary"
            :loading="btnSetPayLoading"
            :disabled="
              editForm.bindingBankName == '' ||
              editForm.bindingBankNo == '' ||
              editForm.status == '否'
            "
            @click="submitPayee"
            >提交</el-button
          >
        </span>
      </el-dialog>
      <el-dialog title="设置交卡人" :visible.sync="dialogSetCard" width="30%">
        <avue-form
          ref="setCard"
          :option="setCardOption"
          v-model="setCardForm"
          @submit="setCardSubmit"
        ></avue-form>
      </el-dialog>

      <generate-card-receipt-page
        v-if="generateCardReceiptVisible"
        :cardReceiptList="generateCardReceiptData"
        :visible.sync="generateCardReceiptVisible"
      ></generate-card-receipt-page>
    </basic-container>
  
     <div>
        <el-dialog
          title="证件不齐全车辆信息"
          :visible.sync="centerDialogVisible"
          width="60%"
          center>
          <span style="color: red">如需在本平台开具发票，请完善相关证件信息</span>
                <el-table
                  :data="dialogObj.dialogData"
                  style="width: 100%; color: red;">
                <el-table-column
                prop="projectName"
                label="项目名称"
                width="150">
                </el-table-column>
                <el-table-column
                prop="truckCode"
                label="车牌"
                width="130">
                </el-table-column>
                <el-table-column
                prop="driverName"
                label="司机"
                width="130">
                </el-table-column>
                 <el-table-column
                prop="captainName"
                label="车队长"
                width="130">
                </el-table-column>
                 <el-table-column
                prop="positiveImgUrl"
                label="行驶证正面"
                width="180">
                <template slot-scope="scope">
                <img :src="scope.row.positiveImgUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                 <el-table-column
                prop="negativeImgUrl"
                label="行驶证反面"
                width="180">
                <template slot-scope="scope">
                <img :src="scope.row.negativeImgUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                 <el-table-column
                
                prop="roadLicencePositiveUrl"
                label="道路运输证正面">
                <!-- 图片的显示 -->
                <template slot-scope="scope">
                <img :src="scope.row.roadLicencePositiveUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                 <el-table-column
                prop="roadLicenceNegativeUrl"
                label="道路运输证反面">
                <template slot-scope="scope">
                <img :src="scope.row.roadLicenceNegativeUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                </el-table>
          <span slot="footer" class="dialog-footer">
            <el-button @click="centerDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="centerDialogVisible = false,doGenerateCardReceipt()">生成交卡回执</el-button>
          </span>
        </el-dialog>
  </div>
  </div>
  
</template>

<script>
import {
  postScanningSettle,
  batchAddWaybillSettle,
  exportWaybillsByCardNoExcel,
  updateCarrierName,
  giveCardUserEdit,
  generateCardReceipt,
  checkOpen,
  cardGiveCheck,
} from "@/api/chain/companysettle";
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";
import waybill from "./waybill.vue";
import generateCardReceiptPage from "./generateCardReceipt.vue";
export default {
  name: "scanCard",
  data() {
    return {
      disScan: false,
      generateCardLoading: false,
      currentSetCard: "",
      selectList: [],
      generateCardReceiptVisible: false,
      generateCardReceiptForm: {},
      generateCardReceiptData: {},
      setCardForm: {},
      setCardDic:[],
      setCardOption: {
        labelWidth:110,
        column: [
          {
            label: "交卡人",
            prop: "giveCardUserId",
            span: 24,
            maxlength: 20,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      dialogSetCard: false,
      realBarcode: "",
      isScanningGun: false,
      btnSetPayLoading: false,
      editForm: {},
      editOption: {
        column: [
          {
            label: "收款人",
            prop: "payeeId",
            type: "select",
            span: 24,
            remote: true,
            props: {
              label: "payeeId",
              value: "payeeId",
            },
            typeformat(item, label, value) {
              return `${item["carrierName"]}-${item["mobile"]}`;
            },
            dicFormatter: (res) => {
              this.payeeDic = res.data;
              return res.data || [];
            },
            dicUrl: `/chain/companywaybill/selectCarrierListByNameOrMobile?param={{key}}`,
            // allowCreate:true,
            filterable: true,
            change: ({ value }) => {
              console.log(value);
              if (value) {
                let tmp = this.payeeDic.filter((v) => v.payeeId == value)[0];
                console.log(tmp);
                if (tmp) {
                  this.editForm.mobile = tmp.mobile;
                  this.editForm.bindingBankNo = tmp.bindingBankNo;
                  this.editForm.bindingBankName = tmp.bindingBankName;
                  this.editForm.status = tmp.status;
                }
              }
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "手机号码",
            prop: "mobile",
            span: 24,
          },
          {
            label: "收款卡号",
            prop: "bindingBankNo",
            span: 24,
          },
          {
            label: "收款银行",
            prop: "bindingBankName",
            span: 24,
          },
          {
            label: "是否签约合同",
            prop: "status",
            span: 24,
          },
        ],
        labelWidth: 120,
        submitBtn: false,
        position: "left",
        emptyBtn: false,
        cancelBtn: true,
      },
      setPayeeVisible: false,
      curCardNo: "",
      detailVisible: false,
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      search: {
        cardNo: "",
      },
      dialogpass:false,
      centerDialogVisible: false,
      dialogVisible: false,
      dialogObj :{
        dialogData: [
          {
            label: '项目名称',
            prop: 'projectName',
            value: 'small',
            isImg: false
          },
          {
            label: '车牌',
            prop: 'truckCode',
            value: 'small',
            isImg: false
          },
          {
            label: '司机',
            prop: 'driverName',
            value: 'small',
            isImg: false
          },
          {
            label: '车队长',
            prop: 'captainName',
            value: 'small',
            isImg: false
          },
          {
            label: '行驶证正面',
            prop: 'positiveImgUrl',
            value: 'small',
            isImg: true
          },
          {
            label: '行驶证反面',
            prop: 'negativeImgUrl',
            value: 'small',
            isImg: true
          },
          {
            label: '道路运输证正面',
            prop: 'roadLicencePositiveUrl',
            value: 'small',
            isImg: true
          },
          {
            label: '道路运输证反面',
            prop: 'roadLicenceNegativeUrl',
            value: 'small',
            isImg: true
          },
        ]
      },
      tableOption: {
        selection: true,
        addBtn: false,
        // menu: true,
        // menuSlot:true,
        border: true,
        refreshBtn: false,
        columnBtn: false,
        // header: false,
        align: "center",
        searchSpan: 8,
        searchBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "卡类型",
            search: true,
            prop: "cardType",
            searchDisabled: true,
            searchReadonly: true,
            disabled: true,
            showColumn: false,
            searchOrder: 2,
          },
          // {
          //   label: "项目名称",
          //   search: true,
          //   prop: "projectName",
          //   searchDisabled: true,
          //   searchReadonly: true,
          //   showColumn: false,
          //   searchOrder: 3,
          // },
          {
            label: "电子结算卡号码",
            search: true,
            prop: "cardNo",
            labelTip: "结算卡第一位必须为大写英文",
            autofocus: true,
            searchLabelWidth: 134,
            showColumn: false,
            searchOrder: 1,
          },
          {
            label: "运单总数",
            prop: "waybillsCount",
          },
          {
            label: "可结算运单数",
            prop: "settleAbleCount",
          },
          {
            label: "修改运单待审核数",
            prop: "updateWaybillsCount",
          },
          {
            label: "交卡时间",
            prop: "giveCardDatetime",
         
          },
          {
            label: "交卡人",
            prop: "giveCardUserId",
          },
          {
            label: "交卡回执单号",
            prop: "giveCardNo",
          },
          // {
          //   label: "车牌",
          //   prop: "truckCode",
          // },
          // {
          //   label: "运输方式",
          //   prop: "tpMode",
          // },
          // {
          //   label: "土质",
          //   prop: "goSoilType",
          // },
          // {
          //   label: "计价类型",
          //   prop: "weightType",
          // },
          // {
          //   label: "重量",
          //   prop: "weightTons",
          // },
          // {
          //   label: "签单时间",
          //   prop: "goDatetime",
          // },
          // {
          //   label: "签单员",
          //   prop: "goStaffName",
          // },
          // {
          //   label: "司机",
          //   prop: "driverName",
          // },
          // {
          //   label: "车队长",
          //   prop: "captainName",
          // },
        ],
        lock: false,
      },
      doGenerateCardReceiptData:[],
      cardsNo: [],
      isCheckOpen:false, //企业是否开启结算单回执
    };
  },
  created() {
    // this.getPage()
  },
  mounted() {
    // 检查是否开启结算单回执
    checkOpen({companyAuthId:""}).then(res=>{
      this.isCheckOpen = res.data.data
      if(this.isCheckOpen){
        this.setCardOption = {
          labelWidth:110,
          column: [
            {
              label: "交卡人",
              prop: "giveCardUserId",
              span: 24,
              maxlength: 20,
              rules: [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "收款人",
              prop: "payeeName",
              type: "select",
              remote: true,
              allowCreate:true,
              filterable:true,
              props: {
                label: "payeeName",
                value: "payeeName",
              },
              dicFormatter: (res) => {
                this.setCardDic = res.data
                return res.data || [];
              },
              change: ({ value }) => {
                console.log(value);
                if (value) {
                  let tmp = this.setCardDic&&this.setCardDic.length>0&&this.setCardDic.filter((v) => v.payeeName == value)[0]
                  console.log(this.setCardDic);
                  console.log(tmp);
                  if (tmp) {
                    this.setCardForm.bindingBankNo = tmp.bindingBankNo;
                    this.setCardForm.bindingBankName = tmp.bindingBankName;
                  }
                }
              },
              dicUrl: `/chain/companysettlewaybillpayeeinfo/getHistoryList?payeeName={{key}}`,
              span: 24,
              maxlength:10,
              rules: [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur",
                },
                {
                  max: 10,
                  message: "字数最长10个字符",
                },
              ],
            },
            {
              label: "账号",
              prop: "bindingBankNo",
              maxlength:20,
              span: 24,
              rules: [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur",
                },
                {
                validator: (rule, value, callback) => {
                  if(value&&value!=''&&!(/^([0-9][0-9]*)$/.test(value))){
                      callback(new Error('输入不正确'));
                  }
                  else {
                    callback();
                  }
                },
                trigger: "blur",
              },
              ],
            },
            {
              label: "开户行",
              prop: "bindingBankName",
              maxlength:30,
              span: 24,
              rules: [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur",
                },
              ],
            },
            {
              label: "申请结算金额",
              prop: "settleAmount",
              type:"number",
              controls: false,
              minRows: 0,
              maxRows: *********.99,
              precision: 2,
              span: 24,
              rules: [
                {
                  required: true,
                  message: "请输入",
                  trigger: "blur",
                },
              ],
            },
          ],
        }
      }
    })
    this.$nextTick(() => {
      this.$refs.inputRef.focus();
    });
  },
  components: {
    waybill,
    generateCardReceiptPage,
  },
  computed: {
    selectSettlementList() {
      return this.selectList.filter((v) => v.giveCardNo);
    },
    selectGenerateCardReceiptList() {
      return this.selectList.filter((v) => v.giveCardUserId && !v.giveCardNo);
    },
    selectSetCardList() {
      return this.selectList.filter((v) => !v.giveCardUserId);
    },
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:garbagecustomersales:add"]
          ? true
          : false,
        delBtn: this.permissions["chain:garbagecustomersales:del"]
          ? true
          : false,
        editBtn: this.permissions["chain:garbagecustomersales:edit"]
          ? true
          : false,
        viewBtn: this.permissions["chain:garbagecustomersales:get"]
          ? true
          : false,
      };
    },
  },

  methods: {
    selectionChange(e) {
      this.selectList = e;
    },
    setCardSubmit(e, done) {
      let data = [];
      if (this.currentSetCard) {
        data = [Object.assign({},this.setCardForm,{
          settleCardNo: this.currentSetCard.cardNo,
          })
        ];
      } else {
        data = this.selectList.map((v) => {
          return Object.assign({},this.setCardForm,{
            settleCardNo: v.cardNo,
          })
        });
      }
      giveCardUserEdit(data).then(() => {
        this.currentSetCard = "";
        this.tableLoading = false;
        this.$message.success("设置成功");
        this.dialogSetCard = false;
        this.setCardForm = {};
        this.$refs.setCard.resetForm();
        this.cardsNo = this.tableData.map((v) => v.cardNo);
        this.getPage();
      });
      done();
    },
    setCard(row) {
      this.currentSetCard = row;
      this.dialogSetCard = true;
    },

    batchSetCard() {
      this.dialogSetCard = true;
    },
    showDialog() {
      // 可替换为API获取数据
      this.centerDialogVisible = true
      // this.dialogVisible = true
    },
    generateCardReceipt() {

      let cardNoList = [];
      // return;
      this.$confirm(
        `确认对此${this.selectGenerateCardReceiptList.length}张电子结算卡生成交卡回执?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.generateCardLoading = true;
        let data = this.selectGenerateCardReceiptList.map((v) => {
          cardNoList.push(v.cardNo);
          return {
            settleCardNo: v.cardNo,
            giveCardUserId: v.giveCardUserId,
            giveCardCnt: v.waybillsCount,
            giveCardDatetime: v.giveCardDatetime,
          };
        });
        this.doGenerateCardReceiptData = data;
        let query = {
          cardNoList: cardNoList
        };
        cardGiveCheck(query)
          .then((res) => { 
          if(res.data.data.length>0){
            this.generateCardLoading = false;
            this.dialogObj.dialogData = res.data.data;
            //打开弹窗
            this.showDialog();
          }else{
            this.doGenerateCardReceipt();
          }
        });
      });
    },
    doGenerateCardReceipt(){
      generateCardReceipt(this.doGenerateCardReceiptData)
          .then((res) => {
            this.tableLoading = false;
            if (res.data.msg) {
              this.$message.success(res.data.msg);
            } else {
              this.$message.success("生成成功");
            }
            let ids = this.selectGenerateCardReceiptList.map((v) => v.cardNo);
            this.cardsNo = this.cardsNo.filter((v) => !ids.includes(v));
            this.getPage();
            this.generateCardLoading = false;
            this.generateCardReceiptData = res.data.data;
            this.generateCardReceiptVisible = true;
          })
          .catch(() => {
            this.generateCardLoading = false;
          });
    },
    
    historyCardReceipt() {
      this.$router.push({ path: "/scan/historyCard" });
    },
    // 处理keyup事件
    handleKeyUp(e) {
      console.log(e);
      if (e.key == "Backspace") {
        return;
      }
      var shiftKey = e.shiftKey; //为true则按了shiftKey
      let keyCode = e.code;
      var key = e.key; //其他按键key
      let array = [
        "Q",
        "W",
        "E",
        "R",
        "T",
        "Y",
        "U",
        "I",
        "O",
        "P",
        "A",
        "S",
        "D",
        "F",
        "G",
        "H",
        "J",
        "K",
        "L",
        "Z",
        "X",
        "C",
        "V",
        "B",
        "N",
        "M",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "0",
      ];
      // 这种情况下 基本上是 中文输入法 才有出现
      console.info(key,keyCode)
      if (key == "Process") {
        for (const a of array) {
          // 如果匹配到是英文
          if (keyCode == "Key" + a) {
            // 判断大小写
            if (shiftKey) {
              this.realBarcode += a;
              this.isScanningGun = true;
            } else {
              this.realBarcode += a.toLowerCase();
              this.isScanningGun = true;
            }
          }
          // 如果匹配到是数字
          else if (keyCode == "Digit" + a) {
            this.realBarcode += String(a);
            this.isScanningGun = true;
          }
        }
        if (keyCode == "Equal") {
          this.realBarcode += "=";
        }
        if (keyCode == "Enter") {
          this.disScan = true;
          console.info(this.realBarcode)
          console.info(this.search.cardNo)
          setTimeout(() => {
            this.search.cardNo = this.getParam(
              this.realBarcode&& this.realBarcode.includes('=') ? this.realBarcode : this.search.cardNo,
              "id"
            );
            this.realBarcode = "";
            this.getSingle();
          }, 100);
        }
      }
      // 这是英文状态下 直接判断输入的英文在没在上面大写字母中
      else if (array.includes(key.toUpperCase())) {
        this.realBarcode += key;
      }
      // 这是英文状态下 直接判断输入的小写英文在没在上面大写字母中
      else if (array.includes(key.toUpperCase())) {
        this.realBarcode += key;
      }
      // 这是数字 直接判断输入的数字在没在上面数字中
      else if (array.includes(key)) {
        this.realBarcode += String(key);
      } else if (keyCode == "Equal") {
        this.realBarcode += "=";
      } else if (keyCode == "Enter" || key == "Enter") {
        this.disScan = true;
        // 监听到enter触发了，执行后续事件
        setTimeout(() => {
          this.search.cardNo = this.getParam(
            this.realBarcode && this.realBarcode.includes('=') ? this.realBarcode : this.search.cardNo,
            "id"
          );
          this.realBarcode = "";
          this.getSingle();
        }, 100);
      }
    },
    cancel() {
      this.setPayeeVisible = false;
      this.tableData = [];
    },
    submitPayee() {
      var ids = [];
      let arr = this.tableData.map((v) => {
        ids = [...ids, ...v.ids];
      });
      let data = ids.map((item) => {
        return {
          id: item,
          payeeId: this.editForm.payeeId,
        };
      });
      console.log(data);
      this.$refs.editForm.validate((valid, done, msg) => {
        if (valid) {
          this.btnSetPayLoading = true;
          updateCarrierName(data)
            .then((res) => {
              this.btnSetPayLoading = false;
              this.$message.success("操作成功");
              this.setPayeeVisible = false;
              this.tableData = [];

              // this.getPage(this.page);
            })
            .catch((err) => {
              this.btnSetPayLoading = false;
            });
        } else {
          this.btnSetPayLoading = false;
        }
        done();
      });
    },
    exportWaybillsByCardNoExcel() {
      let data = this.selectList
        .map((v) => {
          return v.cardNo;
        })
        .join(",");
      let url = "/chain/companysettle/exportWaybillsByCardNoExcel";
      this.btnLoading = true;
      expotOut({ cardNos: data }, url, "电子结算卡")
        .then((res) => {
          this.btnLoading = false;
        })
        .catch((err) => {
          this.btnLoading = false;
        });
      // exportWaybillsByCardNoExcel(data).then(res=>{

      //   window.open(res.data)
      // })
    },
    del(item) {
      this.$confirm(`是否删除此记录！`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.cardsNo = this.cardsNo.filter((v) => v != item.cardNo);
        this.tableData = this.tableData.filter((v) => v.cardNo != item.cardNo);
      });
    },
    lookWaybill(item) {
      this.curCardNo = item.cardNo;
      this.detailVisible = true;
    },
    getSingle() {
      if (this.tableLoading) {
        return;
      }

      if (!this.search.cardNo) {
        this.disScan = false;
        return;
      }
      let isTrue = true
      this.tableData.forEach((item) => {
        if(this.search.cardNo.includes(item.cardNo)){
          this.$message.error(item.cardNo+"已存在列表");
          this.disScan = false;
          isTrue =  false;

          this.$nextTick(() => {
            this.$refs.inputRef.focus();
          });
          this.tableLoading = false;

        }
      })
      if(!isTrue) return
      this.tableLoading = true;

      postScanningSettle(this.search.cardNo.split(","))
        .then((response) => {
          // if (!this.cardsNo.find((v) => v == this.search.cardNo)) {
          //   this.cardsNo.push(this.search.cardNo);
          // }
          this.$message.success({
            message: response.data.msg,
            duration: 5000,
          });
          if (
            !this.tableData.find((v) => response.data.data.cardNo == v.cardNo)
          ) {
            // console.log(response.data.data[0]);
            response.data.data.forEach((v) => {
              this.tableData.unshift(v);
            });
            // this.tableData = this.tableData.push(response.data.data);
            console.log(this.tableData);
            this.tableData.map((v) => {
              if (this.search.cardNo == v.cardNo) {
                this.search.cardType = v.cardType;
                // this.search.projectName = v.projectName;
              }
            });
          }
          this.disScan = false;
          this.$nextTick(() => {
            this.$refs.inputRef.focus();
          });
          this.tableLoading = false;
        })
        .catch(() => {
          this.disScan = false;
          this.$nextTick(() => {
            this.$refs.inputRef.focus();
          });
          this.tableLoading = false;
        });
    },
    getPage() {
      if (this.cardsNo.length == 0) {
        this.tableData = [];
        return false;
      }
      // if (this.tableData.some((item) => item.cardNo == this.search.cardNo)) {
      //   this.$message.error("卡号已存在列表");
      //   return false;
      // }
      // this.tableData = [];
      this.tableLoading = true;
      // let cardNos = JSON.parse(JSON.stringify(this.cardsNo));
      postScanningSettle(this.cardsNo)
        .then((response) => {
          this.tableData = response.data.data;
          this.tableData.map((v) => {
            if (this.search.cardNo == v.cardNo) {
              this.search.cardType = v.cardType;
              // this.search.projectName = v.projectName;
            }
          });

          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage();
    },
    add() {
      let that = this;
      this.$confirm(`确认生成结算单？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          var ids = [];
          let arr = that.selectSettlementList.map((v) => {
            ids = [...ids, ...v.ids];
          });
          that.tableLoading = true;
          batchAddWaybillSettle(ids)
            .then((res) => {
              that.tableLoading = false;
              that.$message.success("操作成功");
              that.setPayeeVisible = true;
              this.getPage();
            })
            .catch((err) => {
              that.tableLoading = false;
            });
        })
        .catch(() => {});
    },
    changeInput(value) {
      this.search.cardNo = this.getParam(value, "id");
      // console.log(value);
      // let reg = /^[A-Z]\d{14}$/;
      // if (reg.test(value)) {

      // } else {
      //   this.search.cardNo = "";
      // }
    },
    //中文输入
    onCompositionStart(e) {
      this.lock = true;
    },
    // onCompositionupdate(e){
    //   console.log(e);
    //   let reg = /^[A-Z]\d{0,14}$/
    //   if(reg.test(e.data)){
    //       console.log(e.data);
    //       e.data = this.getParam(e.data,'id')
    //     }else{
    //       e.data = ''
    //     }
    // },
    //中文输入
    onCompositionEnd(e) {
      console.log(e);
      this.search.cardNo = this.getParam(e.data, "id");
      var reg = new RegExp("(^|\\?|&)" + "id" + "=([^&]*)(\\s|&|$)", "i");

      if (reg.test(e.data)) {
        this.search.cardNo = unescape(RegExp.$2.replace(/\+/g, " "));
        this.getPage();
      } else {
        this.search.cardNo = e.data;
      }
      this.lock = false;
    },
    getParam(path, name) {
      // var reg = new RegExp("(^|\\?|&)" + name + "=([^&]*)(\\s|&|$)", "i");
      console.info(path);
      if (path.indexOf("=") != -1) {
        var n1 = path.length;
        var n2 = path.lastIndexOf("="); //取得=号的位置
        var id = path.substr(n2 + 1, n1 - n2); //从=号后面的内容
        return id;
      }
      return path;
    },
    exOut() {
      this.$export.excel({
        title: "电子卡结算运单",
        columns: [
          {
            label: "运单号",
            prop: "no",
          },
          {
            label: "车牌",
            prop: "truckCode",
          },
          {
            label: "运输方式",
            prop: "tpMode",
          },
          {
            label: "土质",
            prop: "goSoilType",
          },
          {
            label: "计价类型",
            prop: "weightType",
          },
          {
            label: "重量",
            prop: "weightTons",
          },
          {
            label: "签单时间",
            prop: "goDatetime",
          },
          {
            label: "签单员",
            prop: "goStaffName",
          },
          {
            label: "司机",
            prop: "driverName",
          },
          {
            label: "车队长",
            prop: "captainName",
          },
        ],
        data: this.tableData,
      });
    },
    // updateDic() {
    //   getProjectInfoList().then((res) => {
    //     this.$refs.crud.updateDic("projectInfoId", res.data.data);
    //   });
    //   getCustomerList().then((res) => {
    //     this.$refs.crud.updateDic("garbageCustomerId", res.data.data);
    //   });
    // },
  },
};
</script>

<style lang="scss" scoped>
.simple-btn {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
}
.simple-table {
  width: 100%;
  border-collapse: collapse;
}
.simple-table th, .simple-table td {
  padding: 12px;
  border: 1px solid #ebeef5;
  text-align: left;
}
.simple-table th {
  background-color: #f5f7fa;
}

.table-header {
  text-align: center;
  margin-bottom: 20px;
}
.table-title {
  font-size: 18px;
  color: #333;
  margin-bottom: 5px;
}
.table-subtitle {
  font-size: 14px;
  color: #f21212;
  text-align: center;
  font-style: italic;
}
</style>
