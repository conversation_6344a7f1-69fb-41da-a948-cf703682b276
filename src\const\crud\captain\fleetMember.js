export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  searchSpan: 8,
  searchLabelWidth: 116,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchMenuSpan: 6,
  menu:false,
  // calcHeight: 135,
  // height: "auto",
  // defaultSort: {
  //   prop: "joinTime",
  //   order: "descending",
  // },
  column: [
    {
      label: "车队名称",
      prop: "fleetId",
      // sortable: "custom",
      type: "select", // 下拉选择
      search: true,
      props: {
        label: "fleetName",
        value: "id",
      },
      dicUrl: "/chain/driverfleet/list",
      hide:true,
      showColumn:false,
      searchFilterable:true,
    },
    {
      label: "车队名称",
      prop: "fleetName",
      minWidth:140,
      overHidden:true,
    },
    {
      label: "司机姓名",
      prop: "name",
      search: true,
      minWidth:80,
      overHidden:true,
    },
    {
      label: "司机手机号",
      prop: "mobile",
      search: true,
      minWidth:120,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "完成司机认证",
      prop: "hasAuthentication",
      type: "select", // 下拉选择
      dicData: [
        {
          label: "否",
          value: "0",
        },
        {
          label: "是",
          value: "1",
        },
      ],
      minWidth:94,
      overHidden:true,
    },
    {
      label: "司机认证时间",
      prop: "authenticationTime",
      minWidth:140,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "是否绑定车辆",
      prop: "hasBinding",
      type: "select", // 下拉选择
      dicData: [
        {
          label: "否",
          value: "0",
        },
        {
          label: "是",
          value: "1",
        },
      ],
      minWidth:94,
      overHidden:true,
    },
    {
      label: "绑定车辆时间",
      prop: "bindingTime",
      minWidth:140,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "是否已退出车队",
      prop: "isDel",
      search: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "否",
          value: "0",
        },
        {
          label: "是",
          value: "1",
        },
      ],
      minWidth:106,
      overHidden:true,
    },
    {
      label: "加入车队时间",
      prop: "joinTime",
      minWidth:140,
      overHidden:true,
      sortable: "custom",
    },
  ],
};
