<template>
  <div class="driverTaskDetail">
    <el-drawer size="500px"
               :data="visible"
               title="计划进度"
               :visible.sync="visible"
               :before-close="cancelModal">
      <basic-container>
        <el-table :data="list"
                  border
                  style="width: 100%">
          <el-table-column prop="weightUnit"
                           label="单位"
                           align="center"></el-table-column>
          <el-table-column prop="weightTons"
                           label="数量"
                           align="center"></el-table-column>
          <el-table-column prop="waybillCount"
                           label="车数"
                           align="center"></el-table-column>
        </el-table>
      </basic-container>
    </el-drawer>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getProgress } from "@/api/garbage/planManage";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {},
  },
  data () {
    return {
      list: []
    }
  },
  created () {
  },
  mounted: function () {
    this.getData()
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    getData(){
      getProgress({matchGarbagePlanId:this.info.id}).then(res=>{
        this.list = res.data.data
      })
    },
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
</style>
