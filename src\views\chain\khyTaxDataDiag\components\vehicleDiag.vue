<template>
  <div>
    <avue-crud ref="crud" :page.sync="khyHome.page" :data="tableData" :table-loading="tableLoading"
      :option="tableOption" v-model="form" @on-load="getPage" @refresh-change="refreshChange" @sort-change="sortChange"
      @search-change="searchChange">
      <template slot="menuLeft" slot-scope="{ row }">
        <el-button size="small" type="primary" :loading="btnsLoading" @click="exOut">
          导出
        </el-button>
      </template>
      <template slot="isOk" slot-scope="{ row }">
        <i class="el-icon-check checkIcon" v-if="row.isOk==1"></i>
        <i class="el-icon-close closeIcon" v-else></i>
      </template>
      <template slot="xsz" slot-scope="{ row }">
        <span style="color:#409eff;cursor:pointer;" @click="viewRow(row,'drivingLicenseMainFilename')">主页</span>
        <span style="color:#409eff;cursor:pointer;margin-left: 4px;"
          @click="viewRow(row,'drivingLicenseSideFilename')">副页</span>
      </template>
      <template slot="ysxkz" slot-scope="{ row }">
        <span style="color:#409eff;cursor:pointer;" @click="viewRow(row,'transportLicenseMainFilename')">主页</span>
        <span style="color:#409eff;cursor:pointer;margin-left: 4px;"
          @click="viewRow(row,'transportLicenseSideFilename')">副页</span>
      </template>
    </avue-crud>
  </div>
</template>

<script>
  import {
    tableOption
  } from "@/const/crud/chain/khyTaxDataDiag";
  import {
    mapGetters
  } from "vuex";

  export default {
    name: "khyTaxDataDiag",
    inject: ["khyHome"],
    props: {
      tableData: {
        type: Array,
        default () {
          return [];
        },
      },
      btnsLoading: {
        type: Boolean,
        default () {
          return false;
        },
      },
    },
    data() {
      return {
        form: {},
        paramsSearch: {},
        tableLoading: false,
        tableOption: {
          dialogDrag: true,
          border: true,
          indexLabel: "序号",
          stripe: true,
          menuAlign: "center",
          align: "center",
          menuType: "text",
          searchShow: false,
          excelBtn: false,
          printBtn: false,
          viewBtn: false,
          addBtn: false,
          delBtn: false,
          searchMenuSpan: 6,
          labelWidth: 120,
          menu: false,
          column: [{
              label: "是否合格",
              prop: "isOk",
            },
            {
              label: "司机",
              prop: "name",
            },
            {
              label: "手机号",
              prop: "phone",
              width: 120,
            },
            {
              label: "车牌",
              prop: "carLicensePlate",
              width: 100,
            },
            {
              label: "车辆所有人",
              prop: "owner",
              width: 100,
              overHidden: true,
            },
            {
              label: "车辆识别代码",
              prop: "identifyCode",
              width: 120,
              overHidden: true,
            },
            {
              label: "行驶证",
              prop: "xsz",
            },
            {
              label: "运输许可证",
              prop: "ysxkz",
              width: 100,
            },
            {
              label: "车辆类型代码",
              prop: "truckTypeCode",
              width: 120,
            },
            {
              label: "车牌颜色",
              prop: "plateColor",
              type: "select", // 下拉选择
              dicData: [{
                  label: "蓝色",
                  value: "1",
                },
                {
                  label: "黄色",
                  value: "2",
                },
                {
                  label: "黑色",
                  value: "3",
                },
                {
                  label: "白色",
                  value: "4",
                },
                {
                  label: "绿色",
                  value: "5",
                },
                {
                  label: "其他",
                  value: "9",
                },
                {
                  label: "农黄色",
                  value: "91",
                },
                {
                  label: "农绿色",
                  value: "92",
                },
                {
                  label: "黄绿色",
                  value: "93",
                },
                {
                  label: "渐变绿",
                  value: "94",
                },
              ],
            },
            {
              label: "能源类型",
              prop: "energyType",
              type: "select", // 下拉选择
              dicData: [{
                  label: "汽油",
                  value: "A",
                },
                {
                  label: "柴油",
                  value: "B",
                },
                {
                  label: "电",
                  value: "C",
                },
                {
                  label: "混合油",
                  value: "D",
                },
                {
                  label: "天然气",
                  value: "E",
                },
                {
                  label: "液化石油气",
                  value: "F",
                },
                {
                  label: "甲醇",
                  value: "L",
                },
                {
                  label: "乙醇",
                  value: "M",
                },
                {
                  label: "太阳能",
                  value: "N",
                },
                {
                  label: "混合动力",
                  value: "O",
                },
                {
                  label: "无",
                  value: "Y",
                },
                {
                  label: "其他",
                  value: "Z",
                },
              ],
            },
            {
              label: "注册日期",
              prop: "registerDate",
              width: 100,
            },
            {
              label: "过期日期",
              prop: "expireDate",
              width: 100,
            },
            {
              label: "行驶证到期天数",
              prop: "roadLicenceIdCardExpiryDays",
              formatter: (val) => {
                if (val.roadLicenceIdCardExpiryDays <= 0) {
                  return `<span style="color:red">已过期${-val.roadLicenceIdCardExpiryDays}天</span>`
                } else if (val.roadLicenceIdCardExpiryDays && val.roadLicenceIdCardExpiryDays < 90) {
                  return `<span style="color:red">${val.roadLicenceIdCardExpiryDays}天后过期</span>`
                } else {
                  return val.roadLicenceIdCardExpiryDays
                }
              },
              width: 140,
            },
            {
              label: "发证日期",
              prop: "issueDate",
              width: 100,
            },
            {
              label: "发证机关",
              prop: "issueOrganization",
              width: 120,
              overHidden: true,
            },
            {
              label: "使用性质",
              prop: "useNature",
            },
            {
              label: "核定载质量",
              prop: "loadWeight",
              width: 100,
            },
            {
              label: "总质量",
              prop: "totalWeight",
            },
            {
              label: "档案编号",
              prop: "fileNumber",
              width:100,
              overHidden:true,
            },
            {
              label: "挂车牌号",
              prop: "trailerPlateNumber",
              width: 100,
            },
            {
              label: "道路运输号",
              prop: "roadTransportNumber",
              width: 120,
            },
            {
              label: "道路运输经营许可证号",
              prop: "roadCompanyNo",
              width: 200,
            },
            {
              label: "统一社会信用代码",
              prop: "companyNo",
              width: 160,
            },
          ],
        },
        setVisible: false,
        info: {},
      };
    },
    created() {},
    mounted: function() {},
    computed: {
      ...mapGetters(["permissions"]),
      permissionList() {
        return {
          addBtn: this.permissions["chain:khyTaxDataDiag:add"] ? true : false,
          delBtn: this.permissions["chain:khyTaxDataDiag:del"] ? true : false,
          editBtn: this.permissions["chain:khyTaxDataDiag:edit"] ? true : false,
          viewBtn: this.permissions["chain:khyTaxDataDiag:get"] ? true : false,
          excelBtn: this.permissions["chain:khyTaxDataDiag:excel"] ? true : false,
        };
      },
    },
    methods: {
      searchChange(params, done) {
        params = this.filterForm(params);
        this.paramsSearch = params;
        this.khyHome.page.currentPage = 1;
        this.getPage(this.khyHome.page, params);
        done();
      },
      sortChange(val) {
        this.$emit('sortChange', val)
      },
      getPage(page, params) {
        this.$emit('getPage', page)
      },

      /**
       * 刷新回调
       */
      refreshChange(page) {
        this.getPage(this.khyHome.page);
      },
      viewRow(row, label) {
        if (row[label]) {
          this.$ImagePreview(
            [{
              thumbUrl: row[label],
              url: row[label]
            }],
            0, {
              closeOnClickModal: true,
            }
          );
        } else {
          this.$message.error('暂无图片')
        }
      },
      exOut() {
        this.$emit('exOut', '车辆诊断')
      },
    },
  };
</script>

<style lang="scss" scoped>
  /deep/ .closeIcon,
  .checkIcon {
    font-size: 16px;
    color: red;
  }

  /deep/ .checkIcon {
    color: #3dcc90;
  }
</style>
