<template>
  <div class="history">
    <el-drawer size="500px"
               title="审核流程"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">

      <div class="info"
           style="width:400px;margin-left: 20px;margin-top: 20px;"
           v-if="flowList&&flowList.length>0">
        <div class="approvalFlow">
          <el-timeline>
            <el-timeline-item :timestamp="item.companyPositionName"
                              placement="top"
                              class="myActive"
                              color="#409eff"
                              v-for="(item,index) in flowList"
                              :key="index">
                              <div>{{item.companyStaffName}}</div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      <el-empty v-else></el-empty>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getFlowNode} from '@/api/chain/ledgerDig2'

export default {
  props: {
    //弹窗状态
    visible: {
      type: <PERSON>olean,
      default: false,
    },
    flowList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },

  data () {
    return {
    };
  },
  created () { },
  mounted () {
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
/deep/ .el-timeline-item__timestamp{
  line-height: 20px;
}
</style>
