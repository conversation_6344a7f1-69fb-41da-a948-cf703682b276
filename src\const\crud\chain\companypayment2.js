export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  searchSpan: 6,
  searchLabelWidth: 100,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchMenuSpan: 6,
  defaultSort: {
    prop: "paymentNo",
    order: "descending",
  },
  menuWidth:100,
  selection: true,
  selectable:(row)=>{
    return row.invoiceStatus == 0&&row.paymentNo
  },
  column: [
    {
      label: "所属项目",
      prop: "projectInfoId",
      type: "select", // 下拉选择
      search: true,
      props: {
        label: "projectName",
        value: "id",
      },
      dicUrl: "/chain/projectinfo/list",
      filterable: true, //是否可以搜索
      minWidth:160,
      overHidden:true,
    },
    {
      label: "支付单号",
      prop: "paymentNo",
      search: true,
      sortable: "custom",
      minWidth:160,
      overHidden:true,
    },
    {
      label: "结算单号",
      prop: "settleNo",
      search: true,
      sortable: "custom",
      minWidth:160,
      overHidden:true,
    },
    {
      label: "结算申请人",
      prop: "agentName",
      search: true,
      minWidth:84,
      overHidden:true,
    },
    // {
    //   label: "结算单数量",
    //   prop: "settleCnt",
    //   minWidth:84,
    //   overHidden:true,
    // },
    {
      label: "结算运单数量",
      prop: "waybillCnt",
      minWidth:96,
      overHidden:true,
    },
    {
      label: "结算合计金额(元)",
      prop: "settleAmt",
      minWidth:114,
      overHidden:true,
    },
    {
      label: "支付金额(元)",
      prop: "amt",
      minWidth:94,
      overHidden:true,
    },
    {
      label: "支付状态",
      prop: "status",
      search: true,
      type: "select",
      dicData: [
        {
          label: '待支付',
          value: '1'
        },
        {
          label: '已审批',
          value: '2'
        },
        // {
        //   label: '已驳回',
        //   value: '3'
        // },
        // {
        //   label: '已取消',
        //   value: '5'
        // },
        {
          label: '支付中',
          value: '9'
        },
        {
          label: '已支付',
          value: '4'
        },
      ],
      minWidth:96,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "开票状态",
      prop: "invoiceStatus",
      type: "select",
      search: true,
      html: true,
      formatter: (val) => {
        switch (val.invoiceStatus) {
          case "0":
            return '<span style="color:red">未开票</span>';
            break;
          case "1":
            return '<span style="color:orange">开票中</span>';
            break;
          case "2":
            return '<span style="color:green">已开票</span>';
            break;
        }
      },
      dicData: [
        {
          label: "未开票",
          value: "0",
        },
        {
          label: "开票中",
          value: "1",
        },
        {
          label: "已开票",
          value: "2",
        },
      ],
      minWidth:96,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "项目税点",
      prop: "taxRate",
      minWidth:80,
      overHidden:true,
    },
    {
      label: "税费",
      prop: "taxFee",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "可开票运单数",
      prop: "sureWaybillCount",
      minWidth:100,
      overHidden:true,
    },
    {
      label: "可开票运单金额",
      prop: "sureWaybillAmount",
      minWidth:106,
      overHidden:true,
    },
    {
      label: "不可开票运单数",
      prop: "noSureWaybillCount",
      minWidth:110,
      overHidden:true,
    },
    {
      label: "不可开票运单金额",
      prop: "noSureWaybillAmount",
      minWidth:120,
      overHidden:true,
    },
    {
      label: "已开票数量",
      prop: "alreadyWaybillCount",
      minWidth:90,
      overHidden:true,
    },
    {
      label: "已开票金额",
      prop: "alreadyWaybillAmount",
      minWidth:90,
      overHidden:true,
    },
    {
      label: "索票时间",
      prop: "reqDatetime",
      minWidth:140,
      overHidden:true,
    },
  ],
};
