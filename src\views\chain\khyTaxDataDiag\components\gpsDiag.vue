<template>
  <div>
    <avue-crud ref="crud" :page.sync="khyHome.page" :data="tableData" :table-loading="tableLoading"
      :option="tableOption" v-model="form" @selection-change="selectionChange" @on-load="getPage"
      @refresh-change="refreshChange" @sort-change="sortChange" @search-change="searchChange">
      <template slot="header" slot-scope="{ row }">
        <el-button size="small" type="primary" :loading="btnsLoading" @click="exOut">
          导出
        </el-button>
      </template>
      <template slot="isOk" slot-scope="{ row }">
        <i class="el-icon-check checkIcon" v-if="row.isOk==1"></i>
        <i class="el-icon-close closeIcon" v-else></i>
      </template>
    </avue-crud>
  </div>
</template>

<script>
  import {
    mapGetters
  } from "vuex";

  export default {
    name: "khyTaxDataDiag",
    inject: ["khyHome"],
    props: {
      tableData: {
        type: Array,
        default () {
          return [];
        },
      },
      btnsLoading: {
        type: Boolean,
        default () {
          return false;
        },
      },
    },
    data() {
      return {
        form: {},
        paramsSearch: {},
        tableLoading: false,
        tableOption: {
          dialogDrag: true,
          border: true,
          indexLabel: "序号",
          stripe: true,
          menuAlign: "center",
          align: "center",
          menuType: "text",
          searchShow: false,
          excelBtn: false,
          printBtn: false,
          viewBtn: false,
          editBtn: false,
          addBtn: false,
          delBtn: false,
          searchMenuSpan: 6,
          labelWidth: 120,
          menu: false,
          column: [{
              label: "是否合格",
              prop: "isOk",
            },
            {
              label: "项目名称",
              prop: "projectName",
            },
            {
              label: "运单号",
              prop: "no",
            },
            {
              label: "泥尾",
              prop: "garbageName",
            },
            {
              label: "车队长",
              prop: "fleetCaptainName",
            },
            {
              label: "发货时间",
              prop: "goDatetime",
              formatter: (row) => {
                return row.goDatetime&&this.$moment(row.goDatetime).format('YYYY-MM-DD HH:mm:ss');
              }
            },
            {
              label: "卸货时间",
              prop: "completeDatetime",
              formatter: (row) => {
                return row.completeDatetime&&this.$moment(row.completeDatetime).format('YYYY-MM-DD HH:mm:ss');
              }
            },
            {
              label: "GPS状态",
              prop: "status",
            },
          ],
        },
        selectList: [],
        btnLoading: false,
      };
    },
    created() {},
    mounted() {},
    computed: {
      ...mapGetters(["permissions"]),
      permissionList() {
        return {
          addBtn: this.permissions["chain:khyTaxDataDiag:add"] ? true : false,
          delBtn: this.permissions["chain:khyTaxDataDiag:del"] ? true : false,
          editBtn: this.permissions["chain:khyTaxDataDiag:edit"] ? true : false,
          viewBtn: this.permissions["chain:khyTaxDataDiag:get"] ? true : false,
          excelBtn: this.permissions["chain:khyTaxDataDiag:excel"] ? true : false,
        };
      },
    },
    methods: {
      searchChange(params, done) {
        params = this.filterForm(params);
        this.paramsSearch = params;
        this.khyHome.page.currentPage = 1;
        this.getPage(this.khyHome.page, params);
        done();
      },
      sortChange(val) {
        this.$emit("sortChange", val);
      },
      getPage(page, params) {
        this.$emit("getPage", page);
      },

      /**
       * 刷新回调
       */
      refreshChange(page) {
        this.getPage(this.khyHome.page);
      },
      exOut() {
        this.$emit('exOut', '运单GPS诊断')
      },
      selectionChange(e) {
        this.selectList = e;
      },
    },
  };
</script>

<style lang="scss" scoped>
/deep/ .closeIcon,.checkIcon {
  font-size: 16px;
  color: red;
}

/deep/ .checkIcon {
  color: #3dcc90;
}

/deep/ .avue-crud__menu{
  width: 220px;
  float: right;
  right: 0;
}
/deep/ .avue-crud__tip{
  float: left;
  height: 32px;
  margin-top: 0;
  margin-right: 8px;
}
</style>
