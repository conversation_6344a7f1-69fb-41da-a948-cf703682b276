import { validatenull } from '@/util/validate';
import { getAsVal, detailDataType,deepClone } from '@/util/util';

import dayjs from 'dayjs';
export const DIC_SHOW_SPLIT = ' | ';
export const DIC_SPLIT = ',';
export const ARRAY_LIST = ['img', 'array', 'url'];
export const DATE_LIST = [
  'dates',
  'date',
  'datetime',
  'datetimerange',
  'daterange',
  'time',
  'timerange',
  'week',
  'month',
  'monthrange',
  'year'
];
export const MULTIPLE_LIST = ['cascader', 'tree', 'select'];
export const ARRAY_VALUE_LIST = ARRAY_LIST.concat(['upload', 'dynamic', 'map', 'checkbox', 'cascader', 'timerange', 'monthrange', 'daterange', 'datetimerange', 'dates']);

/**
 * 根据位数获取*密码程度
 */
export const getPasswordChar = (result = '', char) => {
  let len = result.toString().length;
  result = '';
  for (let i = 0; i < len; i++) {
    result = result + char;
  }
  return result;
};
/**
 * 根据字典的value显示label
 */

export const getDicValue = (list, value, props = {}) => {
  if (validatenull(list)) return value
  let isArray = Array.isArray(value)
  value = isArray ? value : [value]
  let result = [];
  let labelKey = props['label'] || 'label'
  let groupsKey = props['groups'] || 'groups'
  let dic = deepClone(list);
  dic.forEach(ele => {
    if (ele[groupsKey]) {
      dic = dic.concat(ele[groupsKey]);
      delete ele[groupsKey];
    }
  });
  value.forEach(val => {
    if (Array.isArray(val)) {
      let array_result = []
      val.forEach(array_val => {
        let obj = findNode(dic, props, array_val) || {}
        array_result.push(obj[labelKey] || array_val);
      })
      result.push(array_result);
    } else {
      let obj = findNode(dic, props, val) || {}
      result.push(obj[labelKey] || val);
    }
  })
  if (isArray) {
    return result
  } else {
    return result.join('')
  }
};
export const findNode = (list = [], props = {}, value) => {
  let valueKey = props.value || "value";
  let childrenKey = props.children || 'children';
  let node;
  for (let i = 0; i < list.length; i++) {
    const ele = list[i]
    if (ele[valueKey] == value) {
      return ele
    } else if (ele[childrenKey] && Array.isArray(ele[childrenKey])) {
      let node = findNode(ele[childrenKey], props, value)
      if (node) return node
    }
  }
  return node
}
export const detail = (row = {}, column = {}, option = {}, dic = []) => {
  let result = row[column.prop];
  let type = column.type;
  let separator = column.separator;
  // 深结构绑定处理
  if (column.bind) result = getAsVal(row, column.bind);
  if (!validatenull(result)) {
    let selectFlag = MULTIPLE_LIST.includes(column.type) && column.multiple;
    let arrayFlag = ARRAY_VALUE_LIST.includes(column.type)
    if ((selectFlag || arrayFlag) && !Array.isArray(result) && !column.dataType) column.dataType = 'string'
    if (column.dataType) {
      if (selectFlag || arrayFlag) {
        if (!Array.isArray(result)) result = result.split(separator || DIC_SPLIT)
        result.forEach(ele => {
          ele = detailDataType(ele, column.dataType)
        })
      } else {
        result = detailDataType(result, column.dataType)
      }
    }
    if ('password' === type) {
      result = getPasswordChar(result, '*');
    } else if (DATE_LIST.includes(type) && column.format) {
      const format = column.format
      let formatValue = dayjs().format('YYYY-MM-DD');
      if (type.indexOf('range') !== -1) {
        let date1 = result[0] || '', date2 = result[1] || ''
        if (type === 'timerange' && date1.length <= 8 && date2.length < 8) {
          date1 = `${formatValue} ${date1}`
          date2 = `${formatValue} ${date2}`
        }
        result = [dayjs(date1).format(format), dayjs(date2).format(format)].join(column.separator || '~')
      } else {
        if (type === 'time' && result.length <= 8) {
          result = `${formatValue} ${result}`
        }
        result = dayjs(result).format(format);
      }
    }
  }
  // 字典处理
  if (!validatenull(dic)) {
    result = getDicValue(dic, result, column.props || option.props);
  }
  // 自定义格式化
  if (typeof column.formatter === 'function') {
    result = column.formatter(row, row[column.prop], result, column);
  } else if (Array.isArray(result) && !validatenull(dic)) {
    result = result.join(separator || DIC_SHOW_SPLIT);
  }
  return result;
};
