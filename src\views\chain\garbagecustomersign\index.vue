<template>
  <div class="execution">
      <basic-container>
          <avue-crud ref="crud"
                     :page.sync="page"
                     :data="tableData"
                     :permission="permissionList"
                     :table-loading="tableLoading"
                     :option="tableOption"
                     v-model="form"
                     @on-load="getPage"
                     @refresh-change="refreshChange"
                     @sort-change="sortChange"
                     @search-change="searchChange">
                     <template slot="menuLeft" slot-scope="{ row }">
                        <el-button
                            icon="el-icon-download"
                            v-if="permissions['chain:garbagecustomersign:excel']"
                            size="small"
                            type="primary"
                            @click="exOut"
                          >
                            导出
                        </el-button>
                      </template>
          </avue-crud>
      </basic-container>
  </div>
</template>

<script>
  import {getPage} from '@/api/chain/garbagecustomersign'
  import {tableOption} from '@/const/crud/chain/garbagecustomersign'
  import {mapGetters} from 'vuex'
  import { expotOut } from "@/util/down.js";
  export default {
      name: 'garbagecustomersign',
      data() {
          return {
              form: {},
              tableData: [],
              page: {
                  total: 0, // 总页数
                  currentPage: 1, // 当前页数
                  pageSize: 20, // 每页显示多少条
                  ascs: [],//升序字段
                  descs: []//降序字段
              },
              paramsSearch: {},
              tableLoading: false,
              tableOption: tableOption(this),
          }
      },
      created() {
      },
      mounted: function () {
      },
      computed: {
          ...mapGetters(['permissions']),
          permissionList() {
              return {
                  addBtn: this.permissions['chain:garbagecustomersign:add'] ? true : false,
                  delBtn: this.permissions['chain:garbagecustomersign:del'] ? true : false,
                  editBtn: this.permissions['chain:garbagecustomersign:edit'] ? true : false,
                  viewBtn: this.permissions['chain:garbagecustomersign:get'] ? true : false,
              };
          }
      },
      methods: {
          searchChange(params,done) {
              params = this.filterForm(params)
              this.paramsSearch = params
              this.page.currentPage = 1
              this.getPage(this.page, params)
              done()
          },
          sortChange(val) {
              let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
              if (val.order == 'ascending') {
                  this.page.descs = []
                  this.page.ascs = prop
              } else if (val.order == 'descending') {
                  this.page.ascs = []
                  this.page.descs = prop
              } else {
                  this.page.ascs = []
                  this.page.descs = []
              }
              this.getPage(this.page)
          },
          getPage(page, params) {
              this.tableLoading = true
              if (params) {
                if (params.hasOwnProperty("searchDate")) {
                  params.startDate = params.searchDate[0];
                  params.endDate = params.searchDate[1];
                  delete params.searchDate;
                }
              }
              getPage(Object.assign({
                  current: page.currentPage,
                  size: page.pageSize,
                  descs: this.page.descs,
                  ascs: this.page.ascs,
              }, params, this.paramsSearch)).then(response => {
                  this.tableData = response.data.data.records
                  this.page.total = response.data.data.total
                  this.page.currentPage = page.currentPage
                  this.page.pageSize = page.pageSize
                  this.tableLoading = false
              }).catch(() => {
                // this.tableData = [{
                //   id:'21212121',
                //   companyAuthId:'1546790114389131265',
                //   projectInfoId:'1535115376700952578',
                //   garbageId:'1537681907272208385',
                //   actType:'1',
                //   actNum:'100',
                //   createId:'张三',
                //   createDatetime:'2022-07-28',
                // }]
                  this.tableLoading = false
              })
          },
          /**
           * 刷新回调
           */
          refreshChange(page) {
              this.getPage(this.page)
          },
          exOut(){
            let params = Object.assign({},this.paramsSearch)
            if (params) {
                if (params.hasOwnProperty("searchDate")) {
                params.startDate = params.searchDate[0];
                params.endDate = params.searchDate[1];
                delete params.searchDate;
              }
            }
            let url = '/chain/garbagecustomerwaybill/exportExcel'
            expotOut( params,url,'入场签单');
          },
      }
  }
</script>

<style lang="scss" scoped>
</style>
