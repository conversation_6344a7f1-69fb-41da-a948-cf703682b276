import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companyposition/page',
        method: 'get',
        params: query
    })
}


export function getList(query) {
  return request({
      url: '/chain/companyposition/list',
      method: 'get',
      params: query
  })
}


export function systemmenu(query) {
  return request({
      url: '/chain/systemmenu/list',
      method: 'get',
      params: query
  })
}






export function addObj(obj) {
    return request({
        url: '/chain/companyposition',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companyposition/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companyposition/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companyposition',
        method: 'put',
        data: obj
    })
}
