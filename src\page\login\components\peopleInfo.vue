<template>
  <div>
    <div class="title">法人信息</div>
    <div class="upload">
      <div class="inner">
        <img :src="idBackUrl" alt="" class="pic" v-if="idBackUrl" />
        <el-upload
          v-else
          class="upload-demo"
          style="margin-right: 30px"
          drag
          :headers="setHeaders"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :show-file-list="false"
          :multiple="false"
          :on-error="uploadFail"
          :before-upload="beforeUpload"
          :on-success="uploadSuccess1"
          action="/chain/common/getOcrIdCard"
          :data="{
            isFront: false,
          }"
        >
          <div class="upload-info">
            <img src="@/static/login/back.png" alt="" class="yyzz" />
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
          </div>
          <div class="el-upload__tip" slot="tip">
            请上传身份证正面（国徽面）
          </div>
        </el-upload>
        <img :src="idFontUrl" alt="" class="pic" v-if="idFontUrl" />
        <el-upload
          v-else
          class="upload-demo"
          drag
          :headers="setHeaders"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :show-file-list="false"
          :multiple="false"
          :on-error="uploadFail"
          :before-upload="beforeUpload"
          :on-success="uploadSuccess2"
          action="/chain/common/getOcrIdCard"
          :data="{
            isFront: true,
          }"
        >
          <div class="upload-info">
            <img src="@/static/login/front.png" alt="" class="yyzz" />
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
          </div>
          <div class="el-upload__tip" slot="tip">
            请上传身份证反面（人像面）
          </div>
        </el-upload>
      </div>
      <div class="btn">
        <el-row>
          <el-button type="primary" style="width: 100%" @click="check"
            >提交审核</el-button
          >
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  data() {
    return {
      setHeaders: {
        Authorization:  this.$store.getters.access_token,
        AuthorizationMD5: this.$store.getters.user_md5// user_md5
      },
      fileList: [],
      idBackUrl: "",
      idFontUrl: "",
      peopleInfo: {},
    };
  },
  watch: {
    value: {
      handler(val) {
        this.peopleInfo = val;
      },
      immediate: true,
      deep: true,
    },
    peopleInfo: {
      handler(val) {
        this.$emit("input", val);
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    check(){
      if(!this.idBackUrl){
        return this.$message.error("请上传身份证国徽面");
      }
       if(!this.idFontUrl){
        return this.$message.error("请上传身份证头像面");
      }
      this.$emit('check')
    },
    nextStep() {
      this.$emit("nextStep");
    },
    beforeUpload(file) {
      const isPic =
        file.type === "image/jpeg" ||
        file.type === "image/png" ||
        file.type === "image/jpg";
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isPic) {
        this.$message.error("上传图片只能是 JPG、JPEG、PNG格式!");
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
      }
      return isPic && isLt2M;
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    uploadSuccess1(res, file, fileList) {
      if (res.code === 0) {
        try {
          this.idBackUrl = URL.createObjectURL(file.raw);
        } catch {
          this.idBackUrl = window.URL.createObjectURL(file.raw);
        }
        this.peopleInfo = Object.assign(this.peopleInfo, JSON.parse(res.data));
        console.info(this.peopleInfo);
        this.peopleInfo.fileFont = file.raw;
        this.$message({
          showClose: true,
          message: "身份证国徽面上传成功",
          type: "success",
        });
      } else {
        this.$message({
          showClose: true,
          message: res.msg,
          type: "error",
        });
      }
    },
    uploadSuccess2(res, file, fileList) {
      if (res.code === 0) {
        try {
          this.idFontUrl = URL.createObjectURL(file.raw);
        } catch {
          this.idFontUrl = window.URL.createObjectURL(file.raw);
        }
        console.info(this.peopleInfo);
        this.peopleInfo = Object.assign(this.peopleInfo, JSON.parse(res.data));
        this.peopleInfo.fileBack = file.raw;
        this.$message({
          showClose: true,
          message: "身份证人像面上传成功",
          type: "success",
        });
      } else {
        this.$message({
          showClose: true,
          message: res.msg,
          type: "error",
        });
      }
      console.log(res);
      console.log(file);
      console.log(fileList);
    },
    uploadFail(res, file, fileList) {
      console.log(res);
      console.log(file);
      console.log(fileList);
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  margin: 20px 0;
  font-size: 20px;
  font-weight: 400;
  span {
    color: #ff531a;
    margin-left: 10px;
  }
}
.upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .inner {
    display: flex;
  }
  .pic {
    width: 360px;
    height: 180px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    margin-right: 30px;
    &:last-child {
      margin-right: 0;
    }
  }
  .upload-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;

    .yyzz {
      display: flex;
      align-items: center;
      width: 170px;
      height: 100px;
      margin-bottom: 20px;
    }
  }
}
.btn {
  margin-top: 100px;
  width: 30%;
}
</style>
