<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        :search.sync="search"
        @on-load="getPage" 
        @refresh-change="refreshChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="handleDel"
        @sort-change="sortChange"
        @selection-change="selectionChange"
        @search-change="searchChange"
      >
        <template slot="menu" slot-scope="scope">
          <!-- <el-button type="text"
            v-if="permissions['chain:companyauthrecharge2:recharge']"
            icon="el-icon-plus"
            size="small"
            plain
            @click="recharge(scope.row,scope.index)">
          充值</el-button>
          <el-button type="text"
            v-if="permissions['chain:companyauthrecharge2:rechargeLog']"
            icon="el-icon-document"
            size="small"
            plain
            @click="rechargeLog(scope.row,scope.index)">
          充值记录</el-button> -->
          <el-button
            type="text"
            v-if="permissions['chain:companyauthrecharge2:pay']"
            icon="el-icon-document-checked"
            size="small"
            plain
            @click="beforePayment(scope.row, 1)"
            >付款</el-button>
          <el-button
            type="text"
            v-if="permissions['chain:companyauthrecharge2:view']"
            icon="el-icon-view"
            size="small"
            plain
            @click="beforePayment(scope.row,2)"
            >查看</el-button>
          <el-button
            type="text"
            v-if="permissions['chain:companyauthrecharge2:detail']"
            icon="el-icon-view"
            size="small"
            plain
            @click="detailRow(scope.row, scope.index)"
            >详情</el-button>
          <el-button
            type="text"
            v-if="permissions['chain:companyauthrecharge2:viewPlan']"
            icon="el-icon-view"
            size="small"
            plain
            @click="viewPlan(scope.row, scope.index)"
            >资金支付计划</el-button>
          </template>
          <template slot="header" slot-scope="scope">
          <div style="display: inline-block;position: relative;top: -3px;margin-left: 10px;">
            <el-button
              icon="el-icon-view"
              size="mini"
              v-if="permissions['chain:companyauthrecharge2:viewCarrier']"
              type="primary"
              :disabled="selectList.length==0"
              @click="batchViewVisible = true">
            查看承运人</el-button>
            <el-button
              icon="el-icon-view"
              size="mini"
              v-if="permissions['chain:companyauthrecharge2:freightPayOrder']"
              type="primary"
              :disabled="selectList.length==0"
              @click="orderVisible = true">
            运费付款单</el-button>
            <el-button
              icon="el-icon-view"
              size="mini"
              v-if="permissions['chain:companyauthrecharge2:taxPayOrder']"
              type="primary"
              :disabled="selectList.length==0"
              @click="taxVisible = true">
            税费付款单</el-button>
            <el-button
              icon="el-icon-view"
              size="mini"
              v-if="permissions['chain:companyauthrecharge2:paymentDetail']"
              type="primary"
              :disabled="selectList.length==0"
              @click="paymentDetailVisible = true">
            支付明细单</el-button>
            <el-button
              icon="el-icon-download"
              size="mini"
              v-if="permissions['chain:companyauthrecharge2:excel']"
              type="primary"
              @click="exOut">
            导出</el-button>
            <el-button
              icon="el-icon-printer"
              size="mini"
              v-if="permissions['chain:companyauthrecharge2:voucher']"
              type="primary"
              :disabled="selectList.length==0"
              @click="voucher">
            批量打印凭证</el-button>
            <el-button
              icon="el-icon-document-checked"
              size="mini"
              v-if="permissions['chain:companyauthrecharge2:batchPayTaxFee']"
              type="primary"
              :disabled="totalLength<1"
              @click="batchPayTaxFeeVisible = true">
            批量付税费</el-button>
            <!-- <el-button
              icon="el-icon-plus"
              size="mini"
              v-if="permissions['chain:companyauthrecharge2:batchRecharge']"
              type="primary"
              :disabled="selectList.length==0"
              :loading='btnLoading'
              @click="bulkRecharge">
            批量充值</el-button> -->
          </div>
        </template>
        <template slot="menuRight" slot-scope="scope">
          <el-popover
            placement="top"
            width="420"
            v-model="helpvisible">
            <p>说明：</p>
            <p>待支付运费 = 支付单运费总金额 - 已支付运费 - 待确认支付运费；</p>
            <p>待支付税费 = 支付单税费总金额 - 已支付税费；</p>
            <p>总费用 = 运费 + 税费；</p>
            <div style="text-align: right; margin: 0">
              <el-button type="primary" size="mini" @click="helpvisible = false">确定</el-button>
            </div>
            <!-- <svg-icon slot="reference" icon-class="help" class-name="card-panel-icon"  style="font-size:32px;margin-right:10px;color: #606266;cursor: pointer;"/> -->
            <el-button slot="reference" plain circle size="small" icon="el-icon-question"></el-button>
          </el-popover>
        </template>
        <template slot="projectInfoId" slot-scope="scope">
          <span>{{ scope.row.projectName }}</span>
        </template>
        <template slot="settleCnt" slot-scope="scope">
          <div
            style="color: #409eff; cursor: pointer"
            @click="settleDetail(scope.row)"
          >
            {{ scope.row.settleCnt }}
          </div>
        </template>
      </avue-crud>
      <avue-sign ref="sign"></avue-sign>
    </basic-container>
    <!-- 充值记录 -->
    <el-dialog title="充值记录" width="70%" :visible.sync="viewVisible">
      <avue-crud
        ref="crud2"
        :page="page2"
        :data="tableData2"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="option"
        @on-load="getPages"
        @sort-change="sortChange2"
        v-model="form2"
      >
        <!-- <template slot="pic" slot-scope="scope">
          <img
            :src="scope.row.pic"
            alt=""
            v-if="scope.row.pic"
            @click="previewImg(scope.row.pic)"
            style="width: 50px; height: 50px"
          />
        </template> -->
      </avue-crud>
    </el-dialog>
    <!-- 充值 -->
    <el-dialog
      title="充值"
      width="70"
      :visible.sync="visible"
      :before-close="cancelModal"
      :close-on-click-modal="false"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="转入银行:" class="formtext">
          {{ ruleForm.bankName }}
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="转入户名:" class="formtext">
              {{ ruleForm.accountName}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="转入账户:" class="formtext">
              {{ ruleForm.bankAccount}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="运费:" class="formtext">
              {{ ruleForm.freight }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="税费:" class="formtext">
              {{ ruleForm.taxFee }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="总费用:" class="formtext">
              {{ ruleForm.realPayPrice }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目税点:" class="formtext">
              {{ ruleForm.taxPoint }}%
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="已充值运费:" class="formtext">
              {{ ruleForm.chargedPrice }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="已支付税费:" class="formtext magin10">
              {{ ruleForm.chargedTaxFee }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="待充值运费:" class="magin10">
          <el-input
            oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
            :disabled="true"
            v-model="ruleForm.prepaidPrice"
          ></el-input>
        </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="待充值税费:" class="magin10">
          <el-input
            oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
            :disabled="true"
            v-model="ruleForm.prepaidTaxFee"
          ></el-input>
        </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="充值运费:" prop="money">
          <el-input
            oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
            @blur="handleInput('money')"
            v-model="ruleForm.money"
          ></el-input>
        </el-form-item>
        <el-form-item label="支付税费:" prop="payTaxes">
          <el-input
            oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
            @blur="handleInput('payTaxes')"
            v-model="ruleForm.payTaxes"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注:" prop="remark">
          <el-input type="textarea" v-model="ruleForm.remark"></el-input>
        </el-form-item>
        <el-form-item label="银行凭证:" prop="pic">
          <avue-form
            :option="optionAvatar"
            v-model="ruleForm"
            class="pic"
            :upload-after="handleAvatarSuccess"
          ></avue-form>
        </el-form-item>
        <!-- <el-form-item>
              </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="btnLoading" size="small" @click="submitForm('ruleForm')"
          >确认</el-button>
        <el-button size="small" @click="cancelModal">取消</el-button>
      </span>
    </el-dialog>

    <!-- //付款 -->
    <confirmPayment v-if="confirmVisible" v-on:refreshChange="refreshChange" :isView="isView" :info="currentForm" :visible.sync="confirmVisible"></confirmPayment>
    <batchView v-if="batchViewVisible" :selectList="selectList" :visible.sync="batchViewVisible"></batchView>
    <!-- 付款单 -->
    <payment-order v-if="orderVisible" :selectList="selectList" :payeeName="search.payeeName" :visible.sync="orderVisible"></payment-order>
    <!-- 税费付款单 -->
    <tax-order v-if="taxVisible" :selectList="selectList" :visible.sync="taxVisible"></tax-order>
    <!-- 批量打印凭证 -->
    <voucher v-if="detailVisible" :detailList="detailList" :paymentIdList="paymentIdList" :visible.sync="detailVisible"></voucher>
    <!-- 支付明细单 -->
    <detail-payment v-if="paymentDetailVisible"  :selectList="selectList" :payeeName="search.payeeName" :visible.sync="paymentDetailVisible"></detail-payment>
    <!-- 详情 -->
    <detail v-if="detailDialog"  :info="currentForm" :visible.sync="detailDialog"></detail>
    <!-- 批量充值 -->
    <batch-recharge v-if="batchRechargeDialog" @refreshChange="refreshChange" :info="currentForm" :visible.sync="batchRechargeDialog"></batch-recharge>
    <!-- 资金支付计划 -->
    <pay-plan v-if="planDialog" @refreshChange="refreshChange" :info="currentForm" :visible.sync="planDialog"></pay-plan>
    <!-- 批量付税费 -->
    <walletList v-if="batchPayTaxFeeVisible"
                :selectList="selectList"
                @refreshChange="refreshChange"
                :visible.sync="batchPayTaxFeeVisible"></walletList>
  </div>
</template>

<script>
import {
  getPage,
  getObj,
  addObj,
  putObj,
  delObj,
  payment,
  checkPayment,
} from "@/api/chain/companypayment";
import { getAllSettle } from "@/api/chain/createPayOrder";
import {
  getPages,
  recharge,
  getBankAccount,
  getSubAccount,
  getRechargePage,
  getProjectBankInfoByPaymentId,
  checkPaymentExistNotSetPayeeId,
  getEvidenceByPaymentIdList,
  getEvidenceWhere,
  getBatchChargeByPaymentIdList
} from "@/api/chain/companyauthrecharge";
import { tableOption } from "@/const/crud/chain/companyauthrecharge2";
import { mapGetters } from "vuex";
import { clearNoNum } from "@/util/util.js";
import confirmPayment from './confirmPayment.vue';
import batchView from './batchView.vue';
import paymentOrder from './paymentOrder.vue';
import taxOrder from './taxOrder.vue';
import voucher from './voucher.vue';
import detailPayment from './detailPayment.vue';
import detail from './detail.vue';
import batchRecharge from './batchRecharge.vue';
import payPlan from './payPlan.vue';
import { expotOut } from "@/util/down.js";
import walletList from './walletList.vue';
export default {
  name: "companyauthrecharge2",
  components: {
    confirmPayment,
    batchView,
    paymentOrder,
    taxOrder,
    voucher,
    detailPayment,
    detail,
    batchRecharge,
    payPlan,
    walletList,
  },
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: "create_datetime", //降序字段
      },
      form2: {},
      tableData2: [],
      page2: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: 'payment_datetime', //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      viewVisible: false,
      visible: false,
      helpvisible:false,
      option: {
        border: true,
        stripe: true,
        columnBtn: false,
        // showHeader:false,
        index: this.showIndex,
        size: "mini",
        selection: false,
        addBtn: false,
        refreshBtn: false,
        menu: false,
        // page:this.showPage,
        align: "center",
        menuAlign: "center",
        // menuType:this.menuType,
        // menuBtnTitle:'自定义名称',
        defaultSort:{
          prop:'paymentDatetime',
          order:'descending'
        },
        column: [
          {
            label: "充值时间",
            prop: "paymentDatetime",
            sortable: 'custom',
            minWidth:140,
            overHidden:true,
          },
          {
            label: "充值运费(元)",
            prop: "money",
            sortable: 'custom',
            minWidth:116,
            overHidden:true,
          },
          {
            label: "支付税费(元)",
            prop: "payTaxes",
            minWidth:100,
            overHidden:true,
          },
          {
            label: "充值备注",
            prop: "remark",
            sortable: 'custom',
            minWidth:96,
            overHidden:true,
          },
          {
            label: "上传截图",
            prop: "pic",
            sortable: 'custom',
            type:'upload',
            minWidth:96,
            overHidden:true,
          },
          {
            label: "充值状态",
            prop: "status",
            type: "select",
            sortable: 'custom',
            dicData: [
              {
                label: "确认中",
                value: "1",
              },
              {
                label: "已到账",
                value: "2",
              },
              {
                label: "已拒绝",
                value: "3",
              },
            ],
            minWidth:96,
            overHidden:true,
          },
        ],
      },
      ruleForm: {
        projectInfoId: "",
        bankName: "",
        accountName: "",
        bankAccount: "",
        taxPoint: "",
        money: "0",
        payTaxes: "0",
        remark: "",
        pic: "",
        taxFee:"",
        prepaidPrice:"",
      },
      rules: {
        money: [{ required: true, message: "请输入", trigger: "blur" }],
        payTaxes: [{ required: true, message: "请输入", trigger: "blur" }],
        pic: [{ required: true, message: "请上传截图", trigger: "change" }],
      },
      optionAvatar: {
        emptyBtn: false,
        submitBtn: false,
        labelWidth: 0,
        column: [
          {
            prop: "pic",
            type: "upload",
            listType: "picture-card",
            action: "/upms/file/upload?fileType=image&dir=user/",
            width:100,
            height:100,
            dataType: 'string',
            propsHttp: {
              url: "link",
            },

            loadText: "附件上传中，请稍等",
            span: 24,
            tip: "只能上传jpg/png文件，且不超过500kb",
          },
        ],
      },
      subAccBalance: 0,
      subAccount: "",
      projectList: [],
      confirmVisible: false,
      btnLoading: false,
      currentForm:{},
      companyPaymentId:'',  //支付单id 查交易记录
      selectList:[],
      batchViewVisible:false,
      orderVisible:false,
      taxVisible:false,
      paymentDetailVisible:false,
      searchDom:{},
      payeeName:'',  //承运人
      search:{},
      detailVisible:false,
      detailList:[],
      paymentIdList:[],
      detailDialog:false,
      batchRechargeDialog:false,
      isView:true,
      planDialog:false,
      batchPayTaxFeeVisible:false,
    };
  },
  created() {},
  mounted: function () {
    this.searchDom = this.$refs.crud.$refs.headerSearch;
    window.addEventListener("resize", this.func);
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:companyauthrecharge2:add"]
          ? true
          : false,
        delBtn: this.permissions["chain:companyauthrecharge2:del"]
          ? true
          : false,
        editBtn: this.permissions["chain:companyauthrecharge2:edit"]
          ? true
          : false,
        viewBtn: this.permissions["chain:companyauthrecharge2:get"]
          ? true
          : false,
      };
    },
    //选中支付单待支付税务大于0的数量
    totalLength () {
      return this.selectList
        .map((row) => (row.unPaidTaxFee > 0 ? 1 : 0))
        .reduce((acc, cur) => parseFloat(cur) + acc, 0);
    },
  },
  watch: {
    "searchDom.searchShow": {
      handler(newVal, oldVal) {
        setTimeout(() => {
          let tableHeight = document.querySelector(".el-table").offsetTop;
          this.$refs.crud.tableHeight = window.innerHeight - tableHeight - 182;
          this.$refs.crud.tableOption.height =
            window.innerHeight - tableHeight - 182;
          this.$refs.crud.doLayout();
        }, 300);
      },
      deep: true,
    },
  },
  methods: {
    func() {
      let tableHeight = document.querySelector(".el-table").offsetTop;
      this.$refs.crud.tableHeight = window.innerHeight - tableHeight - 182;
      this.$refs.crud.tableOption.height =
        window.innerHeight - tableHeight - 182;
      this.$refs.crud.doLayout();
    },
    handleInput(label) {
      this.ruleForm[label] = clearNoNum(this.ruleForm[label] + "");
      if(label=='money'&&Number(this.ruleForm.money)>Number(this.ruleForm.prepaidPrice)){
        // console.log(2);
        // this.$message.error('充值运费不能大于待充值运费')
        // this.ruleForm.money = this.ruleForm.prepaidPrice
        //支付税费必须小于或等于待支付税费=税费 - 已支付税费
      }else if(label=='payTaxes'){
        // let prepaidTaxFee = Number(this.ruleForm.taxFee)-Number(this.ruleForm.chargedTaxFee)
        // console.log(prepaidTaxFee);
        // if(Number(this.ruleForm.payTaxes)>prepaidTaxFee){
        //   this.$message.error('支付税费不能大于税费 - 已支付税费')
        //   this.ruleForm.payTaxes = prepaidTaxFee
        // }
      }
      console.log(this.ruleForm);
    },
    getBankAccount() {
      getBankAccount().then((res) => {
        console.log(res);
        this.subAccount = res.data.data;
        this.getSubAccount();
      });
    },
    getSubAccount() {
      let param = {
        subAccount: this.subAccount,
      };
      getSubAccount(param).then((response) => {
        this.subAccBalance = response.data.data;
      });
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("latestPaymentTime")) {
          params.latestPaymentTimeStart = params.latestPaymentTime[0];
          params.latestPaymentTimeEnd = params.latestPaymentTime[1];
          delete params.latestPaymentTime;
        }
        if (params.hasOwnProperty("applyDatetime")) {
          params.startApplyDatetime = params.applyDatetime[0];
          params.endApplyDatetime = params.applyDatetime[1];
          delete params.applyDatetime;
        }
        params.statusList = "2,4,9";
      } else {
        params = {statusList:"2,4,9"};
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    sortChange2(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page2.descs = [];
        this.page2.ascs = prop;
      } else if (val.order == "descending") {
        this.page2.ascs = [];
        this.page2.descs = prop;
      } else {
        this.page2.ascs = [];
        this.page2.descs = [];
      }
      this.getPages(this.page2);
    },
    getPages(page, params) {
      this.tableLoading = true;
      getRechargePage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: page.descs,
            ascs: page.ascs,
            companyPaymentId:this.companyPaymentId
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData2 = response.data.data.records;
          this.page2.total = response.data.data.total;
          this.page2.currentPage = page.currentPage;
          this.page2.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      console.log(11111111);
      this.getPage(this.page);
    },
    detailRow(row){
      this.currentForm = row
      this.detailDialog = true
    },
    viewPlan(row){
      this.currentForm = row
      this.planDialog = true
    },
    //付款前看下钱是否够付款
    beforePayment(row,type) {
      this.currentForm = row
      // checkPaymentExistNotSetPayeeId({paymentId:row.id}).then((res) => {
      this.isView = type==2 //2查看
      this.confirmVisible = true
      // });
    },
    //充值
    recharge(row) {
      this.tableLoading = true;
      getProjectBankInfoByPaymentId({paymentId:row.id}).then(res=>{
        console.log(res);
        this.ruleForm = Object.assign(this.ruleForm,row,res.data.data)
        this.ruleForm.taxPoint = row.taxRate
        this.ruleForm.companyPaymentId = row.id
        delete this.ruleForm.id
        this.tableLoading = false;
        this.visible = true
      }).catch(err=>{
        this.tableLoading = false;
      })
    },
    cancelModal() {
      this.visible = false
      this.ruleForm = {
        projectInfoId: "",
        bankName: "",
        accountName: "",
        bankAccount: "",
        taxPoint: "",
        money: "0",
        payTaxes: "0",
        remark: "",
        pic: "",
        taxFee:"",
        prepaidPrice:"",
      }
    },
    submitForm(formName) {
      console.log(this.ruleForm);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if(this.ruleForm.money==0&&this.ruleForm.payTaxes==0){
            this.$message.error("请输入充值运费或者支付税费");
            return false
          }
          this.btnLoading = true
          recharge(this.ruleForm).then((res) => {
            this.btnLoading = false
            this.ruleForm = {
              projectInfoId: "",
              bankName: "",
              accountName: "",
              bankAccount: "",
              taxPoint: "",
              money: "0",
              payTaxes: "0",
              remark: "",
              pic: "",
              taxFee:"",
              prepaidPrice:"",
            },
            this.visible = false;
            this.$message.success("充值提交成功");
            this.getPage(this.page);
          }).catch(err=>{
            this.btnLoading = false
          })
        } else {
          return false;
        }
      });
    },
    //充值记录
    rechargeLog(row) {
      this.tableLoading = true;
      this.companyPaymentId = row.id
      this.page2.currentPage = 1;
      this.tableData2 = []
      getRechargePage(
          Object.assign({
              current: this.page2.currentPage,
              size: this.page2.pageSize,
              descs: this.page2.descs,
              ascs: this.page2.ascs,
              companyPaymentId:this.companyPaymentId
            })
          )
            .then((response) => {
                this.tableData2 = response.data.data.records;
                this.page2.total = response.data.data.total;
                this.tableLoading = false;
            this.viewVisible = true;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    handleAvatarSuccess(res, done, loading) {
      // this.ruleForm.pic = res.url;
      done();
      this.$refs.ruleForm.clearValidate();
    },
    previewImg(pic) {
      this.$ImagePreview([{ thumbUrl: pic, url: pic }], 0, {
        closeOnClickModal: true,
      });
    },
    //查看结算单详情
    settleDetail(row) {
      this.tableLoading = true;
      setTimeout(() => {
        this.tableLoading = false;
      }, 3000);
      getAllSettle(row.id).then((res) => {
        let settleDetailList = res.data.data;
        this.tableLoading = false;
        this.$router.push({
          path: "/waybill/waybillDetail",
          query: { value: JSON.stringify(settleDetailList) },
        });
      });
    },
    selectionChange(e) {
      this.selectList = e;
    },
    exOut(){
      let params = Object.assign({},this.paramsSearch)
      let url = '/chain/companypayment/exportExcel'
      expotOut(params,url,'付款打款');
    },
    //批量打印凭证
    voucher(){
      let data = this.selectList.map(item=>{
        return item.id
      })
      let param = {
        paymentIdList:data,
      }
      this.paymentIdList = data
      this.$refs.sign.clear()
      getEvidenceWhere(param).then(res=>{
          this.detailList = res.data.data
          this.detailVisible=true
          setTimeout(()=>{
          this.detailList.forEach((item,index) => {
            this.$refs.sign.getStar('支付',item.companyName,item.companyNo)
            console.log(this.$refs.sign.submit(80, 50));
            this.$set(item,'img',this.$refs.sign.submit(80, 50))
            this.$refs.sign.clear()
          });
        },100)
      })
    },
    //批量充值
    bulkRecharge(){
     let same = this.selectList.every((item) => {
        return item.projectInfoId == this.selectList[0].projectInfoId;
      });
      if (!same) {
        this.$message.error("请选择相同项目的运单进行更改");
        return false;
      }
      let data = this.selectList.map(item=>{
        return item.id
      })
      this.btnLoading = true
      getBatchChargeByPaymentIdList(data).then(res=>{
        console.log(res);
        this.btnLoading =false
        this.currentForm = res.data.data
        if(res.data.data.body.length==0){
          this.$message.error("请选择待充值运费或待支付税费大于0的运单")
        }else{
          this.batchRechargeDialog = true
        }
      }).catch(()=>{
        this.btnLoading =false
      })
    }
  },
  destroyed() {
    this.$refs.sign&&this.$refs.sign.clear()
    window.removeEventListener("resize", this.func);
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body{
  padding-top: 0;
  padding-bottom: 0;
}
/deep/ .formtext{
    margin-bottom: 0px;
    .el-form-item__content,.el-form-item__label{
      line-height: 28px;
    }
}
/deep/ .magin10{
    margin-bottom: 10px;
}
.headerContent {
  margin-left: 20px;
  div {
    font-size: 20px;
    font-weight: 700;
  }
  span {
    font-size: 14px;
    color: #7f7f7f;
  }
}

/deep/ .pic {
  .el-icon-plus {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .avue-form__menu {
    display: none;
  }
  .el-form-item {
    margin-bottom: 0;
  }
}
.el-dialog__body {
  padding-top: 0;
}
/deep/ .avue-crud__right{
  display: flex;
}
.avue-sign {
  display: none;
}
</style>
