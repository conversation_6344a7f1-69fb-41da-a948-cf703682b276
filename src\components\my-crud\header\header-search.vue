<template>
  <el-card
    :shadow="crud.isCard"
    class="my_crud__search"
    v-if="searchFlag"
    v-show="searchShow && searchFlag"
  >
  <!-- 自定义头部搜索 -->
  <div v-if="searchCustom==1" class="searchCustom1">
    <searchHeader :searchOption="option" @searchChange="searchChange" v-if="option.column&&option.column.length>0">
      <template slot-scope="scope" v-for="item in crud.searchSlot" :slot="getSlotName(item)">
        <slot :name="item"
              v-bind="Object.assign(scope,{
                  search:searchForm,
                  row:searchForm
                })"></slot>
      </template>
      <template slot-scope="scope"  slot="customForm">
        <searchFormAlias v-bind="scope" :searchOption="option" @searchChange="searchChange"></searchFormAlias>
      </template>
    </searchHeader>
  </div>
  <!-- 自定义头部方案2 -->
  <div v-else-if="searchCustom==2" class="searchCustom2">
    <searchHeader2 :searchOption="option" @searchChange="searchChange" v-if="option.column&&option.column.length>0">
      <!-- <template slot-scope="scope" v-for="item in crud.searchSlot" :slot="getSlotName(item)">
        <slot :name="item"
              v-bind="Object.assign(scope,{
                  search:searchForm,
                  row:searchForm
                })"></slot>
      </template>
      <template slot-scope="scope"  slot="customForm">
        <searchForm v-bind="scope" :searchOption="option" @searchChange="searchChange"></searchForm>
      </template> -->
    </searchHeader2>
  </div>
  <div v-else class="searchCustom3">
    <slot name="search"
          :row="searchForm"
          :search="searchForm"
          :size="crud.isMediumSize"></slot>
     <avue-form :option="option"
               ref="form"
               @submit="searchChange"
               @change="handleChange"
               @reset-change="resetChange"
               v-if="searchShow"
               v-model="searchForm">
      <template slot="menuForm"
                slot-scope="scope">
        <slot name="searchMenu"
              v-bind="Object.assign(scope,{
                  search:searchForm,
                  row:searchForm
                })"></slot>
        <template v-if="isSearchIcon">
          <el-button type="text"
                     v-if="show===false"
                     @click="show=true"
                     icon="el-icon-arrow-down">更多</el-button>
          <el-button type="text"
                     v-if="show===true"
                     @click="show=false"
                     icon="el-icon-arrow-up">收缩</el-button>
        </template>

      </template>
      <template slot-scope="scope"
                v-for="item in crud.searchSlot"
                :slot="getSlotName(item)">
        <slot :name="item"
              v-bind="Object.assign(scope,{
                  search:searchForm,
                  row:searchForm
                })"></slot>
      </template>
    </avue-form>
  </div>
  </el-card>
</template>

<script>
import { filterParams } from "@/util/util";
import config from "../config";
import searchHeader from './components/searchHeader';
import searchHeader2 from './components/searchHeader2';
import searchFormAlias from './components/searchForm';
import { arraySort } from "@/util/util";
export default {
  name: "crud__search",
  inject: ["crud"],
  components: {
    searchHeader,
    searchHeader2,
    searchFormAlias,
  },
  data() {
    return {
      show: false,
      searchShow: true,
      formOption:{},
      option:{},
      searchForm:{},
    };
  },
  props: {
    search: Object
  },
  watch: {
    "crud.propOption": {
      handler() {
        this.searchShow = this.vaildData(
          this.crud.option.searchShow,
          config.searchShow
        );
      },
      immediate: true,
    },
    searchShow() {
      this.crud.getTableHeight()
    },
    show: {
      handler() {
        this.option.column&&this.option.column.forEach((item,index)=>{
          item.display = this.isSearchIcon ? (this.show ? true : index<this.searchIndex) : true
          return item
        })
      },
      immediate: true,
    },
    // search: {
    //   handler () {
    //     this.searchForm = Object.assign({},this.searchForm, this.search);
    //     console.log(this.searchForm,'111');
    //   },
    //   immediate: true,
    //   deep: true
    // },
  },
  created() {
    this.initFun();
    console.log(this.crud.searchSlot,'crud.searchSlot');
  },
  mounted(){
  //   this.crud.$emit('on-load', this.crud.page)
    this.$nextTick(()=>{
      this.initSearchForm()
    })
  },
  computed: {
    // searchForm: {
    //   get() {
    //     console.log(this.crud.search,'this.crud.search');
    //     return this.crud.search;
    //   },
    //   set(val) {
    //     this.crud.$emit("update:search", val);
    //   },
    // },
    isSearchIcon() {
      return (
        this.vaildData(this.crud.option.searchIcon, false) === true &&
        this.columnLen > this.searchIndex
      );
    },
    searchIndex() {
      return this.crud.option.searchIndex || 2;
    },
    columnLen() {
      let count = 0;
      this.crud.propOption.forEach((ele) => {
        if (ele.search) count++;
      });
      return count;
    },

    searchFlag() {
      return !!this.crud.$slots.search || this.columnLen !== 0;
    },
    searchCustom(){
      return this.crud.option.searchCustom || false
    },
  },
  methods: {
    initSearchForm(){
      const option = this.deepClone(this.crud.option);
      const detailColumn = (list = []) => {
        list = this.deepClone(list);
        let column = [];
        let count = 0;
        // list = arraySort(list, 'searchOrder', (a, b) => a.searchOrder- b.searchOrder)

        list = list.sort((a, b) => (a.searchOrder || 99999) - (b.searchOrder || 99999));
        list.forEach((ele) => {
          if (ele.search) {
            let isCount = count < this.searchIndex;
            let obj = {};
            Object.keys(ele).forEach((item) => {
              let key = "search";
              if (item == "searchProp") return;
              if (item.includes(key)) {
                let result = item.replace(key, "");
                if (result.length == 0) return;
                result = result.replace(result, result.toLowerCase());
                obj[result] = ele[item];
              }
            });
            ele = Object.assign(ele, obj, {
              order:ele.searchOrder||99999,
              type: this.getSearchType(ele),
              detail: false,
              multiple:ele.searchMultiple,
              dicFlag: ele.cascader ? true : this.vaildData(ele.dicFlag, false),
              span: ele.searchSpan || option.searchSpan || config.searchSpan,
              control: ele.searchControl,
              labelWidth:
                ele.searchLabelWidth ||
                option.searchLabelWidth ||
                config.searchLabelWidth,
              labelPosition:
                ele.searchLabelPosition || option.searchLabelPosition,
              size: ele.searchSize || option.searchSize,
              value: ele.searchValue,
              rules: ele.searchRules,
              row: ele.searchRow,
              display: this.isSearchIcon ? (this.show ? true : isCount) : true,
              checkStrictly: ele.searchCheckStrictly || option.searchCheckStrictly,
            });
            let whiteList = ["bind", "disabled", "readonly"];
            whiteList.forEach((key) => delete ele[key]);
            column.push(ele);
            count = count + 1;
          }
        });
        return column;
      };
      const detailOption = (list) => {
        let result = this.deepClone(list);
        let obj = {};
        Object.keys(result).forEach((item) => {
          let key = "search";
          if (item.includes(key)) {
            let str = item.replace(key, "");
            if (str.length == 0) return;
            str = str.replace(str[0], str[0].toLowerCase());
            obj[str] = result[item];
          }
        });
        result.column = detailColumn(this.crud.propOption);
        result = Object.assign(result, {
          tabs: false,
          group: false,
          submitText: option.searchBtnText || "搜索",
          submitBtn: this.vaildData(option.searchBtn, config.searchSubBtn),
          submitIcon: this.crud.getBtnIcon("searchBtn"),
          emptyText: option.emptyBtnText || "清空",
          emptyBtn: this.vaildData(option.emptyBtn, config.emptyBtn),
          emptyIcon: this.crud.getBtnIcon("emptyBtn"),
          menuSpan: (() => {
            if (this.show || !this.isSearchIcon) {
              return option.searchMenuSpan||6;
            } else {
              return option.searchMenuSpan < 6 ? option.searchMenuSpan : 6||6;
            }
          })(),
          menuPosition: option.searchMenuPosition || "center",
          dicFlag: false,
          dicData: this.crud.DIC
        });
        let whiteList = ["icon", "index"];
            whiteList.forEach((key) => delete result[key]);
        return result;
      };
      let result = detailOption(option);
      this.option = result
    },
    initFun() {
      ["searchReset", "searchChange"].forEach(
        (ele) => (this.crud[ele] = this[ele])
      );
    },
    getSearchType(column) {
      const type = column.type;
      const range = column.searchRange;
      const DATE_LIST = ["dates","date","datetime","datetimerange","daterange","time","timerange","week","month","monthrange","year"];
      let result = type;
      if (column.searchType) return column.searchType;
      if (["radio", "checkbox", "switch"].includes(type)) {
        result = "select";
      } else if (DATE_LIST.includes(type)) {
        let rangeKey = "range";
        if (range) {
          if (!type.includes(rangeKey)) {
            result = type + rangeKey;
          } else {
            result = type;
          }
        } else result = type.replace(rangeKey, "");
      } else if (["textarea"].includes(type)) {
        result = "input";
      }
      return result;
    },
    getSlotName(item) {
      return item.replace("Search", "");
    },
    // 搜索回调
    searchChange(form, done) {
      form = filterParams(form);
      this.crud.propOption.forEach((ele) => {
        if (ele.searchProp) {
          form[ele.searchProp] = form[ele.prop];
          delete form[ele.prop];
        }
      });
      this.crud.$emit('update:search', form)
      this.crud.$emit("search-change", form, done);

    },
    // 搜索清空
    resetChange() {
      console.log(this.searchForm);
      this.crud.$emit("search-reset", this.searchForm);
    },
    // 搜索清空
    searchReset() {
      this.$refs.form.resetForm();
    },
    handleChange () {
      this.crud.$emit('update:search', this.searchForm)
    },
    handleSearchShow() {
      this.searchShow = !this.searchShow;
    },
  },
};
</script>
<style lang="scss">
</style>
