<template>
  <div class="cardLedgerC">
    <basic-container>
      <el-descriptions style="margin-bottom:20px">
        <el-descriptions-item label="卡号">{{info.cardNo}}</el-descriptions-item>
        <el-descriptions-item label="车队长">{{info.captainName }}</el-descriptions-item>
        <el-descriptions-item label="激活时间">{{info.bindingDate}}</el-descriptions-item>
        <el-descriptions-item label="状态">{{info.status}}</el-descriptions-item>
        <el-descriptions-item label="子卡数量">{{info.subCardCount}}</el-descriptions-item>
        <el-descriptions-item label="运单总数">{{ info.waybillCount }}</el-descriptions-item>
      </el-descriptions>
      <avue-crud
        ref="paymentCrud"
        class="myCrud"
        :page.sync="page"
        :data="tableData"
        :table-loading="tableLoading"
        :option="tableOption"
        @on-load="getPage"
      >
        <template slot="menu" slot-scope="scope" class="menuSlot">
          <el-button
            type="text"
            icon="el-icon-view"
            size="small"
            plain
            v-if="scope.row.waybillCount>0"
            @click="detail(scope.row, scope.index)"
          >
            详情</el-button
          >
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import { getLists as getPage } from "@/api/chain/cardLedgerB";
import { mapGetters } from "vuex";

export default {
  name: "cardLedgerC",
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: 'binding_date', //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: true,
        border: true,
        indexLabel: "序号",
        stripe: true,
        menuAlign: "center",
        align: "center",
        menuType: "text",
        searchShow: false,
        excelBtn: false,
        delBtn: false,
        editBtn: false,
        printBtn: false,
        viewBtn: false,
        refreshBtn: false,
        header:false,
        menuWidth: 120,
        defaultSort: {
          prop: "bindingDate",
          order: "descending",
        },
        column: [
          {
            label: "卡号",
            prop: "cardNo",
          },
          {
            label: "激活时间",
            prop: "bindingDate",
            type: "datetime",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicData: [
              {
                label: "未激活",
                value: "1",
              },
              {
                label: "已激活",
                value: "2",
              },
              {
                label: "已结算",
                value: "3",
              },
              {
                label: "已作废",
                value: "9",
              },
            ],
          },
          {
            label: "运单总数",
            prop: "waybillCount",
          },
          {
            label: "待结算金额",
            prop: "unsettledAmount",
          },
          {
            label: "已结算金额",
            prop: "settledAmount",
          },
          {
            label: "已付款金额",
            prop: "paidAmount",
          },
        ],
      },
      info:{}
    };
  },
  created() {},
  mounted() {
    this.info = JSON.parse(this.$route.query.info)
    console.log(this.info);
  },
  computed: {
    ...mapGetters(["tagList"]),
  },
  methods: {
    getPage(page, params) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
            cardNo:this.info.cardNo||JSON.parse(this.$route.query.info).cardNo
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    detail(row) {
      let tag = this.findTag("/chain/business/companywaybill2").tag;
      if (tag) {
        this.$store.commit("DEL_TAG", tag);
      }
      this.$router.push({
        path: "/chain/business/companywaybill2",
        query: { statements: row.cardNo },
      });
    },
    findTag(url) {
      let tag, key;
      this.tagList.map((item, index) => {
        if (item.value.includes(url)) {
          tag = item;
          key = index;
        }
      });
      return { tag: tag, key: key };
    },
  },
};
</script>

<style lang="scss" scoped></style>
