import request from '@/router/axios'
//流程模板列表
export function getPage(query) {
    return request({
        url: '/chain/companyapproveflow/getCompanyApproveFlow',
        method: 'get',
        params: query
    })
}
//获取职位列表
export function getPositionList(query) {
  return request({
      url: '/chain/companyposition/listForWorkFlow',
      method: 'get',
      params: query
  })
}

//流程模板保存
export function addObj(obj) {
    return request({
        url: '/chain/companyapproveflow/saveCompanyApproveFlow',
        method: 'post',
        data: obj
    })
}


