<template>
  <div class="freight">
    <searchInfo :info="paramsSearch" source="4" @searchChange="searchChange" @exOut="exOut">
    </searchInfo>
    <avue-crud ref="crud" :page.sync="page" :data="tableData" :table-loading="tableLoading" :option="tableOption"
      v-model="form" :search.sync="search" @on-load="getPage">
    </avue-crud>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getWaybillAnalyzeList as getPage } from "@/api/chain/board";
import searchInfo from './searchInfo';
import { exportOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  components: {
    searchInfo
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      search: {},
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        // maxHeight:[800],
        // height:'auto',
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: true,
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        header: false,
        column: [
          {
            label: "运单号",
            prop: "no",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "运单创建时间",
            prop: "createDatetime",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "项目名称",
            prop: "projectName",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "项目负责人",
            prop: "leadingNames",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "运输类型",
            prop: "tpModeName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "出场签单土质",
            prop: "goSoilType",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "是否有泥尾票",
            prop: "isTicketCn",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "泥尾点",
            prop: "garbageName",
            minWidth: 90,
            overHidden: true,
          },
          // {
          //   label: "泥尾票土质",
          //   prop: "goTicketSoilType",
          //   minWidth: 90,
          //   overHidden: true,
          // },
        ],
      },
    }
  },
  created () {
    this.paramsSearch = Object.assign({}, this.info)
  },
  mounted () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    searchChange (params, done) {
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    getPage (page, params = {}) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    exOut (params, done) {
      let url = "/chain/companywaybill/getWaybillAnalyzeListExport";
      exportOut(params, url, "运单分析运费类型", 'post').then(res => {
        done()
      }).catch(() => {
        done()
      })
    },
  },

};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
</style>
