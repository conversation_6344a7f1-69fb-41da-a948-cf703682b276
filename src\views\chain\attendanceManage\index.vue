<template>
  <div class="app-container">
    <basic-container>
      <avue-form :option="option" v-model="searchform" @submit="searchChange" ></avue-form>
      <!-- <AddDataTable
        size="mini"
        :isIndex="true"
        :isHandle="false"
        :tableData="tableData"
        :tableCols="tableCols"
        :page="page"
        @sortChange="sortChange"
      ></AddDataTable> -->
      <div class="header" style="margin-bottom:10px">
        <el-button
                      size="small"
                      icon="el-icon-download"
                      type="primary"
                      :loading="btnLoading"
                      @click="exOut"
                    >
                      导出
                  </el-button>
      </div>
      <el-table ref="myTable" :data="tableData" border :loading="tableLoading" style="width: 100%;" class="myTable">
            <el-table-column :prop="item.prop" v-for="(item) in tableCols" :key="item.prop" align="center" :label="item.label" width="150">
              <template v-if="item.children&&item.children.length>0">
                <el-table-column :prop="item2.prop" :key="index2"  :label="item2.label" :width="item2.width" show-overflow-tooltip v-for="(item2,index2) in item.children">
                </el-table-column>
              </template>
            </el-table-column>
      </el-table>
      <section class="ces-pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="total,sizes ,prev, pager, next,jumper"
        :page-size="page.pageSize"
        :current-page="page.currentPage"
        :total="page.total"
      ></el-pagination>
    </section>
    </basic-container>
  </div>
</template>

<script>
import { getPage } from "@/api/chain/attendanceManage";
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";
export default {
  name: "attendanceManage",
  data() {
    return {
      searchform: {},
      option: {
        menuSpan: 8,
        submitText: "搜索",
        submitIcon: "el-icon-search",
        column: [
          {
            label: "日期",
            prop: "attendanceDate",
            type: "daterange",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            span: 8,
          },
          {
            label: "姓名",
            prop: "name",
            span: 8,
          },
        ],
      },
      tableData: [],
      btnLoading: false,
      tableLoading: false,
      tableCols: [
        {
          label: "日期",
          prop: "attendanceDate",
          align: "center",
          sortable: "custom",
          istrue: true,
        },
        {
          label: "姓名",
          prop: "name",
          align: "center",
          sortable: "custom",
          istrue: true,
        },
        {
          label: "班次1",
          istrue: true,
          children: [
            {
              label: "项目",
              prop: "projectName1",
              align: "center",
              istrue: true,
              width:180,
            },
            {
              label: "班次",
              prop: "shiftTypeName1",
              align: "center",
              istrue: true,
            },
            {
              label: "上班时间",
              prop: "onWorkTime1",
              align: "center",
              istrue: true,
            },
            {
              label: "下班时间",
              prop: "offWorkTime1",
              align: "center",
              istrue: true,
            },
            {
              label: "时长",
              prop: "hours1",
              align: "center",
              istrue: true,
            },
          ],
        },
        {
          label: "合计时长",
          prop: "workHours",
          align: "center",
          sortable: "custom",
          istrue: true,
        },
      ],
      pagination: {
        //分页参数
        pageSize: 10,
        pageNum: 1,
        total: 7,
      },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: "attendance_date", //降序字段
      },
      paramsSearch:{},
    };
  },
  mounted: function () {
    this.getPage(this.page)
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:attendanceManage:add"] ? true : false,
        delBtn: this.permissions["chain:attendanceManage:del"] ? true : false,
        editBtn: this.permissions["chain:attendanceManage:edit"] ? true : false,
        viewBtn: this.permissions["chain:attendanceManage:get"] ? true : false,
      };
    },
  },
  methods: {
    searchChange(params, done) {
      console.log(params);
      console.log(done);
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      console.log(val);
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("attendanceDate")) {
          params.startDate = params.attendanceDate[0];
          params.endDate = params.attendanceDate[1];
          delete params.attendanceDate;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableData = response.data.data.records;
          this.tableLoading = false;
          let num = 1;
          if (this.tableData && this.tableData.length > 0) {
            this.tableData.forEach((item) => {
              num = item.list.length > num ? item.list.length : num;
              item.list.forEach((item2, index2) => {
                item["projectName" + (index2 + 1)] = item2.projectName;
                item["shiftTypeName" + (index2 + 1)] = item2.shiftTypeName;
                item["onWorkTime" + (index2 + 1)] = item2.onWorkTime;
                item["offWorkTime" + (index2 + 1)] = item2.offWorkTime;
                item["hours" + (index2 + 1)] = item2.hours;
              });
            });
          }
          let colum = [
            {
              label: "日期",
              prop: "attendanceDate",
              align: "center",
              sortable: "custom",
              istrue: true,
              width:120,
              overHidden:true,
            },
            {
              label: "姓名",
              prop: "name",
              align: "center",
              sortable: "custom",
              istrue: true,
              width:90,
              overHidden:true,
            },
          ];
          console.log(num);
          for (let index = 0; index < num; index++) {
            let obj = {
              label: "班次" + (index + 1),
              prop:'classes'+(index + 1),
              children: [
                {
                  label: "项目",
                  prop: "projectName" + (index + 1),
                  align: "center",
                  istrue: true,
                  width:180,
                  overHidden:true,
                },
                {
                  label: "班次",
                  prop: "shiftTypeName" + (index + 1),
                  align: "center",
                  istrue: true,
                  width:100,
                  overHidden:true,
                },
                {
                  label: "上班时间",
                  prop: "onWorkTime" + (index + 1),
                  align: "center",
                  istrue: true,
                  width:90,
                  overHidden:true,
                },
                {
                  label: "下班时间",
                  prop: "offWorkTime" + (index + 1),
                  align: "center",
                  istrue: true,
                  width:90,
                  overHidden:true,
                },
                {
                  label: "时长",
                  prop: "hours" + (index + 1),
                  align: "center",
                  istrue: true,
                  width:90,
                  overHidden:true,
                },
              ],
            };
            colum.push(obj);
          }
          colum.push({
            label: "合计时长",
            prop: "workHours",
            align: "center",
            sortable: "custom",
            istrue: true,
            width:100,
            overHidden:true,
          });
          this.tableCols = colum;
          console.log(this.$refs.myTable);
          this.$refs.myTable.doLayout()
          console.log(this.tableCols);
        })
        .catch((err) => {
          console.log(err);
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    exOut() {
      let params = Object.assign({}, this.paramsSearch);
      let url = "chain/projectattendance/exportExcel";
      this.btnLoading = true
      expotOut(params, url, "考勤信息").then(()=>{
        this.btnLoading=false
      }).catch(()=>{
        this.btnLoading=false
      });
      setTimeout(()=>{
        this.tableLoading = false
      },30000)
    },
    // 翻页处理
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getPage(this.page)
      // this.$emit("changePageNum");
    },
    // 改变页面大小处理
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.page.currentPage = 1;
      this.getPage(this.page)
      // this.$emit("changePageSize");
    },
  },
};
</script>

<style lang="scss" scoped></style>
