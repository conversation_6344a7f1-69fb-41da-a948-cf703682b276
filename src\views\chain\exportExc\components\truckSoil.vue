<template>
  <div ref="print">
    <avue-crud
      ref="crud"
      :data="tableData"
      :table-loading="tableLoading"
      :option="tableOption"
    >
      <template slot="index" slot-scope="{ row }">
        <span>{{ row.$index + 1 }}</span>
      </template>
    </avue-crud>
  </div>
  </template>

  <script>
  import { tableOption2 } from "@/const/crud/chain/exportExc";
  import { mapGetters } from "vuex";
  import { queryByCode } from "@/api/chain/companywaybill.js";
  import { print } from "@/util/util.js";

  export default {
    name: "truckSoil",
    data() {
      return {
        form: {},
        paramsSearch: {},
        tableLoading: false,
        tableOption: tableOption2,
        tableData:[],
      };
    },
    created() {},
    mounted: function () {},
    computed: {
      ...mapGetters(["permissions"]),

    },
    methods: {
      queryByCode(form) {
        tableOption2.column[0].children[3].children[0].label = '-'
        let params = Object.assign({}, form);
        if (params.goDatetime && params.goDatetime.length > 0) {
          params.goDatetimeStart = params.goDatetime[0];
          params.goDatetimeEnd = params.goDatetime[1];
          tableOption2.column[0].children[3].children[0].label = params.goDatetimeStart+' — '+params.goDatetimeEnd
        }
        delete params.goDatetime;
        params.code = "truckSoil";
        tableOption2.column[0].children[1].label = form.$projectInfoId
        this.tableOption = JSON.parse(JSON.stringify(tableOption2));
        this.$refs.crud.doLayout();
        this.$refs.crud.refreshTable();
        this.tableLoading = true;
        queryByCode(params)
          .then((res) => {
            this.tableLoading = false;
            this.tableData = res.data.data;
            this.func();
          })
          .catch(() => {
            this.tableLoading = false;
          });
      },
      print(){
        print(this.$refs.print)
      },
    },
  };
  </script>

  <style lang="scss" scoped>
  /deep/ .closeIcon,
  .checkIcon {
    font-size: 16px;
    color: red;
  }
  /deep/ .checkIcon {
    color: #3dcc90;
  }
  </style>
