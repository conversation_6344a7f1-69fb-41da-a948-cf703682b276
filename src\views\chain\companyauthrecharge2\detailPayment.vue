<template>
  <div class="driverTaskDetail">
    <el-drawer size="1200px"  :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <div style="position:absolute;left:32px;top:16px">
          <el-button type="primary"
              icon="el-icon-download"
              size="small"
              :disabled="tableData.length==0"
              @click="exOut">
            导出</el-button>
          <el-button type="primary"
              icon="el-icon-printer"
              :loading="btnLoading"
              size="small" @click="printPDF">
            下载PDF打印</el-button>
        </div>
        <div ref="print">
          <div style="text-align:center;margin-bottom:14px;padding-top:14px">
            <span  style="border-bottom:1px solid #9a9a9a;padding:0px 20px 4px;"> <span v-if="tableData&&tableData.length>0">{{tableData[0].companyName}}</span></span>
            <span>支付明细单</span>
          </div>
          <!-- :summary-method="summaryMethod" -->
          <avue-crud ref="crud" :data="tableData"  :table-loading="tableLoading" :option="tableOption" v-model="form"
            @on-load="getPage">
          </avue-crud>

        </div>
      </basic-container>
    </el-drawer>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {getBatchTransDetailByPaymentIdList,downloadPaymentDetailPdf} from '@/api/chain/companyauthrecharge'
import { print } from "@/util/util.js";
import { expotOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    selectList: {
      type: Array,
      default: ()=>{
        return []
      }
    },
    payeeName: {
      type: String,
      default: ()=>{
        return ''
      }
    },
  },
  data() {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      tableData: [],
      paramsSearch: {

      },
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: false,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: false,
        menu:false,
        showSummary: true,
        sumtext:'合计',
        refreshBtn:false,
        sumColumnList: [
            {
              label:'',
              name: 'waybillCnt',
              type: 'sum'
            },
            {
              name: 'bankPaySuccessAmount',
              type: 'sum'
            },
          ],
        column: [
          {
            label: "支付渠道",
            prop: "paymentChannelName",
          },
          {
            label: "项目",
            prop: "projectInfoName",
            overHidden:true,
          },
          {
            label: "支付时间",
            prop: "payTimes",
            overHidden:true,
          },
          {
            label: "支付单号",
            prop: "paymentNo",
            overHidden:true,
          },
          {
            label: "收款人/车队",
            prop: "payeeName",
            overHidden:true,
          },
          {
            label: "收款卡号",
            prop: "bindingBankNo",
            overHidden:true,
          },
          {
            label: "收款银行",
            prop: "bindingBankName",
            overHidden:true,
          },
          {
            label: "手机号",
            prop: "payeeMobile",
            overHidden:true,
          },
          {
            label: "支付运单数量",
            prop: "waybillCnt",
            overHidden:true,
          },
          {
            label: "付款金额",
            prop: "bankPaySuccessAmount",
            overHidden:true,
          },
          {
            label: "资金计划名称",
            prop: "planName",
            overHidden:true,
          },
          {
            label: "资金计划运单数量",
            prop: "planWaybillCnt",
            overHidden:true,
          },
        ],
      },
      btnLoading:false,
    }
  },
  created() {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    getPage(page, params = {}) {
      this.tableLoading = true
      let data = this.selectList.map(item=>{
        return item.id
      })
      getBatchTransDetailByPaymentIdList({paymentIdList:data,payeeName:this.payeeName}).then(response => {
        this.tableData = response.data.data
        // if(this.tableData&&this.tableData.length>0){
        //   this.tableData.push(
        //     {
        //       projectInfoName:'合计',
        //     }
        //   )
        // }
        // this.page.total = response.data.data.total
        // this.page.currentPage = page.currentPage
        // this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    summaryMethod({ columns, data }){
      const sums = [];
      if (columns.length > 0) {
        columns.forEach((column, index) => {
          // console.log(column);
          let prop=column.property
          if (['waybillCnt','amount','payAmount'].includes(prop)) {
            let values = this.tableData.map(item => Number(item[prop] || 0));
            let all=values.length!==0? sums[index] = values.reduce((a, b)=>{
              return a + b;
            }):0
            // if(prop=='sum'){
              sums[index]=all
            // }else if(prop=='avg'){
            //   sums[index]=all/values.length || 0
            // }else if(prop=='count'){
            //   sums[index]=values.length
            // }
          } else {
            sums[index] = index==6?'合计':''
          }
        });
      }
      return sums;
    },
    exOut(){
      let data = this.selectList.map(item=>{
        return item.id
      })
      let url = '/chain/companypayment/exportPaymentTransDetailExcel'
      // params.id = this.info.id
      expotOut({paymentIdList:data,payeeName:this.payeeName},url,'支付明细单');
    },
    printPDF(){
      this.btnLoading = true
      let data = this.selectList.map(item=>{
        return item.id
      })
      downloadPaymentDetailPdf({paymentIdList:data}).then(res=>{
        console.log(res);
        const url = window.URL.createObjectURL(new Blob([res.data]));
		    const link = document.createElement('a');
        link.href = url;
        let fileName = "支付明细单.pdf" //
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        this.btnLoading = false
      }).catch(err=>{
        this.btnLoading = false
      })
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 0px;
  padding-bottom: 20px;
}
/deep/ .avue-crud__menu{
  display: none;
}
/deep/ .el-table{
  border-bottom: 1px solid #9a9a9a;
  border-right: 1px solid #9a9a9a;
  tr{
    .el-table__cell {
      border-left: 1px solid #9a9a9a;
      border-top: 1px solid #9a9a9a;
    }
      border-right: 1px solid #9a9a9a;

  }
}
</style>
