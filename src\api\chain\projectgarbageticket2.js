import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/garbageTicket/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/projectgarbageticket',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/projectgarbageticket/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/garbageTicket/' + id,
        method: 'delete'
    })
}

export function putObj(data) {
    return request({
        url: '/chain/garbageTicket/update',
        method: 'post',
        data
    })
}
