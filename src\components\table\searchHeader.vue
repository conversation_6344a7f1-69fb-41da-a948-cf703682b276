<!--表格组件 -->
<template>
  <div class="programme">
    <el-tag
      :key="index"
      v-for="(tag, index) in tags"
      :closable="tag.closable"
      :disable-transitions="false"
      :effect="isActive == index ? 'dark' : 'plain'"
      @close="handleClose(tag)"
      @click="tagClick($event, index)"
    >
      {{ tag.name }}
    </el-tag>
    <el-input
      class="input-new-tag"
      v-if="inputVisible"
      v-model="inputValue"
      ref="saveTagInput"
      maxlength="10"
      size="mini"
      @keyup.enter.native="handleInputConfirm"
      @blur="handleInputConfirm"
    >
    </el-input>
    <el-button v-else class="button-new-tag" size="mini" @click="showInput"
      >+新增方案</el-button
    >
    <div class="dialog" ref="dialog" v-if="show" :style="{ left: layerX + 'px' }">
      <div class="btns">
        <el-button size="mini" type="primary" @click="add">新增</el-button>
        <el-button size="mini" type="primary" @click="search" :loading="btnLoading">查询</el-button>
        <el-button size="mini" type="primary" @click="save">保存</el-button>
        <el-button size="mini" type="primary" @click="closeDialog">关闭</el-button>
        <el-tooltip class="item" effect="dark" content="文本框的搜索是包含搜索，下拉框的是等于搜索" placement="top">
          <el-button  plain circle size="small" type="primary" style="float: right;margin-right: 10px;" icon="el-icon-question"></el-button>
        </el-tooltip>
      </div>
      <div class="lists" v-if="list && list.length > 0">
        <ul>
          <li v-for="(item, index) in list" :key="index">
            <div class="left">
              <el-select
                v-model="item.prop"
                filterable
                placeholder="请选择"
                @change="selectChange($event,item, index)"
              >
                <el-option
                  v-for="item2 in searchOption.column"
                  :key="item2.prop"
                  :label="item2.label"
                  :value="item2.prop"
                  placeholder="请选择过滤条件"
                >
                </el-option>
              </el-select>
            </div>
            <div class="center">
              <el-input
                v-if="!item.prop"
                v-model="form[item.prop]"
                disabled
                placeholder="请先选择过滤条件"
              ></el-input>
                <slot v-else-if="item.prop&&item.searchSlot"  :name="item.prop+'Search'"
                      :row="form"></slot>
              <component
                v-else
                :is="getComponent(item.type)"
                v-model="form[item.prop]"
                :dic="searchOption.dicData[item.prop]"
                ref="temp"
                :placeholder="item.placeholder"
                :props="item.props"
                :type="item.type"
                :multiple="item.multiple"
                :valueFormat="item.valueFormat"
                :dataType="item.dataType"
                :filterable="true"
              >
              </component>
            </div>
            <div class="right" @click="del(item,index)">
              <el-button type="text">删除</el-button>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <slot name="customForm" :row="form"></slot>
  </div>
</template>

<script>
import {mapState} from "vuex";
import myCards from './components/cards';
export default {
  props: {
    searchOption: {
      type:Object,
      default:()=>{
        return {}
      }
    },
    searchSlot:{
      type:Array,
      default:()=>{
        return []
      }
    }
  },
  components:{
    myCards
  },
  data() {
    return {
      tags: [{ name: "默认方案", closable: false,searchConfig:[]}],
      inputVisible: false,//添加方案输入框是否显示
      inputValue: "",//添加方案输入框值
      show: false,//是否显示筛选框
      form: {},//筛选值
      list: [],//筛选条件list
      isActive: -1,//选中方案index
      layerX: 0,//选中方案x轴
      btnLoading:false,
    };
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
  },
  watch: {},
  mounted() {
    //先获取配置
    this.getScheme()
       console.log("searchSlot", this.searchSlot);
       console.log("userInfo", this.userInfo);
  },
  methods: {
    //判断使用什么组件
    getComponent(type) {
      let result = type||"input"
      if(["time", "timerange"].includes(type)) {
        result = "time";
      } else if (['dates','date','datetime','datetimerange','daterange','time','timerange','week','month','monthrange','year'].includes(type)) {
        result = "date";
      }
      return result=='card'?"my-cards":"avue-" + result;
    },
    //删除方案
    handleClose(tag) {
        if(this.show){
          this.show = false
          this.temSave()
        }
       this.$confirm("是否确认删除此方案？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(()=> {
        this.tags.splice(this.tags.indexOf(tag), 1);
        this.saveScheme()
      }).catch(function (err) {});
    },
    //点击保存
    save(){
      this.temSave()
      this.saveScheme()
      this.$message.success("保存成功")
    },
    //获取方案配置
    getScheme(){
      let scheme = localStorage.getItem('scheme'+this.userInfo.id)
        console.log(this.searchOption);
      if(scheme){
        this.tags = JSON.parse(scheme)
        //获取后要比较一下筛选条件，如果有删除掉的就不要再显示
        let flag = false
        this.tags.forEach((item,i)=>{
         
          if(i==0 && item.searchConfig.find(v=>v.prop=='goShiftTime') && item.searchConfig.find(v=>v.prop=='goShiftType')){
            // debugger
            let fistIndex = item.searchConfig.findIndex(v=>v.prop=='goShiftTime')
            let tmp = item.searchConfig[fistIndex]
            item.searchConfig.splice(fistIndex,1)
            let secondIndex = item.searchConfig.findIndex(v=>v.prop=='goShiftType')

            item.searchConfig.splice(secondIndex,0,tmp)
          }
          if(item.searchConfig&&item.searchConfig.length>0){
            item.searchConfig.forEach((item2,index,arr)=>{
              let obj = this.searchOption.column.find((item3) => item3.prop == item2.prop)
            
              
              //没有找到
              if(!obj){
                arr.splice(index, 1);
                flag = true
              }
            })
          }
        })
        //删除后重新保存
        if(flag){
          this.saveScheme()
        }
      }else{
        //没有配置就给默认所有配置
        console.log(this.searchOption);
          this.tags[0].searchConfig = this.searchOption.column.map(item=>{
            item.multiple = item.searchMultiple;
            return item
          })
         console.log(this.tags[0].searchConfig);
      }
    },
    //保存方案配置
    saveScheme(){
      localStorage.setItem('scheme'+this.userInfo.id,JSON.stringify(this.tags))
    },
    //临时保存方案配置 关闭前保存
    temSave(){
      this.list.forEach(item=>{
        if(item.prop){
          item.value = this.form[item.prop]
        }
      })
      this.tags[this.isActive].searchConfig = this.list
    },
    //点关闭弹窗
    closeDialog(){
      this.btnLoading = false
      this.temSave()
      this.show=false
      this.isActive = -1
    },
    //添加方案显示输入框
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    //确定添加方案
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.tags.push({ name: inputValue, closable: true,searchConfig:[]});
      }
      this.inputVisible = false;
      this.inputValue = "";
      this.saveScheme()
    },
    //添加搜索条件
    add() {
      this.list.push({ prop: "" });
    },
    //删除搜索条件
    del(item,index) {
      this.form[item.prop] = ""
      this.list.splice(index, 1);
    },
    //点击方案
    tagClick(event, index) {
      if (this.isActive == index) return false;
      //打开状态
      if(this.show){
        this.temSave()
      }
      this.list = this.tags[index].searchConfig
      this.form = {}
      this.list.forEach(item=>{
        if(item.prop&&item.value){
          this.form[item.prop] = item.value
        }
      })
      console.log(this.list);
      if(this.list&&this.list.length==0){
        let obj = JSON.parse(JSON.stringify(this.searchOption.column[0]))
        this.list.push(obj)
      }
      this.show = true
      this.isActive = index;
      this.layerX = event.layerX;
    },
    //点击查询
    search(){
      console.log(this.form);
      let params = {}
      this.list.forEach(item=>{
        if(item.prop&&this.form[item.prop]){
          params[item.prop] = this.form[item.prop]
        }
      })
      console.log(params);
      this.btnLoading = true
      this.$emit("searchChange",params,this.closeDialog)
    },
    //选择筛选条件
    selectChange(event,item, index) {
      let data = event
      let flag = true
      this.list.forEach((item3, index2) => {
        if (data && data == item3.prop && index != index2) {
          flag = false
          item.prop = "";
          // item.dicUrl = "";
          // item.dicData = "";
          item.label = "";
          item.props = "";
          item.placeholder = "";
          item.type = "";
          item.multiple = undefined;
          item.valueFormat = undefined;
          item.dataType = undefined;
          item.searchSlot = false;
          console.log(11111111);
          this.$message.error("此筛选条件已存在，请重新选择！");
        }
      });
      if(flag){
        let obj = this.searchOption.column.find((item) => item.prop == data)
        console.log(obj);
        console.log(this.searchOption);
        item.prop = obj.prop;
        item.searchSlot = obj.searchSlot;
        // item.dicData = this.searchOption.dicData[data]
        item.multiple = obj.searchMultiple;
        item.valueFormat = obj.valueFormat;
        item.label = obj.label;
        item.props = obj.props;
        item.placeholder = obj.placeholder;
        item.type = obj.type;
        item.dataType = obj.dataType;
      }
    },
  },
  created() {},
};
</script>
<style lang="scss" scoped>
.programme {
  position: relative;
  .dialog {
    position: absolute;
    left: 0;
    top: 36px;
    z-index: 999;
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-width: 532px;
    min-height: 100px;
    box-sizing: border-box;
  }
  .lists {
    margin-top: 10px;
    padding: 10px;
    background-color: #fff;
    max-height: 300px;
    overflow-y: auto;
    border-radius: 4px;
    min-width: 532px;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    ul {
      li {
        display: flex;
        line-height: 48px;
        .left {
          margin-right: 6px;
          width: 200px;
        }
        .center {
          margin-right: 6px;
          width: 360px;
          .el-input__inner{
            width: 100%;
          }
          .el-select{
            width: 100%;
          }
         /deep/ .el-date-editor--datetimerange.el-input__inner{
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
