<template>
  <div class="merchantDetail">
    <el-drawer
      size="1120px"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
      :wrapperClosable="false"
      center
    >
      <div id="accountSheet" ref="accountSheet" style="padding: 30px 80px 20px">
        <div class="topTitle">
          <span>结算凭证</span>
        </div>
        <div class="box">
          <div class="item">
            <span class="title">结算单号：</span>
            <span>{{ accountForm.settleNo }}</span>
          </div>
          <div class="item2">
            <span class="title">结算申请人：</span>
            <span>{{ accountForm.agentName }}</span>
          </div>
        </div>
        <div class="box">
          <div class="item">
            <span class="title">付款方：</span>
            <span>{{ accountForm.companyName }}</span>
          </div>
          <div class="item2">
            <span class="title">结算时间：</span>
            <span>{{ accountForm.settleDatetime }}</span>
          </div>
        </div>
        <div
          class="table"
          v-for="(item, index) in waybillData.body"
          :key="index"
        >
          <!-- <div class="tableTitle">
            <span>承运人:{{ item.carrier_name }}</span
            ><span class="red">共计{{ item.cnt }}车，{{ item.amt }}元</span>
          </div> -->

          <div class="inner" style="margin: 20px 0;" v-for="(list, k) in item.detail" :key="k + 100">
            <!-- <div class="tableTitle">
              <span>{{ list.create_datetime }}</span
              ><span class="red"
                >共计{{ list.cnt }}车，{{ list.total_amt }}元</span
              >
            </div> -->
            <el-table :data="list.detailDetail" border class="table">
              <el-table-column
                property="goDatetime"
                width="100px"
                label="出场日期"
                align="center"
              ></el-table-column>
              <el-table-column
                property="projectName"
                width="100px"
                label="项目名称"
                align="center"
              ></el-table-column>
              <el-table-column
                property="fleetName"
                width="100px"
                label="车队名称"
                align="center"
              ></el-table-column>
              <el-table-column
                property="truckCode"
                width="100px"
                label="车牌号"
                align="center"
              ></el-table-column>
              <el-table-column
                property="goSoilType"
                width="100px"
                label="运输土质"
                align="center"
              ></el-table-column>
              <el-table-column
                property="tpMode"
                width="100px"
                label="运输类型"
                align="center"
              ></el-table-column>
              <el-table-column
                property="settleCnt"
                label="运单数"
                align="center"
              ></el-table-column>
              <el-table-column
                property="weightUnit"
                label="单位"
                align="center"
              ></el-table-column>
              <el-table-column
                property="settlePrice"
                label="单价"
                align="center"
              ></el-table-column>
              <el-table-column
                property="totalAmt"
                label="总价"
                align="center"
              ></el-table-column>
            </el-table>
          </div>
          <div class="box">
            <div class="item3">
              <span class="title">收款方：</span>
              <span>{{ item.carrier_name }}</span>
            </div>
            <div class="item4">
              <span class="title">电话：</span>
              <span>{{ item.mobile }}</span>
            </div>
          </div>
          <div class="box">
            <div class="item3">
              <span class="title">开户行：</span>
              <span>{{ item.binding_bank_name }}</span>
            </div>
            <div class="item4">
              <span class="title">卡号：</span>
              <span>{{ item.binding_bank_no }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="demo-drawer__footer">
        <el-button @click="downExport" size="small" icon="el-icon-bottom"
          >导出</el-button
        >
        <el-button
          type="primary"
          :loading="loading"
          @click="printPDF"
          size="small"
          icon="el-icon-printer"
          >下载PDF打印</el-button
        >
      </div>
    </el-drawer>
    <!-- 弹窗 -->
    <el-dialog
      width="70%"
      title="运单详情"
      :visible="dialogVisible"
      :before-close="closeDialog"
      :close-on-click-modal="false"
    >
      <div class="table" style="margin-top: 20px">
        <el-table :data="tableData" border :loading="loading">
          <el-table-column
            property="no"
            min-width="100px"
            label="运单号"
          ></el-table-column>
          <el-table-column
            property="agentName"
            min-width="100px"
            label="项目合作方"
          ></el-table-column>
          <el-table-column
            property="truckCode"
            min-width="100px"
            label="车牌号"
          ></el-table-column>
          <el-table-column property="tpMode" min-width="100px" label="运输方式">
            <template slot-scope="scope">
              <div>{{ filterTpMode(scope.row.tpMode) }}</div>
            </template>
          </el-table-column>
          <el-table-column
            property="garbageName"
            min-width="100px"
            label="泥尾"
          ></el-table-column>
          <el-table-column
            property="goSoilType"
            min-width="100px"
            label="土质"
          ></el-table-column>
          <el-table-column
            property="captainName"
            label="车队长"
          ></el-table-column>
          <el-table-column property="driverName" label="司机"></el-table-column>
          <el-table-column
            property="inStaffName"
            min-width="100px"
            label="挖机签单员"
          ></el-table-column>
          <el-table-column
            property="goStaffName"
            min-width="100px"
            label="出场签单员"
          ></el-table-column>
          <el-table-column
            property="load"
            min-width="100px"
            label="容量"
          ></el-table-column>
          <el-table-column
            property="inDatetime"
            min-width="120px"
            label="挖机签单时间"
          ></el-table-column>
          <el-table-column
            property="goDatetime"
            min-width="120px"
            label="出场签单时间"
          ></el-table-column>
          <el-table-column
            property="completeDatetime"
            min-width="120px"
            label="完成时间"
          ></el-table-column>
          <el-table-column
            property="settlePrice"
            min-width="100px"
            label="结算价"
          ></el-table-column>
          <el-table-column
            property="exAmt"
            min-width="100px"
            label="异常金额"
          ></el-table-column>
          <el-table-column
            property="adjustAmt"
            min-width="100px"
            label="增减值"
          ></el-table-column>
          <el-table-column
            property="goRemark"
            min-width="100px"
            label="备注"
          ></el-table-column>
          <el-table-column
            property="weightTons"
            min-width="100px"
            label="重量(吨)"
          ></el-table-column>
          <el-table-column
            property="isReject"
            min-width="100px"
            label="是否驳回"
          >
            <template slot-scope="scope">
              <div>{{ scope.row.isReject == 1 ? "是" : "否" }}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import { clearNoNum } from "@/util/util.js";
import {
  printWaybillDetail,
  downloadSettleDocPdf,
} from "@/api/chain/companysettle";
import $Print from "avue-plugin-print";
import { print } from "@/util/util.js";
import { expotOut } from "@/util/down.js";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    //结算数据
    accountForm: {},
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      waybillData: this.accountForm.tableData,
      tableData: [],
      quality: 1.0,
    };
  },
  mounted() {
    console.log(this.waybillData);
  },
  methods: {
    /* ------------------------------------------------------- 下载PDF打印 ----------------------------------------------------- */
    // printPDF() {
    //   this.loading = true;
    //   let params = {
    //     settleId: this.accountForm.id,
    //   };
    //   downloadSettleDocPdf(params)
    //     .then((res) => {
    //       console.log(res);
    //       const url = window.URL.createObjectURL(new Blob([res.data]));
    //       const link = document.createElement("a");
    //       link.href = url;
    //       let fileName = "结算凭证.pdf"; //
    //       link.setAttribute("download", fileName);
    //       document.body.appendChild(link);
    //       link.click();
    //       this.loading = false;
    //     })
    //     .catch((err) => {
    //       this.loading = false;
    //     });
    // },
    printPDF() {
      // this.loading = true;
      // let params = {
      //   settleId: this.accountForm.id,
      // };

      const element = document.getElementById("accountSheet");
      // 使用html2canvas将DOM渲染为Canvas
      html2canvas(element, {
        scale: 2, // 提高输出质量
        backgroundColor: "#FFFFFF",
        useCORS: true, // 处理跨域图片
      })
        .then((canvas) => {
          // console.log(canvas);
          // 获取图片数据
          const imgData = canvas.toDataURL("image/jpeg", this.quality);

          const pdf = new jsPDF({
            orientation: "portrait",
            unit: "mm",
            format: "a4",
          });

          const pdfWidth = pdf.internal.pageSize.getWidth(); // 210mm
          const pdfHeight = pdf.internal.pageSize.getHeight(); // 297mm

          const imgWidth = pdfWidth;
          const imgHeight = (canvas.height * imgWidth) / canvas.width;

          let heightLeft = imgHeight;
          let position = 0;

          // 添加第一页
          pdf.addImage(imgData, "JPEG", 0, position, imgWidth, imgHeight);
          heightLeft -= pdfHeight;

          while (heightLeft > 0) {
            position -= pdfHeight;
            pdf.addPage();
            pdf.addImage(imgData, "JPEG", 0, position, imgWidth, imgHeight);
            heightLeft -= pdfHeight;
          }

          pdf.save("结算凭证.pdf");
        })
        .catch((err) => {
          console.error("截图导出失败:", err);
        });
    },

    /* ------------------------------------------------------- 下载exc ----------------------------------------------------- */
    downExport() {
      let params = {
        settleId: this.accountForm.id,
      };
      let url = "/chain/companysettle/exportExcelSettleDoc";
      expotOut(params, url, "结算凭证");
    },
    /* ------------------------------------------------------- 取消查看核算 ----------------------------------------------------- */
    cancelModal() {
      this.$emit("update:visible", false);
    },
    /* ------------------------------------------------------- 关闭弹窗 ----------------------------------------------------- */
    closeDialog() {
      this.dialogVisible = false;
    },
    /* ------------------------------------------------------- 查看运单 ----------------------------------------------------- */
    toWaybill(row, item) {
      let param = {
        settleId: this.accountForm.id,
        settleDate: item.create_datetime,
        truckId: row.truck_id,
      };
      this.tableData = [];
      this.loading = true;
      printWaybillDetail(param)
        .then((res) => {
          this.loading = false;
          this.dialogVisible = true;
          this.tableData = res.data.data;
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.topTitle{
  font-size: 24px;
  font-weight: 700;
  color: #000;
  text-align: center;
  line-height: 40px;
}
.box {
  display: flex;
  justify-content: space-between;
  line-height: 30px;
  color: #2F2F2F;
  .item {
    display: flex;
    justify-content: flex-start;
    .title {
      width: 80px;
      color: #000;
    }
  }
  .item2 {
    width: 250px;
    display: flex;
    justify-content: flex-start;
    .title {
      width: 100px;
      color: #000;
    }
  }
  .item3 {
    
    display: flex;
    justify-content: flex-start;
    .title {
      width: 75px;
      color: #000;
    }
  }
  .item4 {
    width: 225px;
    display: flex;
    justify-content: flex-start;
    .title {
      width: 56px;
      color: #000;
    }
  }
}
.info {
  display: flex;
  justify-content: flex-start;
  justify-content: space-between;
  flex-wrap: wrap;
  // border-bottom: 1px solid #ccc;
  padding-top: 20px;
  padding-bottom: 22px;
  li {
    width: 50%;
    line-height: 30px;
    color: #000;
    div {
      width: 100%;
      justify-content: flex-start;
      justify-content: flex-end;
    }
    label {
      display: inline-block;
      width: 100px;
    }
    span {
      color: #666;
      // font-size: 14px;
    }
  }
}
.isClick {
  color: #409eff;
  cursor: pointer;
  display: block;
  text-decoration: underline;
}
/deep/ .el-drawer__header {
  margin-bottom: 0px;
  span {
    text-align: center;
    font-size: 18px;
    font-weight: 700;
    color: #000;
  }
}
/deep/ .el-drawer__body {
  padding: 0px;
  // padding-bottom: 70px;
  #accountSheet {
    padding: 0 20px;
  }
  .demo-drawer__footer {
    width: 100%;
    text-align: center;
    background-color: rgba(204, 204, 204, 0.7);
    border-radius: 6px;
    line-height: 60px;
    position: absolute;
    bottom: 10px;
  }
}
.tableTitle {
  border: 1px solid #9a9a9a;
  margin-left: 1px;
  border-bottom: none;
  line-height: 32px;
  padding: 0px 10px;
  // color: #606266;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.red {
  color: red;
}
/deep/ .el-table {
  border-bottom: 1px solid #9a9a9a;
  border-right: 1px solid #9a9a9a;
  color: #2F2F2F;
  tr {  color: #2F2F2F;
    .el-table__cell {
      border-left: 1px solid #9a9a9a;
      border-top: 1px solid #9a9a9a;
    }
    border-right: 1px solid #9a9a9a;
  }
  .el-table__row {
    td {
      padding: 4px 0px;
    }
  }
  .el-table__header {
    th {
      padding: 4px 0px;
    }
  }
}
</style>
