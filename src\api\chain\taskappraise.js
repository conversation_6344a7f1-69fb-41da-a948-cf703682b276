import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/taskappraise/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/taskappraise',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/taskappraise/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/taskappraise/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/taskappraise',
        method: 'put',
        data: obj
    })
}
