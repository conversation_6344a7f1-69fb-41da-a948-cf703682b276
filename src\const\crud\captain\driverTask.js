export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  searchSpan: 8,
  searchLabelWidth: 100,
  excelBtn: false,
  printBtn: false,
  viewBtn: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchMenuSpan: 6,
  labelWidth:130,
  menuWidth:160,
  // calcHeight: 135,
  // height: "auto",
  // defaultSort: {
  //   prop: "createDatetime",
  //   order: "descending",
  // },
  column: [
    {
      label: "施工单位",
      prop: "companyAuthName",
      minWidth:160,
      overHidden:true,
    },
    {
      label: "项目名称",
      prop: "projectName",
      search:true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "运输任务开始时间",
      prop: "tpDatetime",
      minWidth:150,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "运输任务类型",
      prop: "tpMode",
      // sortable: true,
      type: "select",
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode',
      minWidth:94,
      overHidden:true,
    },
    {
      label: "项目任务状态",
      prop: "projectStatus",
      search: true,
      type: "select",
      dicData: [
        {
          label: "任务进行中",
          value: "1",
        },
        {
          label: "任务完成",
          value: "9",
        },
      ],
      minWidth:94,
      overHidden:true,
    },
    {
      label: "派单新增时间",
      prop: "createDatetime",
      minWidth:150,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "任务状态",
      prop: "status",
      search: true,
      type: "select",
      dicData: [
        {
          label: "未出发/已收车",
          value: "1",
        },
        {
          label: "运输中",
          value: "2",
        },
        {
          label: "已结束",
          value: "3",
        },
        {
          label: "已完成",
          value: "4",
        },
        {
          label: "取消任务/放弃任务",
          value: "5",
        },
      ],
      minWidth:80,
      overHidden:true,
    },
    {
      label: "派单司机姓名",
      prop: "fleetName",
      search: true,
      minWidth:94,
      overHidden:true,
    },
    {
      label: "司机号码",
      prop: "mobile",
      minWidth:120,
      overHidden:true,
    },
    {
      label: "派单按车价格",
      prop: "price",
      minWidth:96,
      overHidden:true,
    },
    {
      label: "派单称重价格(吨)",
      prop: "tonsPrice",
      minWidth:116,
      overHidden:true,
    },
    {
      label: "运单数量",
      prop: "companyWaybillCount",
      minWidth:80,
      overHidden:true,
    },
  ],
};
