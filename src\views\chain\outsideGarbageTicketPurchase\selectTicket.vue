<template>
  <div class="selectTicket">
    <el-drawer size="900px"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <span slot="title"
            class="drawer-title">
        <el-button type="primary"
                   size="small"
                   @click="submit">保存</el-button>
      </span>
      <basic-container>
        <!-- <el-radio-group v-model="tabPosition"
                        style="margin-bottom:20px;">
          <el-radio-button label="1">选择票号</el-radio-button>
          <el-radio-button label="2">已选择票号</el-radio-button>
        </el-radio-group> -->
        <avue-crud ref="selectTicket"
                 :data="tableData"
                 :page="page"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @search-change="searchChange"
                 @selection-change="handleSelectionChange"
                 @search-reset="searchReset"
                 @on-load="getPage">
          <template slot="nubsSearch"
                    slot-scope="scope">
            <div style="display: flex;">
              <el-input-number style="margin-right:2px;"
                               v-model="startNo"
                               :min="0"
                               :precision="0"
                               :controls="false"
                               placeholder="开始票号"
                               size="small"
                               :step="1"
                               step-strictly></el-input-number>
              至
              <el-input-number style="margin-left: 2px;"
                               v-model="endNo"
                               :min="0"
                               :precision="0"
                               :controls="false"
                               placeholder="结束票号"
                               size="small"
                               :step="1"
                               step-strictly></el-input-number>
            </div>
          </template>
        </avue-crud>
        <!-- <div>
          <el-tag v-for="tag in multipleSelection"
                  :key="tag.ticketNo"
                  v-show="tabPosition==2"
                  @close="handleClose(tag)"
                  closable>
            {{tag.ticketNo}}
          </el-tag>
        </div> -->
      </basic-container>
    </el-drawer>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getTicketNoPage, getTicketPrefix } from '@/api/chain/outsideGarbageTicketPurchase'

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      tableData: [],
      paramsSearch: {

      },
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: false,
        align: "center",
        searchShow: true,
        labelWidth: 150,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: false,
        menu: false,
        refreshBtn: false,
        selection: true,
        reserveSelection: true,
        rowKey: "id",
        height: "auto",
        calcHeight: 30,
        // useVirtual:true,
        // recordTableSelect:true, //是否记录表格的选项id(必须保证行ID存在，且唯一)注意：只有每页不超过1000条数据，才有效
        searchLabelWidth: 80,
        searchMenuSpan:8,
        header:false,
        column: [
          {  //票号前缀
            label: "泥尾票前缀",
            prop: "prefix",
            minWidth:80,
            overHidden:true,
          },
          {
            label: "泥尾票编号",
            prop: "noText",
            minWidth:100,
            overHidden:true,
          },
          {
            label: "价格",
            prop: "price",
            minWidth:80,
            overHidden:true,
          },
          {
            label: "选择票号",
            prop: "ticketNumber",
            search: true,
            searchSpan: 8,
            type: "cascader",
            props: { label: 'itemName', value: 'itemValue', children: 'children', },
            display: false,
            separator: "-",
            hide: true,
            showColumn: false,
            searchFilterable: true,
            searchCheckStrictly: true,
          },
          {
            label: "",
            prop: "nubs",
            searchLabelWidth: 1,
            search: true,
            searchSpan: 8,
            hide: true,
            showColumn: false,
          },
        ],
      },
      btnLoading: false,
      multipleSelection: [],
      tabPosition: "1",
      startNo: '',
      endNo: undefined,
    }
  },
  created () {
  },
  mounted () {
    this.getTicketPrefix()
    if(this.list&&this.list.length>0){
      this.$nextTick(()=>{
        setTimeout(()=>{
          this.multipleSelection = this.list.map(item=>{
          this.$refs.selectTicket.toggleRowSelection(item, true);
            return item
          })
          this.$refs.selectTicket.doLayout()
        },100)
        // this.$refs.selectTicket.toggleSelection(this.list)
        // this.multipleSelection = JSON.parse(JSON.stringify(this.list))
      })
    }
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    searchReset () {
      this.startNo = ''
      this.endNo = undefined
    },
    cancelModal () {
      if (this.multipleSelection.length != 0 && this.multipleSelection.length != this.list.length) {
        this.$confirm('此操作将放弃保存选择票号, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$emit("update:visible", false);
        }).catch(() => { });
      } else {
        this.$emit("update:visible", false);
      }
    },
    searchChange (params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    getPage (page, params = {}) {
      this.tableLoading = true
      if (params.ticketNumber) {
        console.log(params.ticketNumber);
        params.prefix = params.ticketNumber[0]
        params.price = params.ticketNumber[1]
        delete params.ticketNumber
      }
      params.startNo = this.startNo
      params.endNo = this.endNo
      getTicketNoPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        garbageId: this.info.garbageId,
        soilType: this.info.soilType,
        status: 1,
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },
    handleClose (tag) {
      this.multipleSelection.splice(this.multipleSelection.indexOf(tag), 1);
      this.$refs.selectTicket.toggleRowSelection(tag, false);
    },
    getTicketPrefix () {
      getTicketPrefix({
        garbageId: this.info.garbageId,
        soilType: this.info.soilType,
        status: 1,
      }).then(res => {
        this.$refs.selectTicket.updateDic("ticketNumber", res.data.data)
      })
    },

    submit(){
      this.$emit('submit',JSON.stringify(this.multipleSelection))
    },
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 0px;
  padding-bottom: 20px;
}
.el-tag {
  margin-left: 10px;
  margin-bottom: 10px;
}

</style>
