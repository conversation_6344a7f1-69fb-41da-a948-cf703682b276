var validate1 = (rule, value, callback)=>  {
  if (that.form.minPayPrice===undefined||that.form.maxPayPrice===undefined) {
    callback(new Error('请输入'));
  } else if (that.form.minPayPrice>that.form.maxPayPrice) {
    callback(new Error('结束价格要大于开始价格!'));
  } else {
    callback();
  }
};
const COLUMN1 = [
  {
    label: "地址",
    prop: "projectAddress",
    disabled: true,
    span: 24,
    placeHolder: " ",
  },
  {
    label: "土质",
    prop: "listSoilType",
    disabled: true,
    span: 24,
    placeHolder: " ",
  },
  {
    label: "泥尾",
    prop: "garbageNames",
    disabled: true,
    span: 24,
    placeHolder: " ",
  },
  {
    label: "车型",
    prop: "vehicleName",
    type: "select",
    dataType:"string",
    props: {
      label: "itemName",
      value: "itemValue",
    },
    dicUrl: `/chain/systemdictionaryitem/listDictionaryItem?dictionary=go_vehicle_type`,
    span: 24,
    multiple: true,
    rules: [
      {
        required: true,
        message: "请选择车型",
        trigger: "change",
      },
    ],
  },
  {
    label: "车数",
    prop: "carsNumber",
    type:"number",
    controls: false,
    minRows: 1,
    maxRows: 999999,
    precision: 0,
    span: 24,
    rules: [
      {
        required: true,
        message: "车数不能为空",
        trigger: "blur",
      },
    ],
  },
  {
    label: "运输方式",
    prop: "tpModel",
    type: "select", // 下拉选择
    dataType:"string",
    props: {
      label: "itemName",
      value: "itemValue",
    },
    dicUrl: "/chain/systemdictionaryitem/listDictionaryItem?dictionary=make_tp_model",
    span: 24,
    multiple: true,
    rules: [
      {
        required: true,
        message: "请选择运输方式",
        trigger: "change",
      },
    ],
  },
  {
    label: "价格",
    prop: "price",
    span: 24,
    rules:[{ required: true,validator: validate1, trigger: 'blur' }]
  },
  {
    label: "结算周期",
    prop: "settlementCycle",
    type: "select", // 下拉选择
    dataType:"string",
    props: {
      label: "itemName",
      value: "itemValue",
    },
    dicUrl:"/chain/systemdictionaryitem/listDictionaryItem?dictionary=settlement_cycle",
    span: 24,
    multiple: true,
    rules: [
      {
        required: true,
        message: "请选择结算周期",
        trigger: "change",
      },
    ],
  },
  {
    label: "备注",
    prop: "remark",
    type: "textarea",
    span: 24,
    maxlength:300,
  },
  {
    label: "图片",
    prop: "imgPath",
    type: "upload",
    listType: "picture-card",
    dataType: "string",
    propsHttp: {
      url: "link",
    },
    action: "/upms/file/upload?fileType=image&dir=matching",
    limit:5,
    span: 24,
    tip: '最多上传5张',
  },
];

const COLUMN2 = [
  {
    label: "地址",
    prop: "projectAddress",
    disabled: true,
    span: 24,
    placeHolder: " ",
  },
  {
    label: "土质",
    prop: "listSoilType",
    span: 24,
    disabled: true,
    placeHolder: " ",
  },
  {
    label: "价格(元)",
    prop: "price",
    span: 24,
    rules:[{ required: true,validator: validate1, trigger: 'blur' }]
  },
  {
    label: "备注",
    prop: "remark",
    type: "textarea",
    span: 24,
    maxlength:300,
  },
  {
    label: "图片",
    prop: "imgPath",
    type: "upload",
    listType: "picture-card",
    dataType: "string",
    propsHttp: {
      url: "link",
    },
    action: "/upms/file/upload?fileType=image&dir=matching",
    limit:5,
    span: 24,
    tip: '最多上传5张',
  },
];
const COLUMN3 = [
  {
    label: "地址",
    prop: "projectAddress",
    disabled: true,
    span: 24,
    placeHolder: " ",
  },
  {
    label: "资源",
    prop: "soilType",
    span: 24,
    type:'select',
    dataType:"string",
    props:{
      label:'itemValue',
      value:'itemValue'
    },
    dicUrl:'/chain/systemdictionaryitem/listDictionaryItem?dictionary=resource_type',
    multiple: true,
    rules: [
      {
        required: true,
        message: "请选择车型",
        trigger: "change",
      },
    ],
  },
  {
    label: "价格(元)",
    prop: "price",
    span: 24,
    rules:[{ required: true,validator: validate1, trigger: 'blur' }]
  },
  {
    label: "备注",
    prop: "remark",
    type: "textarea",
    span: 24,
    maxlength:300,
  },
  {
    label: "图片",
    prop: "imgPath",
    type: "upload",
    listType: "picture-card",
    dataType: "string",
    propsHttp: {
      url: "link",
    },
    action: "/upms/file/upload?fileType=image&dir=matching",
    limit:5,
    span: 24,
    tip: '最多上传5张',
  },
];
const COLUMN4 = [
  {
    label: "地址",
    prop: "projectAddress",
    disabled: true,
    span: 24,
    placeHolder: " ",
  },
  {
    label: "土质",
    prop: "soilType",
    span: 24,
    type:'select',
    dataType:"string",
    props:{
      label:'itemValue',
      value:'itemValue'
    },
    dicUrl:'/chain/systemdictionaryitem/listDictionaryItem?dictionary=soil_type',
    multiple: true,
    rules: [
      {
        required: true,
        message: "请选择车型",
        trigger: "change",
      },
    ],
  },
  {
    label: "价格(元)",
    prop: "price",
    span: 24,
    rules:[{ required: true,validator: validate1, trigger: 'blur' }]
  },
  {
    label: "备注",
    prop: "remark",
    type: "textarea",
    maxlength:300,
    span: 24,
  },
  {
    label: "图片",
    prop: "imgPath",
    type: "upload",
    listType: "picture-card",
    dataType: "string",
    propsHttp: {
      url: "link",
    },
    action: "/upms/file/upload?fileType=image&dir=matching",
    limit:5,
    span: 24,
    tip: '最多上传5张',
  },
];
let that = ""
const COLUMN = (val)=>{
  that = val
  return {
    COLUMN1,
    COLUMN2,
    COLUMN3,
    COLUMN4
  }
}
export default COLUMN
