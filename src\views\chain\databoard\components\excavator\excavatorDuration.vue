<template>
  <div class="waybillFee">
    <searchInfo :info="paramsSearch" :type="2" unit="时" source="5" @searchChange="searchChange" :tab="tab" @exOut="exOut">
    </searchInfo>
    <avue-crud ref="crud" :page.sync="page" :data="tableData" :table-loading="tableLoading" :option="tableOption"
      v-model="form" :search.sync="search" @on-load="getPage">
    </avue-crud>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getCompanyLedgerDigPage as getPage } from "@/api/chain/board";
import searchInfo from './searchInfo';
import { exportOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    tab:{
      type: String,
      default: ""
    }
  },
  components: {
    searchInfo
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      search: {},
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        // maxHeight:[800],
        // height:'auto',
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: true,
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        header: false,
        column: [
          {
            label: "项目名称",
            prop: "projectName",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "台班编号",
            prop: "ledgerNo",
            minWidth: 180,
            overHidden: true,
          },
          {
            label: "挖机车主姓名",
            prop: "ownerName",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "挖机车主手机号",
            prop: "ownerMobile",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "挖机签单员",
            prop: "staffName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "作业类型",
            prop: "ledgerType",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "挖机班次",
            prop: "inShiftTypeName",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "台班状态",
            prop: "auditStatus",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "挖机类型",
            prop: "inType",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "机械型号",
            prop: "machineCode",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "完成量",
            prop: "scheduleWork",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "单位",
            prop: "unitWork",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "地块",
            prop: "landName",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "作业地点",
            prop: "address",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "开始时间",
            prop: "startDatetime",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "结束时间",
            prop: "endDatetime",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "转换时长(小时)",
            prop: "workHour",
            minWidth: 90,
            overHidden: true,
          },

        ],
      },
      active: "1",
      total:0
    }
  },
  created () {
    this.paramsSearch = Object.assign({}, this.info)
  },
  mounted () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    searchChange (params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    getPage (page, params = {}) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          {
            startDate: this.info.startDate,
            endDate: this.info.endDate,
            checkDynamic: this.info.checkDynamic,
            projectInfoId: this.info.projectInfoId
          },
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    exOut (params, done) {
      let url = "/chain/statisticsboard/exportCompanyLedgerDigExcel";
      exportOut(params, url, "台班总时长", 'get').then(res => {
        done()
      }).catch(() => {
        done()
      })
    },
  },

};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

.count {
  margin-bottom: 10px;

  span {
    font-size: 16px;
    font-weight: 700;
  }
}
</style>
