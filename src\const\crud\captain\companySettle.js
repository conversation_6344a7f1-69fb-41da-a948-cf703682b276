export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  searchSpan: 8,
  searchLabelWidth: 100,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu:true,
  searchMenuSpan: 6,
  // defaultSort: {
  //   prop: "createDatetime",
  //   order: "descending",
  // },
  menuWidth:100,
  column: [
    {
      label: "结算单号",
      prop: "settleNo",
      search:true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "施工单位",
      prop: "companyAuthName",
      minWidth:180,
      overHidden:true,
    },
    {
      label: "项目名称",
      prop: "projectName",
      search:true,
      minWidth:180,
      overHidden:true,
    },
    {
      label: "结算单提交时间",
      prop: "createDatetime",
      minWidth:140,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "结算运单数",
      prop: "settleWaybillCount",
      minWidth:90,
      overHidden:true,
    },
    {
      label: "结算单状态",
      prop: "status",
      search: true,
      type: "select",
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=settle_status',
      minWidth:90,
      overHidden:true,
    },
    {
      label: "核验运单数",
      prop: "checkWaybillCount",
      minWidth:90,
      overHidden:true,
    },
    {
      label: "核验完成时间",
      prop: "settleDatetime",
      minWidth:140,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "支付运单数",
      prop: "waybillCnt",
      minWidth:90,
      overHidden:true,
    },
    {
      label: "付款完成时间",
      prop: "moneyDatetime",
      minWidth:140,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "运单申报时间",
      prop: "searchDate",
      type: "date",
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd',
      hide:true,
      showColumn:false,
    },
  ],
};
