import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/garbage/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/garbage',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/garbage/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/garbage/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/garbage',
        method: 'put',
        data: obj
    })
}

export function getGarbage(query) {
  return request({
    url: '/chain/systemdictionaryitem/listDictionaryItem',
    method: 'get',
    params: query
  })
}
export function garbageRecordList(query) {
  return request({
    url: '/chain/garbagerecord/garbageRecordList',
    method: 'get',
    params: query
  })
}
export function addGarbagerecord(obj) {
  return request({
    url: '/chain/garbage/addGarbageRecord',
    method: 'post',
    data: obj
  })
}
export function checkExistsProject(id) {
  return request({
    url: '/chain/garbage/checkExistsProject/'+id,
    method: 'get',
  })
}
