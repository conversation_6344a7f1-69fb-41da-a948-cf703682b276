<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="70%"
      :title="mode==1?'批量添加成员':'批量删除成员'"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <el-row>
          <el-col  :md="24" :lg="24" :xl="12" style="margin-bottom:20px">
            <!-- <el-transfer
              filterable
              :titles="['可选员工', '已选员工']"
              v-model="memberIdList"
              :props="{
                key: 'id',
                label: 'name'
              }"
              :data="staffList">
            </el-transfer> -->
            <tree-transfer ref="treeTransfer"></tree-transfer>
          </el-col>
          <el-col  :md="24" :lg="24" :xl="12" style="margin-bottom:20px">
            <el-transfer
              filterable
              :titles="['可选项目', '已选项目']"
              v-model="projectInfoIdList"
              :props="{
                key: 'id',
                label: 'projectName'
              }"
              :data="projectList">
            </el-transfer>
          </el-col>
        </el-row>
        <div class="btns" style="text-align:center;margin-top:20px">
          <el-popconfirm
            placement="top" @confirm="handleUpdate" :title="mode==1?'确认将选中的员工添加到选中的项目成员中?':'确认将选中的员工从选中的项目成员及负责人中删除?'">
              <el-button type="primary" :loading="btnLoading" slot="reference">确 定</el-button>
          </el-popconfirm>
              <el-button @click="cancelModal" style="margin-left:20px">取 消</el-button>
        </div>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getMemberList,getProjectList,batchUpdatemember,checkStaffOnWork} from "@/api/chain/projectinfo";
import treeTransfer from "./components/treeTransfer.vue"
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    mode:{
      type: Number,
      default: 1,
    },
  },
  components:{
    treeTransfer
  },
  data() {
    return {
      staffList:[],
      projectList:[],
      memberIdList:[],
      projectInfoIdList:[],
      btnLoading:false,
    };
  },
  created() {},
  mounted() {
    getMemberList().then(res=>{
      this.staffList = res.data.data
    })
    getProjectList().then(res=>{
      this.projectList = res.data.data
    })
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    handleUpdate(){
      console.log(this.$refs.treeTransfer.keyarr);
      this.memberIdList = this.$refs.treeTransfer.keyarr.map(item=>item.id)
      if(this.memberIdList.length<1){
        this.$message.error('请选择人员')
        return false
      }
      if(this.projectInfoIdList.length<1){
        this.$message.error('请选择项目')
        return false
      }
      if(this.mode==2){//删除才需要检查是否有上班的
        checkStaffOnWork({projectInfoIdList:this.projectInfoIdList,memberIdList:this.memberIdList,type:this.mode}).then(res=>{
          if(res.data.code==789){
            this.$confirm(res.data.msg, "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              this.batchUpdatemember()
            }).catch(() => {});
          }else{
            this.batchUpdatemember()
          }
        })
      }else{
        this.batchUpdatemember()
      }
    },
    batchUpdatemember(){
      this.btnLoading = true
      batchUpdatemember(
        {projectInfoIdList:this.projectInfoIdList,memberIdList:this.memberIdList,type:this.mode}
      ).then(res=>{
        this.btnLoading = false
        this.$emit("update:visible", false);
        this.$emit("getPage");
      }).catch(()=>{
        this.btnLoading = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .drawerHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

}
/deep/ .el-transfer-panel__body .el-transfer-panel__filter{
  width: auto;
}
</style>
