<template>
  <!-- 导入 -->
  <el-dialog
    width="400px"
    title="导入"
    center
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
  <div style="text-align:center;position: fixed;background: #fff;width: 350px;">
      <el-button type="primary"  @click="refresh" icon="el-icon-refresh" size="small" style="margin-right:20px">刷新</el-button>
      <el-switch v-model="isRefresh" @change="changeSwitch">
      </el-switch>自动刷新(5s)
  </div>
    <ul v-if="msgList&&msgList.length>0" style="margin-top:40px">
      <li v-for="(item,index) in msgList" style="line-height:32px" :key="index">{{item}}</li>
    </ul>
    <el-empty v-else description="暂无数据" ></el-empty>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary"  @click="handleClose">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {thirdImportTaskMessage} from "@/api/chain/companywaybill";
export default {
  props: {
    visible: {
      type: Boolean,
      default:()=>{
        return false
      },
    },
  },
  data() {
    return {
      timer: null,
      msgList:[],
      isRefresh:true,
    };
  },
  created() {},
  destroyed() {},
  mounted() {
    this.getMsg()
    this.changeSwitch(true)
  },
  beforeDestroy() {},
  computed: {},
  methods: {
    handleClose() {
      window.clearInterval(this.timer)
      this.timer = null
      this.$emit("update:visible", false);
    },
    changeSwitch(val){
      if(val){
        this.timer = setInterval(()=>{
          this.getMsg()
        },5000)
      }else{
        window.clearInterval(this.timer)
        this.timer = null
      }
    },
    getMsg(){
      thirdImportTaskMessage().then(res=>{
        this.msgList = res.data.data
      }).catch((err)=>{
        this.$message.error(err.data.msg||"获取信息失败")
      })
    },
    refresh(){
      this.getMsg()
    }
  },
};
</script>

<style lang="scss" scoped>

</style>
