<template>
  <div class="Echarts">
    <div class="subTitle">
      共出车{{ this.titleData.truckTotalCount }}单，其中{{
        this.titleData.truckCorrectCount
      }}单参与统计<el-tooltip class="item" effect="dark" placement="top-start"
        ><i class="el-icon-question"></i
        ><template #content
          >单位为非车方吨{{ titleData.unitNotCorrectCount }}单不参与统计
          <br />
          单位为吨土质不设置单位换算{{
            titleData.soilTypeNotCorrectCount
          }}单不参与统计
        </template></el-tooltip
      >
    </div>
    <div class="empty" v-if="isEmpty">
      <el-empty description="暂无数据"></el-empty>
    </div>
    <div ref="barchart" style="width: 100%; flex: 1" v-if="!isEmpty"></div>
  </div>
</template>

<script>
export default {
  name: "barEcharts",
  props: {
    isEmpty: {
      type: Boolean,
    },
    titleData: {
      type: Object,
    },
    echartData: {
      type: Object,
    },
  },
  watch: {
    isEmpty(val) {
      if (!val) {
        this.$nextTick(() => {
          this.myEcharts();
          window.addEventListener("resize", this.resizeHanlder);
        });
      }
    },
    echartData: {
      handler(val) {
        if (this.chart) {
          let option = this.chart.getOption();
          option.xAxis = {
            name: "运输方式",
            type: "category",
            splitLine: { show: false },
            axisLine: {
              lineStyle: {
                color: "#5470c6",
                width: 1, //这里是为了突出显示加上的
              },
            },
            axisTick: {
              show: false, //是否显示坐标轴刻度
            },
            data: this.echartData.biPhaseTruckTpModeBarData.tpModeName,
          };
          option.series[0].data =
            this.echartData.biPhaseTruckTpModeBarData.tpModeCount;
          this.chart.setOption(option);
          // this.chart.resize()
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  methods: {
    myEcharts() {
      // 基于准备好的dom，初始化echarts实例
      this.chart = this.$echarts.init(this.$refs.barchart);
      let option = {
        // title: {
        //   text: ``,
        //   // subtext: "Living Expenses in Shenzhen",
        //   x:'center',
        //   textAlign:'left',
        // },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            return params[0].axisValue + " : " + params[0].value;
          },
        },
        grid: {
          left: "3%",
          right: "8%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          name: "运输方式",
          type: "category",
          splitLine: { show: false },
          color: "#5470c6",
          axisLine: {
            lineStyle: {
              color: "#5470c6",
              width: 1, //这里是为了突出显示加上的
            },
          },
          axisTick: {
            show: false, //是否显示坐标轴刻度
          },
          data: this.echartData.biPhaseTruckTpModeBarData.tpModeName,
        },
        yAxis: {
          name: "出车数",
          type: "value",
          axisLine: {
            lineStyle: {
              color: "#5470c6",
              width: 0, //这里是为了突出显示加上的
            },
          },
          axisTick: {
            show: false, //是否显示坐标轴刻度
          },
        },
        series: [
          {
            type: "bar",
            color: "#5470c6",
            label: {
              show: true,
              position: "top",
            },
            data: this.echartData.biPhaseTruckTpModeBarData.tpModeCount,
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      this.chart.setOption(option);
    },
    resizeHanlder() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  },

  mounted() {
    // this.myEcharts();
    window.addEventListener("resize", this.resizeHanlder);
  },
  beforeDestroy() {
    // if (!this.chart) {
    //   return;
    // }
    // window.removeEventListener("resize", this.resizeHanlder);
    // this.chart.dispose();
    // this.chart = null;
  },
};
</script>

<style lang="scss" scoped>
/deep/.el-empty {
  padding: 15px 0 !important;
}
.Echarts {
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.subTitle {
  padding-top: 15px;
  line-height: 30px;
  color: #464646;
  font-size: 16px;
}
</style>
