<template>
    <div ref="print">
      <avue-crud
        ref="crud"
        :data="tableData"
        :table-loading="tableLoading"
        :option="tableOption"
      >
      </avue-crud>
    </div>
  </template>

  <script>
  import { tableOption3 } from "@/const/crud/chain/exportExc";
  import { mapGetters } from "vuex";
  import { queryByCode } from "@/api/chain/companywaybill.js";
  import { print } from "@/util/util.js";

  export default {
    name: "garbageExcel",
    data() {
      return {
        form: {},
        paramsSearch: {},
        tableLoading: false,
        tableOption: tableOption3,
        tableData:[],
      };
    },
    created() {},
    mounted: function () {},
    computed: {
      ...mapGetters(["permissions"]),
    },
    methods: {
      queryByCode(form) {
        this.tableOption.column[0].children[3].label = "-"
        let params = Object.assign({}, form);
        if (params.goDatetime && params.goDatetime.length > 0) {
          params.goDatetimeStart = params.goDatetime[0];
          params.goDatetimeEnd = params.goDatetime[1];
          this.tableOption.column[0].children[3].label = params.goDatetimeStart+' — '+params.goDatetimeEnd
        }
        delete params.goDatetime;
        console.log(form);
        params.code = "garbageExcel";
        this.tableOption.column[0].children[1].label = form.$projectInfoId
        this.$refs.crud.refreshTable();
        this.tableLoading = true;
        queryByCode(params)
          .then((res) => {
            this.tableLoading = false;
            this.tableData = res.data.data;

            if(res.data.data&&res.data.data.length>0){
                let gabageList = []
                res.data.data.forEach(element => {
                    for(var k in element) {
                        if(k!='车牌号'&&k!='放飞')
                    gabageList.push(k)
                    }
                })
                console.log(gabageList);
                //去重泥尾列表
                let newArr=Array.from(new Set(gabageList));
                newArr.forEach((item,index)=>{
                    this.tableOption.column[0].children[index+2].children=[
                      {
                        label:item,
                        prop:item,
                      }
                    ]
                })
                console.log(newArr);
                console.log(this.tableOption);
                console.log(this.$refs.crud);
                // this.$nextTick(()=>{
                  // this.$refs.crud.$refs.table.doLayout()
                  // this.$refs.crud.dataInit()
                  // this.$refs.crud.doLayout()
                  // this.$refs.crud.init()
                this.$refs.crud.columnInit();
                // this.$refs.crud.refreshTable();
                // })
            }

          })
          .catch(() => {
            this.tableLoading = false;
          });
      },
      print(){
        print(this.$refs.print)
      },
    },
  };
  </script>

  <style lang="scss" scoped>
  /deep/ .closeIcon,
  .checkIcon {
    font-size: 16px;
    color: red;
  }
  /deep/ .checkIcon {
    color: #3dcc90;
  }
  </style>
