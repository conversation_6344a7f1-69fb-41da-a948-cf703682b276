export const tableOption = (value)=>{
  let that = value
  console.log(that);
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    dialogClickModal:false,
    dialogWidth:600,
    excelBtn: false,
    printBtn: false,
    addBtn: false,
    viewBtn: false,
    searchSpan:6,
    searchMenuSpan: 6,
    labelWidth:120,
    searchLabelWidth:110,
    menuWidth:100,
    column: [
      {
        label: "项目",
        prop: "projectName",
        sortable: true,
        search: true,
        display:false,
        minWidth:150,
        overHidden:true,
      },
      {
        label: "运单号",
        prop: "companyWaybillNo",
        sortable: true,
        search:true,
        display:false,
        minWidth:160,
        overHidden:true,
      },
      {
        label: "车牌",
        prop: "goTruckCode",
        sortable: true,
        search:true,
        display:false,
        minWidth:90,
        overHidden:true,
      },
      {
        label: "司机",
        prop: "fleetName",
        sortable: true,
        search:true,
        display:false,
        minWidth:70,
        overHidden:true,
      },
      {
        label: "出场时间",
        prop: "goDatetime",
        sortable: true,
        display:false,
        minWidth:140,
        overHidden:true,
      },
      {
        label: "出场凭证",
        prop: "goPicture",
        type:'upload',
        sortable: true,
        display:false,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "签单员",
        prop: "goStaffName",
        search:true,
        sortable: true,
        display:false,
        minWidth:90,
        overHidden:true,
      },
      {
        label: "资源类型",
        prop: "resourceType",
        sortable: true,
        type:'select',
        search:true,
        display:false,
        props:{
          label:'itemValue',
          value:'itemValue'
        },
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=resource_type',
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        minWidth:100,
        overHidden:true,
      },
      {
        label: "单价(元/吨)",
        prop: "price",
        sortable: true,
        order:3,
        span:24,
        type:'number',
        controls:false,
        minRows: 0,
        maxRows: 99999.99,
        precision: 2,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:120,
        overHidden:true,
      },
      {
        label: "进重(吨)",
        prop: "load",
        sortable: true,
        order:1,
        span:24,
        type:'number',
        controls:false,
        precision:2,
        minRows:0,
        maxRows:9999.99,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:110,
        overHidden:true,
      },
      {
        label: "扣重(吨)",
        prop: "cut",
        sortable: true,
        order:2,
        span:24,
        type:'number',
        controls:false,
        precision:2,
        minRows:0,
        maxRows:9999.99,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:110,
        overHidden:true,
      },
      {
        label: "总价",
        prop: "totalPrice",
        sortable: true,
        display:false,
        minWidth:80,
        overHidden:true,
      },
      {
        label: " 票据凭证/磅单",
        prop: "imgUrl",
        type:'upload',
        listType: "picture-img",
        action: "/upms/file/upload?fileType=image&dir=",
        propsHttp: {
          url: "link",
        },
        loadText: "图上上传中，请稍等",
        sortable: true,
        span:24,
        rules: [
          {
            required: true,
            message: "请上传",
            trigger: "change",
          },
        ],
        minWidth:140,
        overHidden:true,
      },
      {
        label: "磅单上传人",
        prop: "uploadName",
        sortable: true,
        search:true,
        display:false,
        minWidth:116,
        overHidden:true,
      },
      {
        label: "出场日期",
        prop: "searchDate",
        sortable: true,
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange:true,
        search:true,
        hide:true,
        showColumn:false,
        display:false,
      },
      {
        label: "磅单上传时间",
        prop: "uploadDatetime",
        sortable: true,
        display:false,
        minWidth:140,
        overHidden:true,
      },
      {
        label: "磅单上传日期",
        prop: "searchDate2",
        sortable: true,
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange:true,
        search:true,
        hide:true,
        showColumn:false,
        display:false,
      },
    ],
  };
}

