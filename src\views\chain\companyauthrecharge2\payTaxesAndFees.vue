<template>
  <div class="payTaxesAndFees">
    <el-descriptions title="支付单信息">
      <template slot="extra">
        <el-button type="primary"
                   size="small"
                   @click="payLog">税费付款记录</el-button>
      </template>
      <el-descriptions-item label="转入银行">{{form.bankName}}</el-descriptions-item>
      <el-descriptions-item label="转入账户">{{form.bankAccount}}</el-descriptions-item>
      <el-descriptions-item label="转入户名">{{form.accountName}}</el-descriptions-item>
      <el-descriptions-item label="运费">{{form.freight}}</el-descriptions-item>
      <el-descriptions-item label="税费">{{form.taxFee}}</el-descriptions-item>
      <el-descriptions-item label="总费用">{{form.realPayPrice}}</el-descriptions-item>
      <el-descriptions-item label="项目税点">{{form.taxRate}}%</el-descriptions-item>
      <el-descriptions-item label="已支付税费">{{form.paidTaxFee}}</el-descriptions-item>
    </el-descriptions>
    <div v-if="!isView">

      <avue-form :option="option"
                 v-if="form.unPaidTaxFee>0"
                 ref="taxForm"
                 v-model="taxForm"
                 @submit="submit">
        <template slot="menuForm">
          <el-button icon="el-icon-close"
                     @click="cancelModal">取消</el-button>
        </template>
      </avue-form>
      <div v-else-if="form.unPaidTaxFee==='0.00'"
           class="paySuccess"
           style="margin-top:30px;text-align: center;">
        <svg-icon icon-class="successFill"
                  class-name="successFill-icon"
                  style="font-size:100px" />
        <div>税费支付完毕</div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getPayTaxInfoByPaymentNo, } from "@/api/chain/companyauthrecharge";
export default {
  props: {
    //弹窗状态
    // visible: {
    //   type: Boolean,
    //   default: false,
    // },
    info: {},
    //是否查看
    isView: {
      type: Boolean,
      default: false
    },
  },
  components: {

  },
  data () {
    return {
      option: {
        emptyBtn: false,
        submitBtn: true,
        labelWidth: 90,
        column: [
          {
            label: "待支付税费",
            prop: "unPaidTaxFee",
            disabled: true,
            span: 24,
          },
          {
            label: "支付税费",
            prop: "amount",
            type: "number",
            minRows: 0.01,
            maxRows: *********.99,
            precision: 2,
            controls: false,
            rules: [
              {
                required: true,
                message: "请输入 支付税费",
                trigger: "blur",
              },
            ],
            span: 24,
          },
        ]
      },
      taxForm: {},
      form: {},
      logVisible: false,
    };
  },
  created () {
    this.getData()
  },
  mounted () {
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    getData () {
      getPayTaxInfoByPaymentNo({ paymentNo: this.info.paymentNo }).then(res => {
        console.log(res);
        this.form = res.data.data
        this.taxForm.unPaidTaxFee = res.data.data.unPaidTaxFee
      })
    },
    submit (form, done) {
      this.$emit('submit',form,done,this.getData)
    },
    payLog () {
      this.$emit("payLog")
    },
    cancelModal () {
      this.$emit("cancelModal")
    }
  },
};
</script>

<style lang="scss" scoped>
</style>
