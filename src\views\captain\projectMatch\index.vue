<template>
  <div class="projectMatch">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="menuLeft"
                  slot-scope="{ row }">
          <el-button type="primary"
                     icon="el-icon-plus"
                     v-if="permissions['captain:projectMatch:release']"
                     size="small"
                     @click="add">发布</el-button>
        </template>
        <template slot="menu"
                  slot-scope="{ row }">
          <el-button type="text"
                     :icon="row.planStatus==1?'el-icon-video-pause':'el-icon-video-play'"
                     v-if="permissions['captain:projectMatch:match']"
                     size="small"
                     :loading="tableLoading"
                     plain
                     @click="start(row)">{{row.planStatus==1?'暂停匹配':'恢复匹配'}}</el-button>
          <el-button type="text"
                     icon="el-icon-info"
                     v-if="permissions['captain:projectMatch:detail']"
                     size="small"
                     plain
                     @click="detail(row)">详情</el-button>
        </template>
        <template slot="matchPoolCount"
                  slot-scope="{ row }">
          <span class="blue"
                @click="viewDialog(row,'1')">{{row.matchPoolCount}}</span>
        </template>
        <template slot="waitingConfirmCount"
                  slot-scope="{ row }">
          <span class="blue"
                @click="viewDialog(row,'2')">{{row.waitingConfirmCount}}</span>
        </template>
        <template slot="waitingProjectConfirmCount"
                  slot-scope="{ row }">
          <span class="blue"
                @click="viewDialog(row,'3')">{{row.waitingProjectConfirmCount}}</span>
        </template>
        <template slot="matchSuccessCount"
                  slot-scope="{ row }">
          <span class="blue"
                @click="viewDialog(row,'4')">{{row.matchSuccessCount}}</span>
        </template>
      </avue-crud>
    </basic-container>
    <matching-list v-if="listDialog"
                   @refreshChange="refreshChange"
                   :info="info"
                   :visible.sync="listDialog"></matching-list>
    <!-- 撮合队列 -->
    <planDetail v-if="detailVisible"
                :detailForm="detailForm"
                :option="detailOption"
                size="50%"
                :visible.sync="detailVisible"
                title="项目撮合计划详情"></planDetail>
    <!-- 发布计划 -->
    <planDetail v-if="addVisible"
                :detailForm="addForm"
                :option="addOption"
                size="70%"
                :visible.sync="addVisible"
                @submit="submit"
                title="发布">
      <template slot="planMatchId"
                slot-scope="{type}">
        <el-select v-model="addForm.planMatchId"
                   placeholder="请选择">
          <el-option v-for="item in fleetList"
                     :key="item.id"
                     :label="item.fleetName"
                     :value="item.id">
          </el-option>
          <template slot="empty">
            <div style="line-height: 40px;text-align: center;font-size: 14px;color: #ccc;">无可选车队长，
              <span @click="addFleet"
                    style="cursor: pointer;color: #66b1ff;">点击新增</span>
            </div>
          </template>
        </el-select>
      </template>
      <template slot="electronicRange"
                slot-scope="{type}">
        <el-input-number v-model="addForm.electronicRange"
                         style="width:100%"
                         :min="100"
                         placeholder="请输入范围"></el-input-number>
      </template>
      <template slot="fence"
                slot-scope="{type}">
        <div style="padding-bottom: 20px">
          <el-amap-search-box class="search-box"
                              :search-option="searchOption"
                              :on-search-result="onSearchResult">
          </el-amap-search-box>
        </div>
        <el-amap vid="amapDemo"
                 :center="center"
                 :amap-manager="amapManager"
                 :zoom="zoom"
                 :events="events"
                 class="amap-demo"
                 style="height: 300px; width: 100%">
          <el-amap-marker ref="marker"
                          vid="component-marker"
                          :position="marker.position"
                          :events="marker.events"
                          :visible="marker.visible"
                          :draggable="marker.draggable"></el-amap-marker>
          <el-amap-circle :center="marker.position"
                          :radius="addForm.electronicRange"
                          :fillOpacity="0.2"></el-amap-circle>
        </el-amap>
      </template>

    </planDetail>
    <addFleet v-if="fleetVisible"
              :visible.sync="fleetVisible"
              @complete="complete"></addFleet>
  </div>
</template>

<script>
import { getPage, addObj, pause, renew, getFleetList, checkAuthForMatchPlan } from "@/api/captain/projectMatch";
import { tableOption } from "@/const/crud/captain/projectMatch";
import { mapGetters } from "vuex";
import matchingList from './matchingList'
import planDetail from '@/components/formDetail/index.vue';
import { AMapManager } from "vue-amap";
const amapManager = new AMapManager();
import { isMobileNumber } from '@/util/validate'
import addFleet from './addFleet'

export default {
  name: "projectMatch",
  components: { matchingList, planDetail, addFleet },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      listDialog: false,
      info: {},
      detailVisible: false, //详情弹窗
      detailForm: {},
      detailOption: {
        detail: true,
        labelWidth: 100,
        column: [
          {
            label: "计划编号",
            prop: "planNo",
            span: 24,
            placeholder: " ",
          },
          {
            label: "车队名称",
            prop: "fleetName",
            span: 24,
            placeholder: " ",
          },
          {
            label: "车队停车场",
            prop: "gpsAddress",
            span: 24,
            placeholder: " ",
          },
          {
            label: "联系人",
            prop: "contactPerson",
            span: 24,
            placeholder: " ",
          },
          {
            label: "联系方式",
            prop: "contactPhone",
            span: 24,
            placeholder: " ",
          },
          {
            label: "运费",
            prop: "freight",
            span: 24,
            placeholder: " ",
          },
          {
            label: "车型",
            prop: "vehicleTypes",
            span: 24,
            placeholder: " ",
          },
          {
            label: "运输方式",
            prop: "tpMode",
            span: 24,
            placeholder: " ",
          },
          {
            label: "账期",
            prop: "settleCycle",
            span: 24,
            placeholder: " ",
          },
          {
            label: "车数/天",
            prop: "carsNumber",
            span: 24,
            placeholder: " ",
          },
        ],
      },
      addVisible: false, //发布弹窗
      addForm: {
        address: "",
        electronicRange: 500,
        fence: "113.98074,22.55251",
        planMatchId: "",
      },
      addOption: {
        labelWidth: 100,
        emptyBtn: false,
        submitBtnText: "创建",
        column: [
          {
            label: "联系人",
            prop: "contactPerson",
            span: 12,
            maxlength: 6,
            rules: [
              {
                required: true,
                message: "请输入联系人",
                trigger: "blur",
              },
            ],
          },
          {
            label: "联系方式",
            prop: "contactPhone",
            span: 12,
            maxlength: 11,
            rules: [
              {
                required: true,
                message: "请输入 联系方式",
                trigger: "blur",
              },
              {
                validator: isMobileNumber,
                trigger: "blur",
              },
            ],
          },
          {
            label: "车型",
            prop: "vehicleTypes",
            multiple: true,
            type: "select",
            dataType: "string",
            dicData: [
              {
                label: "泥头车",
                value: "泥头车",
              },
              {
                label: "小金刚",
                value: "小金刚",
              },
              {
                label: "拖头",
                value: "拖头",
              },
            ],
            span: 12,
            rules: [
              {
                required: true,
                message: "请选择 车型",
                trigger: "change",
              },
            ],
          },
          {
            label: "运费",
            prop: "priceUnit",
            span: 6,
            type: "select",
            gutter: 0,
            dicData: [
              {
                label: "元/车",
                value: "元/车",
              },
              {
                label: "元/吨",
                value: "元/吨",
              },
              {
                label: "元/方",
                value: "元/方",
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "",
            prop: "price",
            labelWidth: -20,
            type: "number",
            span: 6,
            minRows: 0,
            maxRows: 99999.99,
            precision: 2,
            rules: [
              {
                required: true,
                message: "请输入 运费",
                trigger: "blur",
              },
              {
                validator: (rule, value, callback) => {
                  console.log(value);
                  if (value === 0) {
                    callback(new Error('运费不能为0哦'));
                  } else {
                    callback();
                  }
                },
                trigger: "blur",
              },
            ],
          },
          {
            label: "车数/天",
            prop: "carsNumber",
            span: 12,
            type: "number",
            minRows: 1,
            maxRows: 99999,
            precision: 0,
            rules: [
              {
                required: true,
                message: "请输入 车数/天",
                trigger: "blur",
              },
            ],
          },
          {
            label: "账期天数",
            prop: "settleCycle",
            span: 12,
            type: "number",
            minRows: 0,
            maxRows: 9999,
            precision: 0,
            rules: [
              {
                required: true,
                message: "请输入 账期天数",
                trigger: "blur",
              },
            ],
          },
          {
            label: "运输方式",
            prop: "tpMode",
            span: 12,
            multiple: true,
            type: "select", // 下拉选择
            dataType: "string",
            dicData: [
              {
                label: "放飞",
                value: "放飞",
              },
              {
                label: "运费",
                value: "运费",
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择 运输方式",
                trigger: "change",
              },
            ],
            // props: {
            //   label: "itemName",
            //   value: "itemValue",
            // },
            // dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode',
          },
          {
            label: "选择车队",
            prop: "planMatchId",
            span: 12,
            rules: [
              {
                required: true,
                message: "请选择 车队",
                trigger: "change",
              },
            ],
          },
          {
            label: "范围",
            prop: "electronicRange",
            span: 12,
            type: "number",
            minRows: 100,
            precision: 0,
            rules: [
              {
                required: true,
                message: "请选择 范围",
                trigger: "change",
              },
            ],
          },
          {
            label: "车队停车场",
            prop: "address",
            disabled: true,
            span: 12,
            rules: [
              {
                required: true,
                message: "请在地图上选择车队停车场地址",
                trigger: "change",
              },
            ],
          },
          {
            label: "",
            prop: "fence",
            span: 24,
            rules: [
              {
                required: true,
                message: "请在地图上选择车队停车场地址",
                trigger: "change",
              },
            ],
          },
        ],
      },
      searchOption: {
        // 限制搜索城市的范围
        citylimit: false,
      },
      zoom: 14,
      center: [113.98074, 22.55251],
      marker: {
        position: [113.98074, 22.55251],
        visible: true,
        draggable: false,
      },
      amapManager,
      events: {
        click: (e) => {
          console.log(e);
          this.marker.position = [e.lnglat.lng, e.lnglat.lat];
          this.addForm.fence = e.lnglat.lng + "," + e.lnglat.lat;
          this.getFormattedAddress()
        },
      },
      fleetList: [],
      fleetVisible: false,
    };
  },
  created () { },
  mounted: function () { },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList () {
      return {
        addBtn: this.permissions["captain:projectMatch:add"] ? true : false,
        delBtn: this.permissions["captain:projectMatch:del"] ? true : false,
        editBtn: this.permissions["captain:projectMatch:edit"] ? true : false,
        viewBtn: this.permissions["captain:projectMatch:get"] ? true : false,
      };
    },
  },
  methods: {
    getFleetList () {
      getFleetList().then(res => {
        this.fleetList = res.data.data
      })
    },
    searchChange (params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange (val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage (page, params) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page);
    },
    viewDialog (row, type) {
      this.info = row
      this.info.type = type
      this.listDialog = true
    },
    start (row) {
      this.$confirm(row.planStatus == 1 ? "暂停后，该计划将不在平台所有土石方撮合计划中参与匹配显示" : "恢复匹配后，将在平台所有土石方撮合计划中重新参与匹配显示", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          row.planStatus == 1 ? this.pause(row.id) : this.renew(row.id)
        })
        .catch((err) => {
        });
    },
    pause (id) {
      this.tableLoading = true
      pause(id).then(res => {
        this.getPage(this.page)
        this.tableLoading = false
      }).catch(err => {
        this.tableLoading = false
      })
    },
    renew (id) {
      this.tableLoading = true
      renew(id).then(res => {
        this.getPage(this.page)
        this.tableLoading = false
      }).catch(err => {
        this.tableLoading = false
      })
    },
    add () {
      this.addForm = {
        address: "",
        electronicRange: 500,
        fence: "113.98074,22.55251",
        planMatchId: "",
      }
      checkAuthForMatchPlan().then(res => {
        let result = res.data.data
        if (result.idCardAuthStatus == 2 && result.bankCardAuthStatus == 2) {
          this.getFleetList()
          this.addVisible = true
        } else {
          this.$alert(result.idCardAuthStatus==2?'还需要完成银行卡认证才可以发布计划哦，请登录司机APP完善认证信息':'完成认证才可以发布计划哦，请登录司机APP完善认证信息')
        }
      })
    },
    detail (row) {
      this.detailForm = {}
      this.detailForm = Object.assign({}, row)
      this.detailVisible = true;
    },
    onSearchResult (pois) {
      this.center = [pois[0].location.lng, pois[0].location.lat];
      this.marker.position = [pois[0].location.lng, pois[0].location.lat];
      this.addForm.fence = pois[0].location.lng + "," + pois[0].location.lat;
      this.addForm.address = pois[0].address;
    },
    getFormattedAddress () {
      AMap.plugin("AMap.Geocoder", () => {
        let GeocoderOptions = {
          city: "全国",
        };
        let geocoder = new AMap.Geocoder(GeocoderOptions);
        console.log("geocoder", geocoder);
        geocoder.getAddress(this.marker.position, (status, result) => {
          console.log("通过经纬度拿到的地址", result);
          if (status === "complete" && result.info === "OK") {
            this.addForm.address = result.regeocode.formattedAddress;
          } else {
            this.addForm.address = "无法获取地址";
          }
        });
      });
    },
    submit (form, done) {
      console.log(form);
      //planMatchType "计划撮合类型：1=项目，2=泥尾，3=车队"
      let param = Object.assign({}, { planMatchType: 1 }, form)
      addObj(param).then(res => {
        this.$message.success("创建成功")
        this.addVisible = false
        this.getPage(this.page)
        done()
      }).catch(() => {
        done()
      })
    },
    addFleet () {
      this.fleetVisible = true
    },
    complete () {
      this.fleetVisible = false
      this.getFleetList()
    },
  },
};
</script>

<style lang="scss" scoped>
.blue {
  color: #409eff;
  cursor: pointer;
}
</style>
