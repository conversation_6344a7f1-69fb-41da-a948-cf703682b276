<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <template slot="menuLeft" slot-scope="{ row }">
          <el-button
            type="primary"
            icon="el-icon-download"
            v-if="permissions['chain:companywaybillexentrance:excel']"
            size="small"
            :loading="tableLoading"
            @click="exOut"
            >导出</el-button>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import { getPage } from "@/api/chain/companywaybillexentrance";
import { tableOption } from "@/const/crud/chain/companywaybillexentrance";
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";
export default {
  name: "companywaybillexentrance",
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: 'entrance_datetime', //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      info: {},
      editVisible: false,
      title: "查看",
    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:companywaybillexentrance:add"]
          ? true
          : false,
        delBtn: this.permissions["chain:companywaybillexentrance:del"]
          ? true
          : false,
        editBtn: this.permissions["chain:companywaybillexentrance:edit"]
          ? true
          : false,
        viewBtn: this.permissions["chain:companywaybillexentrance:get"]
          ? true
          : false,
      };
    },
  },
  methods: {
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("entranceDatetime")) {
          params.startDate = params.entranceDatetime[0];
          params.endDate = params.entranceDatetime[1];
          delete params.entranceDatetime;
        }
        if (params.hasOwnProperty("shiftTime")) {
          params.startShiftTime = params.shiftTime[0];
          params.endShiftTime = params.shiftTime[1];
          delete params.shiftTime;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },

    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    exOut() {
      let params = Object.assign({}, this.paramsSearch);
      if (params) {
        if (params.hasOwnProperty("entranceDatetime")) {
          params.startDate = params.entranceDatetime[0];
          params.endDate = params.entranceDatetime[1];
          delete params.entranceDatetime;
        }
        if (params.hasOwnProperty("shiftTime")) {
          params.startShiftTime = params.shiftTime[0];
          params.endShiftTime = params.shiftTime[1];
          delete params.shiftTime;
        }
      }
      let url = "/chain/companywaybillexentrance/export";
      this.tableLoading=true
      expotOut(params, url, "入场签单").then(()=>{
        this.tableLoading=false
      }).catch(()=>{
        this.tableLoading=false
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
