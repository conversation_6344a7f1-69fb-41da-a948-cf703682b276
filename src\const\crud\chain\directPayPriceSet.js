export const tableOption  = (value)=>{
  let that = value
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    excelBtn: false,
    printBtn: false,
    addBtn: true,
    addBtnText: "申请",
    addTitle:'申请',
    viewBtn: false,
    editBtn: false,
    delBtn: false,
    menuWidth: 190,
    searchMenuSpan: 6,
    searchLabelWidth: 100,
    labelWidth: 110,
    column: [
      {
        label: "项目名称",
        prop: "projectName",
        search: true,
        disabled: true,
        hide: true,
        showColumn:false,
      },
      {
        label: "运输方式",
        prop: "tpMode",
        type: "select", // 下拉选择
        search: true,
        width:70,
        dicData:[
          {
            label: '放飞',
            value: '1'
          },
          {
            label: '运费',
            value: '2'
          }
        ],
        change:({value})=>{
          var column = that.findObject(that.tableOption.column,'garbageId');
          column.display = value&&value==2
        },
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
      },
      {
        label: "土质",
        prop: "soilType",
        type: "select", // 下拉选择
        search: true,
        width:70,
        dicData: that.soilTypeList,
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
      },
      {
        label: "泥尾",
        prop: "garbageId",
        search: true,
        display:true,
        type: "select", // 下拉选择
        // search:true,
        props: {
          label: "names",
          value: "id",
        },
        dicData: that.garbageList,
        filterable: true, //是否可以搜索
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        formatter: (val) => {
          return val.garbageName
        },
      },
      {
        label: "直付价",
        prop: "directPayPrice",
        type: 'number',
        controls: false,
        minRows: 0,
        maxRows: 999999999.99,
        precision: 2,
        width:70,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "change",
          },
        ],
      },
      {
        label: "车队长和司机",
        prop: "captainAndDriver",
        search: true,
        minWidth:240,
        overHidden:true,
        span:24,
        rules: [
          {
            required: true,
            message: "请选择车队长和司机",
            trigger: "change",
          },
        ],
        formatter: (val) => {
          return val.captainAndDrivers
        },
      },
      {
        label: "状态",
        prop: "status",
        search: true,
        type: "select",
        editDisplay:false,
        addDisplay:false,
        width:70,
        dicData: [
          {
            value: "1",
            label: "待审核",
          },
          {
            value: "2",
            label: "已审核",
          },
          {
            value: "3",
            label: "已失效",
          },
          {
            value: "4",
            label: "已驳回",
          },
        ],
      },
      {
        label: "申请人",
        prop: "applyName",
        sortable: "custom",
        editDisabled:true,
        addDisplay:false,
      },
      {
        label: "申请日期",
        prop: "searchDate",
        sortable: true,
        type: "date",
        search: true,
        searchRange: true,
        hide: true,
        showColumn: false,
        valueFormat: "yyyy-MM-dd",
        display:false,
      },
      {
        label: "申请时间",
        prop: "applyDatetime",
        sortable: true,
        type: "datetime",
        valueFormat: "yyyy-MM-dd HH:mm:ss",
        editDisabled:true,
        addDisplay:false,
      },
      {
        label: "审核人",
        prop: "auditName",
        sortable: "custom",
        editDisplay:false,
        addDisplay:false,
      },
      {
        label: "审核时间",
        prop: "auditDatetime",
        sortable: "custom",
        editDisplay:false,
        addDisplay:false,
      },
      {
        label: "失效人",
        prop: "invalidName",
        sortable: "custom",
        editDisplay:false,
        addDisplay:false,
      },
      {
        label: "失效时间",
        prop: "invalidDatetime",
        sortable: "custom",
        editDisplay:false,
        addDisplay:false,
      },

    ],
  };
}

