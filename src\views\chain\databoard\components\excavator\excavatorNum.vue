<template>
  <div class="waybillFee">
    <searchInfo :info="paramsSearch" :type="2" :total="page.total" unit="车" :tab="tab"  @searchChange="searchChange" @exOut="exOut">
    </searchInfo>
    <avue-crud ref="crud" :page.sync="page" :data="tableData" :table-loading="tableLoading" :option="tableOption"
      v-model="form" :search.sync="search" @on-load="getPage">
    </avue-crud>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getExcavatorLoadingPage as getPage } from "@/api/chain/board";
import searchInfo from './searchInfo';
import { exportOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    tab:{
      type: String,
      default: ""
    }
  },
  components: {
    searchInfo
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      search: {},
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        // maxHeight:[800],
        // height:'auto',
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: true,
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        header: false,
        column: [
          {
            label: "项目名称",
            prop: "projectName",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "项目负责人",
            prop: "leadingNames",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "挖机签单员",
            prop: "inStaffName",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "挖机型号",
            prop: "machineCode",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "挖机车主",
            prop: "ownerName",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "挖机车主手机号",
            prop: "ownerMobile",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "挖机班次",
            prop: "inShiftType",
            minWidth: 90,
            overHidden: true,
          },
          {
            label: "运单号",
            prop: "no",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "运单创建时间",
            prop: "createDatetime",
            minWidth: 160,
            overHidden: true,
          },

        ],
      },
      active: "1",
    }
  },
  created () {
    this.paramsSearch = Object.assign({}, this.info)
  },
  mounted () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    searchChange (params, done) {
      // params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    getPage (page, params = {}) {
      this.tableLoading = true;
      console.info(params)
      console.info(this.paramsSearch)
      console.info(Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch,
          {
            startDate: this.info.startDate,
            endDate: this.info.endDate,
            checkDynamic: this.info.checkDynamic,
            checkRelevance:this.info.ledgerDigActive=='关联'?"1":"0",
            projectInfoId: this.info.projectInfoId
          }
        ))
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          {
            startDate: this.info.startDate,
            endDate: this.info.endDate,
            checkDynamic: this.info.checkDynamic,
            checkRelevance:this.info.ledgerDigActive=='关联'?"1":"0",
            projectInfoId: this.info.projectInfoId
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    exOut (params, done) {
      let url = "/chain/statisticsboard/exportExcavatorLoadingExcel";
      exportOut(params, url, "挖机装车数", 'get').then(res => {
        done()
      }).catch(() => {
        done()
      })
    },
  },

};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

.count {
  margin-bottom: 10px;

  span {
    font-size: 16px;
    font-weight: 700;
  }
}
</style>
