<!--表格组件 -->
<template>
  <div>
    <searchHeader :searchOption="searchOption" @searchChange="searchChange" v-if="showHeader&&searchOption.column&&searchOption.column.length>0">
      <template slot-scope="scope" v-for="item in searchSlot" :slot="item">
        <slot v-bind="scope" :name="item"></slot>
      </template>
      <template slot-scope="scope"  slot="customForm">
        <searchForm v-bind="scope" :searchOption="formOption" @searchChange="searchChange"></searchForm>
      </template>
    </searchHeader>
    <avue-crud v-bind="$attrs" ref="table" v-on="$listeners" @on-load="onLoad" @sort-change="sortChange" @refresh-change="refreshChange">
      <!-- //$slots无法获取具名作用域插槽 -->
      <template #[slotName]="slotProps" v-for="(slot, slotName) in $scopedSlots">
        <slot :name="slotName" v-bind="slotProps" />
      </template>
      <template slot="menuRight" slot-scope="scope" v-if="columnSortBtn">
        <el-tooltip class="item" effect="dark" content="列排序" placement="top">
          <el-button  plain circle size="small" icon="el-icon-setting" @click="setColumn"></el-button>
        </el-tooltip>
        <columnSort v-if="visible" :visible.sync="visible" :column="column" @datadragEnd="datadragEnd" @save="save"></columnSort>
      </template>
    </avue-crud>
  </div>
</template>

<script>
import {getByRoute,saveOrUpdate} from '@/api/chain/companycustompage'
import searchHeader from './searchHeader';
import searchForm from './searchForm';
import columnSort from './columnSort';
import {deepClone} from '@/util/util'

export default {
  props: {
    routerName:'',
  },
  data() {
    return {
      dialogColumn: {},
      searchOption:{},
      searchSlot:[],
      formOption:{},//通过option里面的searchIndex来设置展示多少个
      visible:false,
      column:[],
      search:{},
    };
  },
  computed:{
    // $newListeners() {
    //   return Object.assign(this.$listeners, {
    //    //翻页有问题，需要重写on-load事件
    //     "onLoad": this.onLoad,
    //   })
    // },
    showHeader () {//只有设置了自定义头部才显示
      return !!this.$attrs.option.searchCustom
    },
    columnSortBtn(){//是否列排序
      return !!this.$attrs.option.columnSortBtn
    },
  },
  components: {
    searchHeader,
    searchForm,
    columnSort
  },
  watch: {
    "dialogColumn.columnBox": {
      handler(newVal, oldVal) {
        if(oldVal){
          //关闭弹窗时 保存this.$refs.table.default
          saveOrUpdate({pageRoute:this.routerName,pageTableOption:JSON.stringify(this.$refs.table.default)}).then(res=>{
            console.log(res);
            this.$emit('refreshChange')
          })
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.dialogColumn = this.$refs.table.$refs.dialogColumn;
    this.searchOption = this.$refs.table.$refs.headerSearch.option
    this.search = this.$attrs.search
    let option = deepClone(this.searchOption)
    console.log('option',option);
    option.column =option.column.sort((a, b) => (b.searchOrder || 9999) - (a.searchOrder || 9999)).reverse().splice(0,this.$attrs.option.searchIndex)
    option.dicData = this.searchOption.dicData
    this.formOption = option
    this.searchSlot = this.getSlotList(['Search'], this.$scopedSlots, this.searchOption.column)
    console.log("default", this.$refs.table.default);
    console.log("this.$refs.table.tableOption", this.$refs.table.tableOption);
    console.log("$attrs", this.$attrs);
    // console.log("routerName", this.routerName);
    getByRoute({route:this.routerName}).then(res=>{
      console.log(res);
      if(res.data.data){
        let pageTableOption =  JSON.parse(res.data.data.pageTableOption)
        console.log(pageTableOption);
        for (const key in this.$refs.table.default) {
          if(key in pageTableOption){
            this.$refs.table.default[key].hide = pageTableOption[key].hide
            if(pageTableOption[key].width){
              this.$refs.table.default[key].width = pageTableOption[key].width
            }
            if(pageTableOption[key].order){
              this.$refs.table.default[key].order = pageTableOption[key].order
            }else if(!this.$refs.table.default[key].order){
              this.$refs.table.default[key].order = 9999
            }
          }else if(!this.$refs.table.default[key].order){
            this.$refs.table.default[key].order = 9999
          }
        }
        this.$refs.table.tableOption.column.forEach(item2=>{
            item2.order = this.$refs.table.default[item2.prop].order||9999
        })
        this.$refs.table.refreshTable()
        // //排序
        // this.$refs.table.tableOption.column = this.$refs.table.tableOption.column.sort((a, b) => (a.order) - (b.order) )
        // // 更改排序
        // let list = []
        // this.$refs.table.tableOption.column.forEach(item=>{
        //   if (item.showColumn !== false) {
        //     list.push(this.$refs.table.default[item.prop])
        //   }
        // })
        // this.dialogColumn._watchers[0].value = list
        // console.log("this.$refs.table.tableOption.column",this.$refs.table.tableOption.column);
      }
      console.log("this.$refs.table.default",this.$refs.table.default);
      saveOrUpdate({pageRoute:this.routerName,pageTableOption:JSON.stringify(this.$refs.table.default)}).then(res=>{
        console.log(res);
      })
    })
  },
  methods: {
    onLoad(page){
      console.log(this.search);
      this.$emit('update:search', this.search)
      this.$emit("onLoad",page);
    },
    sortChange(val){
      console.log(val);
      this.$emit('update:search', this.search)
      this.$emit("sortChange",val);
    },
    refreshChange(){
      this.$emit('update:search', this.search)
      this.$emit("refreshChange");
    },
    searchChange(form,done){
      console.log(form);
      this.search = this.filterForm(form)
      console.log(this.search);
      this.$emit('update:search', this.search)
      this.$emit("search-change",this.search,done)
    },
    getSlotList (list = [], slot, propList) {
      propList = propList.map(ele => ele.prop)
      return Object.keys(slot).filter(ele => {
        let result = false;
        if (!propList.includes(ele)) {
          list.forEach(name => {
            if (ele.includes(name)) result = true;
          })
        }
        // console.log(list,'list');
        // console.log(result,'list');
        return result;
      })
    },
    setColumn(){
      this.column = []
      console.log(this.$attrs.option);
      this.$attrs.option.column.forEach(column => {
        if (column.showColumn != false&&!this.$refs.table.default[column.prop].hide) {
          let obj = deepClone(column)
          obj.order = this.$refs.table.default[column.prop].order||9999
          this.column.push(obj)
        }
      })
      console.log(this.column);
      if(this.column.length==0){
        return false
      }
      this.column =this.column.sort((a, b) => (a.order) - (b.order) )
      this.visible = true
    },
    datadragEnd(column,done){
      // let list = []
      column.forEach((item,index)=>{
        this.$refs.table.default[item.prop].order = index +1
        //  if (item.showColumn !== false) {
        //   list.push(this.$refs.table.default[item.prop])
        //  }
        this.$refs.table.tableOption.column.forEach(item2=>{
          if(item2.prop==item.prop){
            item2.order = index +1
          }
        })
      })
      // this.$refs.table.tableOption.column = this.$refs.table.tableOption.column.sort((a, b) => (a.order) - (b.order) )
      // this.dialogColumn._watchers[0].value = list
      // console.log("this.$refs.table.default",this.$refs.table.default);
      // console.log(this.dialogColumn);
      this.$refs.table.refreshTable()
      console.log(this.$refs.table);
      done()
    },
    save(done){
      saveOrUpdate({pageRoute:this.routerName,pageTableOption:JSON.stringify(this.$refs.table.default)}).then(res=>{
        done()
        this.$message.success("保存成功")
        this.visible = false
      }).catch(err=>{
        done()
        this.$message.error("出错啦")
      })
    }
  },
  created() {},
};
</script>
<style lang="scss" scoped>
  /deep/ .avue-crud .el-card__body .avue-crud__menu{
    overflow: inherit;
  }
</style>
