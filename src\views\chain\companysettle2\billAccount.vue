<template>
  <div id="content"
       class="merchantDetail">
    <el-drawer size="100%"
               :title="infoForm.type==1?'账单核算':'查看账单'"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal"
               :wrapperClosable="false">
      <ul class="info">
        <li>
          <label>结算单号：</label>
          <span>{{ infoForm.settleNo }}</span>
        </li>
        <li>
          <label>结算申请人：</label>
          <span>{{ infoForm.agentName }}</span>
        </li>
        <li>
          <label>运单申请数：</label>
          <span>{{ infoForm.waybillCnt }}</span>
        </li>
        <li>
          <label>项目名称：</label>
          <span>{{ infoForm.projectName }}</span>
        </li>
      </ul>
      <div class="search">
        <el-collapse v-model="activeNames"
                     @change="changeCollapse">
          <el-collapse-item title="查询"
                            name="1">
            <el-form class="myForm"
                     label-width="80px"
                     :model="billform"
                     ref="billform"
                     :inline="true">
              <el-row>
                <el-col :span="4">
                  <el-form-item label="运输方式:">
                    <el-select v-model="billform.tpMode"
                               multiple
                               size="small"
                               placeholder="运输方式"
                               clearable>
                      <el-option label="放飞"
                                 value="1"></el-option>
                      <el-option label="运费"
                                 value="2"></el-option>
                      <el-option label="资源"
                                 value="3"></el-option>
                      <el-option label="回填"
                                 value="4"></el-option>
                      <el-option label="内运"
                                 value="5"></el-option>
                      <el-option label="内转回填"
                                 value="6"></el-option>
                      <el-option label="普货"
                                 value="9"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="泥尾:">
                    <el-select v-model="billform.garbageId"
                               size="small"
                               multiple
                               placeholder="泥尾"
                               clearable>
                      <el-option v-for="(item, index) in garbageList"
                                 :key="index"
                                 :label="item.names"
                                 :value="item.garbage_id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="土质:">
                    <el-select v-model="billform.goSoilType"
                               size="small"
                               multiple
                               placeholder="土质"
                               clearable>
                      <el-option v-for="(item, index) in soilList"
                                 :key="index"
                                 :label="item.go_soil_type"
                                 :value="item.go_soil_type"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="车队长:">
                    <el-input v-model="billform.captainName"
                              size="small"
                              placeholder="车队长"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="车牌:">
                    <el-input v-model="billform.truckCode"
                              size="small"
                              placeholder="车牌"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="司机:">
                    <el-input v-model="billform.driverName"
                              size="small"
                              placeholder="司机"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="4">
                  <el-form-item label="备注:">
                    <el-input v-model="billform.goRemark"
                              size="small"
                              clearable
                              placeholder="备注">
                    </el-input>
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="4">
                  <el-form-item label="平台结算价:">
                    <el-select v-model="billform.isPlatformWaybillCost"
                               size="small"
                               placeholder="平台设置结算价"
                               clearable>
                      <el-option label="否"
                                 value="0"></el-option>
                      <el-option label="是"
                                 value="1"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col> -->
                <el-col :span="4">
                  <el-form-item label="是否驳回:">
                    <el-select v-model="billform.isReject"
                               size="small"
                               placeholder="是否驳回"
                               clearable>
                      <el-option label="否"
                                 value="0"></el-option>
                      <el-option label="是"
                                 value="1"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="是否结算:">
                    <el-select v-model="billform.isSettle"
                               size="small"
                               placeholder="是否结算"
                               clearable>
                      <el-option label="否"
                                 value="0"></el-option>
                      <el-option label="是"
                                 value="1"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="单位:">
                    <el-select v-model="billform.weightUnit"
                               size="small"
                               multiple
                               placeholder="单位"
                               clearable>
                      <el-option label="车"
                                 value="车"></el-option>
                      <el-option label="方"
                                 value="方"></el-option>
                      <el-option label="吨"
                                 value="吨"></el-option>
                      <el-option label="时"
                                 value="时"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="4">
                  <el-form-item label="运单号:">
                    <el-input v-model="billform.no"
                              size="small"
                              placeholder="运单号"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="地块:">
                    <el-input v-model="billform.landParcel"
                              size="small"
                              placeholder="地块"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>

                <el-col :span="4">
                  <el-form-item label="签单车型:">
                    <el-select v-model="billform.goVehicleType"
                               size="small"
                               placeholder="出口签单车型"
                               clearable>
                      <el-option :label="item.itemValue"
                                 :value="item.itemValue"
                                 v-for="(item, index) in vehicleTypeList"
                                 :key="index"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="出场时间:">
                    <el-date-picker type="datetimerange"
                                    size="small"
                                    format="yyyy-MM-dd HH:mm:ss"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    v-model="billform.tpDate"
                                    clearable></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="承运人:">
                    <el-input v-model="billform.payeeName"
                              size="small"
                              placeholder="承运人"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="证件齐全:">
                    <el-select v-model="billform.checkCompleteDocuments"
                               size="small"
                               placeholder="是否证件齐全"
                               clearable>
                      <el-option label="否"
                                 value="0"></el-option>
                      <el-option label="是"
                                 value="1"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="预设价:">
                    <div style="display: flex;">
                      <el-input-number style="width: 100%"
                                       v-model="billform.minPayeePrice"
                                       :min="0"
                                       :precision="2"
                                       :controls="false"
                                       placeholder="最小"
                                       size="small"
                                       :step="0.01"
                                       step-strictly></el-input-number>
                      -
                      <el-input-number style="width: 100%;"
                                       v-model="billform.maxPayeePrice"
                                       :min="0"
                                       :precision="2"
                                       :controls="false"
                                       placeholder="最大"
                                       size="small"
                                       :step="0.01"
                                       step-strictly></el-input-number>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="结算价:">
                    <div style="display: flex;">
                      <el-input-number style="width: 100%"
                                       v-model="billform.minSettlePrice"
                                       :min="0"
                                       :precision="2"
                                       :controls="false"
                                       size="small"
                                       placeholder="最小"
                                       :step="0.01"
                                       step-strictly></el-input-number>
                      -
                      <el-input-number style="width: 100%;"
                                       v-model="billform.maxSettlePrice"
                                       :min="0"
                                       :precision="2"
                                       :controls="false"
                                       size="small"
                                       placeholder="最大"
                                       :step="0.01"
                                       step-strictly></el-input-number>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>

                <el-col :span="8  ">
                  <el-button style="margin-left: 14px"
                             type="primary"
                             size="small"
                             icon="el-icon-search"
                             :loading="loading"
                             @click="confirm">搜索</el-button>
                  <el-button @click="reset"
                             size="small"
                             icon="el-icon-delete">清空</el-button>
                  <el-button @click="downExport"
                             size="small"
                             icon="el-icon-download"
                             type="primary"
                             :loading="btnLoading">导出</el-button>
                </el-col>
              </el-row>
              <div style="margin-bottom: 10px; color: #666; font-size: 14px">
                查询统计： 运单{{ waybillData.length }}条，金额合计{{
                  freight
                }}元
              </div>
            </el-form>
          </el-collapse-item>
        </el-collapse>
      </div>
      <!-- <div class="searchBtn">
        <div class="highlight">请勾选运单后，操作批量操作按钮</div>
        <div class="hint">
          待操作运单统计： 运单{{ multipleSelection.length }}条，金额合计
          {{ selectPrice }}元
        </div>
        <div class="btns">
          <el-button type="primary" size="small" icon="el-icon-edit"
            @click="bulkEdit(5)">按数量修改价格</el-button>
          <el-button type="primary" size="small" icon="el-icon-edit-outline" :disabled="multipleSelection.length < 1"
            @click="bulkEdit(2)">批量驳回</el-button>
          <el-button size="small" icon="el-icon-refresh-right" :disabled="multipleSelection.length < 1"
            @click="bulkEdit(3)">批量撤回驳回</el-button>
          <el-button size="small" icon="el-icon-refresh-right" :disabled="totalLength < 1" @click="bulkEdit(6)">批量结算
          </el-button>
          <el-button size="small" icon="el-icon-refresh-right" :disabled="totalLength < 1" @click="bulkEdit(4)">批量撤回结算
          </el-button>
          <el-button type="success" size="small" icon="el-icon-finished" :loading="tableLoading" @click="submitSettleWaybill">核算完毕正式提交
          </el-button>
        </div>
      </div> -->
      <div style="display: flex; align-items: center; margin-top: 10px"
           v-if="waybillData && waybillData.length > 0&&infoForm.type==1">
        <el-tag class="avue-crud__tip">
          <span class="avue-crud__tip-name">
            当前表格已选择
            <span class="avue-crud__tip-count">{{
              multipleSelection.length
            }}</span>
            项
          </span>
          <el-button type="text"
                     size="small"
                     @click="clearSelection">清空</el-button>
        </el-tag>
        <el-input-number style="margin-left: 10px; width: 80px"
                         v-model="start"
                         :min="1"
                         :controls="false"
                         size="small"
                         :step="1"
                         step-strictly></el-input-number>
        至
        <el-input-number style="width: 80px; margin-right: 10px"
                         v-model="end"
                         :min="1"
                         :controls="false"
                         size="small"
                         :step="1"
                         step-strictly></el-input-number>
        <el-button size="small"
                   type="primary"
                   :disabled="!start || !end"
                   @click="checkChange(true)">选择</el-button>
        <el-button size="small"
                   type="primary"
                   :disabled="!start || !end"
                   @click="checkChange(false)">
          取消</el-button>
        <el-button type="primary"
                   size="small"
                   :disabled="multipleSelection.length==0"
                   @click="payeeVisible = true">设置承运人</el-button>
        <el-button type="primary"
                   size="small"
                   :disabled="totalLength < 1"
                   @click="batchEdit(2)">设置预设价</el-button>
        <el-button type="primary"
                   size="small"
                   :disabled="totalLength < 1"
                   @click="batchEdit(1)">设置结算价</el-button>
        <el-button type="primary"
                   size="small"
                   style="margin-right: 10px"
                   :disabled="multipleSelection.length==0"
                   @click="bulkEdit(5)">按数量改结算价</el-button>
        <el-tooltip class="item"
                    effect="dark"
                    content="请先选择运单再操作"
                    placement="top">
          <el-dropdown @command="(event) => bulkEdit(event)">
            <el-button type="primary"
                       size="small"
                       style="margin-right: 10px">
              更多操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :disabled="multipleSelection.length < 1"
                                :command="2">批量驳回</el-dropdown-item>
              <el-dropdown-item :disabled="multipleSelection.length < 1"
                                :command="3">批量撤回驳回</el-dropdown-item>
              <el-dropdown-item :disabled="totalLength < 1"
                                :command="6">批量结算</el-dropdown-item>
              <el-dropdown-item :disabled="totalLength < 1"
                                :command="4">批量撤回结算</el-dropdown-item>
              <el-dropdown-item :disabled="totalLength < 1"
                                :command="7">拆分运单到新结算单</el-dropdown-item>
              <el-dropdown-item :disabled="totalLength < 1"
                                :command="8">批量调整增减值</el-dropdown-item>
              <el-dropdown-item :disabled="totalLength < 1"
                                :command="9">结算价覆盖预设价</el-dropdown-item>
              <el-dropdown-item :disabled="totalLength < 1"
                                :command="10">预设价覆盖结算价</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-tooltip>
        <!-- <el-button type="primary" size="small" icon="el-icon-edit-outline" :disabled="multipleSelection.length < 1"
            @click="bulkEdit(2)">批量驳回</el-button>
          <el-button size="small" icon="el-icon-refresh-right" :disabled="multipleSelection.length < 1"
            @click="bulkEdit(3)">批量撤回驳回</el-button>
          <el-button size="small" icon="el-icon-refresh-right" :disabled="totalLength < 1" @click="bulkEdit(6)">批量结算
          </el-button>
          <el-button size="small" icon="el-icon-refresh-right" :disabled="totalLength < 1" @click="bulkEdit(4)">批量撤回结算
          </el-button> -->
        <el-button type="success"
                   size="small"
                   icon="el-icon-finished"
                   :loading="tableLoading"
                   @click="submitSettleWaybill">核算完毕正式提交
        </el-button>
      </div>
      <div style="margin-top: 10px;" v-if="infoForm.type==2"></div>
      <div class="table"
           ref="mytable">
        <u-table ref="table"
                 style="width: 100%"
                 :data="waybillData"
                 :row-height="61"
                 border
                 use-virtual
                 :big-data-checkbox="true"
                 :height="tableHeight"
                 :highlight-current-row="false"
                 v-loading="tableLoading"
                 element-loading-text="拼命加载中"
                 element-loading-spinner="el-icon-loading"
                 :row-class-name="tableRowClassName"
                 @selection-change="handleSelectionChange">
          <u-table-column type="selection"
                          v-if="infoForm.type==1"
                          fixed="left"
                          width="50"></u-table-column>
          <u-table-column property="no"
                          fixed="left"
                          label="运单编号"
                          width="180"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <div style="color:#409eff;cursor:pointer" @click="viewInfo(scope.row)">{{scope.row.no}}</div>
            </template>
          </u-table-column>
          <u-table-column property="truckCode"
                          label="车牌号"
                          width="90"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="tpMode"
                          label="运输方式"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ filterTpMode(scope.row.tpMode) }}</span>
            </template>
          </u-table-column>
          <u-table-column property="names"
                          label="泥尾"
                          width="140"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="goSoilType"
                          label="土质"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="captainName"
                          label="车队长"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="driverName"
                          label="司机"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="payeeName"
                          label="承运人"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="goName"
                          label="出场签单员"
                          width="96"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="capacity"
                          label="容量"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="goDatetime"
                          label="出场签单时间"
                          width="160"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="completeDatetime"
                          label="完成时间"
                          width="160"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="weightTons"
                          label="数量"
                          width="90px"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.weightTons }}</span>
            </template>
          </u-table-column>
          <u-table-column property="weightUnit"
                          label="单位"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="price"
                          label="单价"
                          width="90px"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.settlePrice == 0 || scope.row.weightTons == 0">0.00</span>
              <span v-else>{{
                (scope.row.settlePrice / scope.row.weightTons).toFixed(2)
              }}</span>
            </template>
             <template slot="iotInPicture" slot-scope="{ row }">
          <el-image
            v-if="row.iotInPicture"
            style="width: 50px; height: 50px"
            :src="filterImg(row.iotInPicture)"
            :preview-src-list="filterImgs(row.iotInPicture)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
          </u-table-column>
          <u-table-column property="goRemark"
                          label="出场备注"
                          width="120px"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="goVehicleType"
                          label="出口签单车型"
                          width="106px"
                          show-overflow-tooltip>
          </u-table-column>
          <u-table-column property="landParcel"
                          label="地块"> </u-table-column>
          <!-- <u-table-column property="isPlatformWaybillCost"
                          label="平台设置结算价"
                          width="120px"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{
                scope.row.isPlatformWaybillCost === "1"
                  ? "是"
                  : scope.row.isPlatformWaybillCost === "0"
                  ? "否"
                  : ""
              }}</span>
            </template>
          </u-table-column> -->
          <u-table-column property="payeePrice"
                          fixed="right"
                          width="100px"
                          label="预设价">
            <template slot-scope="scope">
              <!-- <el-input v-if="scope.row.isSettle == 0 && scope.row.isReject != 1 &&scope.row.isPlatformWaybillCost != 1"
                        placeholder="请输入预设价"
                        oninput="value=value.replace(/[^\d.\-]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
                        @blur="handleInput(scope.row, 'payeePrice')"
                        size="small"
                        maxlength="9"
                        v-model="scope.row.payeePrice"
                        style="width: 80px; margin-right: 20px">
              </el-input> -->
              <el-input-number style="width: 80px; margin-right: 20px"
                        v-if="scope.row.isSettle == 0 && scope.row.isReject != 1 &&scope.row.isPlatformWaybillCost != 1&&infoForm.type==1"
                        v-model="scope.row.payeePrice"
                        :min="0"
                        :max="9999999.99"
                        :precision="2"
                        :controls="false"
                        placeholder="请输入预设价"
                        size="small"
                        :step="0.01"
                        step-strictly>
              </el-input-number>
              <span v-else>{{ scope.row.payeePrice }}</span>
            </template>
          </u-table-column>
          <u-table-column prop="settlePrice"
                          label="结算价"
                          fixed="right"
                          width="100px">
            <template slot-scope="scope">
              <!-- onkeypress="return (/[\d.]/.test(String.fromCharCode(event.keyCode)));" -->
              <!-- <el-input v-if="
                  scope.row.isSettle == 0 &&
                  scope.row.isReject != 1 &&
                  scope.row.isPlatformWaybillCost != 1
                "
                        placeholder="请输入结算价"
                        oninput="value=value.replace(/[^\d.\-]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
                        @blur="handleInput(scope.row, 'settlePrice')"
                        size="small"
                        maxlength="9"
                        v-model="scope.row.settlePrice"
                        style="width: 80px; margin-right: 20px">
              </el-input> -->
              <el-input-number style="width: 80px; margin-right: 20px"
                        v-if="scope.row.isSettle == 0 && scope.row.isReject != 1 &&scope.row.isPlatformWaybillCost != 1&&infoForm.type==1"
                        v-model="scope.row.settlePrice"
                        :min="0"
                        :max="9999999.99"
                        :precision="2"
                        :controls="false"
                        placeholder="请输入结算价"
                        size="small"
                        :step="0.01"
                        step-strictly>
              </el-input-number>
              <span v-else>{{ scope.row.settlePrice }}</span>
              <!-- oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"                       -->
            </template>
          </u-table-column>
          <!-- <u-table-column prop="exAmt" label="异常金额" fixed="right"></u-table-column> -->
          <u-table-column prop="adjustAmt"
                          label="增减值"
                          fixed="right"
                          width="100px">
            <template slot-scope="scope">
              <!-- <el-input v-if="scope.row.isSettle == 0 && scope.row.isReject != 1"
                        placeholder="请输入增减值"
                        oninput="value=value.replace(/[^\d.\-]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
                        @blur="handleInput(scope.row, 'adjustAmt')"
                        size="small"
                        v-model="scope.row.adjustAmt"
                        style="width: 80px; margin-right: 20px">
              </el-input> -->
              <el-input-number style="width: 80px; margin-right: 20px"
                        v-if="scope.row.isSettle == 0 && scope.row.isReject != 1&&infoForm.type==1"
                        v-model="scope.row.adjustAmt"
                        :min="-9999999.99"
                        :max="9999999.99"
                        :precision="2"
                        :controls="false"
                        placeholder="请输入增减值"
                        size="small"
                        :step="0.01"
                        step-strictly>
              </el-input-number>
              <span v-else>{{ scope.row.adjustAmt }}</span>
            </template>
          </u-table-column>
          <u-table-column label="操作"
                          fixed="right"
                          v-if="infoForm.type==1"
                          :width="countWaybill>1?'186px':'90px'">
            <template slot-scope="scope">
              <div style="display: flex">
                <el-button v-if="scope.row.isReject == 1"
                           icon="el-icon-document-remove"
                           style="color: #666"
                           type="text">
                  已驳回</el-button>
                <el-button @click="changeStatus(scope.row, scope.$index)"
                           icon="el-icon-document-delete"
                           v-else
                           type="text">驳回</el-button>
                <el-button @click="cancelWaybill(scope.row, scope.$index)"
                           v-if="countWaybill>1"
                           icon="el-icon-document-delete"
                           type="text">移出核算单</el-button>
              </div>
            </template>
          </u-table-column>
          <u-table-column prop="settle"
                          v-if="infoForm.type==1"
                          label="结算"
                          fixed="right"
                          width="84px">
            <template slot-scope="scope"
                      v-if="scope.row.isReject == 0">
              <el-button v-if="scope.row.isSettle == 0"
                         @click="changeSettle(scope.row, scope.$index)"
                         icon="el-icon-document-remove"
                         type="text">结算</el-button>
              <span v-else>已结算</span>
            </template>
          </u-table-column>
          <!-- <u-table-column prop="view" label="查看" fixed="right" width="80px">
            <template slot-scope="scope">
              <el-button @click="detail(scope.row, scope.$index)" icon="el-icon-view" type="text">查看</el-button>
            </template>
          </u-table-column> -->
        </u-table>
      </div>
    </el-drawer>
    <el-dialog width="300px"
               :visible="dialogVisible"
               :before-close="closeDialog"
               :close-on-click-modal="false">
      <div v-if="mode == 5">
        <div style="margin-bottom: 6px">请输入单价：</div>
        <!-- <el-input placeholder="请输入单价"
                  oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
                  @blur="handleInput(that, 'unitPrice')"
                  maxlength="7"
                  size="small"
                  v-model="unitPrice">
        </el-input> -->
        <el-input-number style="width: 100%;"
                        v-model="unitPrice"
                        :min="0"
                        :max="9999999.99"
                        :precision="2"
                        :controls="false"
                        placeholder="请输入单价"
                        size="small"
                        :step="0.01"
                        step-strictly>
        </el-input-number>
      </div>
      <div v-if="mode == 6"
           class="remind">
        <div>
          您确认结算这
          <span>{{ multipleSelection.length }}</span> 单？
        </div>
        <p v-if="multipleSelection.length - totalLength > 0"
           style="line-height: 16px">
          <span style="font-size: 14px">其中{{
              multipleSelection.length - totalLength
            }}单已驳回不参与此次操作，如需操作请先撤销驳回</span>
        </p>
      </div>
      <div v-if="mode == 1 || mode == 4 || mode == 5"
           class="remind">
        <div>
          您确认<label v-if="mode == 4">撤回</label>结算这
          <span>{{ multipleSelection.length }}</span> 单？
        </div>
        <p v-if="multipleSelection.length - totalLength > 0"
           style="line-height: 16px">
          <span style="font-size: 14px">其中{{
              multipleSelection.length - totalLength
            }}单已驳回不参与此次操作，如需操作请先撤销驳回</span>
        </p>
        <p v-if="multipleSelection.length - platformCostLength > 0"
           style="line-height: 16px; margin: 4px 0px">
          <span style="font-size: 14px">其中{{
              multipleSelection.length - platformCostLength
            }}单已设置平台结算价不参与此次操作</span>
        </p>
        <!-- <p v-if="mode == 1 && haveWeightLength > 0" style="line-height: 16px">
          <span style="font-size: 14px">其中{{
              haveWeightLength
          }}单是按重量核算，不参与此次操作，如需操作请使用按重量修改价格</span>
        </p>
        <p v-if="mode == 5 && noWeightLength > 0" style="line-height: 16px">
          <span style="font-size: 14px">其中{{
              noWeightLength
          }}单是按车次核算，不参与此次操作，如需操作请使用按车次修改价格</span>
        </p> -->
        <p>
          核算金额：<span>{{
            mode == 5 ? (weightTotal * unitPrice).toFixed(2) : selectPrice
          }}</span>
        </p>
        <!-- <p>
          异常费用：<span>{{ abnormalCount }}</span>
        </p> -->
        <!-- mode==1按车计价的  mode5按重量的  mode4 批量撤回结算 6批量结算 -->
        <p>
          增减费用：<span>{{ changValueCount }}</span>
        </p>
      </div>
      <div v-if="mode == 2"
           class="remind">
        <div>
          您确认批量驳回 <span>{{ multipleSelection.length }}</span> 单？
        </div>
      </div>
      <div v-if="mode == 3"
           class="remind">
        <div>
          您确认批量撤回驳回 <span>{{ multipleSelection.length }}</span> 单？
        </div>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary"
                   :loading="loading"
                   @click="saveBill">确认</el-button>
      </span>
    </el-dialog>
    <!-- 重量结算单个 -->
    <el-dialog width="300px"
               :visible.sync="priceVisible"
               :close-on-click-modal="false">
      <div>
        <div style="margin-bottom: 6px">请输入{{batchEditType==2?'预设价：':"结算价："}}</div>
        <!-- <el-input placeholder="请输入价格"
                  oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
                  @blur="handleInput(that, 'unitPrice')"
                  size="small"
                  maxlength="7"
                  v-model="unitPrice">
        </el-input> -->
        <el-input-number style="width: 100%;text-align:left"
                        v-model="unitPrice"
                        :min="0"
                        :max="9999999.99"
                        :precision="2"
                        :controls="false"
                        placeholder="请输入价格"
                        size="small"
                        :step="0.01"
                        step-strictly>
        </el-input-number>
        <div style="color:red;margin-top: 4px;">直接修改{{batchEditType==2?'预设价':"结算价"}},与数量无关!</div>

        <div class="remind">
          <div>
            您确认结算这
            <span>{{ multipleSelection.length }}</span> 单？
          </div>
          <p v-if="multipleSelection.length - totalLength > 0"
             style="line-height: 16px">
            <span style="font-size: 14px">其中{{
              multipleSelection.length - totalLength
            }}单已驳回不参与此次操作，如需操作请先撤销驳回</span>
          </p>
          <p>
            {{batchEditType==2?'预设金额：':"核算金额："}}<span>{{
            (totalLength * unitPrice).toFixed(2)
          }}</span>
          </p>
          <p v-if="batchEditType==1">
            增减费用：<span>{{ changValueCount }}</span>
          </p>
        </div>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="priceVisible = false">取消</el-button>
        <el-button type="primary"
                   :loading="loading"
                   :disabled="unitPrice <= 0"
                   @click="confirmSubmit">确认</el-button>
      </span>
    </el-dialog>
    <!-- <waybill-detail v-if="billVisible"
                    :billForm="billForm"
                    :visible.sync="billVisible"></waybill-detail> -->
    <checkPaymentExistDocIncomplete submitBtnText="继续提交"
                                    :tip="tip"
                                    cancelBtnText="停止提交"
                                    @exOut="exOutExc"
                                    @continuePayment="continuePayment"
                                    v-if="checkVisible"
                                    :tableData="checkList"
                                    :visible.sync="checkVisible"></checkPaymentExistDocIncomplete>
    <setPayee v-if="payeeVisible"
              @submit="submit"
              :visible.sync="payeeVisible"></setPayee>
    <batchEditAdjust v-if="batchEditVisible"
                     @submit="editAdjustComplete"
                     :list="noRejectCheckList"
                     :visible.sync="batchEditVisible"></batchEditAdjust>
    <!-- 运单详情 -->
    <waybill-detail
      v-if="detailVisible"
      :detailForm="detailForm"
      :option="detailOption"
      :visible.sync="detailVisible"
    ></waybill-detail>

    <el-dialog
          title="证件不齐全车辆信息"
          :visible.sync="centerDialogVisible"
          width="60%"
          center>
          <span style="color: red">如需在本平台开具发票，请完善相关证件信息</span>
                <el-table
                  :data="dialogObj.dialogData"
                  style="width: 100%; color: red;">
                <el-table-column
                prop="projectName"
                label="项目名称"
                width="150">
                </el-table-column>
                <el-table-column
                prop="truckCode"
                label="车牌"
                width="130">
                </el-table-column>
                <el-table-column
                prop="driverName"
                label="司机"
                width="130">
                </el-table-column>
                 <el-table-column
                prop="captainName"
                label="车队长"
                width="130">
                </el-table-column>
                 <el-table-column
                prop="positiveImgUrl"
                label="行驶证正面"
                width="180">
                <template slot-scope="scope">
                <img :src="scope.row.positiveImgUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                 <el-table-column
                prop="negativeImgUrl"
                label="行驶证反面"
                width="180">
                <template slot-scope="scope">
                <img :src="scope.row.negativeImgUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                 <el-table-column
                
                prop="roadLicencePositiveUrl"
                label="道路运输证正面">
                <!-- 图片的显示 -->
                <template slot-scope="scope">
                <img :src="scope.row.roadLicencePositiveUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                 <el-table-column
                prop="roadLicenceNegativeUrl"
                label="道路运输证反面">
                <template slot-scope="scope">
                <img :src="scope.row.roadLicenceNegativeUrl" min-width="70" height="70" />
                </template>
                </el-table-column>
                </el-table>
          <span slot="footer" class="dialog-footer">
            <el-button @click="centerDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="centerDialogVisible = false,submitSettleWaybill1()">生成交卡回执</el-button>
          </span>
        </el-dialog>
  </div>
</template>

<script>
import { clearNoNum } from "@/util/util.js";
import {
  submitSettleWaybill,
  getGarbageList,
  getGoSoilType,
  getSettleWaybill,
  batchRejectPrice,
  batchCancelReject,
  batchUpdatePrice,
  batchCancelSettle,
  cancelCompanyWayBill,
  batchSettle,
  checkPayment,
  getDocCompleteRateByCompanySettleIdList,
  spiltCompanySettle,
    cardGiveCheck,
} from "@/api/chain/companysettle";
import { listDictionaryItem } from "@/api/chain/projectinfo";
// import { getGarbage } from "@/api/chain/garbage";
// import waybillDetail from "./waybillDetail.vue";
import checkPaymentExistDocIncomplete from "@/views/chain/companyauthrecharge2/components/checkPaymentExistDocIncomplete.vue";
import { expotOut } from "@/util/down.js";
import setPayee from './components/setPayee';
import { updateCarrierName,getObj } from "@/api/chain/waybillPreset";
import batchEditAdjust from './components/batchEditAdjust';
import waybillDetail from '@/views/chain/companywaybill2/detail.vue';



export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    //结算数据
    infoForm: {},
  },
  components: {
    waybillDetail,
    checkPaymentExistDocIncomplete,
    setPayee,
    batchEditAdjust,
  },
  data () {
    return {
      activeNames: "1",
      that: this,
      billform: {},
      loading: false,
      dialogVisible: false,
      popoverVisible: false,
      numbers: [], //驳回的运单
      settlePrice: 0, //批量修改价格
      unitPrice: 0, //按重量批量修改价格
      multipleSelection: [], //选中的表格
      garbageList: [],
      waybillData: [],
      waybillDataTemp: [],
      tableHeight: 500,
      billVisible: false,
      billForm: {
        soilTypes: "",
      },
      soilList: [],
      mode: 1, //1批量修改报价  2批量驳回   3批量测回驳回   4批量撤回核算  5按重量修改价格
      currentForm: {},
      tableLoading: false,
      vehicleTypeList: [], //出口签单车型列表
      checkVisible: false, //检查五证齐全弹窗
      checkList: [], //检查五证齐全列表
      start: 1,
      end: 1,
      btnLoading: false,
      tip: "",
      priceVisible: false,
      payeeVisible: false,
      batchEditType: 1, //默认按结算价
      batchEditVisible: false, // 批量调整增减值弹窗
      noRejectCheckList: [],//没有驳回的选中数据
      countWaybill:0,
      detailVisible:false, //详情弹窗
      detailForm:{},
      detailOption:{
        detail:true,
        labelWidth:114,
        group:[
          {
            label: '基本信息',
            prop: 'group',
            column: [
              {
                label: '运单号',
                prop: 'no',
                span:12,
                placeholder:" ",
              },
              {
                label: '项目名称',
                prop: 'projectName',
                span:12,
                placeholder:" ",
              },
              {
                label: '项目合作方',
                prop: 'agentName',
                span:12,
                placeholder:" ",
              },
              {
                label: '状态',
                prop: 'statusName',
                span:12,
                placeholder:" ",
              },
              {
                label: '地块',
                prop: 'landParcel',
                span:24,
                placeholder:" ",
              },
            ]
          },
          {
            label: '入场信息',
            prop: 'group',
            hide: true,
            column: [
              {
                label: '入场拍照凭证',
                prop: 'iotInPicture',
                placeholder:" ",
                type:'upload',
                listType: "picture-card",
                dataType: 'string',
                span:24,
                placeholder:" ",
              }
            ]
          },
          {
            label: '挖机签单信息',
            prop: 'group1',
            column: [
              {
                label: '挖机签单员',
                prop: 'inStaffName',
                span:12,
                placeholder:" ",
              },
              {
                label: '挖机土质',
                prop: 'inSoilType',
                span:12,
                placeholder:" ",
              },
              {
                label: '挖机车牌',
                prop: 'inDriverTruckCode',
                span:12,
                placeholder:" ",
              },
              {
                label: '挖机签单时间',
                prop: 'inDatetime',
                span:12,
                placeholder:" ",
              },
              {
                label: '挖机班次',
                prop: 'inShiftTypeName',
                span:12,
                placeholder:" ",
              },
              {
                label: '挖机备注',
                prop: 'inRemark',
                span:12,
                placeholder:" ",
              },
              {
                label: '凭证类型',
                prop: 'inWeightUnit',
                span:12,
                placeholder:" ",
              },
              {
                label: '挖机拍照凭证',
                prop: 'inPicture',
                type:'upload',
                listType: "picture-card",
                dataType: 'string',
                span:24,
                placeholder:" ",
              },
              {
                label: '称重仪器照片',
                prop: 'inWeighInstrumentPicture',
                type:'upload',
                listType: "picture-card",
                dataType: 'string',
                span:24,
                placeholder:" ",
              },
              {
                label: '挖机签单地址',
                prop: 'inAddr',
                span:24,
                placeholder:" ",
              },
            ]
          },
          {
            label: '出场签单信息',
            prop: 'group2',
            column: [
              {
                label: '运输类型',
                prop: 'tpModeName',
                span:12,
                placeholder:" ",
              },
              {
                label: '泥尾',
                prop: 'garbageName',
                span:12,
                placeholder:" ",
              },
              {
                label: '出场签单员',
                prop: 'goStaffName',
                span:12,
                placeholder:" ",
              },
              {
                label: '出场土质',
                prop: 'goSoilType',
                span:12,
                placeholder:" ",
              },
              {
                label: '出场车牌',
                prop: 'goDriverTruckCode',
                span:12,
                placeholder:" ",
              },
              {
                label: '出场签单时间',
                prop: 'goDatetime',
                span:12,
                placeholder:" ",
              },
              {
                label: '出场班次',
                prop: 'goShiftTypeName',
                span:12,
                placeholder:" ",
              },
              {
                label: '入场重量',
                prop: 'inWeight',
                span:12,
                placeholder:" ",
              },
              {
                label: '出场重量',
                prop: 'outWeight',
                span:12,
                placeholder:" ",
              },
              {
                label: '单位',
                prop: 'weightUnit',
                span:12,
                placeholder:" ",
              },
              {
                label: '单价',
                prop: 'unitPrice',
                span:12,
                placeholder:" ",
              },
              {
                label: '数量',
                prop: 'weightTons',
                span:12,
                placeholder:" ",
              },
              {
                label: '价格',
                prop: 'payeePrice',
                span:12,
                placeholder:" ",
              },
              {
                label: '泥尾票土质',
                prop: 'goTicketSoilType',
                span:12,
                placeholder:" ",
              },
              {
                label: "泥尾票号",
                prop: "ticketNo",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾票单价",
                prop: "ticketPrice",
                span: 12,
                placeholder: " ",
              },
              {
                label: '出场拍照凭证',
                prop: 'goPicture',
                type:'upload',
                listType: "picture-card",
                dataType: 'string',
                span:24,
                placeholder:" ",
              },
              {
                label: " 磅单票据",
                prop: "poundbillUrl",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: '出场拍照泥尾票',
                prop: 'ticketImg',
                type:'upload',
                listType: "picture-card",
                dataType: 'string',
                span:24,
                placeholder:" ",
              },
              {
                label: '出场备注',
                prop: 'goRemark',
                span:12,
                placeholder:" ",
              },
              {
                label: '电子结算卡',
                prop: 'settleCardNo',
                span:12,
                placeholder:" ",
              },
              {
                label: '出口签单车型',
                prop: 'goVehicleType',
                span:12,
                placeholder:" ",
              },
              {
                label: '出场签单地址',
                prop: 'goAddr',
                span:24,
                placeholder:" ",
              },
            ]
          },
          {
            label: '司机信息',
            prop: 'group3',
            column: [
              {
                label: '司机',
                prop: 'driverName',
                span:12,
                placeholder:" ",
              },
              {
                label: '车牌',
                prop: 'goDriverTruckCode',
                span:12,
                placeholder:" ",
              },
              {
                label: '司机出场时间',
                prop: 'confirmGoDatetime',
                span:12,
                placeholder:" ",
              },
              {
                label: '司机卸土时间',
                prop: 'completeDatetime',
                span:12,
                placeholder:" ",
              },
              {
                label: '司机卸土地址',
                prop: 'completeAddr',
                span:12,
                placeholder:" ",
              },
              {
                label: '直付类型',
                prop: 'isPlatformDirectPayName',
                span:12,
                placeholder:" ",
              },
              {
                label: '司机联系方式',
                prop: 'driverMobile',
                span:12,
                placeholder:" ",
              },
              {
                label: "行驶证车辆车型",
                prop: "brandType",
                span:12,
                placeholder:" ",
              },
            ]
          },
          {
            label: '空车入场签单',
            prop: 'group4',
            column: [
              {
                label: '入场车牌',
                prop: 'entranceTruckCode',
                span:12,
                placeholder:" ",
              },
              {
                label: '入场签单人',
                prop: 'entranceStaffName',
                span:12,
                placeholder:" ",
                overHidden:true,
              },
              {
                label: '入场签单时间',
                prop: 'entranceDatetime',
                span:12,
                placeholder:" ",
              },
              {
                label: '入场备注',
                prop: 'entranceRemark',
                span:12,
                placeholder:" ",
              },
              {
                label: '入场拍照',
                prop: 'entrancePicture',
                type:'upload',
                listType: "picture-card",
                dataType: 'string',
                span:24,
                placeholder:" ",
              },
            ]
          },
          {
            label: '回填入场签单',
            prop: 'group5',
            column: [
              {
                label: '回填项目',
                prop: 'backfillProjectName',
                span:12,
                placeholder:" ",
              },
              {
                label: '回填车牌',
                prop: 'backfillTruckCode',
                span:12,
                placeholder:" ",
              },
              {
                label: '票号',
                prop: 'backfillTicketNo',
                span:12,
                placeholder:" ",
                overHidden:true,
              },
              {
                label: '备注',
                prop: 'backfillRemark',
                span:12,
                placeholder:" ",
              },
              {
                label: '回填签单人',
                prop: 'backfillSignName',
                span:12,
                placeholder:" ",
              },
              {
                label: '回填签单时间',
                prop: 'backfillSignDatetime',
                span:12,
                placeholder:" ",
              },
              {
                label: '拍照车辆',
                prop: 'backfillPicUrl',
                type:'upload',
                listType: "picture-card",
                dataType: 'string',
                span:24,
                placeholder:" ",
              },
              {
                label: '拍照泥尾票',
                prop: 'backfillTicketNoUrl',
                type:'upload',
                listType: "picture-card",
                dataType: 'string',
                span:24,
                placeholder:" ",
              },
            ]
          },
        ]
      },
      dialogpass:false,
      centerDialogVisible: false,
      dialogObj :{
        dialogData: [
          {
            label: '项目名称',
            prop: 'projectName',
            value: 'small',
            isImg: false
          },
          {
            label: '车牌',
            prop: 'truckCode',
            value: 'small',
            isImg: false
          },
          {
            label: '司机',
            prop: 'driverName',
            value: 'small',
            isImg: false
          },
          {
            label: '车队长',
            prop: 'captainName',
            value: 'small',
            isImg: false
          },
          {
            label: '行驶证正面',
            prop: 'positiveImgUrl',
            value: 'small',
            isImg: true
          },
          {
            label: '行驶证反面',
            prop: 'negativeImgUrl',
            value: 'small',
            isImg: true
          },
          {
            label: '道路运输证正面',
            prop: 'roadLicencePositiveUrl',
            value: 'small',
            isImg: true
          },
          {
            label: '道路运输证反面',
            prop: 'roadLicenceNegativeUrl',
            value: 'small',
            isImg: true
          },
        ]
      },
    };
  },
  computed: {
    //不包括驳回的选中条数
    totalLength () {
      return this.multipleSelection
        .map((row) => (row.isReject == 1 ? 0 : 1))
        .reduce((acc, cur) => parseFloat(cur) + acc, 0);
    },
    //不包括平台设置结算价的选中条数
    platformCostLength () {
      return this.multipleSelection
        .map((row) => (row.isPlatformWaybillCost == 1 ? 0 : 1))
        .reduce((acc, cur) => parseFloat(cur) + acc, 0);
    },
    //不包括驳回的、不包括平台设置结算价、有重量的选中条数
    haveWeightLength () {
      return this.multipleSelection
        .map((row) =>
          row.isReject == 1 ||
            row.isPlatformWaybillCost == 1 ||
            row.weightTons <= 0
            ? 0
            : 1
        )
        .reduce((acc, cur) => parseFloat(cur) + acc, 0);
    },
    //不包括驳回的、不包括平台设置结算价、没有重量的选中条数
    noWeightLength () {
      return this.multipleSelection
        .map((row) =>
          row.isReject == 1 ||
            row.isPlatformWaybillCost == 1 ||
            row.weightTons > 0
            ? 0
            : 1
        )
        .reduce((acc, cur) => parseFloat(cur) + acc, 0);
    },
    //选中总结算价 不包括已驳回的
    selectPrice () {
      return this.multipleSelection
        .map((row) => (row.isReject == 1 ? 0 : row.settlePrice))
        .reduce((acc, cur) => parseFloat(cur) + acc, 0)
        .toFixed(2);
    },
    //总结算价 不包括已驳回的
    freight () {
      return this.waybillData
        .map((row) => (row.isReject == 1 ? 0 : row.settlePrice))
        .reduce((acc, cur) => parseFloat(cur) + acc, 0)
        .toFixed(2);
    },
    //总重量 不包括已驳回的
    weightTotal () {
      return this.multipleSelection
        .map((row) =>
          row.isReject == 1 || row.isPlatformWaybillCost == 1
            ? 0
            : row.weightTons
              ? row.weightTons
              : 0
        )
        .reduce((acc, cur) => parseFloat(cur) + acc, 0);
    },
    //总异常费用 不包括已驳回的、不包括平台设置结算价、
    // abnormalCount() {
    //   return this.multipleSelection
    //     .map((row) => ((row.isReject == 1||row.isPlatformWaybillCost == 1) ? 0 : row.exAmt))
    //     .reduce((acc, cur) => parseFloat(cur) + acc, 0).toFixed(2)
    // },
    //总异常费用 不包括已驳回的、不包括平台设置结算价、不包括有重量的
    // exAmtCount() {
    //   return this.multipleSelection
    //     .map((row) => ((row.isReject == 1||row.isPlatformWaybillCost == 1||row.weightTons > 0) ? 0 : row.exAmt))
    //     .reduce((acc, cur) => parseFloat(cur) + acc, 0).toFixed(2)
    // },
    //总增减项 不包括已驳回的、不包括平台设置结算价、
    changValueCount () {
      return this.multipleSelection
        .map((row) =>
          row.isReject == 1 || row.isPlatformWaybillCost == 1
            ? 0
            : row.adjustAmt
        )
        .reduce((acc, cur) => parseFloat(cur) + acc, 0)
        .toFixed(2);
    },
    //总增减项 不包括已驳回的、不包括平台设置结算价、不包括有重量的
    // adjustAmtCount() {
    //   return this.multipleSelection
    //     .map((row) => ((row.isReject == 1||row.isPlatformWaybillCost == 1||row.weightTons > 0) ? 0 : row.adjustAmt))
    //     .reduce((acc, cur) => parseFloat(cur) + acc, 0).toFixed(2)
    // },
  },
  mounted () {
    this.waybillData = this.infoForm.tableData;
    this.countWaybill = this.infoForm.waybillCnt //运单总数
    this.waybillData.forEach((item, index) => {
      if (item.isReject == 1) {
        this.numbers.push(index);
      }
    });
    this.getGarbageList();
    this.getGoSoilType();
    this.getVehicleTypeList();
    this.$nextTick(() => {
      console.log(this.$refs.table);
      this.tableHeight =
        window.innerHeight - this.$refs.table.$el.offsetTop - 20;
    });
    window.addEventListener("resize", this.func);
  },
  methods: {
    func () {
      this.tableHeight =
        window.innerHeight - this.$refs.table.$el.offsetTop - 20;
      this.$refs.table.doLayout();
    },
    getVehicleTypeList () {
      listDictionaryItem({ dictionary: "go_vehicle_type" }).then((res) => {
        this.vehicleTypeList = res.data.data;
      });
    },
    /* ------------------------------------------------------- input输入 ----------------------------------------------------- */
    handleInput (obj, value) {
      console.log(obj[value]);
      obj[value] = clearNoNum(obj[value] + "");
      console.log(obj[value]);
      if (value == "unitPrice" && obj[value] > 999999999) {
        obj[value] = 999999999;
      }
      obj[value] = obj[value].toFixed(2);
    },
    /* ------------------------------------------------------- 获取泥尾 ----------------------------------------------------- */
    getGarbageList () {
      let param = {
        settleId: this.infoForm.id,
      };
      getGarbageList(param).then((res) => {
        this.garbageList = res.data.data;
      });
    },
    /* ------------------------------------------------------- 获取土质 ----------------------------------------------------- */
    getGoSoilType () {
      let param = {
        settleId: this.infoForm.id,
      };
      getGoSoilType(param).then((res) => {
        this.soilList = res.data.data;
      });
    },
    /* ------------------------------------------------------- 批量修改价格 ----------------------------------------------------- */
    batchUpdatePrice () {
      let arr = [];
      this.multipleSelection.map((item) => {
        // if (this.mode == 1 && item.isReject != 1 &&item.isPlatformWaybillCost != 1 && item.weightTons <= 0) {
        //   arr.push({
        //     id: item.id,
        //     exAmt: item.exAmt,
        //     adjustAmt: item.adjustAmt,
        //     settlePrice: this.settlePrice,
        //   });
        // }
        if (
          this.mode == 5 &&
          item.isReject != 1 &&
          item.isPlatformWaybillCost != 1
        ) {
          arr.push({
            id: item.id,
            // exAmt: item.exAmt,
            adjustAmt: item.adjustAmt,
            settlePrice: item.weightTons * this.unitPrice,
          });
        }
      });
      console.log(arr);
      if (arr.length < 1) {
        this.$message({
          type: "error",
          message: "无运单可修改！请选择要修改的运单",
        });
        return false;
      }
      this.loading = true;
      batchUpdatePrice(arr)
        .then((res) => {
          this.confirm();
          this.loading = false;
          this.dialogVisible = false;
          console.log(res);
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /* ------------------------------------------------------- 批量撤回核算 ----------------------------------------------------- */
    batchCancelSettle () {
      let arr = [];
      this.multipleSelection.map((item) => {
        if (item.isReject != 1 || item.isPlatformWaybillCost != 1) {
          arr.push({
            id: item.id,
          });
        }
      });
      console.log(arr);
      this.loading = true;
      batchCancelSettle(arr)
        .then((res) => {
          this.confirm();
          this.loading = false;
          this.dialogVisible = false;
          console.log(res);
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /* ------------------------------------------------------- 批量驳回 ----------------------------------------------------- */
    batchRejectPrice () {
      let arr = this.multipleSelection.map((item) => {
        return { id: item.id };
      });
      console.log(arr);
      this.loading = true;
      batchRejectPrice(arr)
        .then((res) => {
          this.confirm();
          this.loading = false;
          this.dialogVisible = false;
          console.log(res);
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /* ------------------------------------------------------- 批量撤回驳回 ----------------------------------------------------- */
    batchCancelReject () {
      let arr = this.multipleSelection.map((item) => {
        return { id: item.id };
      });
      console.log(arr);
      this.loading = true;
      batchCancelReject(arr)
        .then((res) => {
          this.confirm();
          this.loading = false;
          this.dialogVisible = false;
          console.log(res);
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /* ------------------------------------------------------- 批量结算 ----------------------------------------------------- */
    batchSettle () {
      let arr = [];
      this.multipleSelection.map((item) => {
        if (item.isReject != 1) {
          arr.push({
            id: item.id,
            settlePrice: item.settlePrice,
            adjustAmt: item.adjustAmt,
            payeePrice: item.payeePrice,
          });
        }
      });
      console.log(arr);
      this.loading = true;
      batchSettle(arr)
        .then((res) => {
          this.confirm();
          this.loading = false;
          this.dialogVisible = false;
          console.log(res);
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /* ------------------------------------------------------- 完成核算 ----------------------------------------------------- */
    saveBill () {
      console.log(this.mode);
      switch (this.mode) {
        case 1:
          this.batchUpdatePrice();
          break;
        case 2: //批量驳回
          this.batchRejectPrice();
          break;
        case 3: //批量撤回驳回
          this.batchCancelReject();
          break;
        case 4: //批量撤回核算
          this.batchCancelSettle();
          break;
        case 5:
          this.batchUpdatePrice();
          break;
        case 6:
          this.batchSettle();
          break;
      }
    },
    /* ------------------------------------------------------- 重置 ----------------------------------------------------- */
    reset () {
      this.billform = {};
      this.settlePrice = "";
      this.unitPrice = 0;
    },
    /* ------------------------------------------------------- 确认筛选 ----------------------------------------------------- */
    confirm () {
      console.log(this.billform);
      let param = Object.assign({}, this.billform);
      param.companySettleId = this.infoForm.id;
      param.tpDateStart =
        this.billform.tpDate && this.billform.tpDate.length > 0
          ? this.billform.tpDate[0]
          : "";
      param.tpDateEnd =
        this.billform.tpDate && this.billform.tpDate.length > 0
          ? this.billform.tpDate[1]
          : "";
      console.log(param);
      let arr = ["tpMode", "garbageId", "goSoilType", "weightUnit"]
      arr.forEach(item => {
        if (param[item] && Array.isArray(param[item])) {
          param[item] = param[item].join(",")
        }
      })
      this.tableLoading = true;
      this.numbers = [];
      getSettleWaybill(param)
        .then((res) => {
          this.tableLoading = false;
          this.waybillData = res.data.data;
          this.waybillData.forEach((item, index) => {
            if (item.isReject == 1) {
              this.numbers.push(index);
            }
          });
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /* ------------------------------------------------------- 取消核算 ----------------------------------------------------- */
    cancelModal () {
      this.$emit("update:visible", false);
      this.$emit("searchData");
      this.numbers = [];
    },
    /* ------------------------------------------------------- 批量修改弹窗关闭前 ----------------------------------------------------- */
    closeDialog () {
      this.dialogVisible = false;
    },
    /* ------------------------------------------------------- 运单驳回 ----------------------------------------------------- */
    changeStatus (row, index) {
      this.$confirm("确定驳回此运单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          batchRejectPrice([{ id: row.id }])
            .then((res) => {
              this.confirm();
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        })
        .catch(() => { });
    },
    /* ------------------------------------------------------- 结算 ----------------------------------------------------- */
    changeSettle (row, index) {
      // if (row.weightTons <= 0||row.isPlatformWaybillCost==1) {
      this.$confirm("确定结算此运单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let arr = [];
          arr.push({
            id: row.id,
            // exAmt: row.exAmt,
            payeePrice:row.payeePrice,
            adjustAmt: row.adjustAmt,
            settlePrice: row.settlePrice,
          });
          console.log(arr);
          this.loading = true;
          batchUpdatePrice(arr)
            .then((res) => {
              // this.confirm();
              row.isSettle = 1;
              row.payeePrice = Number(row.payeePrice).toFixed(2);
              row.settlePrice = Number(row.settlePrice).toFixed(2);
              this.loading = false;
              this.dialogVisible = false;
              console.log(res);
            })
            .catch(() => {
              this.$message.error("结算失败");
              this.loading = false;
            });
        })
        .catch(() => { });
    },
    /* ------------------------------------------------------- 核算完毕正式提交----------------------------------------------------- */
    confirmSubmit () {
      let arr = [];
      this.multipleSelection.map((item) => {
        if (item.isReject != 1 &&
          item.isPlatformWaybillCost != 1
        ) {
          let obj = { id: item.id,companyWaybillId:item.companyWaybillId,adjustAmt:item.adjustAmt, }
          if (this.batchEditType == 2) {
            obj.payeePrice = this.unitPrice
            obj.settlePrice = item.settlePrice
          } else {
            obj.settlePrice = this.unitPrice
            obj.payeePrice = item.payeePrice
          }
          arr.push(obj);
        }
      });
      console.log(arr);
      if (arr.length < 1) {
        this.$message({
          type: "error",
          message: "无运单可修改！请选择要修改的运单",
        });
        return false;
      }
      this.loading = true;
      batchUpdatePrice(arr,this.batchEditType)
        .then((res) => {
          this.confirm();
          this.loading = false;
          this.priceVisible = false;
          console.log(res);
        })
        .catch(() => {
          this.loading = false;
        });
    },
    showDialog() {
      // 可替换为API获取数据
      this.centerDialogVisible = true
      // this.dialogVisible = true
    },
    /* ------------------------------------------------------- 核算完毕正式提交----------------------------------------------------- */
    submitSettleWaybill () {
      let params = [];
       for (let i = 0; i < this.waybillData.length; i++) {
        params.push(this.waybillData[i].companyWaybillId);
      }
       let checkParams = {
        ids: params
      }
      cardGiveCheck(checkParams).then((res) => { 
            if(res.data.data.length>0){
              // this.generateCardLoading = false;
              this.dialogObj.dialogData = res.data.data;
              //打开弹窗
              this.showDialog();
            }else{
              this.submitSettleWaybill1();
            }
          });
    },
    submitSettleWaybill1 () {
      let params = [];
       for (let i = 0; i < this.waybillData.length; i++) {
        params.push(this.waybillData[i].companyWaybillId);
      }
       let checkParams = {
        ids: params
      }
      this.$confirm("请确保所有运单都已核算完毕再提交", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                }).then(() => {
                    //先检查五证齐全
                    let param = {
                      settleId: this.infoForm.id,
                    };
                    //付款前检查证件齐全
                    checkPayment(param).then((res) => {
                      this.checkList = res.data.data.map((item) => {
                        item.roadStatus = item.roadStatus
                          ? item.roadStatus.split(",")
                          : [];
                        item.truckStatus = item.truckStatus
                          ? item.truckStatus.split(",")
                          : [];
                        return item;
                      });

                      if (this.checkList && this.checkList.length > 0) {
                        getDocCompleteRateByCompanySettleIdList([this.infoForm.id]).then(
                          (res) => {
                            this.tip = res.data.data.message;
                            this.checkVisible = true;
                          }
                        );
                      } else {
                        this.continuePayment();
                      }
                    });
                  }).catch(() => { });
    },

    continuePayment () {
      let param = {
        settleId: this.infoForm.id,
      };
      this.tableLoading = true;
      submitSettleWaybill(param)
        .then((res) => {
          this.tableLoading = false;
          console.log(res);
          this.$emit("update:visible", false);
          this.$emit("searchData");
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    //导出
    exOutExc () {
      expotOut(
        {},
        "/chain/companypayment/downloadSettleCheckLicense/" + this.infoForm.id,
        "证书检查"
      );
    },
    /* ------------------------------------------------------- 运单详情----------------------------------------------------- */
    detail (row, index) {
      this.billForm = Object.assign({}, row);
      this.billForm.projectName = this.infoForm.projectName;
      this.billVisible = true;
      // getObj(row.companyWaybillId).then(res=>{
      //   console.log(res);
      // })
    },
    /* ------------------------------------------------------- 设置背景色 ----------------------------------------------------- */
    tableRowClassName ({ row, rowIndex }) {
      // 为每行添加属性index
      row.index = rowIndex;
      let color = "";
      this.numbers.forEach((r, i) => {
        if (rowIndex === r) {
          // 自定义class名称，需要写到全局element-ui 的scss文件中或者用scoped做穿透
          // 本人穿透未成功，所以就没用scoped
          color = "myRowClass";
        }
      });
      return color;
    },
    /* ------------------------------------------------------- 全选 ----------------------------------------------------- */
    handleSelectionChange (val) {
      console.log(val);
      this.multipleSelection = val;
    },
    /* ------------------------------------------------------- 批量修改 ----------------------------------------------------- */
    bulkEdit (val) {
      console.log(val);
      let arr = this.multipleSelection.filter(item => {
        if (item.isReject != 1 && item.isPlatformWaybillCost != 1) {
          return item
        }
      }) || []
      if (val == 8) {
        this.noRejectCheckList = arr
        //如果选中有驳回的
        console.log(arr);
        if (arr.length != this.multipleSelection.length) {
          let num = this.multipleSelection.length - arr.length
          this.$confirm(`您有${num}个运单已驳回，不会参与此操作, 是否继续?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.batchEditVisible = true
          }).catch(() => { });
        } else {
          this.batchEditVisible = true
        }
      } else if (val == 9) {
        this.$confirm(`您确认将选择的${arr.length}单结算价覆盖至预设价?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let priceArr = [];
          this.multipleSelection.map((item) => {
            if (item.isReject != 1 && item.isPlatformWaybillCost != 1) {
              let obj = { id: item.id, companyWaybillId: item.companyWaybillId }
              obj.payeePrice = item.settlePrice
              priceArr.push(obj);
            }
          });
          this.batchEditPrice(priceArr)
        }).catch(() => { });
      } else if (val == 10) {
        this.$confirm(`您确认将选择的${arr.length}单预设价覆盖至结算价?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let priceArr = [];
          this.multipleSelection.map((item) => {
            if (item.isReject != 1 && item.isPlatformWaybillCost != 1) {
              let obj = { id: item.id, companyWaybillId: item.companyWaybillId }
              obj.settlePrice = item.payeePrice
              priceArr.push(obj);
            }
          });
          this.batchEditPrice(priceArr)
        }).catch(() => { });
      } else if (val == 7) {
        this.$prompt("请输入拆单备注", "拆分运单", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          inputPlaceholder: "请输入拆单备注",
          inputPattern: /\S/,
          inputValidator: (value) => {
            if (value && value.length > 100) {
              return "超出限制字数100";
            }
          },
          inputErrorMessage: "请输入拆单备注",
        })
          .then(({ value }) => {
            let companyWaybillIdList = this.multipleSelection.map(item => item.companyWaybillId)
            let param = {
              companySettleId: this.infoForm.id,
              companyWaybillIdList,
              staffRemark: value,
            }
            console.log(param);
            spiltCompanySettle(param).then(res => {
              this.$message.success(res.data.data || "操作成功");
              this.confirm();
            })
          })
          .catch(function (err) { });
      } else {
        let isSame =
          this.multipleSelection &&
          this.multipleSelection.length > 0 &&
          this.multipleSelection.every((item) => {
            return item.weightUnit == this.multipleSelection[0].weightUnit;
          });
        console.log(isSame);
        if (val == 5 && !isSame) {
          this.$message.error("请选择单位一致的运单进行修改");
          return false;
        }
        this.mode = val; //1批量修改报价  2批量驳回   3批量测回驳回   4批量撤回结算  5按重量修改价格  6批量结算
        this.dialogVisible = true;
      }
    },
    batchEditPrice (arr) {
     return new Promise((resolve, reject) => {
        this.loading = true;
        batchUpdatePrice(arr).then((res) => {
          this.confirm();
          this.loading = false;
          resolve()
        }).catch(() => {
          this.loading = false;
          reject()
        });
      })
    },
    /* ------------------------------------------------------- 取消运单 ----------------------------------------------------- */
    cancelWaybill (row) {
      this.$confirm("确定移出核算单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let param = {
          companySettleWaybillId: row.id,
          companyWaybillId: row.companyWaybillId,
        };
        this.tableLoading = true;
        cancelCompanyWayBill(param)
          .then((res) => {
            this.tableLoading = false;
            --this.countWaybill
            console.log(this.countWaybill);
            this.confirm();
          })
          .catch((err) => {
            this.tableLoading = false;
          });
      })
        .catch(() => { });
    },
    /* ------------------------------------------------------- 搜索栏收起来 ----------------------------------------------------- */
    changeCollapse (val) {
      setTimeout(() => {
        console.log(this.$refs.table.$el.offsetTop);
        this.tableHeight =
          window.innerHeight - this.$refs.table.$el.offsetTop - 20;
      }, 500);
    },
    /* ------------------------------------------------------- 导出exc ----------------------------------------------------- */
    downExport () {
      let param = Object.assign({}, this.billform);
      param.companySettleId = this.infoForm.id;
      param.tpDateStart =
        this.billform.tpDate && this.billform.tpDate.length > 0
          ? this.billform.tpDate[0]
          : "";
      param.tpDateEnd =
        this.billform.tpDate && this.billform.tpDate.length > 0
          ? this.billform.tpDate[1]
          : "";
      this.btnLoading = true;
      expotOut(
        param,
        "/chain/companysettlewaybill/getSettleWaybillExcel",
        "财务结算"
      )
        .then(() => {
          this.btnLoading = false;
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
    checkChange (val = false) {
      let start = this.start;
      let end = this.end;
      if (start > end) {
        [start, end] = [end, start];
      }
      if (
        this.waybillData &&
        this.waybillData.length > 0 &&
        start <= this.waybillData.length
      ) {
        if (end > this.waybillData.length) {
          end = this.waybillData.length;
        }
        for (let index = start; index <= end; index++) {
          this.$refs.table.toggleRowSelection([
            {
              row: this.waybillData[index - 1],
              selected: val,
            },
          ]);
        }
      }
    },
    clearSelection () {
      this.$refs.table.clearSelection();
    },
    handleCommand (event) {
      console.log(event);
      this.bulkEdit(event);
    },
    batchEdit (type) {
      // type 2 预设价  1结算价
      this.batchEditType = type
      this.unitPrice = 0
      this.priceVisible = true
    },
    submit (form, done) {
      console.log(form);
      let data = this.multipleSelection.map((item) => {
        return {
          id: item.companyWaybillId,
          payeeId: form.payeeId,
        };
      });
      console.log(data);
      updateCarrierName(data)
        .then((res) => {
          done();
          this.payeeVisible = false;
          this.$message.success("操作成功");
          this.confirm();
        })
        .catch((err) => {
          done();
        });
    },
    editAdjustComplete (averageValue,remainder, done) {
      let data = []
      this.multipleSelection.map((item,i) => {
        if(item.isReject != 1 && item.isPlatformWaybillCost != 1){
          data.push({
            id: item.id,
            companyWaybillId: item.companyWaybillId,
            adjustAmt: i==this.multipleSelection.length-1?remainder:averageValue,
          })
        }
      });
      this.batchEditPrice(data).then(re => {
        done()
      }).catch((err) => {
        done();
      });
    },
    viewInfo(row,index){
      this.tableLoading = true
      getObj(row.companyWaybillId).then(res=>{
        this.tableLoading = false
        this.detailForm = res.data.data
        this.detailForm.ticketNo = this.detailForm.ticketNo&&this.detailForm.manualSelectTicket=='0'?`${this.detailForm.ticketNo}(系统分配)`:this.detailForm.ticketNo||''
        this.detailVisible = true
      }).catch(err=>{
        this.tableLoading = false
      })
    },
  },
  destroyed () {
    window.removeEventListener("resize", this.func);
  },
};
</script>

<style lang="scss" scoped>
.info {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  // border-bottom: 1px solid #ccc;
  padding-bottom: 10px;

  li {
    width: 25%;
    line-height: 36px;
    color: #666;
    font-size: 14px;

    label {
      display: inline-block;
      width: 100px;
      text-align: right;
    }

    span {
      color: #000;
    }

    // &.small{
    //   min-width: 160px;
    // }
  }
}

/deep/ .el-drawer__header {
  margin-bottom: 10px;
  text-align: center;
  color: #000;
}

/deep/ .el-dialog__body {
  padding: 10px 20px 20px;
  .el-input-number input{
    text-align: left;
  }
  .remind {
    margin-top: 10px;

    div {
      color: #000;
      line-height: 30px;
      font-size: 16px;
    }

    p {
      font-size: 14px;
      line-height: 28px;
    }

    span {
      font-size: 16px;
      color: red;
    }
  }

  .dialog-footer {
  }
}

.search {
  border: 1px solid #ccc;
  padding: 10px 20px 0px;

  .el-row {
    margin-bottom: 10px;
  }

  /deep/ .el-form-item {
    display: flex;
    margin-right: 0;
    margin-bottom: 0px;

    .el-form-item__label {
      min-width: 80px;
    }

    .el-form-item__content {
      flex: 1;
    }

    .el-select,
    .el-range-editor--small {
      width: 100%;
    }
  }
}

.searchBtn {
  border: 1px solid #ccc;
  padding: 10px 20px;
  margin-top: 10px;

  .highlight {
    color: red;
    font-size: 14px;
    margin-bottom: 6px;
  }

  .hint {
    font-size: 14px;
    margin-bottom: 8px;
  }
}

/deep/ .el-drawer__body {
  padding: 0px 20px;

  .remark {
    line-height: 36px;
    font-size: 16px;
    position: absolute;
    bottom: 80px;
    line-height: 36px;
    color: #409eff;
  }

  .demo-drawer__footer {
    width: calc(100% - 40px);
    text-align: center;
    // background-color: rgba(204, 204, 204,0.7);
    border-radius: 6px;
    line-height: 60px;
    position: absolute;
    bottom: 20px;
    z-index: 4;
  }
}

/deep/ .el-table .myRowClass {
  background: #bbb;
}

/deep/ .el-table .el-table__fixed-right {
  height: auto !important;
  bottom: 8px !important;

  &::before {
    background-color: transparent;
  }
}
/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}
</style>
