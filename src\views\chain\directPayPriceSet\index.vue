<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        :before-open = "beforeOpen"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="handleDel"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <template slot="menu" slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            style="margin-left: 6px; margin-right: 6px"
            v-if="
              permissions['chain:directPayPriceSet:edit'] &&
              scope.row.status == 1
            "
            @click="editRow(scope.row, scope.index)"
            >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-check"
            v-if="
              permissions['chain:directPayPriceSet:audit'] &&
              scope.row.status == 1
            "
            @click="audit(scope.row)"
            >审核
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-warning-outline"
            v-if="
              permissions['chain:directPayPriceSet:disabled'] &&
              scope.row.status == 2
            "
            @click="failure(scope.row)"
            >失效
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            v-if="
              permissions['chain:directPayPriceSet:edit'] &&
              scope.row.status == 1
            "
            @click="handleDel(scope.row, scope.index)"
            >删除
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            v-if="
              permissions['chain:directPayPriceSet:edit'] &&
              (scope.row.status == 2 || scope.row.status == 4)
            "
            @click="viewDetial(scope.row, scope.index)"
            >查看
          </el-button>
        </template>
        <template slot="projectNameSearch" slot-scope="{ item, value, label }">
          <el-input v-model="search.projectName" disabled></el-input>
        </template>
        <template slot="captainAndDriverSearch" slot-scope="{ item, value, label }">
          <el-input v-model="search.captainAndDrivers" placeholder="车队长和司机"></el-input>
        </template>
        <template slot="soilTypeSearch" slot-scope="{ item, value, label }">
          <el-select v-model="search.soilType" clearable filterable placeholder="请选择">
            <el-option
              v-for="item in soilTypeSearchList"
              :key="item.id"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </template>
        <template slot="garbageIdSearch" slot-scope="{ item, value, label }">
          <el-select v-model="search.garbageName" clearable filterable placeholder="请选择">
            <el-option
              v-for="item in garbageSearchList"
              :key="item.id"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </template>
        <template slot="projectNameForm" slot-scope="{ item, value, label }">
          <el-input v-model="search.projectName" disabled></el-input>
        </template>
        <template
          slot="captainAndDriverForm"
          slot-scope="{ item, value, label }"
        >
          <div class="driverName">
            <div class="names" v-if="Array.isArray(form.captainAndDrivers)">
              <span
                v-for="(item, index) in form.captainAndDrivers"
                :key="index"
                ><span v-if="index!=0">，</span>{{ item.captainName }}(
                  <span v-for="(item2,index2) in item.children" :key="index2"><span v-if="index2!=0">,</span>{{ item2.driverName }}</span>
                  )
                </span>
            </div>
            <div class="names" v-else>
              {{form.captainAndDrivers}}
            </div>
            <span class="changeDriver" v-if="mode!='view'" @click="changeDriver"
              >选择车队长和司机</span
            >
          </div>
        </template>
        <template slot="checkAllForm" slot-scope="{ item, value, label }">
          <el-checkbox @change="changeCheckbox">全选</el-checkbox>
        </template>
      </avue-crud>
    </basic-container>
    <edit-dialog
      v-if="editVisible"
      :info="info"
      :visible.sync="editVisible"
      @refreshChange="refreshChange"
    ></edit-dialog>
    <change-driver
      v-if="changeVisible"
      :info="info"
      :visible.sync="changeVisible"
      @confirm="confirmChangeDriver"
    ></change-driver>
  </div>
</template>

<script>
import {
  getPage,
  addObj,
  putObj,
  delObj,
  failure,
  getSoilType,
  queryProjectGarbage,
  getCaptain,
  searchSoilType,
  searchGarbage,
} from "@/api/chain/directPayPriceSet";
import { tableOption } from "@/const/crud/chain/directPayPriceSet";
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";
import editDialog from "./editDialog.vue";
import changeDriver from "./changeDriver.vue";

export default {
  name: "directPayPriceSet",
  components: {
    editDialog,
    changeDriver,
  },
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption(this),
      editVisible: false,
      info: {},
      driverDic: [],
      search: {},
      garbageList: [],
      soilTypeList: [],
      garbageSearchList: [],
      soilTypeSearchList: [],
      changeVisible: false,
      mode: 'add',
    };
  },
  created() {},
  mounted: function () {
    let info = this.$route.query.info;
    if (info) {
      let obj = JSON.parse(info);
      this.search.projectName = obj.projectName;
      this.search.projectInfoId = obj.id;
      this.getSoilType();
      this.queryProjectGarbage();
      this.searchSoilType();
      this.searchGarbage();
    }
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:directPayPriceSet:add"] ? true : false,
        delBtn: this.permissions["chain:directPayPriceSet:del"] ? true : false,
        editBtn: this.permissions["chain:directPayPriceSet:edit"]
          ? true
          : false,
        viewBtn: this.permissions["chain:directPayPriceSet:get"] ? true : false,
      };
    },
  },
  methods: {
    searchChange(params, done) {
      params = this.filterForm(params);
      params.soilType = this.search.soilType
      params.captainAndDrivers = this.search.captainAndDrivers
      params.garbageName = this.search.garbageName
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params = {}) {
      console.log(this.search);
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.applyStartDate = params.searchDate[0];
          params.applyEndDate = params.searchDate[1];
          delete params.searchDate;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
            projectInfoId: JSON.parse(this.$route.query.info).id || "",
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      console.log(this.tableOption);
      delete row.captainAndDrivers
      row.projectInfoId = this.search.projectInfoId
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
          this.searchSoilType()
          this.searchGarbage()
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      console.log(row);
      delete row.captainAndDrivers
      row.projectInfoId = this.search.projectInfoId
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
          this.searchSoilType()
          this.searchGarbage()
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    beforeOpen(done,type){
    console.log(type);
        this.mode = type
      done()
    },
    searchSoilType(){
      searchSoilType({ projectInfoId: this.search.projectInfoId }).then((res) => {
        this.soilTypeSearchList = res.data.data.map((item,index) => {
          return {
            label: item,
            value: item,
            id:index
          };
        });
      });
    },
    searchGarbage(){
      searchGarbage({ projectInfoId: this.search.projectInfoId }).then((res) => {
        this.garbageSearchList = res.data.data.map((item,index) => {
          return {
            label: item,
            value: item,
            id:index
          };
        });
      });
    },
    editRow(row, index) {
      this.$refs.crud.rowEdit(row, index);
    },
    audit(row) {
      this.info = row;
      this.editVisible = true;
    },
    //查看
    viewDetial(row, index) {
      this.$refs.crud.rowView(row, index);
    },
    //已失效
    failure(row) {
      let _this = this;
      this.$confirm("确认直付价申请调整已失效？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return failure({ id: row.id });
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "操作成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    changeCheckbox(val) {
      console.log(val);
    },
    getCaptain() {
      getCaptain().then((res) => {
        this.driverDic = res.data.adata;
      });
    },
    getSoilType() {
      getSoilType({ projectInfoId: this.search.projectInfoId }).then((res) => {
        this.soilTypeList = res.data.data.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
        console.log(this.soilTypeList);
        this.$refs.crud.updateDic("soilType", this.soilTypeList);
      });
    },
    queryProjectGarbage() {
      queryProjectGarbage({ projectInfoId: this.search.projectInfoId }).then(
        (res) => {
          this.garbageList = res.data.data;
          this.$refs.crud.updateDic("garbageId", this.garbageList);
        }
      );
    },
    changeDriver() {
      if (!this.form.tpMode) {
        this.$message.error("请先选择运输方式");
        return false;
      }
      if (!this.form.soilType) {
        this.$message.error("请先选择土质");
        return false;
      }
      if (this.form.tpMode == 2 && !this.form.garbageId) {
        this.$message.error("请先选择泥尾");
        return false;
      }
      console.log(this.form);
      this.info = {
        tpMode: this.form.tpMode,
        soilType: this.form.soilType,
        garbageId: this.form.garbageId,
        projectInfoId: this.search.projectInfoId,
        projectName: this.search.projectName,
        captainAndDriver:this.form.captainAndDriver,
        type:this.mode=='add'?1:2
      };
      this.changeVisible = true;
    },
    confirmChangeDriver(arr) {
      //车队长名字
      // captainAndDrivers
      this.form.captainAndDriver = arr.map((item) => {
        return {
          captainId: item.captainId,
          driverId: item.driverId,
        };
      });
      console.log(arr);
      this.form.captainAndDrivers = this.toTree(arr);
      console.log(this.form.captainAndDrivers);
      this.changeVisible = false;
    },
    toTree(data) {
      let parent = [];
      let ids = []
      data.forEach((item) => {
        if(!ids.includes(item.captainId)){
          ids.push(item.captainId);
          parent.push({
            captainId:item.captainId,
            captainName:item.captainName,
          })
        }
      });
      console.log(parent);
        parent.forEach(item=>{
          item.children = []
          data.forEach(item2=>{
            if(item2.captainId==item.captainId){
              item.children.push(item2)
            }
          })
        })
      return parent;
    },
  },
};
</script>

<style lang="scss" scoped>
.driverName {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .names {
    min-height: 80px;
    background: #f3f4f5;
    width: calc(100% - 120px);
  }
  .changeDriver {
    cursor: pointer;
    color: #409eff;
  }
}
</style>
