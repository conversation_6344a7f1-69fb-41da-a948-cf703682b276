<template>
  <div class="pdfPreview">
    <div v-show="numPages&&numPages>0"
         v-loading="loading">
      <pdf ref="pdf"
           v-for="i in numPages"
           :key="i"
           :src="pdfUrl"
           :page="i"></pdf>
    </div>
  </div>
</template>

<script>
import pdf from 'vue-pdf'
export default {
  props: {
    pdfUrl: {
      type: String,
      default: () => {
        return ""
      }
    },
  },
  components: {
    pdf
  },
  data () {
    return {
      numPages: null,
      loading: false,
    }
  },
  created () {
  },
  mounted: function () {
    console.log(this.pdfUrl);
    this.getNumPages()
  },
  methods: {
    //计算pdf页码总数
    getNumPages () {
      this.loading = true
      let loadingTask = pdf.createLoadingTask(this.pdfUrl)
      loadingTask.promise.then(pdf => {
        this.numPages = pdf.numPages
        this.loading = false
        console.log(this.numPages);
      }).catch(err => {
        this.loading = false
        this.$message.error("pdf加载失败,请下载文件本地预览");
      })
    },
  }
};
</script>

<style lang="scss" scoped>
</style>
