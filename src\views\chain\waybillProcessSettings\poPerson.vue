<template>
  <!-- 导入 -->
  <el-dialog width="400px"
             center
             title="审批人员"
             :visible.sync="visible"
             :close-on-click-modal="false"
             :before-close="handleClose">
             <div v-if="personNames">{{personNames}}</div>
    <el-empty v-else></el-empty>
    <span slot="footer"
          class="dialog-footer">
      <el-button @click="handleClose">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: () => {
        return false
      },
    },
    personNames: {
      type: String,
      default: () => {
        return ""
      },
    },
  },
  data () {
    return {
    };
  },
  created () { },
  destroyed () { },
  mounted () { },
  beforeDestroy () { },
  computed: {},
  methods: {
    handleClose () {
      this.$emit("update:visible", false);
    },

  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
