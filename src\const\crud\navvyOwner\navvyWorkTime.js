export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  // index:true,
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  dialogClickModal:false,
  excelBtn: false,
  printBtn: false,
  addBtn: false,
  viewBtn: true,
  delBtn: false,
  searchSpan:6,
  searchMenuSpan: 6,
  labelWidth:100,
  defaultSort: {
    prop: "startDatetime",
    order: "descending",
  },
  menuWidth:80,
  column: [
    {
      label: "项目",
      prop: "projectName",
      // sortable: true,
      search: true,
      minWidth:180,
      overHidden:true,
    },
    {
      label: "挖机员",
      prop: "staffName",
      // sortable: true,
      search: true,
      minWidth:80,
      overHidden:true,
    },
    {
      label: "作业类型",
      prop: "ledgerType",
      // sortable: true,
      search: true,
      type: "select", // 下拉选择
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: "/chain/systemdictionaryitem/listDictionaryItem?dictionary=company_ledger_type",
      minWidth:80,
      overHidden:true,
    },
    {
      label: "状态",
      prop: "auditStatus",
      // sortable: true,
      search: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "提交",
          value: "0",
        },
        {
          label: "审批中",
          value: "1",
        },
        {
          label: "已审批",
          value: "2",
        },
        {
          label: "已驳回",
          value: "3",
        },
        {
          label: "已删除",
          value: "7",
        },

      ],
      minWidth:90,
      overHidden:true,
    },
    {
      label: "挖机班次",
      prop: "inShiftType",
      // sortable: true,
      search: true,
      type: "select", // 下拉选择
      dicUrl:"/chain/projectinfo/getShiftOfProject",
      dicFormatter: (res) => {
        return res.data.map((item)=>{
          return {
            label:item,
            value:item,
          }
        })
      },
      formatter:(val)=>{
        return val.inShiftTypeName
      },
      searchFilterable:true,
      minWidth:120,
      overHidden:true,
      display:false,
    },
    {
      label: "挖机班次",
      prop: "inShiftTypeName",
      hide:true,
      showColumn:false,
    },
    {
      label: "挖机类型",
      prop: "inType",
      // sortable: true,
      search: true,
      type: "select", // 下拉选择
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: "/chain/systemdictionaryitem/listDictionaryItem?dictionary=in_type",
      minWidth:80,
      overHidden:true,
    },
    {
      label: "机械型号",
      prop: "machineCode",
      overHidden:true,
      search: true,
      minWidth:120,
      overHidden:true,
    },
    {
      label: "完成量",
      prop: "scheduleWork",
      minWidth:80,
      overHidden:true,
    },
    {
      label: "单位",
      prop: "unitWork",
      type: "select", // 下拉选择
      dicData: [
        {
          label: "车",
          value: "1",
        },
        {
          label: "方",
          value: "2",
        },
      ],
      minWidth:80,
      overHidden:true,
    },
    {
      label: "作业地点",
      prop: "address",
      overHidden:true,
      minWidth:120,
      overHidden:true,
    },
    {
      label: "开始时间",
      prop: "startDatetime",
      sortable: true,
      minWidth:140,
      overHidden:true,
    },
    {
      label: "结束时间",
      prop: "endDatetime",
      sortable: true,
      minWidth:140,
      overHidden:true,
    },
    {
      label: "时长",
      prop: "workTime",
      minWidth:80,
      overHidden:true,
    },
    {
      label: "是否删除",
      prop: "isDel",
      search: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "是",
          value: "1",
        },
        {
          label: "否",
          value: "0",
        },
      ],
      minWidth:80,
      overHidden:true,
    },
    {
      label: "备注",
      prop: "ledgerRemark",
      overHidden:true,
      minWidth:120,
      overHidden:true,
    },
    // {
    //   label: "项目审核人",
    //   prop: "confirmStaffName",
    //   minWidth:90,
    //   overHidden:true,
    // },
    // {
    //   label: "项目审核时间",
    //   prop: "confirmDatetime",
    //   sortable: true,
    //   minWidth:140,
    //   overHidden:true,
    // },
    // {
    //   label: "核算审核人",
    //   prop: "checkStaffName",
    //   minWidth:90,
    //   overHidden:true,
    // },
    // {
    //   label: "核算审核时间",
    //   prop: "checkDatetime",
    //   minWidth:140,
    //   overHidden:true,
    //   sortable:"custom",
    // },
    {
      label: "创建日期",
      prop: "searchDate",
      type:'date',
      valueFormat: 'yyyy-MM-dd',
      searchRange:true,
      search:true,
      showColumn:false,
      hide:true,
      display:false,
    },
  ],
};
