export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: true,
  // printBtn: true,
  viewBtn: true,
  searchMenuSpan: 6,
  menu: false,
  column: [
    // {
    //   label: "ID",
    //   prop: "id",
    //   sortable: true,
    //   minWidth:140,
    //   overHidden:true,
    // },
    {
      label: "转出卡号",
      prop: "sourceCardNo",
      search: true,
      sortable: true,
      minWidth:130,
      overHidden:true,
    },
    {
      label: "转入卡号",
      prop: "targetCardNo",
      sortable: true,
      search: true,
      minWidth:130,
      overHidden:true,
    },
    {
      label: "车牌",
      prop: "truckCode",
      sortable: true,
      search: true,
      minWidth:84,
      overHidden:true,
    },
    {
      label: "出场时间",
      prop: "goDatetime",
      sortable: true,
      minWidth:140,
      overHidden:true,
    },
    {
      label: "运单号",
      search: true,
      prop: "companyWaybillNo",
      sortable: true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "出场价",
      prop: "payeePrice",
      minWidth:80,
      overHidden:true,
    },
    {
      label: "运单状态",
      prop: "statusName",
      minWidth:90,
      overHidden:true,
    },
    {
      label: "公司名称",
      prop: "companyName",
      search: true,
      sortable: true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "项目名称",
      prop: "projectName",
      search: true,
      sortable: true,
      minWidth:160,
      overHidden:true,
    },

    // {
    //   label: "操作人ID",
    //   prop: "createId",
    //   search: true,
    //   sortable: true,
    //   minWidth:160,
    //   overHidden:true,
    // },
    {
      label: "操作人",
      prop: "createName",
      search: true,
      sortable: true,
      minWidth:90,
      overHidden:true,
    },
    {
      label: "操作时间",
      prop: "createDatetime",
      type: "datetime",
      valueFormat: "yyyy-MM-dd",
      searchRange: true,
      sortable: true,
      minWidth:140,
      overHidden:true,
    },
    {
      label: "操作日期",
      prop: "createDatetimeBegin",
      type: "date",
      valueFormat: "yyyy-MM-dd",
      searchRange: true,
      search: true,
      hide: true,
      showColumn: false,
    },
  ],
};
