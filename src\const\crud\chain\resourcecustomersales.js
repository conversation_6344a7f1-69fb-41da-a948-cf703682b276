import {isMobileNumber} from '@/util/validate'

export const tableOption = (value)=>{
  let that = value
  console.log(that);
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    dialogClickModal:false,
    dialogWidth:600,
    excelBtn: false,
    printBtn: false,
    addBtn: false,
    viewBtn: false,
    searchSpan:6,
    searchMenuSpan: 6,
    labelWidth:120,
    searchLabelWidth:110,
    menu:false,
    column: [
      {
        label: "类型",
        prop: "type",
        sortable: true,
        search: true,
        type: "select", // 下拉选择
        display:false,
        dicData: [
          {
            label: "售票",
            value: "1",
          },
          {
            label: "退票",
            value: "2",
          },
        ],
        minWidth:80,
        overHidden:true,
      },
      {
        label: "客户联系方式",
        prop: "customerMobile",
        span:24,
        sortable: true,
        search: true,
        display:true,
        maxlength:11,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
          {
            validator: isMobileNumber,
            trigger: "blur",
          },
        ],
        minWidth:120,
        overHidden:true,
      },
      {
        label: "客户名称",
        prop: "customerName",
        sortable: true,
        search:true,
        maxlength:40,
        span:24,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:120,
        overHidden:true,
      },
      {
        label: "退票客户",
        prop: "resourceCustomerId",
        span:24,
        type:'select',
        display:false,
        hide:true,
        showColumn:false,
        props: {
          label: 'id',
          value: 'id'
        },
        typeformat(item, label, value) {
          return `${item['name']}-${item['mobile']}`
        },
        change:({value})=>{
          that.form.resourceType2 = ''
          that.form.surplus = ''
          that.$refs.crud.updateDic("resourceType2",[])
          if(value){
            that.getResourceType(value)
          }
        },
        dicUrl:"/chain/resourcecustomer/list",
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      {
        label: "资源类型",
        prop: "resourceType",
        sortable: true,
        span:24,
        type:'select',
        search:true,
        props:{
          label:'itemValue',
          value:'itemValue'
        },
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=resource_type',
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        minWidth:100,
        overHidden:true,
      },
      {
        label: "资源类型",
        prop: "resourceType2",
        span:24,
        type:'select',
        hide:true,
        showColumn:false,
        props:{
          label:'resourceType',
          value:'resourceType'
        },
        change:({value})=>{
          if(value&&that.form.resourceCustomerId){
            console.log(that.form);
            that.getInventoryNum(that.form.resourceCustomerId,value)
          }
        },
        // dicData:[],
        // dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=resource_type',
        rules: [
          {
            required: true,
            message: "请选择资源类型",
            trigger: "change",
          },
        ],
      },
      {
        label: "剩余票数",
        prop: "surplus",
        sortable: true,
        disabled:true,
        hide:true,
        showColumn:false,
        display:false,
        span:24,
      },
      {
        label: "经办人",
        prop: "operator",
        sortable: true,
        span:24,
        search: true,
        maxlength:40,
        placeholder:'请输入',
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:90,
        overHidden:true,
      },
      {
        label: "经办日期",
        span:24,
        prop: "searchDate",
        sortable: true,
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange:true,
        search:true,
        hide:true,
        showColumn:false,
        display:false,
        placeholder:'请输入',
      },
      {
        label: "经办日期",
        span:24,
        prop: "operateDate",
        sortable: true,
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        placeholder:'请输入',
        value:that.$moment().format('YYYY-MM-DD'),
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:100,
        overHidden:true,
      },
      {
        label: "数量",
        prop: "qty",
        span:24,
        controls:false,
        type:'number',
        sortable: true,
        display:false,
        precision:0,
        minRows:0,
        rules: [
          {
            required: true,
            message: "请输入数量",
            trigger: "blur",
          },
          {
            min: 0,
            type: "number",
            message: "值不能小于0",
            trigger: "blur",
          },
        ],
        minWidth:80,
        overHidden:true,
      },
      {
        label: "单价(元/张)",
        prop: "price",
        type:'number',
        span:24,
        sortable: true,
        controls:false,
        minRows: 0,
        maxRows: 99999.99,
        precision: 2,
        rules: [
          {
            required: true,
            message: "请输入单价",
            trigger: "blur",
          },
          {
            min: 0,
            type: "number",
            message: "值不能小于0",
            trigger: "blur",
          },
        ],
        minWidth:120,
        overHidden:true,
      },
      {
        label: "票号",
        prop: "ticketNo",
        sortable: true,
        display:false,
        minWidth:160,
        overHidden:true,
      },
      {  //开始票号
        label: "票号",
        prop: "beginNo",
        span:14,
        hide:true,
        showColumn:false,
        placeholder:'开始票号',
        maxlength:11,
        // value:new Date().getFullYear(),
        blur:({value})=>{
          console.log(value);
          if (value&&/^([0-9][0-9]*)$/.test(value)&&that.form.endNo&&/^([0-9][0-9]*)$/.test(that.form.endNo)) {
            that.form.num = Number(that.form.endNo) - Number(value)+1
          }else{
            that.form.num = 0
          }
        },
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if(value&&value!=''){
                if(/^([0-9][0-9]*)$/.test(value)){
                  if(Number(that.form.endNo)>0&&Number(value)>Number(that.form.endNo)){
                    callback(new Error('开始票号不能大于结束票号'));
                  }else{
                    callback();
                  }
                }else{
                  callback(new Error('输入不正确'));
                }
              }
              else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      {  // -
        label: "",
        labelWidth:0,
        span:1,
        prop: "line",
        hide:true,
        showColumn:false,
      },
      {
        label: "",
        labelWidth:0,
        prop: "endNo",
        span:8,
        hide:true,
        showColumn:false,
        placeholder:'结束票号',
        maxlength:11,
        // value:new Date().getFullYear(),
        blur:({value})=>{
          if (value&&/^([0-9][0-9]*)$/.test(value)&&that.form.beginNo&&/^([0-9][0-9]*)$/.test(that.form.beginNo)) {
            that.form.num = Number(value) - Number(that.form.beginNo)+1
          }else{
            that.form.num = 0
          }
        },
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if(value&&value!=''){
                if(/^([0-9][0-9]*)$/.test(value)){
                  console.log(value<=that.form.beginNo);
                  if(that.form.beginNo!=''&&Number(value)<Number(that.form.beginNo)){
                    callback(new Error('结束票号不能小于开始票号'));
                  }else{
                    callback();
                  }
                }else{
                  callback(new Error('输入不正确'));
                }
              }
              else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      {
        label: "售数张数",
        prop: "num",
        span:24,
        disabled:true,
        hide:true,
        showColumn:false,
        placeholder:'请输入',
      },
      {
        label: "创建人",
        prop: "createName",
        display:false,
        sortable: true,
        minWidth:90,
        overHidden:true,
      },
      {
        label: "创建时间",
        prop: "createDatetime",
        display:false,
        sortable: true,
        minWidth:140,
        overHidden:true,
      },
    ],
  };
}

