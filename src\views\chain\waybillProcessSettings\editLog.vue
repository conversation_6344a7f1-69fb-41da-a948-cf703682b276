<template>
  <div class="editLog">
      <slot></slot>
      <avue-crud ref="crud"
               :page.sync="page"
               :data="tableData"
               :permission="permissionList"
               :table-loading="tableLoading"
               :option="tableOption"
               v-model="form"
               @on-load="getPage"
               @refresh-change="refreshChange"
               @sort-change="sortChange"
               @search-change="searchChange">
        <template slot="flow"
                  slot-scope="{ row }">
          <el-button type="text"
                     icon="el-icon-view"
                     size="small"
                     plain
                     @click="view(row)">查看</el-button>
        </template>
      </avue-crud>
  </div>
</template>



<script>
import { getFlowHistoryPage as getPage,getDetailById} from '@/api/chain/waybillProcessSettings'

import { mapGetters } from 'vuex'

export default {
  props:{
    approveType:{
      type:[String,Number],
      default:1,
    }
  },
  name: 'editLog',
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: true,
        border: true,
        indexLabel: "序号",
        stripe: true,
        menuAlign: "center",
        align: "center",
        menuType: "text",
        searchShow: true,
        excelBtn: false,
        printBtn: false,
        // defaultSort: {
        //   prop: 'createDatetime',
        //   order: 'descending'
        // },
        viewBtn: false,
        header:false,
        searchMenuSpan: 6,
        menu: false,
        column: [
          {
            label: "修改人",
            prop: "updateName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "修改时间",
            prop: "updateDatetime",
            minWidth: 140,
            overHidden: true,
          },
          {
            label: "修改项",
            prop: "approveTypeText",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "开关状态",
            prop: "checkOpen",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "创建人",
            prop: "createName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "创建时间",
            prop: "createDatetime",
            minWidth: 170,
            overHidden: true,
          },
          {
            label: "修改前审批流程",
            prop: "flow",
            minWidth: 170,
            overHidden: true,
          },
        ],
      }
    }
  },
  created () {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList () {
      return {
        addBtn: this.permissions['chain:editLog:add'] ? true : false,
        delBtn: this.permissions['chain:editLog:del'] ? true : false,
        editBtn: this.permissions['chain:editLog:edit'] ? true : false,
        viewBtn: this.permissions['chain:editLog:get'] ? true : false
      };
    }
  },
  methods: {
    searchChange (params={}, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage (page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        approveType:this.approveType,   //审批流程类型：1=企业修改运单、2=企业台班、3=企业(带签证)台班、4=资金支付计划、91=企业结算单、92=企业支付单
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page)
    },
    view(row){
      this.tableLoading = true
      getDetailById({id:row.id}).then(res=>{
        this.$emit("viewFlow",res.data.data,this.closeLoading)
      })
    },
    closeLoading(){
      this.tableLoading = false
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
