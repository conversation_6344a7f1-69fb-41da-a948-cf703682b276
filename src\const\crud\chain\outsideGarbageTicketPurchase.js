export const tableOption = (value)=>{
  let that = value
  console.log(that);
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    dialogClickModal:false,
    dialogWidth:500,
    excelBtn: false,
    printBtn: false,
    addBtn: false,
    viewBtn: false,
    searchSpan:6,
    searchMenuSpan: 6,
    labelWidth:100,
    menu:false,
    defaultSort: {
      prop: "purchaseDate",
      order: "descending",
    },
    column: [
      {
        label: "类型",
        prop: "type",
        sortable: true,
        search: true,
        type: "select", // 下拉选择
        display:false,
        dicData: [
          {
            label: "购票",
            value: "1",
          },
          {
            label: "退票",
            value: "2",
          },
        ],
        minWidth:70,
        overHidden:true,
      },
      {
        label: "泥尾",
        prop: "garbageId",
        span:24,
        type:'select',
        hide:true,
        showColumn:false,
        clearable:false,
        filterable:true,
        props:{
          label:'names',
          value:'id'
        },
        change:({value})=>{
          if(value){
            that.getSoilType(value)
          }
        },
        dicUrl: "/chain/garbage/listByCompanyAuthNotInternalTicket",
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
      },
      {
        label: "泥尾",
        prop: "garbageName",
        sortable: true,
        search: true,
        display:false,
        minWidth:120,
        overHidden:true,
      },
      {
        label: "土质",
        prop: "soilTypeName",
        type:'select',
        search: true,
        searchFilterable:true,
        hide:true,
        display:false,
        showColumn:false,
        props:{
          label:'itemValue',
          value:'itemValue'
        },
        dicUrl:'/chain/systemdictionaryitem/listDictionaryItem?dictionary=soil_type'
      },
      {
        label: "土质",
        prop: "soilType",
        type:'select',
        sortable: true,
        span:24,
        clearable:false,
        filterable:true,
        props:{
          label:'label',
          value:'value'
        },
        change:({value})=>{
          if(value&&that.type==2){
            that.getNum(value)
          }
        },
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:80,
        overHidden:true,
      },
      {
        label: "剩余数",
        prop: "num",
        disabled:true,
        hide:true,
        showColumn:false,
        display:false,
        span:24,
      },
      {  //退票泥尾票号
        label: "泥尾票编号",
        prop: "refundTicketNo",
        span:24,
        placeholder:'请选择 泥尾票编号',
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        maxlength:20,
        hide:true,
        showColumn:false,
        minWidth:120,
        overHidden:true,
      },
      // {
      //   label: "单价(元/张)",
      //   prop: "price",
      //   type:'number',
      //   span:24,
      //   sortable: true,
      //   controls:false,
      //   minRows: 0,
      //   maxRows: 99999.99,
      //   precision: 2,
      //   rules: [
      //     {
      //       required: true,
      //       message: "请输入单价",
      //       trigger: "blur",
      //     },
      //     {
      //       min: 0,
      //       type: "number",
      //       message: "值不能小于0",
      //       trigger: "blur",
      //     },
      //   ],
      // },
      {  //票号前缀
        label: "泥尾票前缀",
        prop: "startPrefix",
        span:24,
        placeholder:'泥尾票前缀',
        maxlength:20,
        minWidth:120,
        overHidden:true,
      },
      {  //开始票号
        label: "泥尾票编号",
        prop: "startNo",
        span:14,
        // hide:true,
        // showColumn:false,
        formatter: (val) => {
          return ((val.startNo?val.startNo:'')+'-'+(val.endNo?val.endNo:''))
        },
        placeholder:'开始票号',
        maxlength:20,
        minWidth:120,
        overHidden:true,
        blur:({value})=>{
          console.log(value);
          if (value&&/^([0-9][0-9]*)$/.test(value)&&that.form.endNo&&/^([0-9][0-9]*)$/.test(that.form.endNo)) {
            that.form.qty = Number(that.form.endNo) - Number(value)+1
          }else{
            that.form.qty = 0
          }
        },
        rules: [
          {
            required: true,
            message: "请输入 开始票号",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if(value&&value!=''){
                if(/^([0-9][0-9]*)$/.test(value)){
                  if(Number(that.form.endNo)>0&&Number(value)>Number(that.form.endNo)){
                    callback(new Error('开始票号不能大于结束票号'));
                  }else{
                    callback();
                  }
                }else{
                  callback(new Error('输入不正确'));
                }
              }else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      {  // -
        label: "",
        labelWidth:0,
        span:2,
        prop: "line",
        hide:true,
        showColumn:false,
      },
      {
        label: "",
        labelWidth:0,
        prop: "endNo",
        span:8,
        hide:true,
        showColumn:false,
        maxlength:20,
        blur:({value})=>{
          that.$refs.crud.validateField("startNo")
          if (value&&/^([0-9][0-9]*)$/.test(value)&&that.form.startNo&&/^([0-9][0-9]*)$/.test(that.form.startNo)) {
            that.form.qty = Number(value) - Number(that.form.startNo)+1
          }else{
            that.form.qty = 0
          }
        },
        placeholder:'结束票号',
        rules: [
          {
            required: true,
            message: "请输入 结束票号",
            trigger: "blur",
          },
          // {
          //   validator: (rule, value, callback) => {
          //     if(value&&value!=''){
          //       if(/^([0-9][0-9]*)$/.test(value)){
          //         console.log(value<=that.form.startNo);
          //         if(that.form.startNo!=''&&Number(value)<Number(that.form.startNo)){
          //           callback(new Error('结束票号不能小于开始票号'));
          //         }else{
          //           callback();
          //         }
          //       }else{
          //         callback(new Error('输入不正确'));
          //       }
          //     }
          //     else {
          //       callback();
          //     }
          //   },
          //   trigger: "blur",
          // },
        ],
      },
      {
        label: "数量",
        prop: "qty",
        span:24,
        controls:false,
        type:'number',
        sortable: true,
        precision:0,
        minRows:0,
        maxRows:99999,
        disabled:true,
        placeholder:'请输入',
        rules: [
          {
            required: true,
            message: "请输入数量",
            trigger: "blur",
          },
          {
            min: 0,
            type: "number",
            message: "值不能小于0",
            trigger: "blur",
          },
        ],
        minWidth:80,
        overHidden:true,
      },
      {
        label: "单价(¥）",
        prop: "price",
        span:24,
        sortable: true,
        search:true,
        controls:false,
        type:'number',
        sortable: true,
        precision:2,
        minRows:0.00,
        maxRows:9999999.99,
        placeholder:'请输入',
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:96,
        overHidden:true,
      },
      {
        label: "经办人",
        prop: "purchaseMan",
        span:24,
        sortable: true,
        search:true,
        maxlength:40,
        placeholder:'请输入',
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:84,
        overHidden:true,
      },
      {
        label: "经办日期",
        span:24,
        prop: "searchDate",
        sortable: true,
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange:true,
        search:true,
        hide:true,
        showColumn:false,
        display:false,
        placeholder:'请选择',
      },
      {
        label: "经办日期",
        span:24,
        prop: "purchaseDate",
        sortable: true,
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        placeholder:'请选择',
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        minWidth:100,
        overHidden:true,
      },
      {
        label: "创建人",
        prop: "createName",
        display:false,
        sortable: true,
        minWidth:84,
        overHidden:true,
      },
      {
        label: "创建时间",
        prop: "createDatetime",
        display:false,
        sortable: true,
        minWidth:140,
        overHidden:true,
      },
    ],
  };
}

