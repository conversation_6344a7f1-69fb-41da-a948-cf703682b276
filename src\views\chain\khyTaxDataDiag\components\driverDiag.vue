<template>
  <div>
    <avue-crud ref="crud" :page.sync="khyHome.page" :data="tableData" :table-loading="tableLoading"
      :option="tableOption" v-model="form" @on-load="getPage" @refresh-change="refreshChange" @sort-change="sortChange"
      @search-change="searchChange">
      <template slot="menuLeft" slot-scope="{ row }">
        <el-button size="small" type="primary" :loading="btnsLoading" @click="exOut">
          导出
        </el-button>
      </template>
      <template slot="isOk" slot-scope="{ row }">
        <i class="el-icon-check checkIcon" v-if="row.isOk==1"></i>
        <i class="el-icon-close closeIcon" v-else></i>
      </template>
      <template slot="sfz" slot-scope="{ row }">
        <span style="color:#409eff;cursor:pointer;" @click="viewRow(row,'idCardFrontFilename')">人头像</span>
        <span style="color:#409eff;cursor:pointer;margin-left: 4px;"
          @click="viewRow(row,'idCardBackFilename')">国徽面</span>
      </template>
      <template slot="jsz" slot-scope="{ row }">
        <span style="color:#409eff;cursor:pointer;" @click="viewRow(row,'licenseFilename')">正本</span>
        <span style="color:#409eff;cursor:pointer;margin-left: 4px;"
          @click="viewRow(row,'licenseFilenameCopy')">副本</span>
      </template>
      <template slot="qualificationLicenceMainFilename" slot-scope="{ row }">
        <span style="color:#409eff;cursor:pointer;" @click="viewRow(row,'qualificationLicenceMainFilename')">正本</span>
      </template>
    </avue-crud>
  </div>
</template>

<script>
  import {
    mapGetters
  } from "vuex";
  export default {
    name: "khyTaxDataDiag",
    inject: ["khyHome"],
    props: {
      tableData: {
        type: Array,
        default () {
          return [];
        },
      },
      btnsLoading: {
        type: Boolean,
        default () {
          return false;
        },
      },
    },
    data() {
      return {
        form: {},
        paramsSearch: {},
        tableLoading: false,
        tableOption: {
          dialogDrag: true,
          border: true,
          indexLabel: "序号",
          stripe: true,
          menuAlign: "center",
          align: "center",
          menuType: "text",
          searchShow: false,
          excelBtn: false,
          printBtn: false,
          viewBtn: false,
          addBtn: false,
          delBtn: false,
          searchMenuSpan: 6,
          labelWidth: 120,
          menu: false,
          // rowKey:'',
          column: [{
              label: "是否合格",
              prop: "isOk",
            },
            {
              label: "司机姓名",
              prop: "name",
            },
            {
              label: "手机号码",
              prop: "phone",
              width: 110,
            },
            {
              label: "身份证号",
              prop: "idCard",
              width: 130,
              overHidden: true,
            },
            {
              label: "身份证住址",
              prop: "address",
              width: 120,
              overHidden: true,
            },
            {
              label: "身份证",
              prop: "sfz",
              width: 150,
            },
            {
              label: "身份证有效开始",
              prop: "idCardStartDate",
              width: 120,
            },
            {
              label: "身份证有效结束",
              prop: "idCardEndDate",
              width: 120,
            },
            {
              label: "身份证到期天数",
              prop: "idCardExpiryDays",
              width: 140,
              formatter: (val) => {
                if (val.idCardExpiryDays <= 0) {
                  return `<span style="color:red">已过期${-val.idCardExpiryDays}天</span>`
                } else if (val.idCardExpiryDays && val.idCardExpiryDays < 90) {
                  return `<span style="color:red">${val.idCardExpiryDays}天后过期</span>`
                } else {
                  return val.idCardExpiryDays
                }
              }
            },
            {
              label: "身份证发证机关",
              prop: "idCardIssueOrganization",
              overHidden: true,
              width: 140,
            },
            {
              label: "驾驶证编号",
              prop: "driverLicenseNumber",
              overHidden: true,
              width: 120,
            },
            {
              label: "准驾车型",
              prop: "driveType",
            },
            {
              label: "驾驶证有效开始",
              prop: "driverLicenseStartDate",
              width: 120,
            },
            {
              label: "驾驶证有效结束",
              prop: "driverLicenseEndDate",
              width: 120,
            },
            {
              label: "驾驶证到期天数",
              prop: "archivesExpiryDays",
              width: 140,
              formatter: (val) => {
                if (val.archivesExpiryDays <= 0) {
                  return `<span style="color:red">已过期${-val.archivesExpiryDays}天</span>`
                } else if (val.archivesExpiryDays && val.archivesExpiryDays < 90) {
                  return `<span style="color:red">${val.archivesExpiryDays}天后过期</span>`
                } else {
                  return val.archivesExpiryDays
                }
              }
            },
            {
              label: "驾驶证发证机关",
              prop: "driverLicenseIssueOrganization",
              width: 140,
              overHidden: true,
            },
            {
              label: "驾驶证",
              prop: "jsz",
              overHidden: true,
            },
            {
              label: "从业资格证号",
              prop: "qualificationNumber",
              width: 140,
              overHidden: true,
            },
            {
              label: "从业资格证",
              prop: "qualificationLicenceMainFilename",
              width: 100,
            },
            {
              label: "关联运单数",
              prop: "waybillCnt",
              width: 100,
              overHidden: true,
            },
          ],
        },
        setVisible: false,
        info: {}
      };
    },
    created() {},
    mounted: function() {},
    computed: {
      ...mapGetters(["permissions"]),
      permissionList() {
        return {
          addBtn: this.permissions["chain:khyTaxDataDiag:add"] ? true : false,
          delBtn: this.permissions["chain:khyTaxDataDiag:del"] ? true : false,
          editBtn: this.permissions["chain:khyTaxDataDiag:edit"] ? true : false,
          viewBtn: this.permissions["chain:khyTaxDataDiag:get"] ? true : false,
          excelBtn: this.permissions["chain:khyTaxDataDiag:excel"] ? true : false,
        };
      },
    },
    methods: {
      searchChange(params, done) {
        params = this.filterForm(params);
        this.paramsSearch = params;
        this.khyHome.page.currentPage = 1;
        this.getPage(this.khyHome.page, params);
        done();
      },
      sortChange(val) {
        this.$emit('sortChange', val)
      },
      getPage(page, params) {
        this.$emit('getPage', page)
      },

      /**
       * 刷新回调
       */
      refreshChange(page) {
        this.getPage(this.khyHome.page);
      },
      viewRow(row, label) {
        if (row[label]) {
          this.$ImagePreview(
            [{
              thumbUrl: row[label],
              url: row[label]
            }],
            0, {
              closeOnClickModal: true,
            }
          );
        } else {
          this.$message.error('暂无图片')
        }
      },
      exOut() {
        this.$emit('exOut', '司机诊断')
      },
    },
  };
</script>

<style lang="scss" scoped>
  /deep/ .closeIcon,
  .checkIcon {
    font-size: 16px;
    color: red;
  }

  /deep/ .checkIcon {
    color: #3dcc90;
  }
</style>
