<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="1200px"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <div style="position: absolute; left: 32px; top: 16px">
          <el-button
            type="primary"
            icon="el-icon-download"
            size="small"
            @click="exOut"
          >
            导出XLSX</el-button
          >
        </div>
        <div>
          <el-descriptions class="des2" :column="1" direction="vertical" border>
            <el-descriptions-item
              :label="form.companyName + '支付单'"
              contentStyle="textAlign:center;fontSize:16px"
              labelStyle="textAlign:center;fontSize:18px;color:#000"
            >
              {{ form.projectName }}
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions class="margin-top" :column="2" border>
            <el-descriptions-item label="支付单号">
              {{ form.paymentNo }}
            </el-descriptions-item>
            <el-descriptions-item label="结算单数量">
              {{ form.settleCnt }}
            </el-descriptions-item>
            <el-descriptions-item label="运单数量">
              {{ form.waybillCnt }}
            </el-descriptions-item>
            <el-descriptions-item label="税率">
              {{ form.taxRate }}%
            </el-descriptions-item>
            <el-descriptions-item label="结算合计金额(元)">
              {{ form.settleAmt }}
            </el-descriptions-item>
            <el-descriptions-item label="运费">
              {{ form.freight }}
            </el-descriptions-item>
            <el-descriptions-item label="税费">
              {{ form.taxFee }}
            </el-descriptions-item>
            <el-descriptions-item label="总费用">
              {{ form.realPayPrice }}
            </el-descriptions-item>
            <el-descriptions-item label="充值运费" v-if="rechargeList&&rechargeList.length>0">
              {{ form.chargedPrice }}
            </el-descriptions-item>
            <el-descriptions-item label="待充值运费" v-if="rechargeList&&rechargeList.length>0">
              {{ form.prepaidPrice }}
            </el-descriptions-item>
            <el-descriptions-item label="充值税费" v-if="rechargeList&&rechargeList.length>0">
              {{ form.chargedTaxFee }}
            </el-descriptions-item>
            <el-descriptions-item label="待充值税费" v-if="rechargeList&&rechargeList.length>0">
              {{ form.prepaidTaxFee }}
            </el-descriptions-item>
            <el-descriptions-item label="已付运费">
              {{ form.paidPrice }}
            </el-descriptions-item>
            <el-descriptions-item label="待付运费">
              {{ form.unPaidPrice }}
            </el-descriptions-item>
            <el-descriptions-item label="已付税费">
              {{ form.paidTaxFee }}
            </el-descriptions-item>
            <el-descriptions-item label="待付税费">
              {{ form.unPaidTaxFee }}
            </el-descriptions-item>
            <el-descriptions-item label="证件齐全运单数">
              {{ form.companyWaybillCount }}
            </el-descriptions-item>
            <el-descriptions-item label="证件齐全金额">
              {{ form.completeDocumentsPrice }}
            </el-descriptions-item>
          </el-descriptions>
          <avue-crud
            ref="crud"
            :data="rechargeList"
            :table-loading="tableLoading"
            :option="tableOption"
            v-if="rechargeList&&rechargeList.length>0"
            :empty="false"
          >
          <span slot="empty">暂无数据</span>
          </avue-crud>
          <avue-crud
            ref="crud"
            :data="payeeGroupList"
            :table-loading="tableLoading"
            :option="tableOption2"
          >
          <span slot="empty">暂无数据</span>
          </avue-crud>
        </div>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getDetail } from "@/api/chain/companyauthrecharge";
import { expotOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  data() {
    return {
      tableLoading: false,
      form: {},
      payeeGroupList: [],
      rechargeList: [],
      tableOption:{
        dialogDrag: false,
        border: true,
        stripe: false,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: false,
        menu:false,
        header:false,
        refreshBtn:false,
        column: [
          {
            label: "充值时间",
            prop: "paymentDatetime",
            overHidden:true,
          },
          {
            label: "充值运费(元)",
            prop: "money",
            overHidden:true,
          },
          {
            label: "支付税费(元)",
            prop: "payTaxes",
            overHidden:true,
          },
          {
            label: "充值备注",
            prop: "remark",
            overHidden:true,
          },
        ],
      },
      tableOption2:{
        dialogDrag: false,
        border: true,
        stripe: false,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: false,
        menu:false,
        header:false,
        refreshBtn:false,
        column: [
          {
            label: "收款人",
            prop: "payeeName",
            overHidden:true,
          },
          {
            label: "收款卡号",
            prop: "bindingBankNo",
            overHidden:true,
          },
          {
            label: "收款银行",
            prop: "bindingBankName",
            overHidden:true,
          },
          {
            label: "手机号",
            prop: "payeeMobile",
            overHidden:true,
          },
          {
            label: "支付运单数",
            prop: "payWaybillCnt",
            overHidden:true,
          },
          {
            label: "付款金额",
            prop: "bankPaySuccessAmount",
            overHidden:true,
          },
          {
            label: "付款时间",
            prop: "payTimes",
            overHidden:true,
          },
          {
            label: "付款人",
            prop: "payUsers",
            overHidden:true,
          },
        ],
      },
    };
  },
  created() {
  },
  mounted () {
    this.getPage()
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    getPage() {
      getDetail(this.info.paymentNo).then((res) => {
        this.payeeGroupList = res.data.data.payeeGroupList;
        this.rechargeList = res.data.data.rechargeList;
        this.form = res.data.data.taxInfo;
      });
    },
    exOut() {
      let url =
        "/chain/companypayment/downloadPaymentViewExcel/" + this.info.paymentNo;
      // params.id = this.info.id
      expotOut({}, url, "付款打款详情");
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 0px;
  padding-bottom: 20px;
}

/deep/ .el-descriptions__title {
  padding-left: 8px;
  text-align: left;
  height: 16px;
  line-height: 16px;
  font-size: 16px;
  color: #111528;
  border-left: 4px solid #4688f7;
}
// /deep/ .el-descriptions__body {
//   padding-left: 20px;
// }
/deep/ .avue-crud__pagination{
  display: none;
}
/deep/ .avue-crud__empty{
  padding: 0;
}
</style>
