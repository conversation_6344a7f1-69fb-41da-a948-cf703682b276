<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="handleDel"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <template slot="menu" slot-scope="scope" class="menuSlot">
          <el-button
            type="text"
            v-if="
              permissions['chain:companywaybillex:handle'] &&
              scope.row.status == 1
            "
            icon="el-icon-document"
            size="small"
            plain
            @click="handle(scope.row, scope.index)"
          >
            处理</el-button
          >
        </template>
        <template slot="pictureUrl" slot-scope="{ row }">
          <el-image
            v-if="row.pictureUrl"
            style="width: 50px; height: 50px"
            :src="filterImg(row.pictureUrl)"
            :preview-src-list="filterImgs(row.pictureUrl)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
      </avue-crud>
    </basic-container>
    <!-- 处理弹窗 -->
    <el-dialog
      width="600px"
      title="处理"
      :visible="visible"
      :before-close="closeVisible"
      :close-on-click-modal="false"
    >
      <div style="padding-left:20px">
        <el-descriptions title="运单变更信息" :column="2" v-if="ruleForm.garbageNamesAf||ruleForm.tpModeAf||ruleForm.soilTypeAf||ruleForm.goRemarkAf||ruleForm.goDriverUserNameAf">
            <el-descriptions-item v-if="ruleForm.soilTypeAf" label="土质变更前">{{ruleForm.soilTypeBf}}</el-descriptions-item>
            <el-descriptions-item v-if="ruleForm.soilTypeAf" label="土质变更后">{{ruleForm.soilTypeAf}}</el-descriptions-item>
            <el-descriptions-item v-if="ruleForm.tpModeAf" label="运输类型变更前">{{filterTpMode(ruleForm.tpModeBf)}}</el-descriptions-item>
            <el-descriptions-item v-if="ruleForm.tpModeAf" label="运输类型变更后">{{filterTpMode(ruleForm.tpModeAf)}}</el-descriptions-item>
            <el-descriptions-item v-if="ruleForm.garbageNamesAf" label="泥尾变更前">{{ruleForm.garbageNamesBf}}</el-descriptions-item>
            <el-descriptions-item v-if="ruleForm.garbageNamesAf" label="泥尾变更后">{{ruleForm.garbageNamesAf}}</el-descriptions-item>
            <el-descriptions-item v-if="ruleForm.goRemarkAf" label="出场备注变更后">{{ruleForm.goRemarkAf}}</el-descriptions-item>
            <el-descriptions-item v-if="ruleForm.goDriverUserNameAf" label="出场司机变更后">{{ruleForm.goDriverUserNameAf}}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="挖机签单变更信息" :column="1" v-if="ruleForm.inStaffNameAf">
            <el-descriptions-item v-if="ruleForm.inStaffNameAf" label="挖机员变更后">{{ruleForm.inStaffNameAf}}</el-descriptions-item>
        </el-descriptions>

      </div>
       <el-divider v-if="ruleForm.garbageNamesAf||ruleForm.tpModeAf||ruleForm.soilTypeAf||ruleForm.goRemarkAf||ruleForm.inStaffNameAf||ruleForm.goDriverUserNameAf"></el-divider>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="处理方式" prop="mode">
              <el-select
                size="small"
                v-model="ruleForm.mode"
                placeholder="请选择处理方式"
              >
                <el-option label="同意修改" value="3"></el-option>
                <el-option label="取消运单" value="1"></el-option>
                <el-option label="运单调价" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="ruleForm.mode == 2">
          <el-col :span="12">
            <el-form-item label="调价类型" prop="adjustType">
              <el-select
                size="small"
                v-model="ruleForm.adjustType"
                placeholder="请选择处理方式"
              >
                <el-option label="加价" value="1"></el-option>
                <el-option label="减价" value="2"></el-option>
                <el-option label="不调价" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :span="11"
            style="margin-left: 10px"
            v-if="ruleForm.adjustType == 1 || ruleForm.adjustType == 2"
          >
            <el-form-item prop="adjustPrice" label-width="0">
              <el-input
                size="small"
                v-model="ruleForm.adjustPrice"
                oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
                @blur="handleInput"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="处理备注" prop="remark">
          <el-input
            type="textarea"
            :autosize="{ minRows: 6, maxRows: 10 }"
            v-model="ruleForm.remark"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')"
            >提交</el-button
          >
          <el-button @click="resetForm('ruleForm')">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>

<script>
import {
  getPage,
  getObj,
  addObj,
  putObj,
  delObj,
  updateInStaffChangedWaybillEx,
  updateGoStaffChangedWaybillEx,
} from "@/api/chain/companywaybillex";
import { tableOption } from "@/const/crud/chain/companywaybillex2";
import { mapGetters } from "vuex";
import { clearNoNum } from "@/util/util.js";
export default {
  name: "companywaybillex",
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: 'update_datetime', //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      visible: false,
      ruleForm: {
        mode: "",
        adjustType: "",
        adjustPrice: 0,
        remark: "",
      },
      rules: {
        mode: [
          { required: true, message: "请选择处理方式", trigger: "change" },
        ],
        adjustType: [
          { required: true, message: "请选择调价类型", trigger: "change" },
        ],
        adjustPrice: [
          { required: true, message: "请输入调整价格", trigger: "change" },
        ],
        remark: [{ required: true, message: "请输入备注", trigger: "blur" }],
      },
      tableOption: tableOption,

    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:companywaybillex:add"] ? true : false,
        delBtn: this.permissions["chain:companywaybillex:del"] ? true : false,
        editBtn: this.permissions["chain:companywaybillex:edit"] ? true : false,
        viewBtn: this.permissions["chain:companywaybillex:get"] ? true : false,
      };
    },
  },
  methods: {
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    closeVisible() {
      this.visible = false;
    },
    handle(row) {
      this.ruleForm = {
        companyWaybillId: row.companyWaybillId,
        id: row.id,
        status: 2,
        // causeType:row.causeType,
        // type:row.type,
        mode: "",
        adjustType: "",
        adjustPrice: 0,
        remark: "",
        soilTypeAf:row.soilTypeAf,
        soilTypeBf:row.soilTypeBf,
        tpModeBf:row.tpModeBf,
        tpModeAf:row.tpModeAf,
        garbageNamesBf:row.garbageNamesBf,
        garbageNamesAf:row.garbageNamesAf,
        inStaffNameAf:row.inStaffNameAf,
        goRemarkAf:row.goRemarkAf,
        goDriverUserNameAf:row.goDriverUserNameAf,
        type:row.type
      }
        this.visible = true
    },
    handleInput() {
      this.ruleForm.adjustPrice = clearNoNum(this.ruleForm.adjustPrice + "");
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log(this.ruleForm);
          if (this.ruleForm.mode == 1) {
            this.ruleForm.adjustType = "";
            this.ruleForm.adjustPrice = 0;
          }
          if (this.ruleForm.adjustType == 3) {
            this.ruleForm.adjustPrice = 0;
          }
          if (this.ruleForm.adjustType == 2 && this.ruleForm.adjustPrice > 0) {
            this.ruleForm.adjustPrice = "-" + this.ruleForm.adjustPrice;
          }
          if(this.ruleForm.type==3){
            updateInStaffChangedWaybillEx(this.ruleForm).then(res=>{
              this.visible = false;
              this.getPage(this.page);
            })
          }else if(this.ruleForm.type==4){
            updateGoStaffChangedWaybillEx(this.ruleForm).then(res=>{
              this.visible = false;
              this.getPage(this.page);
            })
          }else{
            putObj(this.ruleForm).then((res) => {
              console.log(res);
              this.visible = false;
              this.getPage(this.page);
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.visible = false;
    },
    filterImg(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? null : url[0];
    },
    filterImgs(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? [] : url;
    },
  },
};
</script>

<style lang="scss" scoped>

</style>
