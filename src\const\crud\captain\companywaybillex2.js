export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: false, // 显示搜索字段
  // excelBtn: true,
  // printBtn: true,
  labelWidth: 150,
  searchLabelWidth: 100,
  addBtn: false,
  editBtn: false,
  defaultSort: {
    prop: "updateDatetime",
    order: "descending",
  },
  viewBtn: true,
  delBtn: false,
  menuWidth:100,
  searchMenuSpan: 6,
  refreshBtn:false,
  column: [
    // {
    //   label: "ID",
    //   prop: "id",
    //   // search: true,
    //   sortable: true,
    //   disabled: true, //弹窗表单字段不允许输入
    //   display: false, //弹窗表单字段隐藏
    // },
    // {
    //   label: "企业名称",
    //   prop: "companyName",
    //   sortable: true,
    //   search: true,
    //   searchOrder:1,
    // },
    {
      label: "运单ID",
      prop: "companyWaybillId",
      sortable: true,
      rules: [
        {
          required: true,
          message: "请输入企业运单ID",
          trigger: "blur",
        },
        {
          max: 36,
          message: "长度在不能超过36个字符",
        },
      ],
      overHidden:true,
    },
    {
      label: "申请原因",
      prop: "cause",
      sortable: true,
      hide: true, //列表页字段隐藏
      rules: [
        {
          required: true,
          message: "请输入申请原因",
          trigger: "blur",
        },
        {
          max: 1000,
          message: "长度在不能超过1000个字符",
        },
      ],
      overHidden:true,
    },
    {
      label: "申请人",
      prop: "applyName",
      sortable: true,
      overHidden:true,
    },
    {
      label: "图片",
      prop: "pictureUrl",
      sortable: true,
      hide: true,   //列表页字段隐藏

      //多图片上传和显示
      type: "upload", //上传一张头像图
      listType: "picture-img",
      action: "/upms/file/upload?fileType=image&dir=",
      propsHttp: {
        url: "link",
      },
      width:200,
      loadText: "图上上传中，请稍等",
      tip: "只能上传jpg/png文件，且不超过1000kb",
      // span: 24,
    },
    {
      label: "出场图片变更后",
      prop: "goPictureAf",
      sortable: true,
      hide: true,   //列表页字段隐藏

      //多图片上传和显示
      type: "upload", //上传一张头像图
      listType: "picture-img",
      action: "/upms/file/upload?fileType=image&dir=",
      propsHttp: {
        url: "link",
      },
      width:200,
      loadText: "图上上传中，请稍等",
      tip: "只能上传jpg/png文件，且不超过1000kb",
      // span: 24,
    },

    {
      label: "异常类型",
      prop: "type",
      sortable: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "司机端提交异常",
          value: "1",
        },
        {
          label: "施工端提交异常",
          value: "2",
        },
        {
          label: "挖机变更提交异常",
          value: "3",
        },
        {
          label: "无出口签单异常",
          value: "4",
        },
      ],
      overHidden:true,
    },
    {
      label: "调整运单价格",
      prop: "adjustPrice",
      sortable: true,
      rules: [],
      overHidden:true,
    },
    {
      label: "处理人",
      prop: "dealName",
      overHidden:true,
    },
    {
      label: "处理状态",
      prop: "status",
      sortable: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "申请",
          value: "1",
        },
        {
          label: "同意",
          value: "2",
        },
        {
          label: "驳回",
          value: "3",
        },
      ],
      overHidden:true,
    },
    {
      label: "处理方式",
      prop: "mode",
      sortable: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "取消运单",
          value: "1",
        },
        {
          label: "运单调价",
          value: "2",
        },
        {
          label: "同意修改",
          value: "3",
        },
      ],
      overHidden:true,
    },
    {
      label: "调价类型",
      prop: "adjustType",
      sortable: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "加价",
          value: "1",
        },
        {
          label: "减价",
          value: "2",
        },
        {
          label: "不调价",
          value: "3",
        },
      ],
      overHidden:true,
    },
    {
      label: "异常备注",
      prop: "remark",
      sortable: true,
      hide: true, //列表页字段隐藏
      overHidden:true,
    },
    {
      label: "创建ID",
      prop: "createId",
      sortable: true,
      hide: true, //列表页字段隐藏
      disabled: true, //弹窗表单字段不允许输入
      display: false, //弹窗表单字段隐藏
      overHidden:true,
    },
    {
      label: "土质变更前",
      prop: "soilTypeBf",
      hide: true, //列表页字段隐藏
      overHidden:true,
    },
    {
      label: "土质变更后",
      prop: "soilTypeAf",
      hide: true, //列表页字段隐藏
      overHidden:true,
    },
    {
      label: "运输方式变更前",
      prop: "tpModeBf",
      hide: true, //列表页字段隐藏
      overHidden:true,
    },
    {
      label: "运输方式变更后",
      prop: "tpModeAf",
      hide: true, //列表页字段隐藏
      overHidden:true,
    },
    {
      label: "泥尾变更前",
      prop: "garbageIdBf",
      hide: true, //列表页字段隐藏
    },
    {
      label: "泥尾变更后",
      prop: "garbageIdAf",
      hide: true, //列表页字段隐藏
    },
    {
      label: "挖机员变更后",
      prop: "inStaffNameAf",
      hide: true, //列表页字段隐藏
    },
    {
      label: "出场备注变更后",
      prop: "goRemarkAf",
      hide: true, //列表页字段隐藏
    },
    {
      label: "创建时间",
      prop: "createDatetime",
      sortable: true,
      hide: true, //列表页字段隐藏
      disabled: true, //弹窗表单字段不允许输入
      display: false, //弹窗表单字段隐藏
      rules: [
        {
          required: true,
          message: "请输入创建日期时间",
          trigger: "blur",
        },
      ],
    },
    {
      label: "修改ID",
      prop: "updateId",
      sortable: true,
      hide: true, //列表页字段隐藏
      disabled: true, //弹窗表单字段不允许输入
      display: false, //弹窗表单字段隐藏
      rules: [
        {
          required: true,
          message: "请输入修改ID",
          trigger: "blur",
        },
        {
          max: 36,
          message: "长度在不能超过36个字符",
        },
      ],
    },
    {
      label: "修改日期时间",
      prop: "updateDatetime",
      sortable: true,
      disabled: true, //弹窗表单字段不允许输入
      display: false, //弹窗表单字段隐藏
      rules: [
        {
          required: true,
          message: "请输入修改日期时间",
          trigger: "blur",
        },
      ],
    },
  ],
};
