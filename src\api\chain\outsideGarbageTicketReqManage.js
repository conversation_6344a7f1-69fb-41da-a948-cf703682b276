import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companyticketprojectapply/applyPage',
        method: 'get',
        params: query
    })
}
export function auditCompanyTicketProjectApply(query) {
    return request({
        url: '/chain/companyticketprojectapply/auditCompanyTicketProjectApply',
        method: 'post',
        data: query
    })
}
export function queryProjectInventoryByGarbageId(query) {
    return request({
        url: '/chain/companyticketprojectinventory/queryProjectInventoryByGarbageId',
        method: 'get',
        params: query
    })
}

export function queryInventoryByGarbageId(query) {
  return request({
      url: '/chain/companyticketinventory/queryInventoryByGarbageId',
      method: 'get',
      params: query
  })
}

export function addObj(obj) {
    return request({
        url: '/chain/companyticketprojectapply',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companyticketprojectapply/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companyticketprojectapply/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companyticketprojectapply',
        method: 'put',
        data: obj
    })
}
