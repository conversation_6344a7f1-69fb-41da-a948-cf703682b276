import {isMobileNumber} from '@/util/validate'

export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  defaultSort:{
    prop:'createDatetime',
    order:'descending'
  },
  viewBtn: false,
  delBtn: false,
  searchMenuSpan: 6,
  menu:false,
  menuWidth:110,
  routerName:"captainManage",
  column: [
    {
      label: "车队长",
      prop: "name",
      sortable: true,
      search:true,
      overHidden:true,
      minWidth:90,
    },
    {
      label: "手机号",
      prop: "mobile",
      sortable: true,
      search:true,
      overHidden:true,
      minWidth:120,
    },
    {
      label: "是否实名",
      prop: "realNameAuthentication",
      sortable: true,
      type:'select',
      dicData: [
        {
          label: '否',
          value: '0'
        },
        {
          label: '是',
          value: '1'
        },
      ],
      overHidden:true,
      minWidth:100,
    },
    {
      label: "身份证号",
      prop: "idCard",
      sortable: true,
      overHidden:true,
      minWidth:160,
    },
    {
      label: "银行卡号",
      prop: "bindingBankNo",
      sortable: true,
      overHidden:true,
      minWidth:160,
    },
    {
      label: "创建时间",
      prop: "createDatetime",
      sortable: true,
      overHidden:true,
      minWidth:170,
    },
  ],
};
