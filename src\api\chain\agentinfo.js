import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/agentinfo/page',
        method: 'get',
        params: query
    })
}


export function getLists() {
  return request({
      url: '/chain/agentinfo/list2',
      method: 'get',

  })
}
export function getLists3() {
  return request({
      url: '/chain/agentinfo/list3',
      method: 'get',

  })
}


export function addObj(obj) {
    return request({
        url: '/chain/agentinfo',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/agentinfo/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/agentinfo/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/agentinfo',
        method: 'put',
        data: obj
    })
}


export function workOffByProjectInfoId(query) {
    return request({
        url: '/chain/projectattendance/workOffByProjectInfoId',
        method: 'get',
        params: query
    })
}
