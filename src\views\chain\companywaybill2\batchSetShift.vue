<template>
  <div class="driverTaskDetail">
    <el-dialog
      width="400px"
      title="批量设置班次"
      center
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="cancelUpload"
    >
      <avue-form ref="form" v-model="form" :option="option" @submit="submit"> </avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelUpload">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="$refs.form.submit()"
          >提交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { batchSetShiftTime } from "@/api/chain/companywaybill";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    selectList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      form: {
        shiftTime: null,
        shiftTypeName: "",
      },
      btnLoading: false,
      option: {
        labelWidth:70,
        submitBtn: false,
        position: "left",
        emptyBtn: false,
        cancelBtn: false,
        column: [
          {
            label: "日期",
            prop: "shiftTime",
            type: "date",
            sortable: "custom",
            searchRange: true,
            search: true,
            valueFormat: "yyyy-MM-dd",
            span:24,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "班次",
            prop: "shiftTypeName",
            type: "select", // 下拉选择
            dicUrl: "/chain/projectshift/getList/"+this.selectList[0].projectInfoId,
            span:24,
            props: {
              label: "shiftName",
              value: "shiftName",
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
        ],
      },
    };
  },
  created() {},
  mounted: function () {},
  computed: {},
  methods: {
    cancelUpload() {
      this.form = {
        shiftTime: null,
        shiftTypeName: "",
      },
      this.$emit("update:visible", false);
    },
    submit(form,done) {
      let waybillIds = this.selectList.map((item) => item.id);
      let param = Object.assign({},form)
      param.waybillIds = waybillIds
      console.log(param);
      this.btnLoading = true;
      done()
      batchSetShiftTime(param)
        .then((res) => {
          this.btnLoading = false;
          this.$message.success("操作成功");
          this.cancelUpload();
          this.$emit("refreshChange");
        })
        .catch((err) => {
          this.btnLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px;

}
</style>
