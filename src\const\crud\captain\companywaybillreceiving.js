// import {isMobileNumber} from '@/util/validate'
export const tableOption = {
    dialogDrag: true,
    dialogWidth:500,
    border: true,
    indexLabel: "序号",
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true, // 显示搜索字段
    excelBtn: false,
    // printBtn: true,
    addBtn: false,
    editBtn: false,
    // defaultSort: {
    //   prop: "sort",
    //   order: "ascending",
    // },
    // calcHeight: 135,
    // height: "auto",
    searchLabelWidth:100,
    viewBtn: false,
    delBtn: false,
    searchMenuSpan: 6,
    column: [
      {
        label: "收单号",
        prop: "acceptNo",
        search: true,
        display: false, //弹窗表单字段隐藏
        minWidth:160,
        overHidden:true,
      },
      {
        label: "企业",
        prop: "companyAuthName",
        search: true,
        display: false, //弹窗表单字段隐藏
        minWidth:160,
        overHidden:true,
      },
      {
        label: "项目",
        prop: "projectName",
        search: true,
        display: false, //弹窗表单字段隐藏
        minWidth:160,
        overHidden:true,
      },
      {
        label: "运单总数",
        prop: "waybillCount",
        display: false, //弹窗表单字段隐藏
        minWidth:80,
        overHidden:true,
      },
      {
        label: "交单人",
        prop: "handName",
        display: false, //弹窗表单字段隐藏
        search: true,
        minWidth:80,
        overHidden:true,
      },
      {
        label: "交单手机号码",
        prop: "handMobile",
        display: false, //弹窗表单字段隐藏
        search: true,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "交单日期",
        prop: "createDateTime",
        display: false, //弹窗表单字段隐藏
        minWidth:140,
        overHidden:true,
        sortable: "custom",
      },
      {
        label: "收单人",
        prop: "acceptName",
        display: false, //弹窗表单字段隐藏
        search: true,
        labelWidth:110,
        span:24,
        // showColumn:false,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:80,
        overHidden:true,
      },
      {
        label: "收单手机号码",
        prop: "acceptMobile",
        display: false, //弹窗表单字段隐藏
        search: true,
        labelWidth:110,
        maxlength:11,
        span:24,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "收单时间",
        prop: "payDateTime",
        display: false, //弹窗表单字段隐藏
        minWidth:140,
        overHidden:true,
        sortable: "custom",
      },
      {   //搜索栏状态
        label: "状态",
        prop: "status",
        search: true,
        type: "select", // 下拉选择
        display: false, //弹窗表单字段隐藏
        hide:true,
        showColumn:false,
        dicData: [{
            label: '未付款',
            value: '1'
        }, {
            label: '已付款',
            value: '2'
        }],
        minWidth:80,
        overHidden:true,
      },
      {   //table状态
        label: "状态",
        prop: "deliveryStatus",
        type: "select", // 下拉选择
        display: false, //弹窗表单字段隐藏
        dicData: [{
            label: '未付款',
            value: '1'
        }, {
            label: '已付款',
            value: '2'
        }],
        minWidth:80,
        overHidden:true,
      },
      {
        label: "交单日期",
        prop: "searchDate",
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange:true,
        display: false, //弹窗表单字段隐藏
        hide:true,
        showColumn:false,
        search: true,
      },
      {
        label: "收单日期",
        prop: "searchDate2",
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange:true,
        display: false, //弹窗表单字段隐藏
        hide:true,
        showColumn:false,
        search: true,
      },
    ],
}

