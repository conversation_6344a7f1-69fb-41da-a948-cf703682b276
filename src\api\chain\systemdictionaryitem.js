import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/systemdictionaryitem/pageCompany',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/systemdictionaryitem',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/systemdictionaryitem/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/systemdictionaryitem/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/systemdictionaryitem',
        method: 'put',
        data: obj
    })
}
