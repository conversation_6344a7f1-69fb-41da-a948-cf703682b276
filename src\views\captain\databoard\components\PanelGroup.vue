<template>
  <el-row :gutter="10" class="panel-group">
    <el-col :xs="12" :sm="8" :lg="6"  class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('newVisitis')">
        <div class="card-panel-icon-wrapper icon-green">
          <svg-icon icon-class="project" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <count-to :start-val="0" :end-val="info.totalProjectCount" :duration="2600" class="card-panel-num" />
          <div class="card-panel-text">
            运单项目数
          </div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="8" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('messages')">
        <div class="card-panel-icon-wrapper icon-blue">
          <svg-icon icon-class="task" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <count-to :start-val="0" :end-val="info.totalTaskCount" :duration="3000" class="card-panel-num" />
          <div class="card-panel-text">
            运单任务数
          </div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="8" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('shoppings')">
        <div class="card-panel-icon-wrapper icon-yellow">
          <svg-icon icon-class="waybill" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <count-to :start-val="0" :end-val="info.totalWaybillCount" :duration="3600" class="card-panel-num" />
          <div class="card-panel-text">
            运单数
          </div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="8" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('shoppings')">
        <div class="card-panel-icon-wrapper icon-purple">
          <svg-icon icon-class="vehicle" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <count-to :start-val="0" :end-val="info.totalTruckCount" :duration="3600" class="card-panel-num" />
          <div class="card-panel-text">
            车辆数
          </div>
        </div>
      </div>
    </el-col>
       <el-col :xs="12" :sm="8" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('purchases')">
        <div class="card-panel-icon-wrapper icon-red">
          <svg-icon icon-class="garbage" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <count-to :start-val="0" :end-val="info.totalGarbageCount" :duration="3200" class="card-panel-num" />
          <div class="card-panel-text">
            泥尾数
          </div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="8" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('shoppings')">
        <div class="card-panel-icon-wrapper icon-green">
          <svg-icon icon-class="soil" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <count-to :start-val="0" :end-val="info.totalSoilCount" :duration="3600" class="card-panel-num" />
          <div class="card-panel-text">
            土类型
          </div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="8" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('shoppings')">
        <div class="card-panel-icon-wrapper icon-blue">
          <svg-icon icon-class="accountPaid" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <count-to :start-val="0" :end-val="info.totalAcceptCount" :duration="3600" class="card-panel-num" />
          <div class="card-panel-text">
            收单付款运单数
          </div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="8" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('shoppings')">
        <div class="card-panel-icon-wrapper icon-blue">
          <svg-icon icon-class="accountPaid" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <count-to :start-val="0" :end-val="info.totalHandCount" :duration="3600" class="card-panel-num" />
          <div class="card-panel-text">
            交单收款运单数
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CountTo from 'vue-count-to'

export default {
  components: {
    CountTo
  },
  props:{
    info:{}
  },
  methods: {
    handleSetLineChartData(type) {
      // this.$emit('handleSetLineChartData', type)
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
  margin: 0px 30px;
  // background-color: #f2f2f2;
  .card-panel-col {
    margin-bottom: 10px;
  }

  .card-panel {
    // height: 108px;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 12px;
    padding: 20px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-green {
        background: #34bfa3;
      }

      .icon-blue {
        background: #36a3f7;
      }

      .icon-red {
        background: #f4516c;
      }

      .icon-yellow {
        background: #fe9a00
      }
      .icon-purple {
        background: #a700fe
      }
    }

    .icon-green {
      color: #34bfa3;
    }

    .icon-blue {
      color: #36a3f7;
    }

    .icon-red {
      color: #f4516c;
    }

    .icon-yellow {
      color: #fe9a00;
    }
    .icon-purple {
      color: #a700fe;
    }

    .card-panel-icon-wrapper {
      float: left;
      padding: 10px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
      margin-right: 20px;
    }

    .card-panel-icon {
      float: left;
      font-size: 30px;
      width: 30px;
      height: 30px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-top: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
