export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true, // 显示搜索字段
  // excelBtn: true,
  // printBtn: true,
  labelWidth: 150,
  addBtn: false,
  editBtn: false,
  viewBtn: false,
  defaultSort: {
    prop: "updateDatetime",
    order: "descending",
  },
  // viewBtn: true,
  searchMenuSpan: 6,
  searchLabelWidth: 100,
  menuWidth: 140,
  column: [
    {
      label: "所属项目",
      prop: "projectName",
      minWidth:150,
      search:true,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "结算单号",
      prop: "settleNo",
      search: true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "结算申请时间",
      prop: "applyDatetime",
      type: "datetime",
      searchRange: true,
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:160,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "审批发起时间",
      prop: "flowDatetime",
      type: "datetime",
      searchRange: true,
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:160,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "结算申请人",
      prop: "agentName",
      minWidth:106,
      search: true,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "核算人",
      prop: "settleName",
      search: true,
      minWidth:86,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "承运人",
      prop: "payeeName",
      search: true,
      minWidth:86,
      overHidden:true,
      sortable:"custom",
    },
    // {
    //   label: "结算数量",
    //   prop: "settleNum",
    // },
    {
      label: "核算数量",
      prop: "checkNum",
      minWidth:100,
      search: true,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "核算金额",
      prop: "checkAmount",
      minWidth:100,
      search: true,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "增减值",
      prop: "adjustAmt",
      minWidth:86,
      search: true,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "拆单备注",
      prop: "staffRemark",
      minWidth:86,
      search: true,
      overHidden:true,
    },
    {
      label: "结算申请时间",
      prop: "searchDate",
      type: "date",
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd',
      hide:true,
      showColumn:false,
    },
    {
      label: "审批发起时间",
      prop: "searchDate2",
      type: "date",
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd',
      hide:true,
      showColumn:false,
    },
    // {
    //   label: "结算金额",
    //   prop: "settleAmount",
    // },
    // {
    //   label: "审批状态",
    //   prop: "flowStatusName",
    // },
  ],
};
