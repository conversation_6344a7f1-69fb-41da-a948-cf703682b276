<template>
  <div class="driverTaskDetail">

      <basic-container>
        <avue-crud
          ref="crud"
          :data="tableData"
          :table-loading="tableLoading"
          :option="tableOption"
          v-model="form"
          :search.sync="search"
          @on-load="getPage"
          @search-change="getPage"
        >
           <!-- 本期用票 -->
           <template slot-scope="{ row,index }" slot="useTickets">
            <span style="color:#409eff;cursor:pointer;" @click="toDetail(row)" v-if="row.isTotalRow==0&&row.isProjectTotalRow==0">{{row.useTickets}}</span>
            <span v-else>{{row.useTickets}}</span>
          </template>
        </avue-crud>
      </basic-container>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getUseTickets } from "@/api/chain/outsideGarbageTicketCount";

export default {
  name:"useTicketsMonth",
  data() {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      tableData: [],
      tableLoading: false,
      search:{
        projectInfoIds:'',
        garbageIds:'',
        soilTypes:'',
        searchDate:[],
      },
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        // searchShow: false,
        index: true,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 8,
        excelBtn: true,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: true,
        delBtn: false,
        menu: false,
        searchMenuSpan: 6,
        index: false,
        refreshBtn: false,
        defaultSort: {
          prop: "operateDate",
          order: "ascending",
        },
        column: [
          {
            label: "月份",
            prop: "monthDetailDate",
          },
          {
            label: "项目",
            prop: "projectInfoIds",
            search: true,
            type: "select",
            hide: true,
            showColumn: false,
            dataType: "string",
            searchMultiple: true,
            props: {
              label: "projectName",
              value: "id",
            },
            dicUrl: "/chain/projectinfo/listTicket",
          },
          {
            label: "项目",
            prop: "projectName",
          },
          {
            label: "泥尾",
            prop: "garbageIds",
            search: true,
            hide: true,
            type: "select",
            dataType: "string",
            searchMultiple: true,
            showColumn: false,
            props: {
              label: "names",
              value: "id",
            },
            dicUrl: "/chain/garbage/listByCompanyAuth",
          },
          {
            label: "泥尾",
            prop: "garbageName",
          },
          {
            label: "土质",
            prop: "soilTypes",
            type: "select",
            search: true,
            dataType: "string",
            searchMultiple: true,
            showColumn: false,
            hide: true,
            props: {
              label: "soilType",
              value: "soilType",
            },
            dicUrl:"/chain/companyticketpurchase/getCompanyTicketPurchaseSoilType",
          },
          {
            label: "土质",
            prop: "soilType",
          },
          {
            label: "前期余票",
            prop: "earlyRemainingTickets",
          },
          {
            label: "本期领票",
            prop: "collectTickets",
          },
          {
            label: "本期用票",
            prop: "useTickets",
          },
          {
            label: "本期退票",
            prop: "refundTickets",
          },
          {
            label: "本期余票",
            prop: "inRemainingTickets",
          },
          {
            label: "申请时间",
            prop: "searchDate",
            search:true,
            type:'date',
            valueFormat: 'yyyy-MM-dd',
            searchRange:true,
            hide:true,
            showColumn:false,
            pickerOptions: {
              // disabledDate(time) {
              //   return time.getTime() < Date.now();
              // },
              shortcuts: [{
                text: '最近一周',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '最近一个月',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '最近三个月',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '最近六个月',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
                  picker.$emit('pick', [start, end]);
                }
              },]
            }
          },
        ],
      },
      obj:{},
      isOne:true, //第一次调用getpage
      once:true,//更改搜索参数前
    };
  },
  created() {},
  mounted() {
    console.log(this.$route.query.info);
    // if (this.$route.query.info) {
    //   console.log(12);
    //   let info = JSON.parse(this.$route.query.info);
    //   this.search.projectInfoIds = info.projectInfoIds
    //   this.search.garbageIds = info.garbageIds
    //   this.search.soilTypes = info.soilTypes
    //   if(info.startDate&&info.endDate){
    //     this.search.searchDate = [info.startDate,info.endDate]
    //   }
    // }

  },
  computed: {
    ...mapGetters(["permissions","tagList"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    getPage(obj,done) {

      this.tableLoading = true;
      //第一次调用
      if(this.isOne&&this.$route.query.info){
        console.log(12);
        let info = JSON.parse(this.$route.query.info);
        this.search.projectInfoIds = info.projectInfoIds
        this.search.garbageIds = info.garbageIds
        this.search.soilTypes = info.soilTypes
        if(info.startDate&&info.endDate){
          this.search.searchDate = [info.startDate,info.endDate]
        }
      }
      let params = Object.assign({},this.search)
      if (params.hasOwnProperty("searchDate")) {
        console.log(params);
        params.startDate = params.searchDate[0];
        params.endDate = params.searchDate[1];
        this.search.startDate = params.searchDate[0];
        this.search.endDate = params.searchDate[1];
        delete params.searchDate;
      }
      //如果搜索条件一致需要增加传值过来的garbageType
      if(this.$route.query.info){
        let info = JSON.parse(this.$route.query.info);
        if(this.once&&params.startDate==info.startDate&&params.endDate==info.endDate
        &&params.projectInfoIds==info.projectInfoIds&&params.garbageIds==info.garbageIds&&params.soilTypes==info.soilTypes){
          params.garbageType = info.garbageType
        }else{
          this.once = false
        }
      }
      getUseTickets(params)
        .then((response) => {
          this.isOne = false
          this.tableData = response.data.data
          this.tableLoading = false;
          done?done():''
        })
        .catch(() => {
          this.isOne = false
          this.tableLoading = false;
          done?done():''
        });
    },
    toDetail(row,type){
      let arr = row.monthDetailDate.split('-')
      console.log(arr);
      let start = row.monthDetailDate+'-01'
      let end = row.monthDetailDate+'-'+this.getMonthLength(arr[0],arr[1])
      this.obj = {
        startDate:start,
        endDate:end,
        projectInfoIds:row.projectInfoId,
        garbageIds:row.garbageId,
        soilTypes:row.soilType,
        garbageType:row.garbageType,
      }
      console.log(this.obj);
      console.log(this.search);
      let startDate = this.search.searchDate&&this.search.searchDate.length>1?this.$moment(this.search.searchDate[0]).format('YYYY-MM'):''
      let endDate = this.search.searchDate&&this.search.searchDate.length>1?this.$moment(this.search.searchDate[1]).format('YYYY-MM'):''
      console.log(startDate);
      console.log(endDate);
      //点击的月份是搜索时间的开始月份
      if(startDate==row.monthDetailDate){
        this.obj.startDate = this.search.searchDate[0]
      }
      //点击的月份是搜索时间的结束月份
      if(endDate==row.monthDetailDate){
        this.obj.endDate = this.search.searchDate[1]
      }
      console.log(this.obj);
      // Object.assign(this.obj,this.search)
      let tag = this.findTag('本期用票(按日)').tag;
      if(tag){
        this.$store.commit("DEL_TAG", tag);
        this.$router.push({path:'/useTicketsDay/index',query:{info:JSON.stringify(this.obj)}})
      }else{
        this.$router.push({path:'/useTicketsDay/index',query:{info:JSON.stringify(this.obj)}})

      }
    },
    //获取一个月有多少天
    getMonthLength(year,month){
      return new Date(year,month,0).getDate()
    },
    findTag(label) {
      let tag, key;
      this.tagList.map((item, index) => {
        if (item.label === label) {
          tag = item;
          key = index;
        }
      });
      return { tag: tag, key: key };
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .drawerHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  li {
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-weight: 700;
    margin-right: 30px;
  }
}

.demo-block-control {
  border-top: 1px solid #eaeefb;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;
  background-color: #fff;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  text-align: center;
  margin-top: -1px;
  color: #d3dce6;
  cursor: pointer;
  position: relative;

  &:hover {
    color: #409eff;
    background-color: #f9fafc;
  }
}
</style>
