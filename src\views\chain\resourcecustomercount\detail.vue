<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%" title="明细" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <avue-crud ref="crud" :data="tableData" :page.sync="page" :table-loading="tableLoading" :option="tableOption" v-model="form"
          @on-load="getPage" >
          <template slot="menuLeft" slot-scope="{ row }">
            <el-button
                size="small"
                icon="el-icon-download"
                type="primary"
                @click="exOut"
              >
                导出
            </el-button>
          </template>
          <template slot-scope="{ row,index }" slot="menu">
            <el-button
                v-if="row.type==3"
                type="text"
                icon="el-icon-view"
                size="small"
                plain
                @click="toDetail(row)">签单详情</el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
    <!-- 签单详情 -->
    <el-dialog
      title="签单详情"
      width="60%"
      center
      :visible.sync="detailVisible"
      :close-on-click-modal="false"
    >
    <avue-crud ref="crud2" v-if="detailVisible" :data="tableData2" :page.sync="page2" :table-loading="tableLoading2" :option="tableOption2"
          @on-load="getPage2" >
          <template slot="menuLeft" slot-scope="{ row }">
            <el-button
                size="small"
                icon="el-icon-download"
                type="primary"
                @click="exOut2"
              >
                导出
            </el-button>
          </template>
        </avue-crud>
    <span slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="detailVisible = false">确 定</el-button>
      </span>
  </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {getPage2,signDetail} from '@/api/chain/resourcecustomercount'
import { expotOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {},
  },
  data() {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      page2: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      tableData: [],
      tableData2: [],
      paramsSearch: {

      },
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 12,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchMenuSpan: 6,
        index: false,
        refreshBtn: false,
        column: [
          {
            label: "类型",
            prop: "type",
            sortable: true,
            type:'select',
            dicData: [
              {
                label: "售票",
                value: "1",
              },
              {
                label: "退票",
                value: "2",
              },
              {
                label: "核销",
                value: "3",
              },
            ],
          },
          {
            label: "名称",
            prop: "name",
            sortable: true,
          },
          {
            label: "票数",
            prop: "qty",
            sortable: true,
          },
          {
            label: "日期",
            prop: "operateDate",
            sortable: true,
          },
          {
            label: "操作人",
            prop: "operator",
            sortable: true,
          },
        ],
      },
      detailVisible:false,
      tableLoading2: false,
      tableOption2: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 12,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: true,
        delBtn: false,
        menu: false,
        searchMenuSpan: 6,
        index: false,
        refreshBtn: false,
        column: [
          {
            label: "车牌",
            prop: "goTruckCode",
          },
          {
            label: "资源类型",
            prop: "resourceType",
            // sortable: true,
          },
          {
            label: "拍照凭证",
            prop: "goPicture",
            type:'upload',
            // sortable: true,
          },
          {
            label: "票据凭证",
            prop: "imgUrl",
            type:'upload',
            // sortable: true,
          },
          {
            label: "备注",
            prop: "remark",
          },
          {
            label: "签单人",
            prop: "goStaffName",
          },
          {
            label: "签单时间",
            prop: "goDatetime",
          },
        ],
      },
      currentRow:{}
    }
  },
  created() {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    getPage(page, params = {}) {
      this.tableLoading = true
      getPage2(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        resourceCustomerId:this.info.resourceCustomerId,
        resourceType:this.info.resourceType
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    getPage2(page, params = {}) {
      this.tableLoading2 = true
      signDetail(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page2.descs,
        ascs: this.page2.ascs,
        operator:this.currentRow.operator,
        operateDate:this.currentRow.operateDate,
      }, params, this.paramsSearch)).then(response => {
        this.tableData2 = response.data.data.records
        this.page2.total = response.data.data.total
        this.page2.currentPage = page.currentPage
        this.page2.pageSize = page.pageSize
        this.tableLoading2 = false
      }).catch(() => {
        this.tableLoading2 = false
      })
    },
    exOut(){
      let params = Object.assign({},this.paramsSearch)
      let url = '/chain/resourcecustomersales/staticsDetailExportExcel'
      params.resourceCustomerId = this.info.resourceCustomerId
      params.resourceType = this.info.resourceType
      expotOut( params,url,'资源票统计明细');
    },
    exOut2(){
      let params = {
        operator:this.currentRow.operator,
        operateDate:this.currentRow.operateDate,
      }
      let url = '/chain/resourcecustomersales/signDetailExportExcel'
      expotOut( params,url,'签单详情明细');
    },
    toDetail(row){
      this.currentRow = row
      this.detailVisible = true
    },
  }
};
</script>

<style lang="scss" scoped>
  /deep/ .el-dialog__body{
    padding-top: 0;
  }
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .drawerHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  li {
    color: rgba(0, 0, 0, .85);
    font-size: 14px;
    font-weight: 700;
    margin-right: 30px;
  }
}

.demo-block-control {
  border-top: 1px solid #eaeefb;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;
  background-color: #fff;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  text-align: center;
  margin-top: -1px;
  color: #d3dce6;
  cursor: pointer;
  position: relative;

  &:hover {
    color: #409eff;
    background-color: #f9fafc;
  }
}

</style>
