<template>
  <div class="mains">
    <codePhoneDiv type="1" :phone="phone" @handleDone="handleBindPhone" />
    <selectCompany v-if="show" :roleType="roleType" :visible.sync="show" />
  </div>
</template>

<script>
import { randomLenNum } from "@/util/util";
import { mapGetters } from "vuex";
import codePhoneDiv from "@/components/code-phone-div/main.vue";
import website from '@/const/website'
import { getAuditPassCompanyAuthList } from "@/api/login.js";
import selectCompany from "./components/selectCompany.vue";
export default {
  name: "userlogin",
  components: {
    codePhoneDiv,
    selectCompany,
  },
  data() {
    return {
      thirdPartyForm: {
        code: "",
        state: "",
      },
      codeTxt: "发送验证码",
      codeFlag: true,
      phone: null,

      checked: false,
      code: {
        src: "/code",
        value: "",
        len: 4,
        type: "image",
      },
      passwordType: "password",
      show: false,
      roleType: "2",
    };
  },
  created() {
    // this.refreshCode();
  },
  mounted() { },
  computed: {
    ...mapGetters(["tagWel"]),
  },
  props: [],
  methods: {
    handleBindPhone(loginForm) {
      //增加先判断是不是多企业
      console.log(loginForm);
      this.$store.dispatch("LoginByPhone", loginForm).then(() => {
        console.log(loginForm);
        this.roleType = loginForm.roleType
        //不是企业用户及泥尾端用户 直接进入首页
        if (loginForm.roleType != 2 && loginForm.roleType != 7) {
          sessionStorage.removeItem("companyUser")
          this.$store.commit("SET_TOP_MENU_INDEX", 0);
          this.$store.dispatch("SetRoleType", loginForm.roleType)
          this.$router.push({ path: this.tagWel.value });
          return false
        }
        getAuditPassCompanyAuthList().then(res => {
          console.log(res);
          if (res.data.data && res.data.data.length > 1) {
            sessionStorage.setItem('companyUser', JSON.stringify(res.data.data))
            // this.$router.push({path:'/changeCompany'})
            this.show = true
          } else {
            sessionStorage.removeItem("companyUser")
            this.$store.commit("SET_TOP_MENU_INDEX", 0);
            this.$store.dispatch("SetRoleType", loginForm.roleType)
            this.$router.push({ path: this.tagWel.value });
          }
        })
      });

    },

    getCode() {
      if (!this.loginForm.phone) {
        this.$message({
          message: "请先填写手机号",
          type: "warning",
        });
        return;
      }
      if (this.codeFlag == false) {
        return;
      }
      this.codeFlag = false;
      var time = 60;
      this.codeTxt = time + "s" + "后获取验证码";
      this.timer = setInterval(() => {
        this.codeTxt = "获取验证码";
        if (time == 1) {
          this.codeFlag = true;
          clearInterval(this.timer);
        } else {
          time--;
          this.codeTxt = time + "s" + "后获取验证码";
        }
      }, 1000);
    },
    refreshCode() {
      this.loginForm.code = "";
      this.loginForm.randomStr = randomLenNum(this.code.len, true);
      this.code.type === "text"
        ? (this.code.value = randomLenNum(this.code.len))
        : (this.code.src = `${window.location.origin}/code?randomStr=${this.loginForm.randomStr}`);
    },
    showPassword() {
      this.passwordType == ""
        ? (this.passwordType = "password")
        : (this.passwordType = "");
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.$store
            .dispatch("LoginByUsername", this.loginForm)
            .then(() => {
              this.$store.commit("SET_TOP_MENU_INDEX", 0);
              this.$router.go(0);
              this.$router.push({ path: this.tagWel.value });
            })
            .catch(() => {
              this.refreshCode();
            });
        }
      });
    },
  },
};
</script>

<style scoped>
.mains {
  color: #ffffff;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.message {
  color: #169bd5;
}
</style>
