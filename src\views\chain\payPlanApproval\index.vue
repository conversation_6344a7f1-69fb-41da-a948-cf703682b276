<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 :defaults.sync="defaults"
                 :search.sync="search"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="menuLeft"
                  slot-scope="{ size }">
          <el-radio-group v-model="tabPosition"
                          @change="changeTab">
            <el-radio-button label="1">待审批</el-radio-button>
            <el-radio-button label="2">已审批</el-radio-button>
            <el-radio-button label="3">已驳回</el-radio-button>
          </el-radio-group>
        </template>
        <template slot="rejectPriceSearch"
                  slot-scope="scope">
          <div style="display: flex;">
            <el-input-number style="margin-right:2px;"
                             v-model="search.rejectPriceBegin"
                             placeholder="最小值"
                             :min="0"
                             :max='*********'
                             :precision="0"
                             :controls="false"
                             size="small"
                             :step="1"
                             step-strictly></el-input-number>
            至
            <el-input-number style="margin-left: 2px;"
                             v-model="search.rejectPriceEnd"
                             placeholder="最大值"
                             :min="0"
                             :max='*********'
                             :precision="0"
                             :controls="false"
                             size="small"
                             :step="1"
                             step-strictly></el-input-number>
          </div>
        </template>
        <template slot="adjustAmtSearch"
                  slot-scope="scope">
          <div style="display: flex;">
            <el-input-number style="margin-right:2px;"
                             v-model="search.adjustAmtBegin"
                             placeholder="最小值"
                             :min="0"
                             :max='*********.99'
                             :precision="2"
                             :controls="false"
                             size="small"
                             :step="0.01"
                             step-strictly></el-input-number>
            至
            <el-input-number style="margin-left: 2px;"
                             v-model="search.adjustAmtEnd"
                             placeholder="最大值"
                             :min="0"
                             :max='*********.99'
                             :precision="2"
                             :controls="false"
                             size="small"
                             :step="0.01"
                             step-strictly></el-input-number>
          </div>
        </template>
        <template slot="checkAmountSearch"
                  slot-scope="scope">
          <div style="display: flex;">
            <el-input-number style="margin-right:2px;"
                             v-model="search.checkAmountBegin"
                             placeholder="最小值"
                             :min="0"
                             :max='*********.99'
                             :precision="2"
                             :controls="false"
                             size="small"
                             :step="0.01"
                             step-strictly></el-input-number>
            至
            <el-input-number style="margin-left: 2px;"
                             v-model="search.checkAmountEnd"
                             placeholder="最大值"
                             :min="0"
                             :max='*********.99'
                             :precision="2"
                             :controls="false"
                             size="small"
                             :step="0.01"
                             step-strictly></el-input-number>
          </div>
        </template>
        <template slot="companyPaymentPlanAmountSearch"
                  slot-scope="scope">
          <div style="display: flex;">
            <el-input-number style="margin-right:2px;"
                             v-model="search.companyPaymentPlanAmountBegin"
                             placeholder="最小值"
                             :min="0"
                             :max='*********.99'
                             :precision="2"
                             :controls="false"
                             size="small"
                             :step="0.01"
                             step-strictly></el-input-number>
            至
            <el-input-number style="margin-left: 2px;"
                             v-model="search.companyPaymentPlanAmountEnd"
                             placeholder="最大值"
                             :min="0"
                             :max='*********.99'
                             :precision="2"
                             :controls="false"
                             size="small"
                             :step="0.01"
                             step-strictly></el-input-number>
          </div>
        </template>
        <template slot="menu"
                  slot-scope="scope">
          <el-button type="text"
                     v-if="permissions['chain:payPlanApproval:check']&&tabPosition==1"
                     icon="el-icon-document"
                     size="small"
                     plain
                     @click="viewAccountSheet(scope.row,scope.index)">
            {{scope.row.isReCheck==1?'复核审批':'审批'}}</el-button>
          <el-button type="text"
                     v-if="permissions['chain:payPlanApproval:get']&&(tabPosition==2 || tabPosition==3)"
                     icon="el-icon-view"
                     size="small"
                     plain
                     @click="viewAccountSheet(scope.row,scope.index)">
            查看</el-button>
        </template>
      </avue-crud>
    </basic-container>
    <!-- 结算单审批 -->
    <el-drawer title="资金支付计划审批"
               :visible.sync="accountVisible"
               size="90%"
               :data="accountVisible"
               :wrapperClosable="false">
      <el-collapse v-model="activeNames">
        <el-collapse-item name="3"
                          v-if="isCheckOpen">
          <template slot="title">
            <div class="detail">回执信息</div>
          </template>
          <div v-if="receiptList&&receiptList.length>0">
            <div v-for="(item,index) in receiptList" :key="index">
              <avue-crud :data="item.payeeInfoList"
                        :span-method="event=>spanMethod(event,item)"
                        :option="receiptOption">
              </avue-crud>
            </div>
          </div>
        </el-collapse-item>
        <h3 style="margin-top: 10px;">结算单信息</h3>
        <el-descriptions>
          <el-descriptions-item label="结算单号">{{accountForm.settleNo}}</el-descriptions-item>
          <el-descriptions-item label="所属项目">{{accountForm.projectName}}</el-descriptions-item>
          <el-descriptions-item label="结算申请人">{{accountForm.agentName}}</el-descriptions-item>
          <el-descriptions-item label="结算申请时间">{{accountForm.applyDatetime}}</el-descriptions-item>
          <el-descriptions-item label="承运人"
                                :span="2">{{accountForm.applyName}}</el-descriptions-item>
          <el-descriptions-item label="核算运单数">{{accountForm.checkNum}}</el-descriptions-item>
          <el-descriptions-item label="核算运单金额">{{accountForm.settleAmount}}</el-descriptions-item>
          <el-descriptions-item label="核算人">{{accountForm.settleName}}</el-descriptions-item>
        </el-descriptions>
        <el-collapse-item name="1">
          <template slot="title">
            <div class="detail">资金支付计划关联运单</div>
          </template>
          <avue-crud :data="accountForm.waybillList"
                     :option="option">
          </avue-crud>
        </el-collapse-item>
        <div class="approvalInfo">
          <h3>审批流程</h3>
          <div class="info"
               style="width:400px;margin-left: 20px;margin-top: 20px;"
               v-if="flowList&&flowList.length>0">
            <div class="approvalFlow">
              <el-timeline>
                <el-timeline-item :timestamp="item.companyPositionName"
                                  placement="top"
                                  class="myActive"
                                  color="#409eff"
                                  v-for="(item,index) in flowList"
                                  :key="index">
                  <div style="display:flex;align-items: center;justify-content: space-between;margin-bottom:10px">
                    <h4>{{item.approveUsername}}</h4><span>{{item.approveDatetime}}</span>
                  </div>
                  <el-input type="textarea"
                            v-if="item.approveRemark"
                            v-model="item.approveRemark"
                            :autosize="{ minRows: 3, maxRows: 8}"
                            disabled></el-input>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </div>
        <el-form class="approvalform"
                 label-width="86px"
                 :model="approvalform"
                 :rules="rules"
                 ref="approvalform"
                 v-if="tabPosition==1">
          <el-form-item label="审核操作:"
                        prop="auditStatus">
            <el-radio-group v-model="approvalform.auditStatus">
              <el-radio label="2"
                        value="2">同意</el-radio>
              <el-radio label="3"
                        value="3">驳回</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见:"
                        prop="auditRemark">
            <el-input type="textarea"
                      v-model="approvalform.auditRemark"></el-input>
          </el-form-item>

          <el-form-item label-width="20px">
            <el-button type="primary"
                       size="small"
                       icon="el-icon-circle-check"
                       @click="submitForm('approvalform')">提交</el-button>
            <el-button size="small"
                       icon="el-icon-circle-close"
                       @click="accountVisible=false">取消</el-button>
          </el-form-item>
        </el-form>
  </el-collapse>
  </el-drawer>
  </div>
</template>

<script>
import { paymentPlanPage, getObj, addObj, putObj, delObj, getSettleWaybillPlanId, auditPaymentPlan, getFlowNode } from '@/api/chain/payPlanApproval'
import { checkOpen, getListGroupBatchNo } from "@/api/chain/companysettle";

import { tableOption } from '@/const/crud/chain/payPlanApproval'
import { mapGetters } from 'vuex'
//引入账单核算
// import accountSheet from '../companysettle2/accountSheet.vue'
export default {
  name: 'payPlanApproval',
  components: {
    // accountSheet,
  },
  data () {
    return {
      form: {},
      tableData: [],
      search: {
        rejectPriceBegin: undefined,
        rejectPriceEnd: undefined,
        checkAmountBegin: undefined,
        checkAmountEnd: undefined,
        adjustAmtBegin: undefined,
        adjustAmtEnd: undefined,
        companyPaymentPlanAmountBegin: undefined,
        companyPaymentPlanAmountEnd: undefined,
      },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: "flow_datetime"//降序字段
      },
      defaults: {},
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      tabPosition: '1',
      activeNames: '2',
      accountVisible: false,  //查看核算单的弹窗
      accountForm: {},  //查看核算单数据
      approvalform: {},
      rules: {
        auditStatus: [
          { required: true, message: '请选择审核操作', trigger: 'change' }
        ],
        auditRemark: [
          { required: true, message: '请填写审核意见', trigger: 'blur' }
        ]
      },
      option: {
        border: true,
        stripe: true,
        columnBtn: false,
        // showHeader:false,
        index: this.showIndex,
        size: 'mini',
        selection: false,
        addBtn: false,
        refreshBtn: false,
        menu: false,
        // page:this.showPage,
        align: 'center',
        menuAlign: 'center',
        height: 500,
        // menuType:this.menuType,
        // menuBtnTitle:'自定义名称',
        column: [
          {
            label: '运单信息',
            children: [
              {
                label: '运单号',
                prop: 'no',
                width: '180px'
              },
              {
                label: '出场日期',
                prop: 'goDatetime1',
                formatter: (val) => {
                  return val.goDatetime && this.$moment(val.goDatetime).format("YYYY-MM-DD");
                },
                width: '90px'
              },
              {
                label: '出场时间',
                prop: 'goDatetime',
                formatter: (val) => {
                  return val.goDatetime && this.$moment(val.goDatetime).format("HH:mm:ss");
                },
                width: '80px'
              },
              {
                label: '班次',
                prop: 'goShiftTypeCn',
              },
              {
                label: '签单员',
                prop: 'goStaffName',
              },
              {
                label: '车牌',
                prop: 'truckCode',
              },
              {
                label: '车型',
                prop: 'goVehicleType',
              },
              {
                label: '容量(方)',
                prop: 'capacity',
              },
              {
                label: '土质',
                prop: 'goSoilType',
              },
              {
                label: '运输方式',
                prop: 'tpModeCn',
              },
              {
                label: '泥尾',
                prop: 'garbageName',
              },
              {
                label: '司机',
                prop: 'driverName',
              },
              {
                label: '车队长',
                prop: 'driverCaptainName',
              },
              {
                label: '数量',
                prop: 'weightTons',
              },
              {
                label: '单位',
                prop: 'weightUnit',
              },
              {
                label: '单价',
                prop: 'unitPrice',
              },
              {
                label: '预付价',
                prop: 'payeePrice',
              },
              {
                label: '地块',
                prop: 'landParcel',
              },
              {
                label: '备注',
                prop: 'remark',
              },
            ]
          },
          {
            label: '结算信息',
            children: [
              {
                label: '单价',
                prop: 'settleWeightUnit',
              },
              {
                label: '核算价',
                prop: 'settlePrice',
              },
              {
                label: '增减值',
                prop: 'adjustAmt',
              },
            ]
          },

        ]
      },
      isCheckOpen: false, //企业是否开启结算单回执
      receiptOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 12,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: true,
        delBtn: false,
        menu: false,
        searchMenuSpan: 6,
        index: false,
        refreshBtn: false,
        column: [
          {
            label: "合计",
            prop: "sumSettleAmount",
            minWidth:80,
            overHidden:true,
          },
          {
            label: "类型",
            prop: "type",
            minWidth: 80,
            overHidden: true,
            formatter: (val) => {
              return val.type == 1 ? '结算单' : '回执单'
            }
          },
          {
            label: "初始结算单号/电子卡号",
            prop: "initNo",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "最终结算单单号/回执单号",
            prop: "parentNo",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "收款人",
            prop: "payeeName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "账号",
            prop: "bindingBankNo",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "开户行",
            prop: "bindingBankName",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "申请结算金额",
            prop: "settleAmount",
            minWidth:100,
            overHidden:true,
          },
          {
            label: "核算运单数",
            prop: "settleCount",
            minWidth:100,
            overHidden:true,
          },
          {
            label: "申请时间",
            prop: "createDatetime",
            minWidth:140,
            overHidden:true,
          },
        ],
      },
      receiptList: [],
      flowList: [],
      id:"",
    }
  },
  created () {
  },
  mounted () {
    // 检查是否开启结算单回执
    checkOpen({ companyAuthId: "" }).then(res => {
      this.isCheckOpen = res.data.data
    })
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList () {
      return {
        addBtn: this.permissions['chain:payPlanApproval:add'] ? true : false,
        delBtn: this.permissions['chain:payPlanApproval:del'] ? true : false,
        editBtn: this.permissions['chain:payPlanApproval:edit'] ? true : false,
        viewBtn: this.permissions['chain:payPlanApproval:get'] ? true : false
      };
    },
    // totalAmount () {
    //   return this.receiptList.map((row) => (row.settleAmount)).reduce((acc, cur) => parseFloat(cur) + acc, 0).toFixed(2);
    // }
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage (page, params) {
      this.tableLoading = true
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.applyDatetimeBegin = params.searchDate[0];
          params.applyDatetimeEnd = params.searchDate[1];
          delete params.searchDate;
        }
        if (params.hasOwnProperty("searchDate2")) {
          params.flowDatetimeBegin = params.searchDate2[0];
          params.flowDatetimeEnd = params.searchDate2[1];
          delete params.searchDate2;
        }
      }
      paymentPlanPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        flowStatus: this.tabPosition
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        _this.$message({
          showClose: true,
          message: '删除成功',
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(response => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page)
    },
    changeTab () {
      this.page.currentPage = 1
      this.getPage(this.page)
      this.$refs.crud.refreshTable()
    },
    //查看核算单
    viewAccountSheet (row, index) {
      this.tableLoading = true
      this.approvalform = {
        auditStatus: '',
        auditRemark: '',
      }
      this.id = row.id
      //获取结算单信息
      getSettleWaybillPlanId({ companySettleId: row.companySettleId, companyPaymentPlanId: row.id }).then(res => {
        this.accountForm = res.data.data
        this.accountVisible = true
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
      getFlowNode(row.id).then(res => {
        this.flowList = res.data.data
      })
      //如果需要回执信息 获取回执信息
      if (this.isCheckOpen) {
        getListGroupBatchNo({ settleNo: row.settleNo })
          .then((response) => {
            this.receiptList = response.data.data;
          })
      }
    },
    spanMethod ({ row, column, rowIndex, columnIndex },item) {
      if (columnIndex === 0) {
        return {
          rowspan: item.payeeInfoList.length,
          colspan: rowIndex>0?0:1
        }
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // alert('submit!');
          // console.log(this.approvalform);
          // console.log(this.accountForm.id);
          let param = Object.assign({}, this.approvalform)
          param.companyPaymentPlanIdList = [this.id]
          auditPaymentPlan(param).then(res => {
            this.accountVisible = false
            this.getPage(this.page)
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },

  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ccc;
}
/deep/ .el-drawer__body {
  padding: 20px;
  padding-top: 0px;
  .el-collapse-item {
    .el-collapse-item__header {
      .detail {
        padding-left: 8px;
        text-align: left;
        font-weight: 700;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: #111528;
        border-bottom: none;
        border-left: 4px solid #4688f7;
      }
    }
  }
  .el-collapse-item__content {
    text-align: center;
    .avue-crud__menu {
      display: none;
    }
  }
  .approvalInfo {
    text-align: left;
    display: inline-block;
    margin-top: 20px;
    width: 100%;
  }
  h3 {
    margin-bottom: 20px;
    font-weight: 700;
    padding-left: 8px;
    text-align: left;
    height: 14px;
    line-height: 14px;
    font-size: 14px;
    color: #111528;
    border-left: 4px solid #4688f7;
  }
  .approvalInfo {
    .approvalTitle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 16px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ccc;
      margin-bottom: 20px;
      .approvalSource {
        padding-left: 8px;
        text-align: left;
        height: 18px;
        line-height: 18px;
        font-size: 16px;
        color: #111528;
        // border-left: 4px solid #4688f7;
      }
    }
    .approvalFlow {
      width: 400px;
    }

    .el-timeline-item__timestamp {
      color: #333;
    }
    .el-timeline-item__content {
      color: #909399;
    }
    .el-timeline-item__tail {
      border-left: 2px solid #409eff;
    }
    .approvalform {
      padding: 20px 20px 0px;
    }
    .info {
      padding: 0px 20px;
    }
  }
}
</style>
