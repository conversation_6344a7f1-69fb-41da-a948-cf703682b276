<template>
  <div>
    <captainDataboard v-if="roleType==1"/>
    <navvyOwnerDataboard v-else-if="roleType==6"/>
    <garbageDataboard v-else-if="roleType==7"/>
    <databoard v-else/>
  </div>
</template>

<script>
  import databoard from "@/views/chain/databoard/home2.vue"
  import captainDataboard from "@/views/captain/databoard/index.vue"
  import navvyOwnerDataboard from "@/views/navvyOwner/databoard/index.vue"
  import garbageDataboard from "@/views/garbage/databoard/index.vue"
  import {mapGetters} from "vuex"
  export default {
    components:{
      databoard,
      captainDataboard,
      navvyOwnerDataboard,
      garbageDataboard,
    },
    name: 'wel',
    data() {
      return {

      }
    },
    computed: {
      ...mapGetters(['website',"roleType"])
    },
    mounted() {
      console.log(this.roleType);
    },
    methods: {

    }
  }
</script>

<style lang="scss">

</style>
