<template>
  <div class="projectDetail">
    <component :is="dialogType"
               :width="size"
               :size="size"
               :title="title"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <!-- <basic-container> -->
      <avue-form :option="option"
                 ref="componentForm"
                 v-model="form"
                 @resetForm="resetForm"
                 @submit="submit">
        <template #[slotName]="slotProps"
                  v-for="(slot, slotName) in $scopedSlots">
          <slot :name="slotName"
                v-bind="slotProps" />
        </template>
        <template slot="menuForm">
          <el-button icon="el-icon-close" v-if="showCancel"
                     @click="cancelModal">取消</el-button>
        </template>
      </avue-form>
      <!-- </basic-container> -->
    </component>

  </div>
</template>

<script>
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    showCancel: {
      type: Boolean,
      default: false
    },
    detailForm: {
      type: Object,
      default: () => {
        return {}
      }
    },
    option: {
      type: Object,
      default: () => {
        return {}
      }
    },
    title: {
      type: String,
      default: "详情"
    },
    size: {
      type: String,
      default: "70%"
    },
    dialogType: {
      type: String,
      default: "elDrawer"
    },
  },
  data () {
    return {
      form: {}
    }
  },
  created () {
  },
  watch: {
    detailForm: {
      handler () {
        this.form = Object.assign({}, this.form, this.detailForm)
      },
      immediate: true,
      deep: true,
    },
  },
  mounted: function () {
    this.form = this.detailForm
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    submit (form, done) {
      this.$emit("submit", form, done);
    },
    resetForm (form, done) {
      this.$emit("resetForm");
    },
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 20px;
}
/deep/ .avue-upload .el-icon-plus {
  display: none;
}
/deep/ .avue-upload--list .el-upload {
  border: none;
  .avue-upload__avatar {
    width: 100px;
    height: 100px;
  }
}
/deep/ .el-dialog__body{
  padding-top: 0;
  padding-bottom: 0;
}
</style>
