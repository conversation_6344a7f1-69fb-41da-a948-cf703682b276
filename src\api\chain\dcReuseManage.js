import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companycardresetlog/page',
        method: 'get',
        params: query
    })
}
export function validatedCardReset(query) {
    return request({
        url: '/chain/companycardresetlog/validatedCardReset',
        method: 'get',
        params: query
    })
}

export function resetCardList(obj) {
    return request({
        url: '/chain/companycardresetlog/resetCardList',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companycardresetlog/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companycardresetlog/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companycardresetlog',
        method: 'put',
        data: obj
    })
}
