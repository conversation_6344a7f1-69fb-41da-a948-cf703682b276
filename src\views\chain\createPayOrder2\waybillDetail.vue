<template>
  <div class="execution">
    <basic-container>
      <el-collapse
        v-model="activeNames"
        v-for="(accountForm, index) in settleDetailList"
        :key="index"
      >
        <el-collapse-item :title="'结算单：'+accountForm.header.settleNo" :name="index">
          <div class="dialogContent" :ref="'print' + (index + 1)">
            <div class="dialogTitle">结算单</div>
            <ul class="info">
              <li>
                <label>结算单号：</label>
                <span>{{ accountForm.header.settleNo }}</span>
              </li>
              <li>
                <label>结算申请人：</label>
                <span>{{ accountForm.header.agentName }}</span>
              </li>
              <li>
                <label>项目名称：</label>
                <span>{{ accountForm.header.projectName }}</span>
              </li>
              <li>
                <label>运单数：</label>
                <span>{{ accountForm.header.settleCnt }}</span>
              </li>
              <li>
                <label>结算金额：</label>
                <span>{{ accountForm.header.settleAmt }}</span>
              </li>
              <li>
                <label>核算人：</label>
                <span>{{ accountForm.header.settleName }}</span>
              </li>
            </ul>
            <div
              class="table" v-for="(item, index2) in accountForm.body" :key="index2"
            >
              <div class="tableTitle">
                <span>承运人:{{ item.carrier_name }}</span
                ><span class="red">共计{{ item.cnt }}车，{{ item.amt }}元</span>
              </div>

              <div
                class="inner"
                v-for="(list, k) in item.detail"
                :key="k + 100"
              >
                <div class="tableTitle">
                  <span>{{ list.create_datetime }}</span
                  ><span class="red"
                    >共计{{ list.cnt }}车，{{ list.total_amt }}元</span
                  >
                </div>
                <el-table :data="list.detailDetail" border class="table">
                  <el-table-column
                    property="truckCode"
                    width="100px"
                    label="车牌号"
                  ></el-table-column>
                  <el-table-column
                    property="settleCnt"
                    label="运单数"
                  ></el-table-column>
                  <el-table-column
                    property="weightTons"
                    label="总数量"
                  ></el-table-column>
                  <el-table-column
                    property="weightUnit"
                    label="单位"
                  ></el-table-column>
                  <el-table-column
                    property="settlePrice"
                    label="单价"
                  ></el-table-column>
                  <el-table-column
                    property="payeePrice"
                    label="预付价总额"
                  ></el-table-column>
                  <el-table-column
                    property="totalAmt"
                    label="核算价总额"
                  ></el-table-column>
                </el-table>
              </div>
            </div>
          </div>
          <div class="btn">
            <el-button
              size="small"
              type="primary"
              @click="downExport(accountForm.header.id)"
              >导出</el-button
            >
            <el-button size="small" :loading="btnLoading" @click="printPDF(accountForm.header.id)">下载PDF打印</el-button>
          </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>
  </div>
</template>

<script>
import { expotOut } from "@/util/down.js";
import {downloadSettlePdf } from "@/api/chain/companysettle";

export default {
  name: "companysettle",
  components: {},
  data() {
    return {
      settleDetailList: [],
      activeNames: [0],
      accountForm:{},
      btnLoading:false,
    };
  },
  created() {},
  mounted: function () {
    this.settleDetailList = JSON.parse(this.$route.query.value);
    console.log(this.settleDetailList);
  },
  computed: {},
  methods: {
    //导出
    downExport(id) {
      console.log(id);
      let params = {
        settleId: id,
      };
      let url = "/chain/companysettle/exportExcelSettle";
      expotOut(params, url, "结算单")
    },
    printPDF(id){
      this.btnLoading = true
      let params = {
        settleId: id,
      };
      downloadSettlePdf(params).then(res=>{
        console.log(res);
        const url = window.URL.createObjectURL(new Blob([res.data]));
		    const link = document.createElement('a');
        link.href = url;
        let fileName = "结算单.pdf" //
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        this.btnLoading = false
      }).catch(err=>{
        this.btnLoading = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  // border-bottom: 1px solid #ccc;
  padding-top: 20px;
  margin-bottom: 10px;
  padding-bottom: 20px;
  li {
    width: 33%;
    line-height: 30px;
    color: #000;
    label {
      display: inline-block;
      width: 100px;
      text-align: right;
    }
    span {
      color: #666;
      // font-size: 14px;
    }
  }
}
.tableTitle {
  border: 1px solid #9a9a9a;
  margin-left: 1px;
  border-bottom: none;
  line-height: 32px;
  padding: 0px 10px;
  // color: #606266;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.red {
  color: red;
}
/deep/ .el-table{
  border-bottom: 1px solid #9a9a9a;
  border-right: 1px solid #9a9a9a;
  tr{
    .el-table__cell {
      border-left: 1px solid #9a9a9a;
      border-top: 1px solid #9a9a9a;
    }
      border-right: 1px solid #9a9a9a;

  }
  .el-table__row{
    td{
      padding: 4px 0px;
    }
  }
  .el-table__header{
    th{
      padding: 4px 0px;
    }
  }
}
.dialogContent {
  // border: 1px solid #ccc;
  // margin-bottom: 30px;
  .dialogTitle {
    font-size: 18px;
    font-weight: 700;
    text-align: center;
    line-height: 40px;
    // border-bottom: 1px solid #ccc;
    // margin-bottom: 20px;
  }
  .myTable {
    padding: 0 10px;
  }
}
.total {
  margin-top: 40px;
  background-color: #f2f2f2;
  color: #333;
  font-weight: 700;
  line-height: 40px;
  text-align: center;
}
.btn {
  margin-top: 30px;
  text-align: center;
}
</style>
