﻿export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: true,
  searchMenuSpan: 6,
  column: [
    //     {
    //     label: 'ID',
    //     prop: 'id',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
    //     {
    //     label: '企业认证ID',
    //     prop: 'companyAuthId',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入企业认证ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
    {
      label: '职位名称',
      prop: 'positionName',
      sortable: true,
      rules: [
        {
          max: 100,
          message: '长度在不能超过100个字符'
        },
      ]
    },
    //     {
    //     label: '系统菜单IDS，多个以英文逗号分开',
    //     prop: 'systemMenuIds',
    //     sortable: true,
    //     rules: [
    //                         ]
    // },
    //     {
    //     label: '创建日期时间',
    //     prop: 'createDatetime',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入创建日期时间',
    //                 trigger: 'blur'
    //             },
    //                         ]
    // },
    //     {
    //     label: '创建ID',
    //     prop: 'createId',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入创建ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
    //     {
    //     label: '修改日期时间',
    //     prop: 'updateDatetime',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入修改日期时间',
    //                 trigger: 'blur'
    //             },
    //                         ]
    // },
    //     {
    //     label: '修改ID',
    //     prop: 'updateId',
    //     sortable: true,
    //     rules: [
    //             {
    //                 required: true,
    //                 message: '请输入修改ID',
    //                 trigger: 'blur'
    //             },
    //                             {
    //                 max: 36,
    //                 message: '长度在不能超过36个字符'
    //             },
    //         ]
    // },
  ]
}
