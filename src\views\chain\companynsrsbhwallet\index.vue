<template>
  <div class="execution">
    <basic-container class="basicPadding">
      <el-tabs v-model="activeName"
              v-loading="tableLoading"
               @tab-click="handleClick">
        <el-tab-pane :name="item.platformBranchId"
                     v-for="item in tabList"
                     :key="item.platformBranchId">
          <span slot="label"><span class="roundTip" v-if="item.platformBranchId!=1&&(Number(item.balance)<Number(item.waitPaymentTotalAmount))"></span>{{item.platformBranchNsrmc}}</span>
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="list flex flex-items-center">
                <div class="group1 flex flex-items-center flex-center">
                  <div class="imgWrapper flex flex-items-center flex-center">
                    <svg-icon icon-class="purse"
                              class-name="purse-icon" />

                  </div>
                </div>
                <div class="group2 flex flex-between">
                  <div class="left">
                    <div class="title">钱包总余额：</div>
                    <div class="money">¥ {{walletUnInfo.balance }}</div>
                  </div>
                  <div class="right flex flex-column flex-between">
                    <el-button type="primary"
                               size="small"
                               v-if="permissions['chain:companynsrsbhwallet:recharge']"
                               @click="recharge">充 值</el-button>
                    <el-link type="primary"
                             v-if="permissions['chain:companynsrsbhwallet:rechargeLog']"
                             @click="logVisible=true">充值记录</el-link>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="16">
              <div class="list flex flex-items-center purple">
                <div class="group1 flex flex-items-center flex-center">
                  <div class="imgWrapper flex flex-items-center flex-center">
                    <svg-icon icon-class="money"
                              class-name="money-icon" />

                  </div>
                </div>
                <div class="group2 flex flex-between">
                  <div class="tip"
                       v-if="tipIsShow"><i class="el-icon-error"
                       @click="tipIsShow=false"></i>税洼地钱包余额不足，请充值</div>
                  <div class="left">
                    <div class="title">待付总费用：</div>
                    <div class="money">¥ {{walletUnInfo.unpaidTotal }}</div>
                  </div>
                  <div class="group3 flex flex-items-center">
                    <div class="block">
                      <div class="title">待付运费</div>
                      <div class="money">¥ {{walletUnInfo.unPaidPrice }}</div>
                    </div>
                    <div class="block margin16">
                      <div class="title">待付税费</div>
                      <div class="money">¥ {{walletUnInfo.unPaidTaxFee }}</div>
                    </div>
                    <div class="block">
                      <div class="title">待付服务费</div>
                      <div class="money">¥ {{walletUnInfo.chargeTotal }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      <my-crud ref="crud"
               :page.sync="page"
               :data="tableData"
               :permission="permissionList"
               :table-loading="tableLoading"
               :option="tableOption"
               v-model="form"
               @on-load="getPage"
               @refresh-change="refreshChange"
               @sort-change="sortChange"
               @search-change="searchChange">
        <template slot="businessSerialNoHeader"
                  slot-scope="{ row, index }">
          <span>业务流水(支付+充值)</span>
        </template>
        <template slot="searchMenu"
                  slot-scope="{ row, index }">
          <el-button type="primary"
                     icon="el-icon-download"
                     size="small"
                     :loading="tableLoading"
                     v-if="permissions['chain:companynsrsbhwallet:excel']"
                     @click="exOut">
            导出</el-button>
        </template>
        <template slot="menu"
                  slot-scope="{ row, index }">
          <el-button type="text"
                     icon="el-icon-view"
                     size="small"
                     plain
                     v-if="permissions['chain:companynsrsbhwallet:voucher']&&row.transactionType&&(row.transactionType==1||row.transactionType==6)"
                     @click="detail(row)">
                     <span v-if="row.transactionType==1">充值</span>
                     <span v-if="row.transactionType==6">退款</span>凭证</el-button>
          <el-button type="text"
                     icon="el-icon-view"
                     size="small"
                     plain
                     v-if="permissions['chain:companynsrsbhwallet:voucher']&&(row.transactionType==2||row.transactionType==7||row.transactionType==8||row.transactionType==9)&&row.stt==20"
                     @click="detail(row)">
                     支付凭证</el-button>
        </template>
      </my-crud>
    </basic-container>
    <walletList v-if="visible"
                @saveForWallet="rechargeComplete"
                :tableData="walletList"
                :visible.sync="visible"></walletList>
    <!-- 充值 -->
    <addRecharge v-if="addVisible"
                 :detailForm="addForm"
                 :option="addOption"
                 :showCancel="true"
                 ref="addForm"
                 size="634px"
                 :visible.sync="addVisible"
                 @submit="submit"
                 title="账户余额充值"></addRecharge>
    <!-- 充值记录 -->
    <rechargeLog v-if="logVisible"
                 :platformBranchId="activeName==1?'':activeName"
                 :visible.sync="logVisible"></rechargeLog>
    <!-- 打印凭证 -->
    <voucher v-if="detailVisible"
             :detailList="detailList"
             :sourceId="sourceId"
             :visible.sync="detailVisible"></voucher>
    <avue-sign ref="sign"></avue-sign>
  </div>
</template>

<script>
import { getPage, getBranchWalletList, saveForWallet, getCompanyWalletUnInfo,getProof,getEvidence } from '@/api/chain/companynsrsbhwallet'
import { tableOption } from '@/const/crud/chain/companynsrsbhwallet'
import { mapGetters } from 'vuex'
import walletList from './walletList.vue';
import voucher from '../companyauthrecharge2/voucher.vue';
import rechargeLog from './rechargeLog.vue';
import addRecharge from '@/components/formDetail/index.vue';
import { exportOut } from "@/util/down.js";
export default {
  name: 'companynsrsbhwallet',
  components: {
    walletList,
    voucher,
    addRecharge,
    rechargeLog,
  },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      activeName: "1",
      tabList: [],
      tipIsShow: false,
      branchWalletList: [],
      walletList: [],
      visible: false,
      walletUnInfo: {},
      detailVisible: false,
      logVisible: false,
      detailList: [],
      sourceId: "",
      addVisible: false, //充值
      addForm: {},
      addOption: {
        labelWidth: 100,
        emptyBtn: false,
        submitBtnText: "创建",
        column: [
          {
            label: "充值金额(¥)",
            prop: "money",
            span: 24,
            type: "number",
            minRows: 0.01,
            maxRows: 999999999.99,
            precision: 2,
            controls: false,
            rules: [
              {
                required: true,
                message: "请输入 充值金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "充值企业银行",
            prop: "platformBranchId",
            span: 24,
            disabled: true,
            type: "select",
            props: {
              label: "platformBranchNsrmc",
              value: "platformBranchId",
            },
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 3,
            maxRows: 5,
            span: 24,
          },
          {
            label: "上传凭证",
            prop: "pic",
            type: "upload",
            listType: "picture-card",
            action: "/upms/file/upload?fileType=image&dir=batchRecharge/",
            dataType: 'string',
            propsHttp: {
              url: "link",
            },
            // uploadError:(error,column)=>{
            //   this.$message.error(error)
            //   console.log(error);
            //   console.log(column);
            // },
            loadText: "附件上传中，请稍等",
            span: 24,
            accept:".jpg,.png",
            // fileSize:500,
            tip: "只能上传jpg/png文件",
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
        ],
      },
    }
  },
  created () {
    this.getBranchWalletList()
    this.getCompanyWalletUnInfo()
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList () {
      return {
        addBtn: this.permissions['chain:companynsrsbhwallet:add'] ? true : false,
        delBtn: this.permissions['chain:companynsrsbhwallet:del'] ? true : false,
        editBtn: this.permissions['chain:companynsrsbhwallet:edit'] ? true : false,
        viewBtn: this.permissions['chain:companynsrsbhwallet:get'] ? true : false
      };
    }
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      this.getCompanyWalletUnInfo()
      done()
    },
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage (page, params = {}) {
      if (this.activeName != 1) {
        params.platformBranchId = this.activeName
      } else {
        params.platformBranchId = params.platformBranchIds || ""
        delete params.platformBranchIds
      }
      console.log(params);
      console.log(this.paramsSearch);
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.begTransactionDatetime = params.searchDate[0];
          params.endTransactionDatetime = params.searchDate[1];
          delete params.searchDate;
        }
      }
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page)
    },
    getBranchWalletList () {
      // returnWaitPaymentTotalAmount:false  需要小红点，不能用
      getBranchWalletList({}).then(res => {
        let arr = [{
          platformBranchId: '1',
          platformBranchNsrmc: "全部"
        }]
        this.branchWalletList = res.data.data || []
        this.tabList = arr.concat(this.branchWalletList)
        this.$refs.crud.updateDic("platformBranchIds", res.data.data);
        //特殊情况
        if (this.activeName != "1" && !this.branchWalletList.some(item2 => item2.platformBranchId == this.activeName)) {
          this.activeName = "1"
        }
      })
    },
    getCompanyWalletUnInfo () {
      getCompanyWalletUnInfo({ platformBranchId: this.activeName == 1 ? '' : this.activeName }).then(res => {
        this.walletUnInfo = res.data.data
        this.tipIsShow = Number(this.walletUnInfo.balance) < Number(this.walletUnInfo.unpaidTotal)
      })
    },
    handleClick (tab, event) {
      console.log(tab, event);
      this.tableOption.column.forEach(item => {
        if (item.prop == 'platformBranchIds') {
          item.search = tab.name == 1
          console.log(item.search);
        }
      })
      this.paramsSearch.platformBranchId = tab.name==1?"":tab.name
      //全部
      this.$refs.crud.initSearchForm()
      this.page.currentPage = 1
      this.getPage(this.page)
      this.getCompanyWalletUnInfo()
    },
    recharge () {
      if (this.activeName == 1) {
        //全部
        getBranchWalletList({}).then(res => {
          this.walletList = res.data.data || []
          this.visible = true
        })
      } else {
        this.addForm.platformBranchId = this.activeName
        this.addOption.column.find(item => {
          if (item.prop == "platformBranchId") {
            item.dicData = this.branchWalletList
          }
          return item.prop == 'platformBranchId'
        })
        this.addVisible = true
        // this.$nextTick(()=>{
        //   this.$refs.addForm.updateDic("platformBranchId", this.branchWalletList);
        // })
      }
    },
    //充值成功
    rechargeComplete () {
      this.getBranchWalletList()
      this.getCompanyWalletUnInfo()
    },
    submit (form, done) {
      console.log(form);
      let param = Object.assign({}, form)
      saveForWallet(param).then(res => {
        this.$message.success("充值成功")
        this.addVisible = false
        // 记得需要更新税洼地金额
        // this.rechargeComplete()
        done()
      }).catch(() => {
        done()
      })
    },
    exOut(){
      let params = Object.assign({}, this.paramsSearch);
      if (params.hasOwnProperty("searchDate")) {
        params.begTransactionDatetime = params.searchDate[0];
        params.endTransactionDatetime = params.searchDate[1];
        delete params.searchDate;
      }
      let url = '/chain/excelExport/postCreateByCode'
      params.code="CompanyNsrsbhWalletDetailExport"
      this.tableLoading = true
      exportOut(params,url,'钱包',"post").then(res=>{
        this.tableLoading = false
      }).catch(err=>{
        this.$message.error('导出失败')
        this.tableLoading = false
      })
    },
    detail(row){
      //1充值  2支付  6退款
      switch (row.transactionType) {
        case "2":
        case "7":
        case "8":
        case "9":
          this.voucher(row)
          break;
        case "1":
        case "6":
          this.getProof(row.id,row.transactionType)
          break;
        default:
          break;
      }
    },
    getProof(id,transactionType){
      getProof({walletDetailId:id,transactionType}).then(res=>{
        console.log(res);
        let pic = res.data.data.pic
        let datas = pic&&pic.split(',').map(item=>{
          return {
            thumbUrl:item,
            url:item,
            type:'img'
          }
        })
        this.$ImagePreview(datas, 0);
      })
    },
    voucher(row){
      let param = {
        thirdVoucher:row.sourceId
      }
      this.sourceId = row.sourceId
      this.$refs.sign.clear()
      getEvidence(param).then(res=>{
          this.detailList = res.data.data
          this.detailVisible=true
          setTimeout(()=>{
          this.detailList.forEach((item,index) => {
            this.$refs.sign.getStar('支付',item.companyName,item.companyNo)
            this.$set(item,'img',this.$refs.sign.submit(80, 50))
            this.$refs.sign.clear()
          });
        },100)
      })
    },
  }
}
</script>

<style lang="scss" scoped>
/deep/ .basicPadding .el-card > .el-card__body {
  padding-top: 0px;
}
.el-tabs{
  height: 150px;
}
/deep/ .roundTip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #d40000;
  margin-right: 3px;
}
.list {
  height: 80px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  margin-bottom: 16px;

  .group1 {
    width: 80px;
    height: 80px;
    background: #419ffd;
    border-radius: 4px 0px 0px 4px;
    .imgWrapper {
      width: 50px;
      height: 50px;
      background: #ffffff;
      border-radius: 50%;
      line-height: 80px;
      .purse-icon {
        width: 24px;
        height: 24px;
        color: #419ffd;
      }
      .money-icon {
        width: 24px;
        height: 24px;
        color: #6b50c6;
      }
    }
  }
  .group2 {
    padding: 10px;
    flex: 1;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    .left {
      .title {
        margin-top: 4px;
        height: 20px;
        font-size: 16px;
        font-weight: 400;
        color: #474a59;
        line-height: 20px;
      }
      .money {
        height: 44px;
        font-size: 1.8vw;
        color: #474a59;
        line-height: 44px;
      }
    }
    .group3 {
      .block {
        width: 8.2vw;
        min-width: 120px;
        height: 56px;
        background: #ece8f8;
        border-radius: 2px;
        border-left: 2px solid #6b50c6;
        padding-top: 6px;
        padding-left: 20px;
        box-sizing: border-box;
        .title {
          height: 20px;
          font-size: 0.8vw;
          font-weight: 400;
          color: #474a59;
          line-height: 20px;
        }
        .money {
          height: 25px;
          font-size: 0.9vw;
          color: #6b50c6;
          line-height: 25px;
          font-weight: 600;
        }
        &.margin16 {
          margin-left: 16px;
          margin-right: 16px;
        }
      }
    }
  }
  &.purple {
    background: #f6f5fa;
    .group1 {
      background: #6b50c6;
    }
    .group2 {
      .tip {
        width: 254px;
        height: 40px;
        background: #ffffff;
        box-shadow: 0px 2px 24px 0px rgba(200, 201, 204, 0.5);
        border-radius: 4px;
        position: absolute;
        left: 120px;
        padding-left: 16px;
        line-height: 40px;
        box-sizing: border-box;
        font-weight: 600;
        color: #d40000;
        font-size: 14px;
        .el-icon-error {
          margin-right: 16px;
          cursor: pointer;
          font-size: 16px;
        }
      }
    }
  }
}
.avue-sign {
  display: none;
}
</style>
