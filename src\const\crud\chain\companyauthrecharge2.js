export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  // stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  searchLabelWidth:100,
  searchSpan:6,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  addBtn:false,
  editBtn:false,
  delBtn:false,
  searchMenuSpan: 6,
  selection: true,
  defaultSort:{
    prop:'createDatetime',
    order:'descending'
  },
  column: [
    {
      label: "所属项目",
      prop: "projectInfoId",
      type: "select", // 下拉选择
      search: true,
      props: {
        label: "projectName",
        value: "id",
      },
      dicUrl: "/chain/projectinfo/list",
      searchFilterable: true, //是否可以搜索
      minWidth:150,
      sortable:"custom",
    },
    {
      label: "支付单号",
      prop: "paymentNo",
      search: true,
      minWidth:160,
    },
    {
      label: "结算单号",
      prop: "settleNo",
      search: true,
      minWidth:160,
    },
    {
      label: "结算申请人",
      prop: "agentName",
      search: true,
      minWidth:86,
    },
    {
      label: "承运人",
      prop: "payeeName",
      search: true,
      minWidth:100,
      sortable:"custom",
    },
    {
      label: "结算单数量",
      prop: "settleCnt",
      minWidth:86,
    },
    {
      label: "结算运单数量",
      prop: "waybillCnt",
      minWidth:100,
    },
    {
      label: "运费合计金额(元)",
      prop: "settleAmt",
      minWidth:116,
    },
    // {
    //   label: "运费",
    //   prop: "freight",
    //   minWidth:80,
    //   overHidden:true,
    // },
    {
      label: "税费",
      prop: "taxFee",
      minWidth:80,
    },
    {
      label: "总费用",
      prop: "realPayPrice",
      minWidth:80,
    },
    {
      label: "税率",
      prop: "taxRate",
      formatter: (val) => {
        return val.taxRate&&val.taxRate +'%'
      },
      minWidth:80,
    },
    {
      label: "已支付运费",
      prop: "waybillFreight",
      minWidth:106,
    },
    {
      label: "已支付税费",
      prop: "paidTaxFee",
      minWidth:106,
    },
    {
      label: "已付合计金额",
      prop: "paidPrice",
      minWidth:96,
    },
    {
      label: "支付中金额",
      prop: "duringPayPrice",
      minWidth:106,
    },
    {
      label: "剩余未支付金额",
      prop: "unPaidPrice",
      minWidth:106,
    },
    {
      label: "待支付税费",
      prop: "unPaidTaxFee",
      minWidth:86,
    },
    // {
    //   label: "已付金额",
    //   prop: "paidPrice",
    //   minWidth:80,
    //   overHidden:true,
    // },
    {
      label: "支付状态",
      prop: "status",
      minWidth:96,
      search:true,
      searchMultiple:true,
      dataType:'string',
      type:'select',
      formatter: (val) => {
        return val.statusName;
      },
      dicData: [
        // {
        //   label: '待支付',
        //   value: '1'
        // },
        {
          label: '已审批',
          value: '2'
        },
        {
          label: '支付中',
          value: '9'
        },
        {
          label: '已支付',
          value: '4'
        },
      ],
      sortable:"custom",
    },
    {
      label: "最新申请时间",
      prop: "applyDatetime",
      search:true,
      type:'date',
      searchRange:true,
      valueFormat: 'yyyy-MM-dd',
      minWidth:140,
      // overHidden:true,
      sortable:"custom",
      
    },
    {
      label: "最新支付时间",
      prop: "latestPaymentTime",
      search:true,
      type:'date',
      searchRange:true,
      valueFormat: 'yyyy-MM-dd',
      minWidth:140,
      // overHidden:true,
      sortable:"custom",
    },
    {
      label: "创建时间",
      prop: "createDatetime",
      valueFormat: 'yyyy-MM-dd',
      sortable:"custom",
      minWidth:140,
    },
    // {
    //   label: "是否充值",
    //   prop: "isRechargeAmount",
    //   hide:true,
    //   showColumn:false,
    //   search:true,
    //   type:'select',
    //   dicData: [
    //     {
    //       label: '否',
    //       value: '0'
    //     },
    //     {
    //       label: '是',
    //       value: '1'
    //     },
    //   ],
    // },
    {
      label: "资金支付计划",
      prop: "isPaymentPlan",
      search:true,
      hide:true,
      showColumn:false,
      type:'select',
      dicData: [
        {
          label: '否',
          value: '0'
        },
        {
          label: '是',
          value: '1'
        },
      ],
    },
    {
      label: "税费支付状态",
      prop: "isUnpaidTax",
      search:true,
      hide:true,
      showColumn:false,
      type:'select',
      dicData: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '待支付',
          value: '0'
        },
        {
          label: '已支付',
          value: '1'
        },
      ],
    },
  ],
};
