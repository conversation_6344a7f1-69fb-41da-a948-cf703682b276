export const tableOption = (value) => {
  let that = value
  console.log(that);
  let defaultTime = that.$moment().format("HH:mm:ss")
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    // index:true,
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    dialogClickModal: false,
    dialogWidth: 600,
    excelBtn: false,
    printBtn: false,
    addBtn: false,
    viewBtn: false,
    menu:false,
    searchSpan: 8,
    searchMenuSpan: 6,
    defaultSort: {
      prop: "recipientDatetime",
      order: "descending",
    },
    labelWidth: 150,
    column: [
      {
        label: "类型",
        prop: "type",
        sortable: true,
        search: true,
        display: false,
        type: "select", // 下拉选择
        dicData: [
          {
            label: "全部",
            value: "",
          },
          {
            label: "领票",
            value: "1",
          },
          {
            label: "退票",
            value: "2",
          }
        ],
        overHidden:true,
      },
      {
        label: "项目",
        prop: "projectName",
        display: false,
        search: true,
        // type: "select", // 下拉选择
        // search: true,
        // searchFilterable:true,
        // filterable:true,
        // props: {
        //   label: "projectName",
        //   value: "id",
        // },
        // dicUrl: "/chain/companypayment/getProjectList",
        span: 24,
        overHidden:true,
      },
      {
        label: "项目",
        prop: "projectInfoId",
        hide: true,
        search: false,
        type: "select", // 下拉选择
        search: false,
        searchFilterable: true,
        filterable: true,
        props: {
          label: "projectName",
          value: "id",
          inventory: "inventory"
        },
        dicFormatter: (res) => {
          that.projects = res.data
          return res.data || []
        },
        change: ({ value }) => {
          // console.info(that.projects.filter(v=>v.id==value)[0])
          if (value) {
            that.form.inventory = that.projects.filter(v => v.id == value)[0].inventory

          }
        },
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        dicUrl: `/chain/projectinfo/queryStartedProjectByCompanyAuthIdOfTicket`,
        span: 24,
        overHidden:true,
      },
      {
        label: "泥尾票库存数",
        prop: "inventory",
        span: 24,
        disabled: true,
        controls: false,
        type: 'number',
        hide: true,
        sortable: true,
        precision: 0,
        minRows: 0,
        maxRows: 99999,
        placeholder: '请输入',
        overHidden:true,
      },
      {
        label: "领取泥尾票数量(张)",
        prop: "recipientNum",
        type: 'number',
        controls: false,
        minRows: 0,
        precision: 0,
        span: 24,
        sortable: true,
        placeholder: '请输入',
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:150,
        overHidden:true,
      },

      // {
      //   label: "退泥尾票数",
      //   prop: "qty",
      //   span: 24,
      //   controls: false,
      //   type: 'number',
      //   hide: true,
      //   sortable: true,
      //   precision: 0,
      //   minRows: 0,
      //   maxRows: 99999,
      //   placeholder: '请输入',
      //   rules: [
      //     {
      //       required: true,
      //       message: "请输入数量",
      //       trigger: "blur",
      //     },
      //     {
      //       min: 0,
      //       type: "number",
      //       message: "值不能小于0",
      //       trigger: "blur",
      //     },
      //   ],
      // },
      {
        label: "票号",
        prop: "no",
        sortable: true,
        display: false,
        formatter: (val) => {
          return ((val.startNo ? val.startNo : '') + '-' + (val.endNo ? val.endNo : ''))
        },
        span: 24,
        overHidden:true,
      },
      {
        label: "领取人",
        prop: "recipient",
        search: true,
        sortable: true,
        placeholder: '请输入',
        span: 24,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:86,
        overHidden:true,
      },
      {
        label: "领取时间",
        prop: "recipientDatetime",
        placeholder: '请选择',
        sortable: true,
        display: true,
        type: 'datetime',
        defaultTime:defaultTime,
        format:'yyyy-MM-dd HH:mm:ss',
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        span: 24,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "change",
          },
        ],
        minWidth:100,
        overHidden:true,
      },

      {
        label: "经办人",
        prop: "operator",
        sortable: true,
        display: false,
        minWidth:86,
        overHidden:true,
      },
      {
        label: "经办时间",
        prop: "agentDatetime",
        sortable: true,
        display: false,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "领取/经办日期",
        span: 24,
        searchLabelWidth: 110,
        prop: "searchDate",
        type: 'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange: true,
        search: true,
        sortable: true,
        hide: true,
        display: false,
        showColumn: false,
        overHidden:true,
      },
      {  //开始票号
        label: "票号",
        prop: "startNo",
        span: 14,
        hide: true,
        showColumn: false,
        placeholder: '开始票号',
        maxlength: 20,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        overHidden:true,
      },
      {  // -
        label: "",
        labelWidth: 0,
        span: 2,
        prop: "line",
        hide: true,
        showColumn: false,
      },
      {
        label: "",
        labelWidth: 0,
        prop: "endNo",
        span: 8,
        hide: true,
        showColumn: false,
        maxlength: 20,
        placeholder: '结束票号',
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
    ],
  };
}

