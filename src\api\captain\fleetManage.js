import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/driverfleet/page',
        method: 'get',
        params: query
    })
}
export function putObj(obj) {
    return request({
        url: '/chain/driverfleet/team',
        method: 'put',
        data: obj
    })
}
export function addObj(obj) {
    return request({
        url: '/chain/driverfleet/team',
        method: 'post',
        data: obj
    })
}

