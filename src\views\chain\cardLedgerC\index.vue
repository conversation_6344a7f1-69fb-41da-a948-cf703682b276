<template>
  <div class="cardLedgerC">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <template slot="menu" slot-scope="scope" class="menuSlot">
          <el-button
            type="text"
            v-if="permissions['chain:cardLedgerC:detail']&&scope.row.waybillCount>0"
            icon="el-icon-view"
            size="small"
            plain
            @click="detail(scope.row, scope.index)">
            详情</el-button>
          <el-button type="text" v-if="permissions['chain:cardLedgerC:freeze']"
            icon="el-icon-view" size="small" plain @click="updateStatus(scope.row)">
            {{scope.row.status==9?'解禁':'冻结'}}</el-button>
        </template>
        <template slot="parentCardNoHeader" slot-scope="{ row }">
          <span>绑定B卡</span>
        </template>
        <template slot="captainNameHeader" slot-scope="{ row }">
          <span>B卡车队长</span>
        </template>
        <template slot="menuLeft" slot-scope="{ row }">
          <div class="headerContent">
              <el-button size="small" style="margin-right: 20px;margin-bottom: 0;"  icon="el-icon-download" type="primary" @click="exOut">
                导出
              </el-button>
              <div>
              C卡总数:<span class="blue">{{countForm.allCount}}</span>张
               <span class="label">激活数:</span><span class="blue">{{countForm.activateCount}}</span>张
                <span class="label">未激活数:</span><span class="blue">{{countForm.unActivateCount}}</span>张
                 <span class="label">已作废:</span><span class="blue">{{countForm.voidCount}}</span>张
              </div>
          </div>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import { updateStatus } from "@/api/chain/cardLedgerA";
import { getPage,getCCardAccountCount } from "@/api/chain/cardLedgerC";
import { tableOption } from "@/const/crud/chain/cardLedgerC";
import { mapGetters } from "vuex";
import { exportOut } from "@/util/down.js";

export default {
  name: "cardLedgerC",
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: 'binding_date', //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      countForm:{}
    };
  },
  created() {},
  mounted: function () {
    this.getCCardAccountCount()
  },
  computed: {
    ...mapGetters(["permissions", "tagList"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:cardLedgerC:add"] ? true : false,
        delBtn: this.permissions["chain:cardLedgerC:del"] ? true : false,
        editBtn: this.permissions["chain:cardLedgerC:edit"] ? true : false,
        viewBtn: this.permissions["chain:cardLedgerC:get"] ? true : false,
      };
    },
  },
  methods: {
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.bindingDateStart = params.searchDate[0];
          params.bindingDateEnd = params.searchDate[1];
          delete params.searchDate;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    childView(row){
      let tag = this.findTag("/cardLedgerC/index").tag;
      if (tag) {
        this.$store.commit("DEL_TAG", tag);
      }
      this.$router.push({
        path: "/cardLedgerC/index",
        query: { info: JSON.stringify(row) },
      });
    },
    detail(row) {
      let tag = this.findTag("/chain/business/companywaybill2").tag;
      if (tag) {
        this.$store.commit("DEL_TAG", tag);
      }
      this.$router.push({
        path: "/chain/business/companywaybill2",
        query: { statements: row.cardNo },
      });
    },
    findTag(url) {
      let tag, key;
      this.tagList.map((item, index) => {
        if (item.value.includes(url)) {
          tag = item;
          key = index;
        }
      });
      return { tag: tag, key: key };
    },
    getCCardAccountCount(){
      getCCardAccountCount({}).then(res=>{
        this.countForm = res.data.data
      })
    },
    exOut () {
      let params = Object.assign({}, this.paramsSearch)
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.bindingDateStart = params.searchDate[0];
          params.bindingDateEnd = params.searchDate[1];
          delete params.searchDate;
        }
      }
      let url = '/chain/companycard/getCCardAccountInfoExport'
      exportOut(params, url, 'C卡台账','post');
    },
    updateStatus(row){
      this.tableLoading = true
      updateStatus({id:row.id},row.status==9?2:1).then(res=>{
        this.tableLoading = false
        this.$message.success('操作成功')
        this.getPage(this.page);
      }).catch(()=>{
        this.tableLoading = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.headerContent{
  display: flex;
  align-items: center;
  .label{
    margin-left: 8px;
  }
  .blue{
    color: #409eff;
  }
}
</style>
