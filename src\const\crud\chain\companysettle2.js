export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  // stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true, // 显示搜索字段
  // excelBtn: true,
  // printBtn: true,
  labelWidth: 150,
  searchLabelWidth:100,
  defaultSort: {
    prop: "updateDatetime",
    order: "descending",
  },
  // viewBtn: true,
  searchMenuSpan: 16,
  menuWidth:200,
  selection:true,
  selectable:(row)=>{
    return !row.paymentNo
  },
  column: [
    {
      label: "结算单号",
      prop: "settleNo",
      searchOrder: 1,
      search: true,
      rules: [
        {
          required: true,
          message: "请输入结算单号",
          trigger: "blur",
        },
        {
          max: 50,
          message: "长度在不能超过50个字符",
        },
      ],
      minWidth:175,
      overHidden:true,
    },
    {
      label: "支付单号",
      prop: "paymentNo",
      search:true,
      minWidth:160,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "项目名称",
      prop: "projectInfoId",
      type: "select", // 下拉选择
      search: true,
      searchOrder: 2,
      props: {
        label: "projectName",
        value: "id",
      },
      dicUrl: "/chain/projectinfo/list",
      searchFilterable: true, //是否可以搜索
      filterable: true, //是否可以搜索
      rules: [
        {
          required: true,
          message: "请输入项目信息ID",
          trigger: "blur",
        },
        {
          max: 36,
          message: "长度在不能超过36个字符",
        },
      ],
      minWidth:180,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "结算状态",
      prop: "status",
      type: "select", // 下拉选择
      search: true,
      searchOrder: 4,
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=settle_status',
      rules: [
        {
          required: true,
          message: "请选择状态",
          trigger: "blur",
        },
        {
          max: 1,
          message: "长度在不能超过1个字符",
        },
      ],
      minWidth:96,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "审核人",
      prop: "approveName",
      minWidth:96,
      overHidden:true,
    },
    {
      label: "结算申请人",
      prop: "agentName",
      search: true,
      searchOrder: 3,
      rules: [
        {
          required: true,
          message: "请输入申请人：对应agent_info.id",
          trigger: "blur",
        },
        {
          max: 36,
          message: "长度在不能超过36个字符",
        },
      ],
      minWidth:106,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "申请时间",
      prop: "applyDatetime",
      sortable:"custom",
      type: "datetime",
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:140,
      overHidden:true,
    },
    {
      label: "承运人",
      prop: "payeeName",
      search: true,
      minWidth:80,
      overHidden:true,
    },
    {
      label: "运单数量",
      prop: "waybillCnt",
      minWidth:80,
      overHidden:true,
    },
    {
      label: "已核算运单数量",
      prop: "settleCnt",
      minWidth:110,
      overHidden:true,
    },
    {
      label: "预设价",
      prop: "payeePrice",
      minWidth:80,
      overHidden:true,
    },
    {
      label: "结算价",
      prop: "settleAmt",
      minWidth:80,
      overHidden:true,
    },
    // {
    //   label: "税费",
    //   prop: "taxFee",
    //   minWidth:80,
    //   overHidden:true,
    // },
    // {
    //   label: "税率",
    //   prop: "taxPoint",
    //   formatter: (val) => {
    //     return val.taxPoint && val.taxPoint + "%";
    //   },
    //   minWidth:80,
    //   overHidden:true,
    // },
    // {
    //   label: "实付价",
    //   prop: "realPayPrice",
    //   minWidth:80,
    //   overHidden:true,
    // },
    {
      label: "付款人",
      prop: "moneyName",
      search: true,
      minWidth:80,
      overHidden:true,
    },
    {
      label: "付款时间",
      prop: "moneyDatetime",
      type: "datetime",
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:140,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "交卡人",
      prop: "giveCardUserId",
      search: true,
      minWidth:86,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "交卡回执单号",
      prop: "giveCardNo",
      search: true,
      minWidth:160,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "备注",
      prop: "remark",
      minWidth:160,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "拆单备注",
      prop: "staffRemark",
      search: true,
      minWidth:120,
      overHidden:true,
      sortable:"custom",
    },
    {
      label: "核算时间",
      prop: "settleDatetime",
      searchRange:true,
      search: true,
      type: "datetime",
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:140,
      overHidden:true,
      sortable:"custom",
    },
  ],
};
