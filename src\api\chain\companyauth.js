import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companyauth/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companyauth',
        method: 'post',
        data: obj,
        toast:true
    })
}
export function companyReg(obj) {
    return request({
        url: '/chain/common/companyReg',
        method: 'post',
        data: obj,
        toast:true
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companyauth/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companyauth/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companyauth',
        method: 'put',
        data: obj
    })
}

export function uploadImage(formData) {

    return request({
        url: '/upms/file/upload?fileType=image&dir=authPic/',
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data'},
        data:formData
    })
}
export function uploadImage2(formData) {

    return request({
        url: '/chain/common/upload?fileType=image&dir=authPic/',
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data'},
        data:formData
    })
}
export function createAssetsMatchCompanyInfoZC(obj) {
  return request({
      url: '/chain/resourcesMatchmaking/createAssetsMatchCompanyInfoZC',
      method: 'post',
      data: obj
  })
}
