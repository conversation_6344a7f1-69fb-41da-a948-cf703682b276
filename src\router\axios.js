/**
 * 全站http配置
 *
 * header参数说明
 * serialize是否开启form表单提交
 * isToken是否需要token
 */
import axios from 'axios'
import store from '@/store'
import router from '@/router/router'
import { serialize } from '@/util/util'
import NProgress from 'nprogress' // progress bar
import errorCode from '@/const/errorCode'
import { Message, MessageBox } from 'element-ui'
import 'nprogress/nprogress.css'
import { switchDemon } from '@/config/env'

axios.defaults.timeout = 1000*60*5
// 返回其他状态吗
axios.defaults.validateStatus = function (status) {
  return status // 默认的
}
// 跨域请求，允许保存cookie
axios.defaults.withCredentials = true
// NProgress Configuration
NProgress.configure({
  showSpinner: false
})

// HTTPrequest拦截
axios.interceptors.request.use(config => {
  NProgress.start() // start progress bar
  const isToken = (config.headers || {}).isToken === false
  let token = store.getters.access_token
  let user_md5 = store.getters.user_md5
  if (token && !isToken) {
    config.headers['Authorization'] = 'Bearer ' + token// token
    config.headers['AuthorizationMD5'] =  user_md5// user_md5
  }
  return config
}, error => {
  return Promise.reject(error)
})


// HTTPresponse拦截
axios.interceptors.response.use(res => {
  NProgress.done()
  const status = Number(res.status) || 200
  const message = res.data.msg || errorCode[status] || errorCode['default']
  if (status === 401) {
    Message({
      message: '登录过期，请重新登录',
      type: 'error'
    })
    store.dispatch('FedLogOut').then(() => {
      router.push({ path: '/login' })
    })
    return
  }

  if (status !== 200 || res.data.code === 1) {
    if (res.config.toast) {
      return Promise.reject(message)
    } else {
      Message({
        message: message,
        type: 'error'
      })
      return Promise.reject(new Error(message))
    }

  }

  return res
}, error => {
  if (error.message.includes('timeout')) {      
    Message({
        message: '请求超时,请稍后重试',
        type: 'error',
        duration: 5 * 1000
    })
  } 
  NProgress.done()
  return Promise.reject(new Error(error))
})

export default axios
