<template>
  <div class="execution">
      <basic-container>
          <avue-crud ref="crud"
                     :page.sync="page"
                     :data="tableData"
                     :permission="permissionList"
                     :table-loading="tableLoading"
                     :option="tableOption"
                     v-model="form"
                     @on-load="getPage"
                     @row-save="handleSave"
                     @refresh-change="refreshChange"
                     @sort-change="sortChange"
                     @search-change="searchChange">
                     <template slot="menuLeft" slot-scope="{ row }">
                        <el-button
                            icon="el-icon-document-add"
                            v-if="permissions['chain:garbagecustomersales:sale']"
                            size="small"
                            type="primary"
                            @click="add(1)"
                          >
                            售票
                        </el-button>
                        <el-button
                            icon="el-icon-document-remove"
                            v-if="permissions['chain:garbagecustomersales:refund']"
                            size="small"
                            type="primary"
                            @click="add(2)"
                          >
                            退票
                        </el-button>
                        <el-button
                            icon="el-icon-download"
                            v-if="permissions['chain:garbagecustomersales:excel']"
                            size="small"
                            type="primary"
                            @click="exOut"
                          >
                            导出
                        </el-button>
                      </template>
                      <!-- <template slot-scope="{disabled,size}" slot="garbageCustomerNameForm">
                        <el-input v-if="type==1" :size="size" placeholder="请输入 客户名称" v-model="form.garbageCustomerName"></el-input>
                      </template> -->
                      <template slot-scope="{disabled,size}" slot="operateDateLabel">
                        <span>{{type==2?'退票日期':'销售日期'}}</span>
                      </template>
                      <template slot-scope="{disabled,size}" slot="operatorLabel">
                        <span>{{type==2?'退票人':'销售人'}}</span>
                      </template>
                      <template slot-scope="{disabled,size}" slot="lineForm">
                        -
                      </template>
                      <template slot-scope="{disabled,size}" slot="beginNoLabel">
                        <span>{{type==2?'退票号':'销售号'}}</span>
                      </template>
                      <template slot-scope="{disabled,size}" slot="numLabel">
                        <span>{{type==2?'退票数':'销售数'}}</span>
                      </template>
          </avue-crud>
      </basic-container>
  </div>
</template>

<script>
  import {getPage,addObj,getCustomerList,getProjectInfoList} from '@/api/chain/garbagecustomersales'
  import {tableOption} from '@/const/crud/chain/garbagecustomersales'
  import {mapGetters} from 'vuex'
  import { expotOut } from "@/util/down.js";
  export default {
      name: 'garbagecustomersales',
      data() {
          return {
              form: {
              },
              tableData: [],
              page: {
                  total: 0, // 总页数
                  currentPage: 1, // 当前页数
                  pageSize: 20, // 每页显示多少条
                  ascs: [],//升序字段
                  descs: []//降序字段
              },
              paramsSearch: {},
              tableLoading: false,
              type:1,
              tableOption: tableOption(this),
              customerList:[],
          }
      },
      created() {
      },
      mounted: function () {
      },
      computed: {
          ...mapGetters(['permissions']),
          permissionList() {
              return {
                  addBtn: this.permissions['chain:garbagecustomersales:add'] ? true : false,
                  delBtn: this.permissions['chain:garbagecustomersales:del'] ? true : false,
                  editBtn: this.permissions['chain:garbagecustomersales:edit'] ? true : false,
                  viewBtn: this.permissions['chain:garbagecustomersales:get'] ? true : false,
              };
          }
      },
      methods: {
          searchChange(params,done) {
              params = this.filterForm(params)
              this.paramsSearch = params
              this.page.currentPage = 1
              this.getPage(this.page, params)
              done()
          },
          sortChange(val) {
              let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
              if (val.order == 'ascending') {
                  this.page.descs = []
                  this.page.ascs = prop
              } else if (val.order == 'descending') {
                  this.page.ascs = []
                  this.page.descs = prop
              } else {
                  this.page.ascs = []
                  this.page.descs = []
              }
              this.getPage(this.page)
          },
          getPage(page, params) {
              this.tableLoading = true
              if (params) {
                if (params.hasOwnProperty("searchDate")) {
                  params.startDate = params.searchDate[0];
                  params.endDate = params.searchDate[1];
                  delete params.searchDate;
                }
              }
              getPage(Object.assign({
                  current: page.currentPage,
                  size: page.pageSize,
                  descs: this.page.descs,
                  ascs: this.page.ascs,
              }, params, this.paramsSearch)).then(response => {
                  this.tableData = response.data.data.records
                  this.page.total = response.data.data.total
                  this.page.currentPage = page.currentPage
                  this.page.pageSize = page.pageSize
                  this.tableLoading = false
              }).catch(() => {
                  this.tableLoading = false
              })
          },
          /**
             * @title 数据添加
             * @param row 为当前的数据
             * @param done 为表单关闭函数
             *
             **/
             handleSave: function (row, done, loading) {
                console.log(row);
                row.type = this.type
                if(this.type==1){
                  row.projectInfoId = row.projectInfoId2
                }
                row.qty = row.num
                if(this.type == 2 &&row.qty>row.num){
                  this.$message.error('退票数不能大于剩余数')
                  loading()
                  return false
                }
                if(row.endNo.length!=row.beginNo.length){
                  this.$confirm("开始票号与结束票号位数不一致，会导致销售数量增多，确认继续提交？", "提示", {
                      confirmButtonText: "确定",
                      cancelButtonText: "取消",
                      type: "warning",
                    }).then((data) => {
                      addObj(row).then(response => {
                            this.$message({
                                showClose: true,
                                message: '操作成功',
                                type: 'success'
                            })
                            done()
                            this.updateDic()
                            this.getPage(this.page)
                        }).catch(() => {
                            loading()
                          })
                        })
                        .catch(function (err) {
                          loading()
                      });
                }else{
                  addObj(row).then(response => {
                      this.$message({
                          showClose: true,
                          message: '操作成功',
                          type: 'success'
                      })
                      done()
                      this.updateDic()
                      this.getPage(this.page)
                  }).catch(() => {
                      loading()
                  })
                }
            },
          /**
           * 刷新回调
           */
          refreshChange(page) {
              this.getPage(this.page)
          },
          add(val){
            this.type = val
            if(val==2){
              this.$refs.crud.tableOption.column[1].display= false
              this.$refs.crud.tableOption.column[2].display= false
              this.$refs.crud.tableOption.column[3].display= true
              this.$refs.crud.tableOption.column[4].display= true
              this.$refs.crud.tableOption.column[5].display= false
              this.$set(this.tableOption,'addTitle','退票')
              console.log(this.$refs.crud.tableOption);
            }else{
              this.$refs.crud.tableOption.column[1].display= true
              this.$refs.crud.tableOption.column[2].display= true
              this.$refs.crud.tableOption.column[3].display= false
              this.$refs.crud.tableOption.column[4].display= false
              this.$refs.crud.tableOption.column[5].display= true
              this.$set(this.tableOption,'addTitle','售票')
              console.log(this.$refs.crud.tableOption);
            }
            this.$refs.crud.rowAdd()
          },
          exOut(){
            let params = Object.assign({},this.paramsSearch)
            if (params) {
                if (params.hasOwnProperty("searchDate")) {
                params.startDate = params.searchDate[0];
                params.endDate = params.searchDate[1];
                delete params.searchDate;
              }
            }
            let url = '/chain/garbagecustomersales/exportExcel'
            expotOut( params,url,'内部泥尾票销售');
          },
          updateDic(){
            getProjectInfoList().then(res=>{
              this.$refs.crud.updateDic("projectInfoId",res.data.data)
            })
            getCustomerList().then(res=>{
              this.$refs.crud.updateDic("garbageCustomerId",res.data.data)
            })
          }
      }
  }
</script>

<style lang="scss" scoped>
</style>
