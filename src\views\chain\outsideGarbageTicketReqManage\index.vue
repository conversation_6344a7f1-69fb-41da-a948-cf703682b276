<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="menuLeft"
                  slot-scope="{ row }">
          <el-button icon="el-icon-download"
                     v-if="permissions['chain:outsideGarbageTicketReqManage:excel']"
                     size="small"
                     type="primary"
                     @click="exOut">
            导出
          </el-button>
        </template>
        <template slot-scope="{ row,index }"
                  slot="menu">
          <el-button type="text"
                     v-if="permissions['chain:outsideGarbageTicketReqManage:check']&&row.status==1"
                     icon="el-icon-check"
                     size="small"
                     plain
                     @click="handleEdit(row, index)">审核</el-button>
          <el-button type="text"
                     v-if="permissions['chain:outsideGarbageTicketReqManage:check']&&row.status==1"
                     icon="el-icon-remove-outline"
                     size="small"
                     plain
                     @click="audit(row,2)">驳回</el-button>
        </template>
        <template slot="bindTicketNoForm"
                  slot-scope="scope">
          <div>
            <div style="
                  border: 1px solid #d9d9d9;
                  padding: 0px 10px;
                  border-radius: 4px;
                  line-height: 30px;
                  min-height: 32px;
                  max-height: 130px;
                  overflow-y: auto;
                  cursor: pointer;
                ">
              <span style="color:#b4bccc"
                    v-if="ticketList.length==0">
                请选择泥尾票号
              </span>
              <span v-else>
                <el-tag closable
                        v-for="(item, index) in ticketList"
                        size="mini"
                        type='info'
                        @close="preventDefault(item,index)"
                        :key="index"
                        style="margin-left:4px">
                  {{ item.ticketNo }}
                </el-tag>
              </span>
              <el-button size="small"
                         type="text"
                         style="position: absolute;right: 8px;bottom:0px;cursor: pointer;"
                         @click.stop="changeTicket">选择</el-button>
            </div>
          </div>
        </template>
      </avue-crud>
    </basic-container>
     <!-- 选择票号 -->
     <selectTicket
        v-if="visible3"
        :visible.sync="visible3"
        :list="ticketList"
        :info="info"
        @submit="changeTicketList"
      ></selectTicket>
  </div>
</template>

<script>
import { getPage, auditCompanyTicketProjectApply, queryInventoryByGarbageId, queryProjectInventoryByGarbageId } from '@/api/chain/outsideGarbageTicketReqManage'
import { tableOption } from '@/const/crud/chain/outsideGarbageTicketReqManage'
import { mapGetters } from 'vuex'
import { expotOut } from "@/util/down.js";
import selectTicket from './selectTicket';
import { getTicketNoPage } from '@/api/chain/outsideGarbageTicketPurchase'

export default {
  name: 'outsideGarbageTicketReqManage',
  components:{
    selectTicket
  },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: 'apply_datetime'//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption(this),
      ticketList:[],
      visible3:false,
      info:{},
    }
  },
  created () {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList () {
      return {
        addBtn: this.permissions['chain:outsideGarbageTicketReqManage:add'] ? true : false,
        delBtn: this.permissions['chain:outsideGarbageTicketReqManage:del'] ? true : false,
        editBtn: this.permissions['chain:outsideGarbageTicketReqManage:edit'] ? true : false,
        viewBtn: this.permissions['chain:outsideGarbageTicketReqManage:get'] ? true : false,
        excelBtn: this.permissions['chain:outsideGarbageTicketReqManage:excel'] ? true : false,
      };
    }
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage (page, params) {
      this.tableLoading = true
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
        if (params.hasOwnProperty("searchDate2")) {
          params.checkTakerStartDate = params.searchDate2[0];
          params.checkTakerEndDate = params.searchDate2[1];
          delete params.searchDate2;
        }
      }
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      let param = {
        id: row.id,
        status: '2',
        auditsQty: row.auditsQty,
        bindTicketNo:row.bindTicketNo
      }
      if(row.type==2){
        //退票要备注
        param.auditRemark = row.auditRemark
      }
      auditCompanyTicketProjectApply(param).then(res => {
        this.$message({
          showClose: true,
          message: "操作成功",
          type: "success",
        });
        done();
        this.getPage(this.page)
      }).catch(err => {
        loading();
      })
      // putObj(row)
      //   .then((response) => {
      //     this.$message({
      //       showClose: true,
      //       message: "修改成功",
      //       type: "success",
      //     });
      //     done();
      //     this.getPage(this.page);
      //   })
      //   .catch(() => {
      //     loading();
      //   });
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page)
    },
    handleEdit (row, index) {
      let data = {
        garbageId: row.garbageId,
        soilType: row.soilType,
      }
      this.info = Object.assign({},row)
      row.auditsQty = 0
      this.ticketList = []
      if(row.type==2){
        //查看是否有申请的退款票号，是否需要反写
        getTicketNoPage({garbageId: data.garbageId,soilType: data.soilType,size:-1,ticketNo:row.bindTicketNo}).then(res=>{
          this.ticketList = res.data.data.records||[]
          row.auditsQty = row.qty
        })
      }

      this.tableLoading = true
      setTimeout(() => {
        this.tableLoading = false
      }, 5000)
      let auditRemark = this.findObject(this.tableOption.column, 'auditRemark');

      this.$set(auditRemark, 'display', row.type == 2)
      if (row.source == 2 || row.type == 2) {
        //项目
        data.projectInfoId = row.type == 2 ? row.projectInfoId : row.sourceId
        queryProjectInventoryByGarbageId(data).then(res => {
          this.tableLoading = false
          row.num = res.data.data
          // row.auditsQty = row.qty
          this.$refs.crud.rowEdit(row, index);
        })
      } else {
        //公司
        queryInventoryByGarbageId(data).then(res => {
          this.tableLoading = false
          row.num = res.data.data
          // row.auditsQty = row.qty
          this.$refs.crud.rowEdit(row, index);
        })
      }
    },
    exOut () {
      let params = Object.assign({}, this.paramsSearch)
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
        if (params.hasOwnProperty("searchDate2")) {
          params.checkTakerStartDate = params.searchDate2[0];
          params.checkTakerEndDate = params.searchDate2[1];
          delete params.searchDate2;
        }
      }
      let url = '/chain/companyticketprojectapply/applyPageExportExcel'
      expotOut(params, url, '外部泥尾票申请记录');
    },
    audit (row, type) {
      //只有驳回2了
      this.$confirm(type == 1 ? "确认审核此记录?" : "确认驳回此记录?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.tableLoading = true
        auditCompanyTicketProjectApply({ id: row.id, status: type == 1 ? '2' : '3' }).then(res => {
          this.tableLoading = false
          this.getPage(this.page)
        }).catch(err => {
          this.tableLoading = false
        })
      })
        .catch(function (err) { });
    },
    changeTicketList(value) {
      let arr = JSON.parse(value);
      this.ticketList = arr
      this.visible3 = false
      this.form.auditsQty = this.ticketList.length
      this.form.bindTicketNo = this.ticketList&&this.ticketList.map(item=>item.ticketNo).join(",")||''
    },
    preventDefault(item, index) {
      this.ticketList.splice(index, 1);
      this.form.auditsQty = this.ticketList.length
      this.form.bindTicketNo = this.ticketList&&this.ticketList.map(item=>item.ticketNo).join(",")||''
    },
    changeTicket(){
      this.visible3 = true
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
