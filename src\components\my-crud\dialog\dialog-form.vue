<template>
  <component :is="dialogType"
             lock-scroll
             :destroy-on-close="crud.tableOption.dialogDestroy"
             :wrapperClosable="crud.tableOption.dialogClickModal"
             :direction="direction"
             v-dialogDrag="vaildData(crud.tableOption.dialogDrag,config.dialogDrag)"
             :class="['avue-dialog','my-dialog','avue-crud__dialog',{'avue-dialog--fullscreen':fullscreen},{'avue-dialog--none':isView}]"
             :custom-class="crud.tableOption.dialogCustomClass"
             modal-append-to-body
             append-to-body
             :top="dialogTop"
             :title="dialogTitle"
             :close-on-press-escape="crud.tableOption.dialogEscape"
             :close-on-click-modal="vaildData(crud.tableOption.dialogClickModal,false)"
             :modal="crud.tableOption.dialogModal"
             :show-close="crud.tableOption.dialogCloseBtn"
             :visible.sync="boxVisible"
             v-bind="params"
             :before-close="hide"
             @opened="handleOpened">
    <div slot="title"
         class="avue-crud__dialog__header">
      <span class="el-dialog__title">{{dialogTitle}}</span>
      <div class="avue-crud__dialog__menu">
        <i @click="handleFullScreen"
           :class="fullscreen?'el-icon-news':'el-icon-full-screen'"
           class="el-dialog__close"></i>
      </div>
    </div>
    <avue-form v-model="crud.tableForm"
               ref="tableForm"
               v-if="boxVisible"
               @change="handleChange"
               @submit="handleSubmit"
               @reset-change="hide"
               @tab-click="handleTabClick"
               @error="handleError"
               v-bind="$uploadFun({},crud)"
               :option="option">
      <template slot-scope="scope"
                v-for="item in crud.formSlot"
                :slot="getSlotName(item)">
        <slot :name="item"
              v-bind="Object.assign(scope,{
                    type:boxType,
                    row:crud.tableForm
                  }) "></slot>
      </template>
    </avue-form>
    <span class="avue-dialog__footer"
          :class="'avue-dialog__footer--'+dialogMenuPosition" v-if="!isView">
      <el-button v-if="vaildData(option.submitBtn,true)"
                 @click="submit"
                 :disabled="disabled"
                 :size="crud.isMediumSize"
                 :icon="option.submitIcon"
                 type="primary">{{option.submitText}}</el-button>
      <el-button v-if="vaildData(option.emptyBtn,true)"
                 @click="reset"
                 :disabled="disabled"
                 :size="crud.isMediumSize"
                 :icon="option.emptyIcon">{{option.emptyText}}</el-button>
      <slot name="menuForm"
            :disabled="disabled"
            :size="crud.isMediumSize"
            :type="boxType"></slot>
    </span>
  </component>
</template>

<script>
import config from "../config";
import { filterParams,filterDefaultParams } from '@/util/util'
export default{
  name: "crud",
  inject: ["crud"],
  data () {
    return {
      disabled: false,
      config: config,
      boxType: "",
      fullscreen: false,
      size: null,
      boxVisible: false
    };
  },
  props: {
    value: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    option () {
      let option = this.deepClone(this.crud.tableOption);
      console.log(option,'option');
      option.boxType = this.boxType;
      option.column = this.deepClone(this.crud.propOption);
      option.menuBtn = false;
      if (this.isAdd) {
        option.submitBtn = option.saveBtn;
        option.submitText = this.vaildData(this.crud.tableOption['saveBtn' + 'Text'], '保存')
        option.submitIcon = this.crud.getBtnIcon('saveBtn')
      } else if (this.isEdit) {
        option.submitBtn = option.updateBtn;
        option.submitText = this.vaildData(this.crud.tableOption['updateBtn' + 'Text'], '修改')
        option.submitIcon = this.crud.getBtnIcon('updateBtn')
      } else if (this.isView) {
        option.detail = true;
      }
      option.emptyBtn = option.cancelBtn;
      option.emptyText = this.vaildData(this.crud.tableOption['cancelBtn' + 'Text'], '取消')
      option.emptyIcon = this.crud.getBtnIcon('cancelBtn')
      //不分组的表单不加载字典
      // if (!this.crud.isGroup) {
      //   option.dicFlag = false;
      //   option.dicData = this.crud.DIC;
      // }
      if (!this.validatenull(option.dicFlag)) {
        option.column.forEach(ele => {
          ele.boxType = this.boxType;
          ele.dicFlag = ele.dicFlag || option.dicFlag
        })
      }
      return option;
    },
    isView () {
      return this.boxType === 'view'
    },
    isAdd () {
      return this.boxType === 'add'
    },
    isEdit () {
      return this.boxType === 'edit'
    },
    direction () {
      return this.crud.tableOption.dialogDirection
    },
    width () {
      return this.vaildData(this.crud.tableOption.dialogWidth + '', this.crud.isMobile ? '100%' : config.dialogWidth + '')
    },
    dialogType () {
      return this.isDrawer ? 'elDrawer' : 'elDialog'
    },
    dialogTop () {
      return (!this.isDrawer && !this.fullscreen) ? this.crud.tableOption.dialogTop : '0'
    },
    isDrawer () {
      return this.crud.tableOption.dialogType === 'drawer';
    },
    params () {
      return this.isDrawer ?
        {
          size: this.fullscreen ? '100%' : this.width,
          direction: this.crud.tableOption.dialogDirection
        } : {
          width: this.width,
          fullscreen: this.fullscreen
        };
    },
    dialogTitle () {
      const key = `${this.boxType}`;
      if (!this.validatenull(this.boxType)) {
        let obj = {
          editTitle: '编 辑',
          copyTitle: '复 制',
          addTitle: '新 增',
          viewTitle: '查 看',
        }
        return this.crud.tableOption[key + 'Title'] || obj[`${key}Title`]
      }
    },
    dialogMenuPosition () {
      return this.crud.option.dialogMenuPosition || 'right'
    }
  },
  methods: {
    submit () {
      this.$refs.tableForm.submit()
    },
    reset () {
      this.$refs.tableForm.resetForm()
    },
    getSlotName (item) {
      return item.replace('Form', '')
    },
    handleOpened () {
      this.$nextTick(() => {
        ['clearValidate', 'validate', 'resetForm'].forEach(ele => {
          this.crud[ele] = this.$refs.tableForm[ele]
        })
      })
    },
    handleChange () {
      this.crud.$emit('input', this.crud.tableForm)
      this.crud.$emit('change', this.crud.tableForm)
    },
    handleTabClick (tab, event) {
      this.crud.$emit('tab-click', tab, event)
    },
    handleFullScreen () {
      if (this.isDrawer) {
        if (this.validatenull(this.size)) {
          this.size = '100%'
        } else {
          this.size = ''
        }
      }
      if (this.fullscreen) {
        this.fullscreen = false;
      } else {
        this.fullscreen = true;
      }
    },
    handleError (error) {
      this.crud.$emit('error', error)
    },
    handleSubmit (form, hide) {
      this.disabled = true
      if (this.isAdd) {
        this.rowSave(hide);
      } else if (this.isEdit) {
        this.rowUpdate(hide);
      }
    },
    hideLoading(){
      this.$refs.tableForm.hide()
      this.disabled = false
    },
    // 保存
    rowSave (hide) {
      this.crud.$emit(
        "row-save",
        filterDefaultParams(this.crud.tableForm, this.crud.tableOption.translate),
        this.closeDialog,
        this.hideLoading
      );
    },
    // 更新
    rowUpdate (hide) {
      this.crud.$emit(
        "row-update",
        filterDefaultParams(this.crud.tableForm, this.crud.tableOption.translate),
        this.crud.tableIndex,
        this.closeDialog,
        this.hideLoading
      );
    },
    closeDialog (row) {
      row = this.deepClone(row);
      const callback = () => {
        if (this.isEdit) {
          let { parentList, index } = this.crud.findData(row[this.crud.rowKey])
          if (parentList) {
            parentList.splice(index, 1);
            parentList.splice(index, 0, row);
          }
        } else if (this.isAdd) {
          let { item } = this.crud.findData(row[this.crud.rowParentKey])
          if (item) {
            if (!item[this.crud.childrenKey]) {
              item[this.crud.childrenKey] = []
              item[this.crud.hasChildrenKey] = true
            }
            item[this.crud.childrenKey].push(row)
          } else {
            this.crud.list.push(row);
          }
        }
      }
      if (row) callback();
      this.hide();
    },
    // 隐藏表单
    hide (done) {
      const callback = () => {
        done && done();
        this.disabled = false
        this.crud.tableIndex = -1;
        this.boxVisible = false;
        Object.keys(this.crud.tableForm).forEach(ele => {
          this.$delete(this.crud.tableForm, ele);
        })
      };
      if (typeof this.crud.beforeClose === "function") {
        this.crud.beforeClose(callback, this.boxType);
      } else {
        callback();
      }
    },
    // 显示表单
    show (type) {
      this.boxType = type;
      const callback = () => {
        this.fullscreen = this.crud.tableOption.dialogFullscreen
        this.boxVisible = true;
      };
      if (typeof this.crud.beforeOpen === "function") {
        this.crud.beforeOpen(callback, this.boxType);
      } else {
        callback();
      }
    }
  }
};
</script>
<style lang="scss">
.my-dialog .el-dialog__body{padding:30px 20px 20px;margin-bottom:50px}
.avue-dialog--none .el-dialog__body{margin-bottom:0}
.my-dialog .el-dialog .el-dialog__body{-webkit-box-flex:1;-ms-flex:1;flex:1;overflow:hidden;overflow-y:auto}
.my-dialog .el-drawer,.my-dialog .el-drawer__body{-webkit-box-flex:1;-ms-flex:1;flex:1;overflow:auto}
.my-dialog .el-drawer__body{padding:30px 10px 60px 30px}
.my-dialog .avue-dialog__footer{display:block;padding:10px 16px;-webkit-box-sizing:border-box;box-sizing:border-box;border-top:1px solid #f0f0f0;width:100%;position:absolute;left:0;bottom:0;background-color:#fff;text-align:right}
</style>
