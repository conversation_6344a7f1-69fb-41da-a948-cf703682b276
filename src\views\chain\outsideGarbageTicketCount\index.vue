<template>
  <div class="execution">
      <basic-container>
          <avue-crud ref="crud"
                     :data="tableData"
                     :permission="permissionList"
                     :table-loading="tableLoading"
                     :option="tableOption"
                     :span-method="spanMethod"
                     v-model="form"
                     @on-load="getPage"
                     @refresh-change="refreshChange"
                     @search-change="searchChange">
                     <template slot="menuLeft" slot-scope="{ row }">
                        <el-button
                            icon="el-icon-download"
                            v-if="permissions['chain:outsideGarbageTicketCount:excel']"
                            size="small"
                            type="primary"
                            :loading="btnLoading"
                            @click="exOut"
                          >
                            导出
                        </el-button>
                        <el-button
                            icon="el-icon-check"
                            v-if="permissions['chain:outsideGarbageTicketCount:rebuild']"
                            size="small"
                            type="primary"
                            :loading="btnLoading"
                            @click="openDialog"
                          >
                            重新生成
                        </el-button>
                      </template>
                      <!-- 本期领票 -->
                      <template slot-scope="{ row,index }" slot="collectTickets">
                        <span style="color:#409eff;cursor:pointer;" @click="toDetail(row,1)" v-if="row.isTotalRow==0&&row.isProjectTotalRow==0">{{row.collectTickets}}</span>
                        <span v-else>{{row.collectTickets}}</span>
                      </template>
                      <!-- 本期用票 -->
                      <template slot-scope="{ row,index }" slot="useTickets">
                        <span style="color:#409eff;cursor:pointer;" @click="toDetail(row,2)" v-if="row.isTotalRow==0&&row.isProjectTotalRow==0">{{row.useTickets}}</span>
                        <span v-else>{{row.useTickets}}</span>
                      </template>
                      <!-- 本期退票 -->
                      <template slot-scope="{ row,index }" slot="refundTickets">
                        <span style="color:#409eff;cursor:pointer;" @click="toDetail(row,3)" v-if="row.isTotalRow==0&&row.isProjectTotalRow==0">{{row.refundTickets}}</span>
                        <span v-else>{{row.refundTickets}}</span>
                      </template>
          </avue-crud>
      </basic-container>
       <!-- 重新生成 -->
       <el-dialog
          width="400px"
          title="选择生成日期"
          center
          :visible.sync="visible"
          :before-close="cancelModal"
          :close-on-click-modal="false">
          <el-date-picker
            v-model="startDate"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width:100%"
            align="right"
            type="date"
            placeholder="请选择重新生成的日期"
            :picker-options="pickerOptions">
          </el-date-picker>
          <span slot="footer" class="dialog-footer">
              <el-button type="primary" :loading="btnLoading" size="small" @click="submitForm"
                >确 认</el-button
              >
              <el-button size="small" @click="cancelModal">取 消</el-button>
            </span>
        </el-dialog>
      <collectTickets v-if="collectVisible" :info="info" :visible.sync="collectVisible"></collectTickets>
      <refundTickets v-if="refundVisible" :info="info" :visible.sync="refundVisible"></refundTickets>
  </div>
</template>

<script>
  import {getPage,calculateTicketMonthCostPrice} from '@/api/chain/outsideGarbageTicketCount'
  import {tableOption} from '@/const/crud/chain/outsideGarbageTicketCount'
  import {mapGetters} from 'vuex'
  import { expotOut } from "@/util/down.js";
  import collectTickets from './collectTickets.vue'
  import useTicketsMonth from './useTicketsMonth.vue'
  import refundTickets from './refundTickets.vue'

  export default {
      name: 'outsideGarbageTicketCount',
      components: {
        collectTickets,
        useTicketsMonth,
        refundTickets,
      },
      data() {
          return {
              form: {},
              tableData: [],
              paramsSearch: {},
              tableLoading: false,
              tableOption: tableOption(this),
              collectVisible:false,
              refundVisible:false,
              info:{},   //info.type 1项目剩余总数  2剩余总数
              pickDate:{},
              btnLoading:false,
              startDate:'',
              visible:false,
              pickerOptions: {
                disabledDate(time) {
                  return time.getTime() > Date.now();
                },
                shortcuts: [{
                  text: '今天',
                  onClick(picker) {
                    picker.$emit('pick', new Date());
                  }
                }, {
                  text: '昨天',
                  onClick(picker) {
                    const date = new Date();
                    date.setTime(date.getTime() - 3600 * 1000 * 24);
                    picker.$emit('pick', date);
                  }
                }, {
                  text: '一周前',
                  onClick(picker) {
                    const date = new Date();
                    date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
                    picker.$emit('pick', date);
                  }
                }]
              },
          }
      },
      created() {
      },
      mounted: function () {
      },
      computed: {
          ...mapGetters(['permissions','tagList']),
          permissionList() {
              return {
                  addBtn: this.permissions['chain:outsideGarbageTicketCount:add'] ? true : false,
                  delBtn: this.permissions['chain:outsideGarbageTicketCount:del'] ? true : false,
                  editBtn: this.permissions['chain:outsideGarbageTicketCount:edit'] ? true : false,
                  viewBtn: this.permissions['chain:outsideGarbageTicketCount:get'] ? true : false,
                  excelBtn: this.permissions['chain:outsideGarbageTicketCount:excel'] ? true : false,
              };
          }
      },
      methods: {
          getPickDate(pick) {
            this.pickDate = pick
          },
          searchChange(params,done) {
              params = this.filterForm(params)
              this.paramsSearch = params
              this.getPage(params)
              done()
          },
          getPage(params={}) {
              this.tableLoading = true
              if (params) {
                if (params.hasOwnProperty("searchDate")) {
                  params.startDate = params.searchDate[0];
                  params.endDate = params.searchDate[1];
                  delete params.searchDate;
                }
              }
              getPage(Object.assign(params, this.paramsSearch)).then(response => {
                  // this.tableData = response.data.data
                  this.assembleData(response.data.data)
                  this.tableLoading = false
              }).catch(() => {
                  this.tableLoading = false
              })
          },
          /**
           * 刷新回调
           */
          refreshChange() {
              this.getPage()
          },
          exOut(){
            let params = Object.assign({},this.paramsSearch)
            if (params) {
                if (params.hasOwnProperty("searchDate")) {
                params.startDate = params.searchDate[0];
                params.endDate = params.searchDate[1];
                delete params.searchDate;
              }
            }
            let url = '/chain/companyticketpurchase/statisticsExportExcel'

            this.btnLoading = true;
            expotOut( params,url,'外部泥尾票统计').then((res) => {
              this.btnLoading = false;
            })
            .catch((err) => {
              this.btnLoading = false;
            });
          },
          toDetail(row,type){
            this.info = {
              startDate:this.paramsSearch.startDate,
              endDate:this.paramsSearch.endDate,
              projectInfoIds:row.projectInfoId,
              garbageIds:row.garbageId,
              soilTypes:row.soilType,
              garbageType:row.garbageType,
            }
            switch (type) {
              case 1:
                this.collectVisible = true
                break;
              case 2:
                let tag = this.findTag('本期用票(按月)').tag;
                console.log(tag)
                if(tag){
                  this.$store.commit("DEL_TAG", tag);
                  this.$router.push({path:'/useTicketsMonth/index',query:{info:JSON.stringify(this.info)}})
                }else{
                  this.$router.push({path:'/useTicketsMonth/index',query:{info:JSON.stringify(this.info)}})
                }
                break;
              case 3:
                this.refundVisible = true
                break;
              default:
                break;
            }
          },
          findTag(label) {
            let tag, key;
            this.tagList.map((item, index) => {
              if (item.label === label) {
                tag = item;
                key = index;
              }
            });
            return { tag: tag, key: key };
          },
          spanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex === 1) {
                return {
                  rowspan: row.mergeCol == 0 ? 0 : row.mergeCol,
                  colspan: row.mergeCol == 0 ? 0 :1
                }
            }
          },
          handleSpan({ row, column, rowIndex, columnIndex }) {
            // //合并第一 二列,这里columnIndex==0根1 根据业务要在前端写死
            if (columnIndex == 0) {
              //计算合并的行数列数
              let x = row.mergeCol == 0 ? 0 : row.mergeCol;
              let y = row.mergeCol == 1;
              //console.log(x , y)
              return [x, y];
            } else if (columnIndex == 1) {
              let x = row.mergeCol2 == 0 ? 0 : row.mergeCol2;
              let y = row.mergeCol2 == 0 ? 0 : 1;
              return [x, y];
            }
          },
          assembleData(data) {
            let names = [];
            //筛选出不重复的 name值,将其放到 names数组中
            data.forEach(e => {
              if (e.projectName&&!(names.some(item=>item.projectName==e.projectName&&item.garbageType==e.garbageType))) {
                names.push({projectName:e.projectName,garbageType:e.garbageType});
              }
            });
            let nameNums = [];
            console.log(names);
            //将names数组中的 name值设置默认合并0个单元格,放到 nameNums中
            names.forEach(e => {
              nameNums.push({ name: e.projectName,garbageType:e.garbageType, num: 0 });
            });
            console.log(nameNums);

            //计算每种 name值所在行需要合并的单元格数
            data.forEach(e => {
              nameNums.forEach(n => {
                if (e.projectName === n.name&&e.garbageType === n.garbageType) {
                  n.num++;
                }
              });
            });
            //将计算后的合并单元格数整合到 data中
            data.forEach(e => {
              nameNums.forEach(n => {
                if (e.projectName == n.name&&e.garbageType === n.garbageType) {
                  if (names.some(item=>item.projectName==e.projectName&&item.garbageType==e.garbageType)) {
                    e.mergeCol = n.num;
                    //删除已经设置过的值(防止被合并的单元格进到这个 if 语句中)
                    names = names.filter(v=>!(v.projectName==n.name&&v.garbageType==n.garbageType))
                    console.log(names);
                    // names.splice(names.indexOf({projectName:n.name,garbageType:n.garbageType}), 1);
                  } else {
                    //被合并的单元格设置为 0
                    e.mergeCol = 0;
                  }
                }else if(!e.projectName){
                  e.mergeCol = 1;
                }
              });
            });
            //将整理后的数据交给表格渲染
            this.tableData = data;
            console.log(this.tableData);
          },
          openDialog(){
            this.btnLoading=false
            this.visible = true
            this.$nextTick(()=>{
              this.startDate = this.$moment().format('YYYY-MM-DD')
            })
          },
          cancelModal() {
            this.startDate = this.$moment().format('YYYY-MM-DD')
            this.visible = false
          },
          submitForm(){
            if(!this.startDate){
              this.$message.error('请输入重新生成日期')
              return false
            }
            this.btnLoading = true
            calculateTicketMonthCostPrice({startDate:this.startDate}).then((res) => {
              this.btnLoading = false
              this.$message.success("提交成功");
              this.visible = false;
              this.getPage();
            }).catch(()=>{
              this.btnLoading = false
            })
          },
      }
  }
</script>

<style lang="scss" scoped>
</style>
