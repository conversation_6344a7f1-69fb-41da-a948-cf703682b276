<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :search.sync="searchForm"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="handleDel"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <template slot="inPicture" slot-scope="{ row }">
          <el-image
            v-if="row.inPicture"
            style="width: 50px; height: 50px"
            :src="filterImg(row.inPicture)"
            :preview-src-list="filterImgs(row.inPicture)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="goPicture" slot-scope="{ row }">
          <el-image
            v-if="row.goPicture"
            style="width: 50px; height: 50px"
            :src="filterImg(row.goPicture)"
            :preview-src-list="filterImgs(row.goPicture)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="inPictureForm" slot-scope="{ row }">
          <span v-if="row.inPicture">
            <el-tooltip
              class="item"
              effect="dark"
              content="此处只显示一张，有多张请点击图片预览"
              placement="top-start"
            >
              <el-image
                style="width: 50px; height: 50px"
                :src="filterImg(row.inPicture)"
                :preview-src-list="filterImgs(row.inPicture)"
              >
              </el-image>
            </el-tooltip>
          </span>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="goPictureForm" slot-scope="{ row }">
          <span v-if="row.goPicture">
            <el-tooltip
              class="item"
              effect="dark"
              content="此处只显示一张，有多张请点击图片预览"
              placement="top-start"
            >
              <el-image
                style="width: 50px; height: 50px"
                :src="filterImg(row.goPicture)"
                :preview-src-list="filterImgs(row.goPicture)"
              >
              </el-image>
            </el-tooltip>
            <span v-if="filterImgs(row.goPicture).length > 1"
              >多张图片点击图片进行预览</span
            >
          </span>
          <span v-else>暂无图片信息</span>
        </template>
        <!-- <template slot="menu" slot-scope="scope" class="menuSlot">
          <el-button
            type="text"
            v-if="permissions['chain:companywaybill:track']"
            icon="el-icon-document"
            size="small"
            plain
            @click="track(scope.row, scope.index)"
          >
            查看轨迹</el-button
          >
        </template> -->
        <!-- <template slot="searchMenu" slot-scope="scope">
          <el-button
            v-if="permissions['chain:companywaybill:exc']"
            size="small"
            type="primary"
            @click="exOut"
          >
            数据导出
          </el-button>
        </template> -->
      </avue-crud>
    </basic-container>
    <!-- 任务弹窗 -->
    <el-dialog
      width="60%"
      title="运单轨迹"
      center
      :visible="trackVisible"
      :before-close="closeVisible"
      :close-on-click-modal="false"
    >
      <div id="maps" style="height: 500px"></div>
      <div class="input-card">
        <h4>轨迹回放控制</h4>
        <span
          ><el-slider v-model="speed" :min="30" :max="2000"></el-slider
        ></span>
        <div class="input-item">
          <el-row class="my-row" :gutter="12">
            <el-col :span="12">
              <el-button type="primary" size="small" @click="startAnimation"
                >开始动画</el-button
              >
            </el-col>
            <el-col :span="12">
              <el-button type="primary" size="small" @click="pauseAnimation"
                >暂停动画</el-button
              >
            </el-col>
          </el-row>
          <el-row class="my-row" :gutter="12">
            <el-col :span="12">
              <el-button type="primary" size="small" @click="resumeAnimation"
                >继续动画</el-button
              >
            </el-col>
            <el-col :span="12">
              <el-button type="primary" size="small" @click="stopAnimation"
                >停止动画</el-button
              >
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPage,
  getObj,
  addObj,
  putObj,
  delObj,
  getGpsList,
} from "@/api/chain/companywaybill";
import {tableOption} from "@/const/crud/navvyOwner/waybill";
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";

export default {
  name: "waybill",
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        pageSizes: [10,20,50,100, 500, 1000,5000],
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: 'go_datetime', //降序字段
      },
      paramsSearch: {},
      searchForm: {},
      tableLoading: false,
      trackVisible: false,
      tableHeight:'100px',
      tableOption: tableOption,
      marker: null,
      map: null,
      polyline: null,
      speed: 50,
      firstArr: [113.98074, 22.55251],
      lineArr: [
        [121.5389385, 31.21515044],
        [121.5389385, 31.29615044],
        [121.5273285, 31.21515044],
      ],
      graspRoad: null, //轨迹纠偏
      graspRoadList: [], //轨迹纠偏数据
    };
  },
  created() {},
  mounted: function () {
     this.$nextTick(() => {
      console.log(window.innerHeight);
      console.log(this.$refs.crud.$el.offsetTop);
      // this.tableHeight = window.innerHeight - this.$refs.crud.$el.offsetTop - 20;
    });
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        // addBtn: this.permissions['chain:companywaybill:add'] ? true : false,
        // delBtn: this.permissions['chain:companywaybill:del'] ? true : false,
        // editBtn: this.permissions['chain:companywaybill:edit'] ? true : false,
        viewBtn: this.permissions["chain:companywaybill:get"] ? true : false,
      };
    },
  },
  methods: {
    expotOut,
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      console.log(this.paramsSearch);
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("inDatetime")) {
          params.inDatetimeStart = params.inDatetime[0];
          params.inDatetimeEnd = params.inDatetime[1];
          delete params.inDatetime;
        }
        if (params.hasOwnProperty("goDatetime")) {
          params.goDatetimeStart = params.goDatetime[0];
          params.goDatetimeEnd = params.goDatetime[1];
          delete params.goDatetime;
        }
        if (params.hasOwnProperty("completeDatetime")) {
          params.completeDatetimeStart = params.completeDatetime[0];
          params.completeDatetimeEnd = params.completeDatetime[1];
          delete params.completeDatetime;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch,
          JSON.parse(this.$route.query.info)
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    filterImg(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? null : url[0];
    },
    filterImgs(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? [] : url;
    },
    closeVisible() {
      this.trackVisible = false;
    },
    track(row) {
      this.lineArr = [];
      this.firstArr = [];
      this.graspRoadList = [];
      this.marker = null;
      getGpsList({ companyWaybillId: row.id }).then((res) => {
        if (res.data.data && res.data.data.length > 0) {
          res.data.data.forEach((item, index) => {
            if (item.gps) {
              let arr = item.gps.split(",");
              this.lineArr.push(arr);
              if (index % 10 == 0) {
                this.graspRoadList.push({
                  x: arr[0],
                  y: arr[1],
                  sp: 20,
                  ag: 0,
                  tm: new Date(item.createDatetime).getTime() / 1000,
                });
              }
            }
          });
          console.log(this.lineArr);
          this.firstArr = this.lineArr[0];
          this.trackVisible = true;
          console.log(this.lineArr);
          setTimeout(() => {
            this.initMap();
            this.initroad();
          }, 500);
        } else {
          this.$message.error("暂无轨迹信息");
        }
      });
    },
    calcAngle(start, end) {
      console.log(start);
      console.log(end);
      var p_start = this.map.lngLatToContainer(start),
        p_end = this.map.lngLatToContainer(end);
      var diff_x = p_end.x - p_start.x,
        diff_y = p_end.y - p_start.y;
      return (360 * Math.atan2(diff_y, diff_x)) / (2 * Math.PI) + 90;
    },
    //初始化地图
    initMap() {
      this.map = new AMap.Map("maps", {
        resizeEnable: true, //窗口大小调整
        center: this.firstArr, //中心 firstArr: [116.478935, 39.997761],
        zoom: 17,
      });
      var historyItem = null
      this.graspRoadList.forEach((item) => {
        console.log(historyItem);
        console.log(item);
        item.ag = historyItem ? this.calcAngle(historyItem, [item.x, item.y]) : 0;
        historyItem = [item.x, item.y];
      });
      this.graspRoad = new AMap.GraspRoad();
      this.graspRoad.driving(this.graspRoadList, function (error, result) {
        console.log(result);
        // if(!error){
        //   var path2 = [];
        //   var newPath = result.data.points;
        //   for(var i =0;i<newPath.length;i+=1){
        //     path2.push([newPath[i].x,newPath[i].y])
        //   }
        //   var newLine = new AMap.Polyline({
        //     path:path2,
        //     strokeWeight:8,
        //     strokeOpacity:0.8,
        //     strokeColor:'#0091ea',
        //     showDir:true
        //   })
        //   map.add(newLine)
        //   map.setFitView()
        // }
      });
      // 创建一个车 Icon
      var car = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(70, 69),
        // 图标的取图地址
        image: require("../../../static/trucks.png"),
        // 图标所用图片大小
        imageSize: new AMap.Size(69, 39),
        angle: 90,
      });
      this.marker = new AMap.Marker({
        map: this.map,
        position: this.firstArr,
        icon: car,
        offset: new AMap.Pixel(-20, -20),
        autoRotation: true,
        angle: -270,
      });
      // 创建一个起点 Icon
      var startIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(25, 34),
        // 图标的取图地址
        image:
          "//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(135, 40),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(-9, -3),
      });
      let startMarker = new AMap.Marker({
        map: this.map,
        position: this.firstArr,
        icon: startIcon,
        offset: new AMap.Pixel(-13, -30),
      });
      // 创建一个终点 Icon
      var endIcon = new AMap.Icon({
        size: new AMap.Size(25, 34),
        image:
          "//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
        imageSize: new AMap.Size(135, 40),
        imageOffset: new AMap.Pixel(-95, -3),
      });
      let endMarker = new AMap.Marker({
        map: this.map,
        position: this.lineArr[this.lineArr.length - 1],
        icon: endIcon,
        offset: new AMap.Pixel(-13, -30),
      });
    },
    //初始化轨迹
    initroad(row) {
      //绘制还未经过的路线
      this.polyline = new AMap.Polyline({
        map: this.map,
        path: this.lineArr,
        showDir: true,
        strokeColor: "#28F", //线颜色--蓝色
        // strokeOpacity: 1,     //线透明度
        strokeWeight: 6, //线宽
        // strokeStyle: "solid"  //线样式
      });
      // // 绘制路过了的轨迹
      var passedPolyline = new AMap.Polyline({
        map: this.map,
        strokeColor: "#AF5", //线颜色-绿色
        //path: this.lineArr,
        // strokeOpacity: 1,     //线透明度
        strokeWeight: 6, //线宽
        // strokeStyle: "solid"  //线样式
      });
      this.marker.on("moving", (e) => {
        passedPolyline.setPath(e.passedPath);
      });
      this.map.setFitView(); //合适的视口
    },
    startAnimation() {
      this.marker.moveAlong(this.lineArr, this.speed);
    },
    pauseAnimation() {
      this.marker.pauseMove();
    },
    resumeAnimation() {
      this.marker.resumeMove();
    },
    stopAnimation() {
      this.marker.stopMove();
    },
    exOut(value) {
      this.$router.push({ path: "/exportExc/exportExc" });
    },
  },
};
</script>

<style lang="scss" scoped>
.input-card {
  position: absolute;
  right: 40px;
  bottom: 40px;
  background-color: #fff;
  padding: 10px;
  .my-row {
    margin-top: 15px;
  }
}
</style>
