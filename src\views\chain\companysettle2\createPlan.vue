<template>
  <div id="content"
       class="merchantDetail">
    <el-drawer size="100%"
               title="生成资金支付计划"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal"
               :wrapperClosable="false">
      <ul class="info">
        <li>
          <label>结算单号：</label>
          <span>{{ infoForm.settleNo }}</span>
        </li>
        <li>
          <label>结算申请人：</label>
          <span>{{ infoForm.agentName }}</span>
        </li>
        <li>
          <label>运单申请数：</label>
          <span>{{ infoForm.waybillCnt }}</span>
        </li>
        <li>
          <label>项目名称：</label>
          <span>{{ infoForm.projectName }}</span>
        </li>
      </ul>
      <div class="search">
        <el-collapse v-model="activeNames"
                     @change="changeCollapse">
          <el-collapse-item title="查询"
                            name="1">
            <el-form class="myForm"
                     label-width="96px"
                     :model="billform"
                     ref="billform"
                     :inline="true">
              <el-row>
                <el-col :span="4">
                  <el-form-item label="运输方式:">
                    <el-select v-model="billform.tpMode"
                               multiple
                               size="small"
                               placeholder="运输方式"
                               clearable>
                      <el-option label="放飞"
                                 value="1"></el-option>
                      <el-option label="运费"
                                 value="2"></el-option>
                      <el-option label="资源"
                                 value="3"></el-option>
                      <el-option label="回填"
                                 value="4"></el-option>
                      <el-option label="内运"
                                 value="5"></el-option>
                      <el-option label="内转回填"
                                 value="6"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="泥尾:">
                    <el-select v-model="billform.garbageId"
                               size="small"
                               multiple
                               placeholder="泥尾"
                               clearable>
                      <el-option v-for="(item, index) in garbageList"
                                 :key="index"
                                 :label="item.names"
                                 :value="item.garbage_id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="土质:">
                    <el-select v-model="billform.goSoilType"
                               size="small"
                               multiple
                               placeholder="土质"
                               clearable>
                      <el-option v-for="(item, index) in soilList"
                                 :key="index"
                                 :label="item.go_soil_type"
                                 :value="item.go_soil_type"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="车队长:">
                    <el-input v-model="billform.captainName"
                              size="small"
                              placeholder="车队长"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="车牌:">
                    <el-input v-model="billform.truckCode"
                              size="small"
                              placeholder="车牌"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="司机:">
                    <el-input v-model="billform.driverName"
                              size="small"
                              placeholder="司机"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="4">
                  <el-form-item label="备注:">
                    <el-input v-model="billform.goRemark"
                              size="small"
                              clearable
                              placeholder="备注">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="平台结算价:">
                    <el-select v-model="billform.isPlatformWaybillCost"
                               size="small"
                               placeholder="平台设置结算价"
                               clearable>
                      <el-option label="否"
                                 value="0"></el-option>
                      <el-option label="是"
                                 value="1"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="单位:">
                    <el-select v-model="billform.weightUnit"
                               size="small"
                               multiple
                               placeholder="单位"
                               clearable>
                      <el-option label="车"
                                 value="车"></el-option>
                      <el-option label="方"
                                 value="方"></el-option>
                      <el-option label="吨"
                                 value="吨"></el-option>
                      <el-option label="时"
                                 value="时"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="4">
                  <el-form-item label="运单号:">
                    <el-input v-model="billform.no"
                              size="small"
                              placeholder="运单号"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="地块:">
                    <el-input v-model="billform.landParcel"
                              size="small"
                              placeholder="地块"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="签单车型:">
                    <el-select v-model="billform.goVehicleType"
                               size="small"
                               placeholder="出口签单车型"
                               clearable>
                      <el-option :label="item.itemValue"
                                 :value="item.itemValue"
                                 v-for="(item, index) in vehicleTypeList"
                                 :key="index"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="4">
                  <el-form-item label="出场时间:">
                    <el-date-picker type="datetimerange"
                                    size="small"
                                    format="yyyy-MM-dd HH:mm:ss"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    v-model="billform.tpDate"
                                    clearable></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="承运人:">
                    <el-input v-model="billform.payeeName"
                              size="small"
                              placeholder="承运人"
                              clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="证件齐全:">
                    <el-select v-model="billform.checkCompleteDocuments"
                               size="small"
                               placeholder="是否证件齐全"
                               clearable>
                      <el-option label="否"
                                 value="0"></el-option>
                      <el-option label="是"
                                 value="1"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="预设价:">
                    <div style="display: flex;">
                      <el-input-number style="width: 100%"
                                       v-model="billform.minPayeePrice"
                                       :min="0"
                                       :precision="2"
                                       :controls="false"
                                       placeholder="最小"
                                       size="small"
                                       :step="0.01"
                                       step-strictly></el-input-number>
                      -
                      <el-input-number style="width: 100%;"
                                       v-model="billform.maxPayeePrice"
                                       :min="0"
                                       :precision="2"
                                       :controls="false"
                                       placeholder="最大"
                                       size="small"
                                       :step="0.01"
                                       step-strictly></el-input-number>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="结算价:">
                    <div style="display: flex;">
                      <el-input-number style="width: 100%"
                                       v-model="billform.minSettlePrice"
                                       :min="0"
                                       :precision="2"
                                       :controls="false"
                                       size="small"
                                       placeholder="最小"
                                       :step="0.01"
                                       step-strictly></el-input-number>
                      -
                      <el-input-number style="width: 100%;"
                                       v-model="billform.maxSettlePrice"
                                       :min="0"
                                       :precision="2"
                                       :controls="false"
                                       size="small"
                                       placeholder="最大"
                                       :step="0.01"
                                       step-strictly></el-input-number>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-button style="margin-left: 14px"
                             type="primary"
                             size="small"
                             icon="el-icon-search"
                             :loading="loading"
                             @click="confirm">搜索</el-button>
                  <el-button @click="reset"
                             size="small"
                             icon="el-icon-delete">清空</el-button>

                </el-col>
              </el-row>
              <el-row>

              </el-row>

              <div style="margin-bottom: 10px; color: #666; font-size: 14px">
                查询统计： 运单{{ waybillData.length }}条，金额合计{{
                  freight
                }}元，<span style="color:red">已选择合计金额{{ moneyCount }}元，当前页只能查看核算状态为已核算的运单</span>
              </div>
            </el-form>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div style="display: flex; align-items: center; margin-top: 10px"
           v-if="waybillData && waybillData.length > 0">
        <el-tag class="avue-crud__tip">
          <span class="avue-crud__tip-name">
            当前表格已选择
            <span class="avue-crud__tip-count">{{
              multipleSelection.length
            }}</span>
            项
          </span>
          <el-button type="text"
                     size="small"
                     @click="clearSelection">清空</el-button>
        </el-tag>
        <el-input-number style="margin-left: 10px; width: 80px"
                         v-model="start"
                         :min="1"
                         :controls="false"
                         size="small"
                         :step="1"
                         step-strictly></el-input-number>
        至
        <el-input-number style="width: 80px; margin-right: 10px"
                         v-model="end"
                         :min="1"
                         :controls="false"
                         size="small"
                         :step="1"
                         step-strictly></el-input-number>
        <el-button icon="el-icon-check"
                   size="small"
                   type="primary"
                   :disabled="!start || !end"
                   @click="checkChange(true)">选择</el-button>
        <el-button icon="el-icon-close"
                   size="small"
                   type="primary"
                   :disabled="!start || !end"
                   @click="checkChange(false)">
          取消</el-button>
          <el-input-number style="width: 80px; margin-left: 10px;margin-right: 10px"
                         v-model="money"
                         :min="0"
                         :controls="false"
                         placeholder="请输入金额"
                         size="small"
                         :precision="2"
                         :step="0.01"
                         clearable
                         step-strictly></el-input-number>
          <el-button icon="el-icon-check"
                   size="small"
                   type="primary"
                   :disabled="!money"
                   @click="moneyChange">选择</el-button>
        <el-button type="primary"
                   size="small"
                   style="margin-left: 10px; margin-right: 10px"
                   icon="el-icon-edit"
                   :disabled="multipleSelection.length==0"
                   @click="payeeVisible=true">设置承运人</el-button>
        <el-button type="primary"
                   size="small"
                   :disabled="multipleSelection.length==0"
                   style="margin-left: 10px; margin-right: 10px"
                   icon="el-icon-edit"
                   @click="add">生成资金支付计划</el-button>
      </div>
      <div class="table"
           ref="mytable">
        <u-table ref="table"
                 style="width: 100%"
                 :data="waybillData"
                 :row-height="61"
                 border
                 use-virtual
                 :big-data-checkbox="true"
                 :height="tableHeight"
                 :highlight-current-row="false"
                 v-loading="tableLoading"
                 element-loading-text="拼命加载中"
                 element-loading-spinner="el-icon-loading"
                 @selection-change="handleSelectionChange">
          <u-table-column type="selection"
                          fixed="left"
                          width="50"></u-table-column>
          <u-table-column property="no"
                          fixed="left"
                          label="运单编号"
                          width="180"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="truckCode"
                          label="车牌号"
                          width="90"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="tpMode"
                          label="运输方式"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ filterTpMode(scope.row.tpMode) }}</span>
            </template>
          </u-table-column>
          <u-table-column property="names"
                          label="泥尾"
                          width="140"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="goSoilType"
                          label="土质"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="captainName"
                          label="车队长"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="driverName"
                          label="司机"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="payeeName"
                          label="承运人"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="goName"
                          label="出场签单员"
                          width="96"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="capacity"
                          label="容量"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="goDatetime"
                          label="出场签单时间"
                          width="160"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="completeDatetime"
                          label="完成时间"
                          width="160"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="weightTons"
                          label="数量"
                          width="90px"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.weightTons }}</span>
            </template>
          </u-table-column>
          <u-table-column property="weightUnit"
                          label="单位"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="price"
                          label="单价"
                          width="90px"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.settlePrice == 0 || scope.row.weightTons == 0">0.00</span>
              <span v-else>{{
                (scope.row.settlePrice / scope.row.weightTons).toFixed(2)
              }}</span>
            </template>
          </u-table-column>
          <u-table-column property="goRemark"
                          label="出场备注"
                          width="120px"
                          show-overflow-tooltip></u-table-column>
          <u-table-column property="goVehicleType"
                          label="出口签单车型"
                          width="106px"
                          show-overflow-tooltip>
          </u-table-column>
          <u-table-column property="landParcel"
                          label="地块"> </u-table-column>
          <u-table-column property="isPlatformWaybillCost"
                          label="平台设置结算价"
                          width="120px"
                          show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{
                scope.row.isPlatformWaybillCost === "1"
                  ? "是"
                  : scope.row.isPlatformWaybillCost === "0"
                  ? "否"
                  : ""
              }}</span>
            </template>
          </u-table-column>
          <u-table-column prop="payeePrice"
                          label="预设价"
                          fixed="right"
                          width="100px"
                          show-overflow-tooltip>
          </u-table-column>
          <u-table-column prop="settlePrice"
                          label="结算价"
                          fixed="right"
                          width="100px"
                          show-overflow-tooltip>
          </u-table-column>
          <u-table-column prop="adjustAmt"
                          label="增减值"
                          fixed="right"
                          width="100px"
                          show-overflow-tooltip>
          </u-table-column>
          <!-- <u-table-column prop="view" label="查看" fixed="right" width="80px">
            <template slot-scope="scope">
              <el-button @click="detail(scope.row, scope.$index)" icon="el-icon-view" type="text">查看</el-button>
            </template>
          </u-table-column> -->
        </u-table>
      </div>
    </el-drawer>
    <!-- 设置承运人 -->
    <setPayee v-if="payeeVisible"
              @submit="submit"
              :visible.sync="payeeVisible"></setPayee>
              <!-- 生成支付计划 -->
    <generateFundPaymentPlan v-if="generateVisible"
              @submit="generateSubmit"
              :tableData="generateData"
              :visible.sync="generateVisible"></generateFundPaymentPlan>
  </div>
</template>

<script>
import { clearNoNum } from "@/util/util.js";
import {
  getGarbageList,
  getGoSoilType,
  getSettleWaybill,
  createPaymentPlan,
  previewPaymentPlan,
} from "@/api/chain/companysettle";
import { updateCarrierName } from "@/api/chain/waybillPreset";
import { listDictionaryItem } from "@/api/chain/projectinfo";
import setPayee from './components/setPayee';
import generateFundPaymentPlan from './components/generateFundPaymentPlan';
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    //结算数据
    infoForm: {},
  },
  components: { setPayee,generateFundPaymentPlan },
  data () {
    return {
      activeNames: "1",
      billform: {},
      loading: false,
      multipleSelection: [], //选中的表格
      garbageList: [],
      waybillData: [],
      tableHeight: 500,
      soilList: [],
      tableLoading: false,
      vehicleTypeList: [], //出口签单车型列表
      start: 1,
      end: 1,
      btnLoading: false,
      payeeVisible: false,
      generateVisible: false,
      money:undefined,
      generateData:[],
    };
  },
  computed: {
    //总结算价 不包括已驳回的
    freight () {
      return this.waybillData
        .map((row) => (row.isReject == 1 ? 0 : row.settlePrice))
        .reduce((acc, cur) => parseFloat(cur) + acc, 0)
        .toFixed(2);
    },
    //选中金额
    moneyCount () {
      let arr = this.multipleSelection
        .map((row) => (parseFloat(row.settlePrice)+parseFloat(row.adjustAmt)))
       console.log(arr);
       let result =  arr.reduce((acc, cur) => parseFloat(cur) + acc, 0)
       .toFixed(2);
       console.log(result);
        return result
    },
  },
  mounted () {
    this.waybillData = this.infoForm.tableData;
    this.getGarbageList();
    this.getGoSoilType();
    this.getVehicleTypeList();
    this.$nextTick(() => {
      console.log(this.$refs.table);
      this.tableHeight =
        window.innerHeight - this.$refs.table.$el.offsetTop - 20;
    });
    window.addEventListener("resize", this.func);
  },
  methods: {
    func () {
      this.tableHeight =
        window.innerHeight - this.$refs.table.$el.offsetTop - 20;
      this.$refs.table.doLayout();
    },
    getVehicleTypeList () {
      listDictionaryItem({ dictionary: "go_vehicle_type" }).then((res) => {
        this.vehicleTypeList = res.data.data;
      });
    },
    /* ------------------------------------------------------- 获取泥尾 ----------------------------------------------------- */
    getGarbageList () {
      let param = {
        settleId: this.infoForm.id,
      };
      getGarbageList(param).then((res) => {
        this.garbageList = res.data.data;
      });
    },
    /* ------------------------------------------------------- 获取土质 ----------------------------------------------------- */
    getGoSoilType () {
      let param = {
        settleId: this.infoForm.id,
      };
      getGoSoilType(param).then((res) => {
        this.soilList = res.data.data;
      });
    },
    /* ------------------------------------------------------- 重置 ----------------------------------------------------- */
    reset () {
      this.billform = {};
    },
    /* ------------------------------------------------------- 确认筛选 ----------------------------------------------------- */
    confirm () {
      console.log(this.billform);
      let param = Object.assign({}, this.billform);
      param.companySettleId = this.infoForm.id;
      param.tpDateStart =
        this.billform.tpDate && this.billform.tpDate.length > 0
          ? this.billform.tpDate[0]
          : "";
      param.tpDateEnd =
        this.billform.tpDate && this.billform.tpDate.length > 0
          ? this.billform.tpDate[1]
          : "";
      console.log(param);
      let arr = ["tpMode","garbageId","goSoilType","weightUnit"]
      arr.forEach(item=>{
        if(param[item]&&Array.isArray(param[item])){
          param[item] = param[item].join(",")
        }
      })
      this.tableLoading = true;
      //这里默认就要查未驳回 已结算
      param.isReject = 0
      param.isSettle = 1
      param.isPlan = 1
      getSettleWaybill(param)
        .then((res) => {
          this.tableLoading = false;
          this.waybillData = res.data.data;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    cancelModal () {
      this.$emit("update:visible", false);
      this.$emit("searchData");
    },
    /* ------------------------------------------------------- 设置背景色 ----------------------------------------------------- */
    /* ------------------------------------------------------- 全选 ----------------------------------------------------- */
    handleSelectionChange (val) {
      console.log(val);
      this.multipleSelection = val;
    },
    /* ------------------------------------------------------- 搜索栏收起来 ----------------------------------------------------- */
    changeCollapse (val) {
      setTimeout(() => {
        console.log(this.$refs.table.$el.offsetTop);
        this.tableHeight =
          window.innerHeight - this.$refs.table.$el.offsetTop - 20;
      }, 500);
    },
    checkChange (val = false) {
      let start = this.start;
      let end = this.end;
      if (start > end) {
        [start, end] = [end, start];
      }
      if (
        this.waybillData &&
        this.waybillData.length > 0 &&
        start <= this.waybillData.length
      ) {
        if (end > this.waybillData.length) {
          end = this.waybillData.length;
        }
        for (let index = start; index <= end; index++) {
          this.$refs.table.toggleRowSelection([
            {
              row: this.waybillData[index - 1],
              selected: val,
            },
          ]);
        }
      }
    },
    clearSelection () {
      this.$refs.table.clearSelection();
    },
    moneyChange(){
      console.log(this.money);
      //需要先清空所有已选择
      this.clearSelection()
      let money = this.money
      this.waybillData.forEach((item,index)=>{
        money = money-(parseFloat(item.settlePrice)+parseFloat(item.adjustAmt))
        if(money>=0){
          this.$refs.table.toggleRowSelection([
            {
              row: this.waybillData[index],
              selected: true,
            },
          ]);
        }
      })
    },
    submit (form, done) {
      console.log(form);
      let data = this.multipleSelection.map((item) => {
        return {
          id: item.companyWaybillId,
          payeeId: form.payeeId,
        };
      });
      console.log(data);
      updateCarrierName(data)
        .then((res) => {
          done();
          this.payeeVisible = false;
          this.$message.success("操作成功");
          this.confirm();
        })
        .catch((err) => {
          done();
        });
    },
    add(){
      let param = {
        companySettleId:this.infoForm.id,
        companyWaybillIdList:this.multipleSelection.map(item=>{
          return item.companyWaybillId
        })
      }
      previewPaymentPlan(param).then(res=>{
        this.generateVisible = true
        this.generateData = res.data.data
      })
    },
    generateSubmit (paymentPlanName, done) {
      console.log(paymentPlanName);
      let param = {
        companySettleId:this.infoForm.id,
        paymentPlanName,
        companyPaymentPlanAmount:this.moneyCount,
        companyWaybillIdList:this.multipleSelection.map((item) => item.companyWaybillId)
      }
      console.log(param);
      createPaymentPlan(param)
        .then((res) => {
          done();
          this.generateVisible = false;
          this.$message.success("操作成功");
          this.confirm();
        })
        .catch((err) => {
          done();
        });
    },
  },
  destroyed () {
    window.removeEventListener("resize", this.func);
  },
};
</script>

<style lang="scss" scoped>
.info {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  // border-bottom: 1px solid #ccc;
  padding-bottom: 10px;

  li {
    width: 25%;
    line-height: 36px;
    color: #666;
    font-size: 14px;

    label {
      display: inline-block;
      width: 100px;
      text-align: right;
    }

    span {
      color: #000;
    }

    // &.small{
    //   min-width: 160px;
    // }
  }
}

/deep/ .el-drawer__header {
  margin-bottom: 10px;
  text-align: center;
  color: #000;
}

/deep/ .el-dialog__body {
  padding: 10px 20px 20px;

  .remind {
    margin-top: 10px;

    div {
      color: #000;
      line-height: 30px;
      font-size: 16px;
    }

    p {
      font-size: 14px;
      line-height: 28px;
    }

    span {
      font-size: 16px;
      color: red;
    }
  }

  .dialog-footer {
  }
}

.search {
  border: 1px solid #ccc;
  padding: 10px 20px 0px;

  .el-row {
    margin-bottom: 10px;
  }

  /deep/ .el-form-item {
    display: flex;
    margin-right: 0;
    margin-bottom: 0px;

    .el-form-item__label {
      min-width: 80px;
    }

    .el-form-item__content {
      flex: 1;
    }

    .el-select,
    .el-range-editor--small {
      width: 100%;
    }
  }
}

.searchBtn {
  border: 1px solid #ccc;
  padding: 10px 20px;
  margin-top: 10px;

  .highlight {
    color: red;
    font-size: 14px;
    margin-bottom: 6px;
  }

  .hint {
    font-size: 14px;
    margin-bottom: 8px;
  }
}

/deep/ .el-drawer__body {
  padding: 0px 20px;

  .remark {
    line-height: 36px;
    font-size: 16px;
    position: absolute;
    bottom: 80px;
    line-height: 36px;
    color: #409eff;
  }

  .demo-drawer__footer {
    width: calc(100% - 40px);
    text-align: center;
    // background-color: rgba(204, 204, 204,0.7);
    border-radius: 6px;
    line-height: 60px;
    position: absolute;
    bottom: 20px;
    z-index: 4;
  }
}

/deep/ .el-table .myRowClass {
  background: #bbb;
}

/deep/ .el-table .el-table__fixed-right {
  height: auto !important;
  bottom: 8px !important;

  &::before {
    background-color: transparent;
  }
}
/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}
</style>
