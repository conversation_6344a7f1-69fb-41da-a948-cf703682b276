export const tableOption = (value)=>{
  let that = value
  console.log(that);
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    index:true,
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    dialogClickModal:false,
    dialogWidth:600,
    excelBtn: false,
    printBtn: false,
    addBtn: false,
    viewBtn: false,
    searchSpan:6,
    searchLabelWidth:110,
    searchMenuSpan: 6,
    labelWidth:120,
    menu:false,
    column: [
      {
        label: "项目名称(泥尾)",
        prop: "projectName",
        search: true,
        sortable: true,
        minWidth:130,
        overHidden:true,
      },
      {
        label: "客户手机号码",
        prop: "garbageCustomerMobile",
        sortable: true,
        search:true,
        minWidth:120,
        overHidden:true,
      },
      {
        label: "客户名称",
        prop: "garbageCustomerName",
        sortable: true,
        search:true,
        minWidth:96,
        overHidden:true,
      },
      {
        label: "车牌",
        prop: "truckCode",
        sortable: true,
        search:true,
        minWidth:96,
        overHidden:true,
      },
      {
        label: "拍照凭证",
        prop: "pictureUrl",
        type:'upload'
      },
      {
        label: "拍照泥尾票",
        prop: "noUrl",
        type:'upload',
        minWidth:100,
        overHidden:true,
      },
      {
        label: "票号",
        prop: "no",
        sortable: true,
        search:true,
        minWidth:160,
        overHidden:true,
      },
      {
        label: "运单号",
        prop: "companyWaybillNo",
        search:true,
        minWidth:160,
        overHidden:true,
      },
      {
        label: "备注",
        prop: "remark",
        sortable: true,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "签单人",
        prop: "operator",
        search:true,
        minWidth:80,
        overHidden:true,
      },
      {
        label: "签单日期",
        prop: "searchDate",
        // sortable: true,
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange:true,
        search:true,
        hide:true,
        showColumn:false,
      },
      {
        label: "签单时间",
        prop: "operateDate",
        sortable: true,
        minWidth:140,
        overHidden:true,
      },
      {
        label: "是否删除",
        prop: "isDel",
        search: true,
        type: "select",
        dicData: [
          {
            label: "未删除",
            value: "0",
          },
          {
            label: "已删除",
            value: "1",
          },
        ],
        minWidth: 80,
        overHidden: true,
      },
    ],
  };
}

