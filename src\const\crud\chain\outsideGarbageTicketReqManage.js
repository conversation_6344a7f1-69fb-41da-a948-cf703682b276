export const tableOption = (value)=>{
  let that = value
  console.log(that);
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    index:true,
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    dialogClickModal:false,
    dialogWidth:500,
    excelBtn: false,
    printBtn: false,
    addBtn: false,
    viewBtn: false,
    updateBtnText:'审核通过',
    searchSpan:6,
    searchMenuSpan: 6,
    labelWidth:100,
    defaultSort: {
      prop: "applyDatetime",
      order: "descending",
    },
    menuWidth:130,
    editTitle:"审核",
    column: [
      {
        label: "申请来源",
        prop: "sourceName",
        search: true,
        sortable: 'custom',
        order:1,
        span:24,
        editDisabled:true,
        minWidth:160,
        overHidden:true,
      },
      {
        label: "申请项目",
        prop: "projectName",
        search: true,
        sortable: 'custom',
        order:2,
        span:24,
        editDisabled:true,
        minWidth:160,
        overHidden:true,
      },
      {
        label: "申请类型",
        prop: "type",
        search: true,
        sortable: 'custom',
        order:2,
        span:24,
        editDisabled:true,
        type:'select',
        props:{
          label:'itemName',
          value:'itemValue'
        },
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=garbage_ticket_apply_type',
        minWidth:94,
        overHidden:true,
      },
      {
        label: "申请泥尾",
        prop: "garbageName",
        search: true,
        sortable: 'custom',
        order:3,
        span:24,
        editDisabled:true,
        minWidth:120,
        overHidden:true,
      },
      {
        label: "申请土质",
        prop: "soilType",
        search: true,
        sortable: 'custom',
        order:4,
        span:24,
        editDisabled:true,
        minWidth:96,
        overHidden:true,
      },
      {
        label: "申请数",
        prop: "qty",
        sortable: 'custom',
        order:5,
        span:24,
        editDisabled:true,
        minWidth:84,
        overHidden:true,
      },
      {
        label: "审核数",
        prop: "auditsQty",
        sortable: 'custom',
        type:'number',
        order:9,
        span:24,
        controls:false,
        minRows: 0,
        precision: 0,
        disabled:true,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if(value&&value!=''){
                  if(Number(value)>Number(that.form.num)){
                    callback(new Error('审核数不能大于剩余库存'));
                  }else{
                    callback();
                  }
              }
              else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        minWidth:84,
        overHidden:true,
      },
      {
        label: "票号",
        prop: "bindTicketNo",
        order:10,
        span:24,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:84,
        overHidden:true,
      },
      {
        label: "申请人",
        prop: "applyName",
        search:true,
        sortable: 'custom',
        order:6,
        span:24,
        editDisabled:true,
        minWidth:84,
        overHidden:true,
      },
      {
        label: "申请日期",
        prop: "searchDate",
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange:true,
        search:true,
        display:false,
        sortable: 'custom',
        hide:true,
        showColumn:false,
      },
      {
        label: "申请时间",
        prop: "applyDatetime",
        sortable: 'custom',
        order:7,
        span:24,
        editDisabled:true,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "状态",
        prop: "status",
        search:true,
        sortable: 'custom',
        type:'select',
        display:false,
        props:{
          label:'itemName',
          value:'itemValue'
        },
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=company_ticket_project_apply_audit_status',
        // dicData: [
        //   {
        //     label: "已申请",
        //     value: "1",
        //   },
        //   {
        //     label: "已审核",
        //     value: "2",
        //   },
        //   {
        //     label: "已驳回",
        //     value: "3",
        //   },
        //   {
        //     label: "已确认",
        //     value: "4",
        //   },
        // ],
        minWidth:84,
        overHidden:true,
      },
      {
        label: "审核人",
        prop: "auditName",
        sortable: 'custom',
        display:false,
        minWidth:84,
        overHidden:true,
      },
      {
        label: "审核时间",
        prop: "auditDatetime",
        sortable: 'custom',
        display:false,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "确认人",
        prop: "checkTakerName",
        sortable: 'custom',
        display:false,
        minWidth:84,
        overHidden:true,
      },
      {
        label: "确认日期",
        span:24,
        prop: "searchDate2",
        type:'date',
        valueFormat: 'yyyy-MM-dd',
        searchRange:true,
        search:true,
        sortable: 'custom',
        hide:true,
        showColumn:false,
        display:false,
      },
      {
        label: "确认时间",
        prop: "checkTakerDatetime",
        sortable: 'custom',
        display:false,
        minWidth:100,
        overHidden:true,
      },
      {
        label: "剩余库存",
        prop: "num",
        order:8,
        span:24,
        editDisabled:true,
        hide:true,
        showColumn:false,
      },
      {
        label: "审核备注",
        prop: "auditRemark",
        order:10,
        span:24,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:100,
        overHidden:true,
      },
    ],
  };
}

