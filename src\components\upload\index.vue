<template>
  <div v-if="type == 'image'">
    <ul
      class="el-upload-list el-upload-list--picture-card"
      v-for="(item, index) in value"
      :key="index"
    >
      <li
        tabindex="0"
        class="el-upload-list__item is-ready"
        :style="'width: ' + width + 'px;height: ' + height + 'px'"
      >
        <div>
          <img :src="item" alt="" class="el-upload-list__item-thumbnail" />
          <span class="el-upload-list__item-actions">
            <span
              class="el-upload-list__item-preview"
              v-if="index != 0"
              @click="moveMaterial(index, 'up')"
            >
              <i class="el-icon-back"></i>
            </span>
            <span
              class="el-upload-list__item-preview"
              @click="zoomMaterial(index)"
            >
              <i class="el-icon-view"></i>
            </span>
            <span
              class="el-upload-list__item-delete"
              @click="deleteMaterial(index)"
            >
              <i class="el-icon-delete"></i>
            </span>
            <span
              class="el-upload-list__item-preview"
              v-if="index != value.length - 1"
              @click="moveMaterial(index, 'down')"
            >
              <i class="el-icon-right"></i>
            </span>
          </span>
        </div>
      </li>
    </ul>


    <el-dialog
      append-to-body
      :visible.sync="dialogVisible"
      width="35%">
      <img :src="url" alt="" style="width: 100%">
    </el-dialog>


    <el-upload
      action="/upms/file/upload?fileType=image&dir=material"
      :headers="headers"
      :file-list="[]"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      v-if="num > value.length"
    >
      <div
        tabindex="0"
        class="el-upload el-upload--picture-card"
        v-if="num > value.length"
        :style="
          'width: ' +
          width +
          'px;height: ' +
          height +
          'px;' +
          'line-height:' +
          height +
          'px;'
        "
      >
        <i class="el-icon-plus"></i>
      </div>
    </el-upload>
  </div>
</template>

<script>
import store from "@/store";
export default {
  name: "uploadInput",
  props: {
    //素材数据
    value: {
      type: Array,
      default() {
        return [];
      },
    },
    //素材类型
    type: {
      type: String,
    },
    //素材限制数量，默认5个
    num: {
      type: Number,
      default() {
        return 5;
      },
    },
    //宽度
    width: {
      type: Number,
      default() {
        return 150;
      },
    },
    //宽度
    height: {
      type: Number,
      default() {
        return 150;
      },
    },
  },
  data() {
    return {
      headers: {
        Authorization: "Bearer " + store.getters.access_token,
        AuthorizationMD5: store.getters.user_md5// user_md5
      },
      url:'',
      dialogImageUrl: "",
      dialogVisible: false,
      listDialogVisible: false,
    };
  },
  methods: {
    moveMaterial(index, type) {
      if (type == "up") {
        let tempOption = this.value[index - 1];
        this.$set(this.value, index - 1, this.value[index]);
        this.$set(this.value, index, tempOption);
      }
      if (type == "down") {
        let tempOption = this.value[index + 1];
        this.$set(this.value, index + 1, this.value[index]);
        this.$set(this.value, index, tempOption);
      }
    },
    zoomMaterial(index) {
      this.dialogVisible = true;
      this.url = this.value[index];
    },
    deleteMaterial(index) {
      let that = this;
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(function () {
        that.value.splice(index, 1);
      });
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },

    handleProgress(event, file, fileList) {
      // let uploadProgress = file.percentage.toFixed(0)
      // this.uploadProgress = uploadProgress
    },
    handleSuccess(response, file, fileList) {
      let that = this;
      this.uploadProgress = 0;
      this.$set(this.value, this.value.length, response.link);
      this.listDialogVisible = false;
    },
    handleError(err, file, fileList) {
      this.$message.error(err + "");
    },
    beforeUpload(file) {
      const isPic =
        file.type === "image/jpeg" ||
        file.type === "image/png" ||
        file.type === "image/gif" ||
        file.type === "image/jpg";
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isPic) {
        this.$message.error("上传图片只能是 JPG、JPEG、PNG、GIF 格式!");
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
      }
      return isPic && isLt2M;
    },
  },
};
</script>

<style lang="scss" scoped></style>
