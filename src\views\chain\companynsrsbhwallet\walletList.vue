<template>
  <div class="walletList">
    <el-drawer size="70%"
               :data="visible"
               :visible.sync="visible"
               title="选择银行账户充值"
               :before-close="cancelModal">
      <basic-container>
        <avue-crud ref="walletList"
                   :data="tableData"
                   :table-loading="tableLoading"
                   :option="tableOption">
          <span slot="empty">暂无数据</span>
          <template slot="platformBranchNsrmc"
                  slot-scope="{ row, index }">
          <div><span class="roundTip" v-if="Number(row.balance)<Number(row.waitPaymentTotalAmount)"></span>{{ row.platformBranchNsrmc }}</div>
        </template>
          <template slot="menu"
                    slot-scope="{ row, index }">
            <el-button type="primary"
                       icon="el-icon-wallet"
                       size="small"
                       v-if="permissions['chain:companynsrsbhwallet:recharge']"
                       @click="recharge(row)">
              充值</el-button>
            <el-button type="text"
                       icon="el-icon-tickets"
                       size="small"
                       v-if="permissions['chain:companynsrsbhwallet:rechargeLog']"
                       plain
                       @click="rechargeLog(row)">
              充值记录</el-button>
            <span style="color:#d40000" v-if="Number(row.balance)<Number(row.waitPaymentTotalAmount)">钱包余额不足，请充值</span>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
    <!-- 充值 -->
    <addRecharge v-if="addVisible"
                 :detailForm="addForm"
                 :option="addOption"
                 :showCancel="true"
                 size="634px"
                 :visible.sync="addVisible"
                 @submit="submit"
                 title="账户余额充值"></addRecharge>
    <!-- 充值记录 -->
    <rechargeLog v-if="logVisible"
                 :platformBranchId="platformBranchId"
                 :visible.sync="logVisible"></rechargeLog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { saveForWallet } from '@/api/chain/companynsrsbhwallet'
import rechargeLog from './rechargeLog.vue';
import addRecharge from '@/components/formDetail/index.vue';
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    tableData: {
      type: Array,
      default: () => {
        return []
      }
    },
  },
  components: {
    addRecharge,
    rechargeLog,
  },
  data () {
    return {
      tableOption: {
        header: false,
        page: false,
        editBtn: false,
        delBtn: false,
        menuWidth: 180,
        column: [
          {
            label: "益路银行企业",
            prop: "platformBranchNsrmc",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "钱包余额(元)",
            prop: "balance",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "待付总费用(元)",
            prop: "waitPaymentTotalAmount",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "收款人",
            prop: "platformBranchBankName",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "收款卡号",
            prop: "platformBranchBankAccount",
            minWidth: 140,
            overHidden: true,
          },
        ]
      },
      tableLoading: false,
      logVisible: false,
      addVisible: false, //充值
      addForm: {},
      addOption: {
        labelWidth: 100,
        emptyBtn: false,
        submitBtnText:"创建",
        column: [
          {
            label: "充值金额(¥)",
            prop: "money",
            span: 24,
            type: "number",
            minRows: 0.01,
            maxRows: *********.99,
            precision: 2,
            controls: false,
            rules: [
              {
                required: true,
                message: "请输入 充值金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "充值企业银行",
            prop: "platformBranchId",
            span: 24,
            disabled:true,
            type:"select",
            props:{
              label:"platformBranchNsrmc",
              value:"platformBranchId",
            },
            dicData:this.tableData,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 3,
            maxRows: 5,
            span: 24,
          },
          {
            label: "上传凭证",
            prop: "pic",
            type: "upload",
            listType: "picture-card",
            action: "/upms/file/upload?fileType=image&dir=batchRecharge/",
            dataType: 'string',
            propsHttp: {
              url: "link",
            },
            loadText: "附件上传中，请稍等",
            span: 24,
            accept:".jpg,.png",
            tip: "只能上传jpg/png文件",
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
        ],
      },
      platformBranchId:"",
    };
  },
  created () {
    // this.getData()
  },
  mounted () {
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    recharge(row){
        this.addForm.platformBranchId = row.platformBranchId
        this.addVisible = true
    },
    rechargeLog(row){
      this.platformBranchId = row.platformBranchId
      this.logVisible = true
    },
    submit (form, done) {
      console.log(form);
      let param = Object.assign({}, form)
      saveForWallet(param).then(res => {
        this.$message.success("充值成功")
        this.addVisible = false
        // 记得需要更新税洼地金额
        // this.$emit('saveForWallet')
        done()
      }).catch(() => {
        done()
      })
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
