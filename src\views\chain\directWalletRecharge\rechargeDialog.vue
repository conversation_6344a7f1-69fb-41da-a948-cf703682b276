<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="50%"
      :title="title"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <avue-form
        :option="option"
        ref="dialogForm"
        v-model="dialogForm"
        @submit="submit"
      >
      </avue-form>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import {
  getFee,addObj
} from "@/api/chain/directWalletRecharge";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
    title:{
      type: String,
      default: "充值",
    }
  },
  data() {
    return {
      dialogForm: {},
      option: {
        labelWidth: 100,
        submitText: "确认充值",
        position: "left",
        cancelBtn: true,
        disabled: this.title=='查看',
        submitBtn:this.title=='充值',
        emptyBtn: false,
        column: [
          {
            label: "类型",
            prop: "applyType",
            type: "select", // 下拉选择
            disabled: true,
            display:this.title=='查看',
            dicData: [
              {
                value: "1",
                label: "充值",
              },
              {
                value: "2",
                label: "提现",
              },
            ],
            span: 24,
          },
          {
            label: "项目名称",
            prop: "projectInfoId",
            type: 'select',   // 下拉选择
            filterable:true,
            props: {
              label: 'projectName',
              value: 'id'
            },
            dicUrl:'/chain/projectinfo/getDirectPayProjectList',
            span: 24,
            row: true,
            change:({value})=>{
              if(value&&!!this.dialogForm.amount&&this.title=="充值"){
                getFee({projectInfoId:value,amount:this.dialogForm.amount,type:1}).then(res=>{
                  this.dialogForm.platformFee = res.data.data.fee
                  this.dialogForm.actualAmount = res.data.data.actualAmount
                })
              }
              if(value&&this.title=="充值"){
                let form = this.$refs.dialogForm;
                let obj = form.DIC.projectInfoId.find((item) => item.id == value)
                // this.dialogForm.companyName = obj.companyAuthName
                this.dialogForm.platformBranchBankAccountName = obj.withdrawBankAccountName
                this.dialogForm.platformBranchBankAccount = obj.withdrawBankAccount
                this.dialogForm.platformBranchId = obj.platformBranchId
                this.dialogForm.platformBranchNsrmc = obj.platformBranchNsrmc
              }
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          // {
          //   label: "收款企业",
          //   prop: "platformBranchId",
          //   type: 'select',   // 下拉选择
          //   props: {
          //     label: 'nsrmc',
          //     value: 'id'
          //   },
          //   filterable:true,
          //   display:this.title!='提现查看',
          //   dicUrl: '/chain/platformbranch/getList',
          //   change:({value})=>{
          //     if(value&&this.title=="充值"){
          //       let form = this.$refs.dialogForm;
          //       let obj = form.DIC.platformBranchId.find((item) => item.id == value)
          //       this.dialogForm.platformBranchBankAccountName = obj.bankName
          //       this.dialogForm.platformBranchBankAccount = obj.bankAccount
          //     }
          //   },
          //   span: 24,
          //   row: true,
          //   rules: [
          //     {
          //       required: true,
          //       message: "请选择",
          //       trigger: "change",
          //     },
          //   ],
          // },
          {
            label: "收款企业",
            prop: "platformBranchNsrmc",
            span: 24,
            row: true,
            disabled: true,
            placeholder:' ',
            readonly:true,
          },
          {
            label: "收款银行",
            prop: "platformBranchBankAccountName",
            span: 24,
            row: true,
            disabled: true,
            placeholder:' ',
          },
          {
            label: "收款账户",
            prop: "platformBranchBankAccount",
            span: 24,
            row: true,
            disabled: true,
            placeholder:" ",
          },

          {
            label: "充值金额",
            prop: "amount",
            span: 24,
            row: true,
            type: 'number',
            controls: false,
            maxRows: *********.99,
            minRows: 0,
            precision: 2,
            blur:({value})=>{
              if(value&&!!this.dialogForm.projectInfoId&&this.title=="充值"){
                //type 1充值  2提现
                getFee({projectInfoId:this.dialogForm.projectInfoId,amount:value,type:1}).then(res=>{
                  this.dialogForm.platformFee = res.data.data.fee
                  this.dialogForm.actualAmount = res.data.data.actualAmount
                })
              }
            },
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "手续费",
            prop: "platformFee",
            span: 24,
            row: true,
            disabled:true,
            placeholder:" ",
          },
          {
            label:"钱包到账金额",
            prop: "actualAmount",
            span: 24,
            row: true,
            disabled:true,
            placeholder:" ",
          },
          {
            label: "银行流水号",
            prop: "frontLogNo",
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "备注",
            prop: "remark",
            type:'textarea',
            minRows:3,
            maxRows:5,
            span: 24,
            placeholder:this.title=='查看'?" ":'',
          },
          {
            label: "银行凭证",
            prop: "frontLogPic",
            span: 24,
            type: "upload",
            listType: "picture-img",
            action: "/upms/file/upload?fileType=image&dir=directWalletRecharge/",
            propsHttp: {
              url: "link",
            },
            loadText: "图上上传中，请稍等",
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
            // listType: "picture-img",
          },
          {
            label: "充值时间",
            prop: "applyDatetime",
            type: "datetime",
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            span: 24,
            placeholder:" ",
            display:this.title=='查看'
          },
          {
            label: "充值人",
            prop: "applyName",
            span: 24,
            placeholder:" ",
            display:this.title=='查看'
          },
          {
            label: "确认时间",
            prop: "auditDatetime",
            type: "datetime",
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            span: 24,
            placeholder:" ",
            display:this.title=='查看'
          },
          {
            label: "确认人",
            prop: "auditName",
            span: 24,
            placeholder:" ",
            display:this.title=='查看'
          },

        ],
      },
    };
  },
  created() {},
  mounted () {
    this.dialogForm = this.info
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    submit(form, done) {
      let param = Object.assign({},form)
      param.applyType = 1 //充值
      addObj(param).then(res=>{
        this.cancelModal()
        this.$emit("refreshChange");
        done()
      }).catch(()=>{
        done()
      })
    }
  },
};
</script>

<style lang="scss" scoped>


</style>
