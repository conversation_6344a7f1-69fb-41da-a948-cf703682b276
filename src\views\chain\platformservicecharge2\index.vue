<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 :search="search"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @sort-change="sortChange"
                 @search-change="searchChange">
        <template slot="menu"
                  slot-scope="scope">
          <el-button size="mini"
                     type="text"
                     icon="el-icon-check"
                     :disabled="scope.row.isPay=='是'"
                     v-if="permissions['chain:platformservicecharge2:confirm']"
                     @click="open(scope.row)">付款
          </el-button>
          <el-button size="mini"
                     type="text"
                     icon="el-icon-view"
                     v-if="permissions['chain:platformservicecharge2:view']"
                     @click="toDetail(scope.row, scope.index)">查看
          </el-button>
          <el-button size="mini"
                     type="text"
                     icon="el-icon-download"
                     v-if="permissions['chain:platformservicecharge2:export']"
                     @click="exOut(scope.row)">导出
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
    <detail v-if="detailVisible"
            :info="info"
            :visible.sync="detailVisible"></detail>
            <!-- 充值 -->
    <addRecharge v-if="addVisible"
                 :detailForm="addForm"
                 :option="addOption"
                 :showCancel="true"
                 ref="addForm"
                 size="634px"
                 :visible.sync="addVisible"
                 @submit="submit"
                 :title="title"></addRecharge>
  </div>
</template>

<script>
import {
  getPage,
  confirmPay,
  getBranchWalletList,
} from "@/api/chain/platformservicecharge";
import { tableOption } from "@/const/crud/chain/platformservicecharge2";
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";
import detail from './detail.vue'
import addRecharge from '@/components/formDetail/index.vue';
import { saveForWallet} from '@/api/chain/companynsrsbhwallet'
export default {
  name: "platformservicecharge2",
  components: {
    detail,
    addRecharge,
  },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      search: {
        presentDate: this.$moment().format("YYYY-MM"),
        isConfirm: '0',
        isPay: '0',
      },
      isFrist: true,
      detailVisible: false,
      info: {},
      title:"充值",
      addVisible: false, //充值
      addForm: {},
      addOption: {
        labelWidth: 100,
        emptyBtn: false,
        column: [
          {
            label: "充值金额(¥)",
            prop: "money",
            span: 24,
            type: "number",
            minRows: 0.01,
            maxRows: 999999999.99,
            precision: 2,
            controls: false,
            rules: [
              {
                required: true,
                message: "请输入 充值金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "充值企业银行",
            prop: "platformBranchNsrmc",
            span: 24,
            disabled: true,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 3,
            maxRows: 5,
            span: 24,
          },
          {
            label: "上传凭证",
            prop: "pic",
            type: "upload",
            listType: "picture-card",
            action: "/upms/file/upload?fileType=image&dir=batchRecharge/",
            dataType: 'string',
            propsHttp: {
              url: "link",
            },
            loadText: "附件上传中，请稍等",
            span: 24,
            tip: "只能上传jpg/png文件，且不超过500kb",
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
        ],
      },
    };
  },
  created () { },
  mounted: function () { },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList () {
      return {
        addBtn: this.permissions["chain:platformservicecharge2:add"]
          ? true
          : false,
        delBtn: this.permissions["chain:platformservicecharge2:del"]
          ? true
          : false,
        editBtn: this.permissions["chain:platformservicecharge2:edit"]
          ? true
          : false,
        viewBtn: this.permissions["chain:platformservicecharge2:get"]
          ? true
          : false,
      };
    },
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange (val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage (page, params = {}) {
      this.tableLoading = true;
      if (this.isFrist) {
        params.presentDate = this.search.presentDate
        params.isConfirm = this.search.isConfirm
        params.isPay = this.search.isPay
      }
      // this.tableData = [{
      //   companyName:'测试',
      //   isChecking:'0',
      //   isPay:'1',
      //   presentDate:'2022-12',
      // }]
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
            isChecking: '2',
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
          this.isFrist = false
        })
        .catch(() => {
          this.isFrist = false
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page);
    },
    exOut (row) {
      let params = {
        code: 'PlatformServiceChargeExcelExport',
        companyAuthId: row.companyAuthId,
        presentDate: row.presentDate,
      }
      let url = "/chain/excelExport/createByCode";
      expotOut(params, url, row.companyName + row.presentDate + "服务费应付");
    },
    open (row) {
      //获取默认的税洼地 深圳益路
      getBranchWalletList({isPlatformServiceBranch: "1"}).then(res=>{
        let datas = res.data.data
        if(datas&&datas.length>0){
          let obj = datas[0]
          if(Number(obj.balance)<Number(row.chargeTotal)){
            //钱不够支付
            this.title = `当前余额${obj.balance||0}，不足以付款，请充值`
            this.addForm.platformBranchId = obj.platformBranchId
            this.addForm.platformBranchNsrmc = obj.platformBranchNsrmc
            this.addVisible = true
          }else{
            this.$confirm(`您将支付服务费金额${row.chargeTotal}元,点击确定将从钱包余额扣除`, "提示", { closeOnClickModal: false })
              .then(() => {
                this.confirmPay(row.presentDate)
              }).catch(() => {});
          }
        }else{
          this.$message.error("未配置益路银行企业，请联系管理员进行配置")
        }
      })
    },
    confirmPay (presentDate) {
      this.tableLoading = true
      confirmPay({ presentDate}).then(res => {
        this.$message.success("付款成功")
        this.getPage(this.page);
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    toDetail (row, type) {
      this.info = row
      this.detailVisible = true
    },
    submit (form, done) {
      console.log(form);
      let param = Object.assign({}, form)
      saveForWallet(param).then(res => {
        this.$message.success("操作成功")
        this.addVisible = false
        done()
      }).catch(() => {
        done()
      })
    },
  },
};
</script>

<style lang="scss" scoped></style>
