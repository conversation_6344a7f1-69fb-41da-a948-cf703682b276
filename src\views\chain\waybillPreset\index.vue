<template>
  <div class="execution">
    <basic-container>
      <el-form
        ref="form"
        :model="searchForm"
        label-width="100px"
        v-show="!isMore"
      >
        <el-row :gutter="14">
          <el-col :span="6">
            <el-form-item label="运单号：">
              <el-input
                v-model="searchForm.no"
                size="small"
                placeholder="运单号"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="车队长：">
              <el-input
                v-model="searchForm.fleetCaptainName"
                size="small"
                placeholder="车队长"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="车牌号：">
              <el-input
                v-model="searchForm.goTruckCode"
                size="small"
                placeholder="车牌号"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button
              style="margin-left: 10px"
              size="small"
              icon="el-icon-search"
              type="primary"
              @click="searchData"
            >
              搜索
            </el-button>
            <el-button
              class="el-button--small"
              type="primary"
              icon="el-icon-caret-bottom"
              @click="getmoreshow(1)"
              >更多</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <avue-form
        v-show="isMore"
        :option="searchOption"
        v-model="searchForm"
        @submit="searchChange"
        @reset-change="searchReset"
      >
      <template slot="iotInPicture" slot-scope="{ row }">
          <el-image
            v-if="row.iotInPicture"
            style="width: 50px; height: 50px"
            :src="filterImg(row.iotInPicture)"
            :preview-src-list="filterImgs(row.iotInPicture)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="menuForm" slot-scope="scope">
          <el-button
            class="el-button--small"
            type="primary"
            icon="el-icon-caret-top"
            @click="getmoreshow(2)"
            >隐藏</el-button
          >
        </template>
        <template slot="payeePrice" slot-scope="scope">
          <!-- amount -->
          <div style="display: flex">
            <el-input-number
              style="margin-right: 2px"
              v-model="minPayeePrice"
              placeholder="最小值"
              :min="0"
              :precision="2"
              :controls="false"
              size="small"
              :step="0.01"
              step-strictly
            ></el-input-number>
            至
            <el-input-number
              style="margin-left: 2px"
              v-model="maxPayeePrice"
              placeholder="最大值"
              :min="0"
              :precision="2"
              :controls="false"
              size="small"
              :step="0.01"
              step-strictly
            ></el-input-number>
          </div>
        </template>
      </avue-form>
      <div>
        <div>
          <el-tag class="avue-crud__tip">
            <span class="avue-crud__tip-name">
              当前表格已选择
              <span class="avue-crud__tip-count">{{ selectLen }}</span>
              项， 结算价合计<span class="avue-crud__tip-count">{{
                settlePriceTotal
              }}</span>
              元
            </span>
            <el-button type="text" size="small" @click="clearSelection"
              >清空</el-button
            >
            <slot name="tip"></slot>
          </el-tag>
          <el-input-number
            style="margin-left: 10px; width: 100px"
            v-model="start"
            :min="1"
            :controls="false"
            size="small"
            :step="1"
            step-strictly
          ></el-input-number>
          至
          <el-input-number
            style="width: 100px; margin-right: 10px"
            v-model="end"
            :min="1"
            :controls="false"
            size="small"
            :step="1"
            step-strictly
          ></el-input-number>
          <el-button
            icon="el-icon-check"
            size="small"
            type="primary"
            :disabled="!start || !end"
            @click="checkChange(true)"
          >
            选择</el-button
          >
          <el-button
            icon="el-icon-close"
            size="small"
            type="primary"
            :disabled="!start || !end"
            @click="checkChange(false)"
          >
            取消</el-button
          >
          <el-tooltip
            class="item"
            effect="dark"
            content="批量设置预算价按单位请单独设置"
            placement="top-start"
          >
            <el-button
              :disabled="!isDisabled"
              v-if="permissions['chain:waybillPreset:edits'] && !isFromDialog"
              size="small"
              type="primary"
              @click="batchEdit"
            >
              批量设置预设价
            </el-button>
          </el-tooltip>

          <el-button
            style="margin-left: 14px"
            v-if="permissions['chain:waybillPreset:setPayee']"
            :disabled="selectList.length == 0"
            size="small"
            type="primary"
            @click="setPayee"
          >
            设置承运人
          </el-button>
          <el-button
            :disabled="!isDisabledStatement"
            :loading="btnSettleLoading"
            style="margin-left: 14px"
            v-if="
              permissions['chain:waybillPreset:createStatement'] &&
              !isFromDialog
            "
            size="small"
            type="primary"
            @click="creatSettleWaybill"
          >
            生成结算单{{ form.isSettle }}
          </el-button>
          <el-button
            style="margin-left: 14px"
            v-if="permissions['chain:waybillPreset:excel']"
            size="small"
            :loading="btnLoading"
            type="primary"
            @click.stop.prevent="exOut"
          >
            导出
          </el-button>
        </div>
      </div>
      <el-table
        ref="multipleTable"
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%"
        @selection-change="selectionChange"
        :height="tableHeight"
        border
      >
        <el-table-column
          type="selection"
          fixed="left"
          width="50"
        ></el-table-column>
        <el-table-column type="index" label="序号" width="60"></el-table-column>
        <el-table-column
          prop="no"
          label="运单号"
          width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span
              style="color: #409eff; cursor: pointer"
              @click="viewInfo(scope.row, scope.index)"
              >{{ scope.row.no }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="projectInfoName"
          label="项目名称"
          width="160"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="goStaffName"
          label="出场签单员"
          width="96"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="goSoilType"
          label="出场签单土质"
          width="106"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="tpModeName"
          label="运输类型"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="garbageName"
          label="泥尾"
          width="140"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="weightTons" label="数量" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="weightUnit" label="单位" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="goTruckCode"
          label="车牌号"
          width="100"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="goShiftTypeName"
          label="出场班次"
          width="130"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="fleetCaptainName"
          label="车队长"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="captainMobile"
          label="车队长手机号"
          width="110"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="fleetName" label="司机" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="mobile"
          label="司机手机号"
          width="110"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="statusName"
          label="运单状态"
          width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="isException" label="修改申请">
          <template slot-scope="scope">
            <span>{{ scope.row.isException == 1 ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="goRemark" label="出场备注" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="carrierName"
          label="收款人/承运人"
          width="110"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="goDatetime"
          label="出场签单时间"
          width="160"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="completeDatetime"
          label="完成时间"
          width="160"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="payeePrice" label="预设价" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="settlePrice"
          label="结算价"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="taxFee" label="税费" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="taxPoint" label="税率" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="realPayPrice"
          label="实付价"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="settleStatus"
          label="承运人结算状态"
          width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="waybillPayStatus"
          label="承运人支付状态"
          width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="isSettle" label="是否生成结算单" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.isSettle == 1 ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="companyPaymentNo"
          label="支付单号"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="companySettleNo"
          label="结算单号"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="giveCardUserId"
          label="交卡人"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="giveCardNo"
          label="交卡回执单号"
          width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="invoiceStatus" label="是否开票">
          <template slot-scope="scope">
            <span>{{ scope.row.invoiceStatus == 1 ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="companyPaymentPlanName"
          label="支付计划名称"
          width="120"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page.currentPage"
        :page-sizes="page.pageSizes"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
      >
      </el-pagination>
      <u-table
        v-if="false"
        :data="tableData"
        ref="crud"
        :pagination-show="true"
        :total="page.total"
        :page-size="page.pageSize"
        :current-page="page.currentPage"
        v-loading="tableLoading"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        @handlePageSize="handlePageSize"
        :height="tableHeight"
        :row-height="53"
        :header-cell-style="{ backgroundColor: '#f7f9fd' }"
        header-row-class-name="tableHeader"
        border
        use-virtual
        row-key="id"
        :big-data-checkbox="true"
        @selection-change="selectionChange"
      >
        <u-table-column
          type="selection"
          fixed="left"
          width="50"
        ></u-table-column>
        <u-table-column type="index" label="序号" width="60"></u-table-column>
        <u-table-column
          prop="no"
          label="运单号"
          width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span
              style="color: #409eff; cursor: pointer"
              @click="viewInfo(scope.row, scope.index)"
              >{{ scope.row.no }}</span
            >
          </template>
        </u-table-column>
        <u-table-column
          prop="projectInfoName"
          label="项目名称"
          width="160"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="goStaffName"
          label="出场签单员"
          width="96"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="goSoilType"
          label="出场签单土质"
          width="106"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="tpModeName"
          label="运输类型"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="garbageName"
          label="泥尾"
          width="140"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column prop="weightTons" label="数量" show-overflow-tooltip>
        </u-table-column>
        <u-table-column prop="weightUnit" label="单位" show-overflow-tooltip>
        </u-table-column>
        <u-table-column
          prop="goTruckCode"
          label="车牌号"
          width="100"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="goShiftTypeName"
          label="出场班次"
          width="130"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="fleetCaptainName"
          label="车队长"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="captainMobile"
          label="车队长手机号"
          width="110"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column prop="fleetName" label="司机" show-overflow-tooltip>
        </u-table-column>
        <u-table-column
          prop="mobile"
          label="司机手机号"
          width="110"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="statusName"
          label="运单状态"
          width="120"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column prop="isException" label="修改申请">
          <template slot-scope="scope">
            <span>{{ scope.row.isException == 1 ? "是" : "否" }}</span>
          </template>
        </u-table-column>
        <u-table-column prop="goRemark" label="出场备注" show-overflow-tooltip>
        </u-table-column>
        <u-table-column
          prop="carrierName"
          label="收款人/承运人"
          width="110"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="goDatetime"
          label="出场签单时间"
          width="160"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="completeDatetime"
          label="完成时间"
          width="160"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column prop="payeePrice" label="预设价" show-overflow-tooltip>
        </u-table-column>
        <u-table-column prop="settlePrice" label="结算价" show-overflow-tooltip>
        </u-table-column>
        <u-table-column prop="taxFee" label="税费" show-overflow-tooltip>
        </u-table-column>
        <u-table-column prop="taxPoint" label="税率" show-overflow-tooltip>
        </u-table-column>
        <u-table-column
          prop="realPayPrice"
          label="实付价"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="settleStatus"
          label="承运人结算状态"
          width="120"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="waybillPayStatus"
          label="承运人支付状态"
          width="120"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column prop="isSettle" label="是否生成结算单" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.isSettle == 1 ? "是" : "否" }}</span>
          </template>
        </u-table-column>
        <u-table-column
          prop="companyPaymentNo"
          label="支付单号"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="companySettleNo"
          label="结算单号"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="giveCardUserId"
          label="交卡人"
          show-overflow-tooltip
        >
        </u-table-column>
        <u-table-column
          prop="giveCardNo"
          label="交卡回执单号"
          width="120"
          show-overflow-tooltip
        >
        </u-table-column>
        <!-- <u-table-column prop="isDirectPay"
                        label="是否直付">
          <template slot-scope="scope">
            <span>{{ scope.row.isDirectPay == 1 ? "是" : "否" }}</span>
          </template>
        </u-table-column> -->
        <u-table-column prop="invoiceStatus" label="是否开票">
          <template slot-scope="scope">
            <span>{{ scope.row.invoiceStatus == 1 ? "是" : "否" }}</span>
          </template>
        </u-table-column>
        <u-table-column
          prop="companyPaymentPlanName"
          label="支付计划名称"
          width="120"
          show-overflow-tooltip
        >
        </u-table-column>
      </u-table>
    </basic-container>
    <!-- 设置预设价 -->
    <el-dialog
      width="400px"
      title="设置预设价"
      center
      :append-to-body="true"
      :visible.sync="editVisible"
      :close-on-click-modal="false"
    >
      <div>
        <div style="margin-bottom: 6px">请输入单价：</div>
        <el-input
          placeholder="请输入单价"
          oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
          @blur="handleInput(that, 'unitPrice')"
          maxlength="7"
          size="small"
          v-model="unitPrice"
        >
        </el-input>
      </div>
      <div class="remind">
        <div>
          您确认预设这<span>{{ selectList.length }}</span> 单？
        </div>
        <div>总数量：{{ weightTotal }}</div>
        <p>
          预设金额：<span>{{ (weightTotal * unitPrice).toFixed(2) }}</span>
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editVisible = false">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="submit"
          >提交</el-button
        >
      </span>
    </el-dialog>
    <!-- 任务弹窗 -->
    <el-dialog
      width="500px"
      title="设置承运人"
      center
      :append-to-body="true"
      :visible.sync="setPayeeVisible"
      :close-on-click-modal="false"
    >
      <el-button
        type="primary"
        size="small"
        style="position: absolute; right: 35px; top: 57px; z-index: 2"
        @click="selectDialog = true"
        >常用收款人</el-button
      >
      <avue-form ref="editForm" v-model="editForm" :option="editOption">
        <template slot-scope="{ disabled, size }" slot="mobile">
          <span>{{ editForm.mobile }}</span>
        </template>
        <template slot-scope="{ disabled, size }" slot="bindingBankNo">
          <span>{{ editForm.bindingBankNo }}</span>
        </template>
        <template slot-scope="{ disabled, size }" slot="bindingBankName">
          <span>{{ editForm.bindingBankName }}</span>
        </template>
        <template slot-scope="{ disabled, size }" slot="status">
          <span>{{ editForm.status }}</span>
        </template>
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="setPayeeVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="btnSetPayLoading"
          :disabled="
            editForm.bindingBankName == '' ||
            editForm.bindingBankNo == '' ||
            editForm.status == '否'
          "
          @click="submitPayee"
          >提交</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      width="60%"
      title="运单轨迹"
      center
      :append-to-body="true"
      :visible="trackVisible"
      :before-close="closeVisible"
      :close-on-click-modal="false"
    >
      <div id="maps" style="height: 500px"></div>
      <div class="input-card">
        <h4>轨迹回放控制</h4>
        <span
          ><el-slider v-model="speed" :min="30" :max="2000"></el-slider
        ></span>
        <div class="input-item">
          <el-row class="my-row" :gutter="12">
            <el-col :span="12">
              <el-button type="primary" size="small" @click="startAnimation"
                >开始动画</el-button
              >
            </el-col>
            <el-col :span="12">
              <el-button type="primary" size="small" @click="pauseAnimation"
                >暂停动画</el-button
              >
            </el-col>
          </el-row>
          <el-row class="my-row" :gutter="12">
            <el-col :span="12">
              <el-button type="primary" size="small" @click="resumeAnimation"
                >继续动画</el-button
              >
            </el-col>
            <el-col :span="12">
              <el-button type="primary" size="small" @click="stopAnimation"
                >停止动画</el-button
              >
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
    <selectCommonPayees
      v-if="selectDialog"
      @submit="selectComplete"
      :visible.sync="selectDialog"
    ></selectCommonPayees>
    <!-- 运单详情 -->
    <waybill-detail
      v-if="detailVisible"
      :detailForm="detailForm"
      :option="detailOption"
      :visible.sync="detailVisible"
    ></waybill-detail>
  </div>
</template>

<script>
import {
  getPage,
  getObj,
  addObj,
  putObj,
  delObj,
  getGpsList,
  updatePrice,
  updateCarrierName,
  batchAddWaybillSettlePre,
  postCreateByCode,
} from "@/api/chain/waybillPreset";
import { searchOption } from "@/const/crud/chain/waybillPreset";
import { mapGetters } from "vuex";
import { exportOut } from "@/util/down.js";
import { clearNoNum } from "@/util/util.js";
import selectCommonPayees from "./selectCommonPayees";
import WaybillDetail from "@/views/chain/companywaybill2/detail.vue";

export default {
  name: "waybillPreset",
  props: {
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    selectCommonPayees,
    WaybillDetail,
  },
  data() {
    return {
      btnSetPayLoading: false,
      editForm: {},
      setPayeeVisible: false,
      lock: false,
      inputVisible: false,
      inputValue: "",
      statements: [],
      that: this,
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 10, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
        pageSizes: [10,50,100,300,500,1000,2000], //每页显示多少条
      },
      paramsSearch: {},
      searchForm: {
        status: "2,3,4,11,12,21,22",
      },
      tableLoading: false,
      trackVisible: false,
      tableHeight: "600",
      marker: null,
      map: null,
      polyline: null,
      speed: 50,
      firstArr: [113.98074, 22.55251],
      lineArr: [
        [121.5389385, 31.21515044],
        [121.5389385, 31.29615044],
        [121.5273285, 31.21515044],
      ],
      graspRoad: null, //轨迹纠偏
      graspRoadList: [], //轨迹纠偏数据
      searchDom: {},
      editVisible: false,
      selectList: [],
      settlePrice: 0, //批量修改价格
      unitPrice: 0, //按重量批量修改价格
      btnLoading: false,
      btnSettleLoading: false,
      searchOption: searchOption,
      isMore: false,
      minPayeePrice: undefined,
      maxPayeePrice: undefined,
      editOption: {
        column: [
          {
            label: "收款人",
            prop: "payeeId",
            type: "select",
            span: 24,
            remote: true,
            props: {
              label: "payeeId",
              value: "payeeId",
            },
            typeformat(item, label, value) {
              console.log(item);
              return `${item["carrierName"]}-${item["mobile"]}`;
            },
            dicFormatter: (res) => {
              this.payeeDic = res.data;
              return res.data || [];
            },
            dicUrl: `/chain/companywaybill/selectCarrierListByNameOrMobile?param={{key}}`,
            // allowCreate:true,
            filterable: true,
            change: ({ value }) => {
              console.log(value);
              if (value) {
                let tmp = this.payeeDic.filter((v) => v.payeeId == value)[0];
                console.log(tmp);
                if (tmp) {
                  this.editForm.mobile = tmp.mobile;
                  this.editForm.bindingBankNo = tmp.bindingBankNo;
                  this.editForm.bindingBankName = tmp.bindingBankName;
                  this.editForm.status = tmp.status;
                }
              }
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "手机号码",
            prop: "mobile",
            span: 24,
          },
          {
            label: "收款卡号",
            prop: "bindingBankNo",
            span: 24,
          },
          {
            label: "收款银行",
            prop: "bindingBankName",
            span: 24,
          },
          {
            label: "是否签约合同",
            prop: "status",
            span: 24,
          },
        ],
        labelWidth: 120,
        submitBtn: false,
        position: "left",
        emptyBtn: false,
        cancelBtn: true,
      },
      payeeDic: [],
      payeeDic2: [],
      selectDialog: false,
      start: 1,
      end: 1,
      detailVisible: false, //详情弹窗
      detailForm: {},
      detailOption: {
        detail: true,
        labelWidth: 114,
        group: [
          {
            label: "基本信息",
            prop: "group",
            column: [
              {
                label: "运单号",
                prop: "no",
                span: 12,
                placeholder: " ",
              },
              {
                label: "项目名称",
                prop: "projectName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "项目合作方",
                prop: "agentName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "状态",
                prop: "statusName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "地块",
                prop: "landParcel",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: '入场信息',
            prop: 'group',
            column: [
              {
                label: '入场拍照凭证',
                prop: 'iotInPicture',
                placeholder:" ",
                type:'upload',
                listType: "picture-card",
                dataType: 'string',
                span:24,
                placeholder:" ",
              }
            ]
          },
          {
            label: "挖机签单信息",
            prop: "group1",
            column: [
              {
                label: "挖机签单员",
                prop: "inStaffName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机土质",
                prop: "inSoilType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机车牌",
                prop: "inDriverTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机签单时间",
                prop: "inDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机班次",
                prop: "inShiftTypeName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机备注",
                prop: "inRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "凭证类型",
                prop: "inWeightUnit",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机拍照凭证",
                prop: "inPicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "称重仪器照片",
                prop: "inWeighInstrumentPicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "挖机签单地址",
                prop: "inAddr",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: "出场签单信息",
            prop: "group2",
            column: [
              {
                label: "运输类型",
                prop: "tpModeName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾",
                prop: "garbageName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场签单员",
                prop: "goStaffName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场土质",
                prop: "goSoilType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场车牌",
                prop: "goDriverTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场签单时间",
                prop: "goDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场班次",
                prop: "goShiftTypeName",
                span: 12,
                placeholder: " ",
              },
              {
                label: '入场重量',
                prop: 'inWeight',
                span:12,
                placeholder:" ",
              },
              {
                label: '出场重量',
                prop: 'outWeight',
                span:12,
                placeholder:" ",
              },
              {
                label: "单位",
                prop: "weightUnit",
                span: 12,
                placeholder: " ",
              },
              {
                label: "单价",
                prop: "unitPrice",
                span: 12,
                placeholder: " ",
              },
              {
                label: "数量",
                prop: "weightTons",
                span: 12,
                placeholder: " ",
              },
              {
                label: "价格",
                prop: "payeePrice",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾票土质",
                prop: "goTicketSoilType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾票号",
                prop: "ticketNo",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾票单价",
                prop: "ticketPrice",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场拍照凭证",
                prop: "goPicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: " 磅单票据",
                prop: "poundbillUrl",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "出场拍照泥尾票",
                prop: "ticketImg",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "出场备注",
                prop: "goRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "电子结算卡",
                prop: "settleCardNo",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出口签单车型",
                prop: "goVehicleType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场签单地址",
                prop: "goAddr",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: "司机信息",
            prop: "group3",
            column: [
              {
                label: "司机",
                prop: "driverName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "车牌",
                prop: "goDriverTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机出场时间",
                prop: "confirmGoDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机卸土时间",
                prop: "completeDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机卸土地址",
                prop: "completeAddr",
                span: 12,
                placeholder: " ",
              },
              {
                label: "直付类型",
                prop: "isPlatformDirectPayName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机联系方式",
                prop: "driverMobile",
                span: 12,
                placeholder: " ",
              },
              {
                label: "行驶证车辆车型",
                prop: "brandType",
                span: 12,
                placeholder: " ",
              },
            ],
          },
          {
            label: "空车入场签单",
            prop: "group4",
            column: [
              {
                label: "入场车牌",
                prop: "entranceTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "入场签单人",
                prop: "entranceStaffName",
                span: 12,
                placeholder: " ",
                overHidden: true,
              },
              {
                label: "入场签单时间",
                prop: "entranceDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "入场备注",
                prop: "entranceRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "入场拍照",
                prop: "entrancePicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: "回填入场签单",
            prop: "group5",
            column: [
              {
                label: "回填项目",
                prop: "backfillProjectName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "回填车牌",
                prop: "backfillTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "票号",
                prop: "backfillTicketNo",
                span: 12,
                placeholder: " ",
                overHidden: true,
              },
              {
                label: "备注",
                prop: "backfillRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "回填签单人",
                prop: "backfillSignName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "回填签单时间",
                prop: "backfillSignDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "拍照车辆",
                prop: "backfillPicUrl",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "拍照泥尾票",
                prop: "backfillTicketNoUrl",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
            ],
          },
        ],
      },
      calcHeight: 140,
      isFromDialog: false, //弹窗进来查看的
    };
  },
  created() {},
  mounted() {
    window.addEventListener("resize", this.func);
    //全屏组件特有
    if (JSON.stringify(this.info) != "{}") {
      console.log(111199999999999999999);
      this.searchForm = Object.assign({}, this.info);
      this.calcHeight = 50;
      this.isFromDialog = true;
      // this.paramsSearch.status=""
    }
    this.searchData();
    setTimeout(() => {
      let tableHeight = this.$refs.crud.$el.offsetTop;
      console.log(tableHeight);
      this.tableHeight = window.innerHeight - tableHeight - this.calcHeight;
      this.$refs.crud.doLayout();
      console.log(this.tableHeight);
    }, 300);
  },
  activated() {
    setTimeout(() => {
      let tableHeight = this.$refs.crud.$el.offsetTop;
      console.log(tableHeight);
      this.tableHeight = window.innerHeight - tableHeight - this.calcHeight;
      this.$refs.crud.doLayout();
      console.log(this.tableHeight);
    }, 300);
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        // addBtn: this.permissions['chain:waybillPreset:add'] ? true : false,
        // delBtn: this.permissions['chain:waybillPreset:del'] ? true : false,
        // editBtn: this.permissions['chain:waybillPreset:edit'] ? true : false,
        viewBtn: this.permissions["chain:waybillPreset:get"] ? true : false,
      };
    },
    selectLen() {
      return this.selectList ? this.selectList.length : 0;
    },
    //已选结算价合计金额
    settlePriceTotal() {
      return this.selectList
        .map((row) => (row.settlePrice ? row.settlePrice : 0))
        .reduce((acc, cur) => parseFloat(cur) + acc, 0)
        .toFixed(2);
    },
    //总数量
    weightTotal() {
      return this.selectList
        .map((row) => (row.weightTons ? row.weightTons : 0))
        .reduce((acc, cur) => parseFloat(cur) + acc, 0);
    },
    isDisabled() {
      return (
        this.selectList &&
        this.selectList.length > 0 &&
        this.selectList.every((item) => {
          return (
            item.weightUnit == this.selectList[0].weightUnit &&
            item.isSettle != 1
          );
        })
      );
    },
    isDisabledStatement() {
      return (
        this.selectList &&
        this.selectList.length > 0 &&
        this.selectList.every((item) => {
          return item.isSettle != 1;
        })
      );
    },
  },
  watch: {},
  methods: {
    searchReset(event) {
      this.minPayeePrice = undefined;
      this.maxPayeePrice = undefined;
    },
    submitPayee() {
      let data = this.selectList.map((item) => {
        return {
          id: item.id,
          payeeId: this.editForm.payeeId,
        };
      });
      console.log(data);
      this.$refs.editForm.validate((valid, done, msg) => {
        if (valid) {
          this.btnSetPayLoading = true;
          updateCarrierName(data)
            .then((res) => {
              this.btnSetPayLoading = false;
              this.$message.success("操作成功");
              this.setPayeeVisible = false;
              this.getPage(this.page);
            })
            .catch((err) => {
              this.btnSetPayLoading = false;
            });
        } else {
          this.btnSetPayLoading = false;
        }
        done();
      });
    },
    setPayee() {
      this.editForm.payeeId = "";
      this.editForm.mobile = "";
      this.editForm.bindingBankNo = "";
      this.editForm.bindingBankName = "";
      this.editForm.status = "";
      this.setPayeeVisible = true;
    },
    creatSettleWaybill() {
      let data = this.selectList.map((item) => {
        return item.id;
      });
      console.log(data);
      this.btnSettleLoading = true;
      batchAddWaybillSettlePre(data)
        .then((res) => {
          this.btnSettleLoading = false;
          if (res.data.msg) {
            this.$message.success(res.data.msg);
          } else {
            this.$message.success("操作成功");
          }
          this.getPage(this.page);
        })
        .catch((err) => {
          this.btnSettleLoading = false;
        });
    },
    getParam(path, name) {
      var reg = new RegExp("(^|\\?|&)" + name + "=([^&]*)(\\s|&|$)", "i");
      if (reg.test(path)) return unescape(RegExp.$2.replace(/\+/g, " "));
      return path;
    },
    changeInput(value) {
      this.inputValue = value;
      // console.log(value);
      // if (!this.lock) {
      //   let reg = /^[A-Z]\d{0,14}$/
      //   if(reg.test(value)){
      //     console.log(value);
      //     this.statements.push(this.getParam(value,'id'))
      //   }else{
      //     this.statements.push(value)
      //   }
      // }
      // if(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/.test(value)){
      // console.log(value);
      // this.getPage()
    },
    //中文输入
    onCompositionStart(e) {
      this.lock = true;
    },
    // onCompositionupdate(e){
    //   console.log(e);
    //   let reg = /^[A-Z]\d{0,14}$/
    //   if(reg.test(e.data)){
    //       console.log(e.data);
    //       e.data = this.getParam(e.data,'id')
    //     }else{
    //       e.data = ''
    //     }
    // },
    //中文输入
    onCompositionEnd(e) {
      console.log(e);
      // this.search.cardNo = this.getParam(e.data,'id')
      var reg = new RegExp("(^|\\?|&)" + "id" + "=([^&]*)(\\s|&|$)", "i");

      if (reg.test(e.data)) {
        if (
          this.statements.some(
            (v) => v == unescape(RegExp.$2.replace(/\+/g, " "))
          )
        ) {
          this.inputValue = "";
          return;
        }
        this.statements.push(unescape(RegExp.$2.replace(/\+/g, " ")));
      } else {
        if (this.statements.some((v) => v == e.data)) {
          this.inputValue = "";
          return;
        }
        this.statements.push(e.data);
      }
      this.lock = false;
      this.inputVisible = false;
      this.inputValue = "";
    },
    handleClose(tag) {
      this.statements.splice(this.statements.indexOf(tag), 1);
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm(e) {
      let inputValue = this.inputValue;

      if (inputValue) {
        if (!this.lock) {
          let reg = /^[A-Z]\d{0,14}$/;
          if (this.getParam(inputValue, "id")) {
            if (
              this.statements.some((v) => v == this.getParam(inputValue, "id"))
            ) {
              this.inputValue = "";
              return;
            }
            this.statements.push(this.getParam(inputValue, "id"));
          } else if (!this.getParam(inputValue, "id")) {
            if (this.statements.some((v) => v == inputValue)) {
              this.inputValue = "";
              return;
            }
            this.statements.push(inputValue);
          }
        }
        // this.statements.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    exportOut,
    func() {
      let tableHeight = this.$refs.crud.$el.offsetTop;
      this.tableHeight = window.innerHeight - tableHeight - this.calcHeight;
      this.$refs.crud.doLayout();
    },
    /* ------------------------------------------------------- input输入 ----------------------------------------------------- */
    handleInput(obj, value) {
      obj[value] = clearNoNum(obj[value] + "");
      // if (value == 'unitPrice' && obj[value] > 1000) {
      //   obj[value] = 1000
      // }
    },
    getmoreshow(type) {
      console.log(this.$refs.crud);
      this.isMore = type == 1;
      setTimeout(() => {
        let tableHeight = this.$refs.crud.$el.offsetTop;
        this.tableHeight = window.innerHeight - tableHeight - this.calcHeight;
      }, 300);
    },
    searchChange(params = {}, done) {
      params.minPayeePrice = this.minPayeePrice;
      params.maxPayeePrice = this.maxPayeePrice;
      params = this.filterForm(params);
      this.paramsSearch = params;
      console.log(this.paramsSearch);
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    searchData() {
      this.page.currentPage = 1;
      let params = this.filterForm(this.searchForm);
      console.log(params);
      console.log(this.paramsSearch);
      params.minPayeePrice = this.minPayeePrice;
      params.maxPayeePrice = this.maxPayeePrice;
      this.paramsSearch = params;
      this.getPage(this.page, params);
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.page.pageSize=val
      this.getPage(this.page);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);

      this.page.currentPage = val;
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("inDatetime")) {
          params.inDatetimeStart = params.inDatetime[0];
          params.inDatetimeEnd = params.inDatetime[1];
          delete params.inDatetime;
        }
        if (params.hasOwnProperty("goDatetime")) {
          params.goDatetimeStart = params.goDatetime[0];
          params.goDatetimeEnd = params.goDatetime[1];
          delete params.goDatetime;
        }
        if (params.hasOwnProperty("completeDatetime")) {
          params.completeDatetimeStart = params.completeDatetime[0];
          params.completeDatetimeEnd = params.completeDatetime[1];
          delete params.completeDatetime;
        }
        if (params.hasOwnProperty("goShiftTime")) {
          params.goShiftTimeStart = params.goShiftTime[0];
          params.goShiftTimeEnd = params.goShiftTime[1];
          delete params.goShiftTime;
        }
      }
      // if(this.statements.length>0){
      //   params.companySettleNo=this.statements.join(',')
      // }

      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    handlePageSize({ page, size }) {
      console.log(page);
      if (size != this.page.pageSize) {
        this.page.currentPage = 1;
      } else {
        this.page.currentPage = page;
      }
      this.page.pageSize = size;
      this.getPage(this.page);
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    filterImg(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? null : url[0];
    },
    filterImgs(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? [] : url;
    },
    closeVisible() {
      this.trackVisible = false;
    },
    calcAngle(start, end) {
      console.log(start);
      console.log(end);
      var p_start = this.map.lngLatToContainer(start),
        p_end = this.map.lngLatToContainer(end);
      var diff_x = p_end.x - p_start.x,
        diff_y = p_end.y - p_start.y;
      return (360 * Math.atan2(diff_y, diff_x)) / (2 * Math.PI) + 90;
    },
    //初始化地图
    initMap() {
      this.map = new AMap.Map("maps", {
        resizeEnable: true, //窗口大小调整
        center: this.firstArr, //中心 firstArr: [116.478935, 39.997761],
        zoom: 17,
      });
      var historyItem = null;
      this.graspRoadList.forEach((item) => {
        console.log(historyItem);
        console.log(item);
        item.ag = historyItem
          ? this.calcAngle(historyItem, [item.x, item.y])
          : 0;
        historyItem = [item.x, item.y];
      });
      this.graspRoad = new AMap.GraspRoad();
      this.graspRoad.driving(this.graspRoadList, function (error, result) {
        console.log(result);
        // if(!error){
        //   var path2 = [];
        //   var newPath = result.data.points;
        //   for(var i =0;i<newPath.length;i+=1){
        //     path2.push([newPath[i].x,newPath[i].y])
        //   }
        //   var newLine = new AMap.Polyline({
        //     path:path2,
        //     strokeWeight:8,
        //     strokeOpacity:0.8,
        //     strokeColor:'#0091ea',
        //     showDir:true
        //   })
        //   map.add(newLine)
        //   map.setFitView()
        // }
      });
      // 创建一个车 Icon
      var car = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(70, 69),
        // 图标的取图地址
        image: require("../../../static/trucks.png"),
        // 图标所用图片大小
        imageSize: new AMap.Size(69, 39),
        angle: 90,
      });
      this.marker = new AMap.Marker({
        map: this.map,
        position: this.firstArr,
        icon: car,
        offset: new AMap.Pixel(-20, -20),
        autoRotation: true,
        angle: -270,
      });
      // 创建一个起点 Icon
      var startIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(25, 34),
        // 图标的取图地址
        image:
          "//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(135, 40),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(-9, -3),
      });
      let startMarker = new AMap.Marker({
        map: this.map,
        position: this.firstArr,
        icon: startIcon,
        offset: new AMap.Pixel(-13, -30),
      });
      // 创建一个终点 Icon
      var endIcon = new AMap.Icon({
        size: new AMap.Size(25, 34),
        image:
          "//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
        imageSize: new AMap.Size(135, 40),
        imageOffset: new AMap.Pixel(-95, -3),
      });
      let endMarker = new AMap.Marker({
        map: this.map,
        position: this.lineArr[this.lineArr.length - 1],
        icon: endIcon,
        offset: new AMap.Pixel(-13, -30),
      });
    },
    //初始化轨迹
    initroad(row) {
      //绘制还未经过的路线
      this.polyline = new AMap.Polyline({
        map: this.map,
        path: this.lineArr,
        showDir: true,
        strokeColor: "#28F", //线颜色--蓝色
        // strokeOpacity: 1,     //线透明度
        strokeWeight: 6, //线宽
        // strokeStyle: "solid"  //线样式
      });
      // // 绘制路过了的轨迹
      var passedPolyline = new AMap.Polyline({
        map: this.map,
        strokeColor: "#AF5", //线颜色-绿色
        //path: this.lineArr,
        // strokeOpacity: 1,     //线透明度
        strokeWeight: 6, //线宽
        // strokeStyle: "solid"  //线样式
      });
      this.marker.on("moving", (e) => {
        passedPolyline.setPath(e.passedPath);
      });
      this.map.setFitView(); //合适的视口
    },
    startAnimation() {
      this.marker.moveAlong(this.lineArr, this.speed);
    },
    pauseAnimation() {
      this.marker.pauseMove();
    },
    resumeAnimation() {
      this.marker.resumeMove();
    },
    stopAnimation() {
      this.marker.stopMove();
    },
    exOut(e) {
      let params = Object.assign({}, this.paramsSearch);
      if (params) {
        if (params.hasOwnProperty("inDatetime")) {
          params.inDatetimeStart = params.inDatetime[0];
          params.inDatetimeEnd = params.inDatetime[1];
          delete params.inDatetime;
        }
        if (params.hasOwnProperty("goDatetime")) {
          params.goDatetimeStart = params.goDatetime[0];
          params.goDatetimeEnd = params.goDatetime[1];
          delete params.goDatetime;
        }
        if (params.hasOwnProperty("completeDatetime")) {
          params.completeDatetimeStart = params.completeDatetime[0];
          params.completeDatetimeEnd = params.completeDatetime[1];
          delete params.completeDatetime;
        }
        if (params.hasOwnProperty("goShiftTime")) {
          params.goShiftTimeStart = params.goShiftTime[0];
          params.goShiftTimeEnd = params.goShiftTime[1];
          delete params.goShiftTime;
        }
      }
      for (const key in params) {
        if (key.includes("$") || params[key] == undefined) {
          delete params[key];
        }
      }
      params.code = "BuilderCompanyWaybillPreSetPriceExcelExport";
      this.btnLoading = true;
      postCreateByCode(params)
        .then((res) => {
          this.btnLoading = false;
          // this.$message.success('操作123成功')
          this.$store.commit("SET_DOWN_EXCEL_SHOW", true);
        })
        .catch(() => {
          this.btnLoading = false;
          this.$message.error("导出失败");
        });
    },
    batchEdit() {
      this.editVisible = true;
    },
    selectionChange(e) {
      this.selectList = e;
    },
    submit() {
      let data = this.selectList.map((item) => {
        return {
          id: item.id,
          payeePrice: (this.unitPrice * item.weightTons).toFixed(2),
        };
      });
      this.btnLoading = true;
      updatePrice(data)
        .then((res) => {
          this.btnLoading = false;
          this.$message.success("操作成功");
          this.editVisible = false;
          this.getPage(this.page);
        })
        .catch((err) => {
          this.btnLoading = false;
        });
    },
    clearSelection() {
      this.$refs.crud.clearSelection();
    },
    checkChange(val = false) {
      let start = this.start;
      let end = this.end;
      if (start > end) {
        [start, end] = [end, start];
      }
      if (
        this.tableData &&
        this.tableData.length > 0 &&
        start <= this.tableData.length
      ) {
        if (end > this.tableData.length) {
          end = this.tableData.length;
        }
        console.log(start);
        console.log(end);
        console.log(val);
        let data = this.tableData.slice(start - 1, end);
        console.log(data);
        console.log(this.$refs.crud);
        this.$refs.crud.partRowSelections(data, val);
      }
    },
    selectComplete(arr) {
      if (arr && arr.length > 0) {
        let tmp = arr[0];
        let form = {
          payeeId: tmp.systemUserId,
          carrierName: tmp.payeeName,
          mobile: tmp.payeeMobile,
          bindingBankNo: tmp.bindingBankNo,
          bindingBankName: tmp.bindingBankName,
          status: tmp.isContract,
        };
        this.$refs.editForm.updateDic("payeeId", [form]);
        this.payeeDic = [form];
        // this.$set(this.editForm,'payeeId',form.payeeId)
        setTimeout(() => {
          this.editForm.payeeId = form.payeeId;
        }, 100);
        // this.editForm.mobile = tmp.payeeMobile;
        // this.editForm.bindingBankName = tmp.bindingBankNo;
        // this.editForm.bindingBankName = tmp.bindingBankName;
        // this.editForm.status = tmp.isContract;
      }
    },
    viewInfo(row, index) {
      this.tableLoading = true;
      getObj(row.id)
        .then((res) => {
          this.tableLoading = false;
          this.detailForm = res.data.data;
          this.detailForm.ticketNo =
            this.detailForm.ticketNo &&
            this.detailForm.manualSelectTicket == "0"
              ? `${this.detailForm.ticketNo}(系统分配)`
              : this.detailForm.ticketNo || "";
          this.detailVisible = true;
        })
        .catch((err) => {
          this.tableLoading = false;
        });
    },
  },
  destroyed() {
    window.removeEventListener("resize", this.func);
  },
};
</script>
<style>
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
<style lang="scss" scoped>
.input-card {
  position: absolute;
  right: 40px;
  bottom: 40px;
  background-color: #fff;
  padding: 10px;
  .my-row {
    margin-top: 15px;
  }
}
.remind {
  margin-top: 10px;

  div {
    color: #000;
    line-height: 30px;
    font-size: 16px;
  }

  p {
    color: #000;
    font-size: 16px;
    line-height: 28px;
  }

  span {
    font-size: 16px;
    color: red;
  }
}
/deep/ .el-dialog__body {
  padding-top: 0;
}
/deep/ .el-table {
  width: 99% !important;
  margin-left: 0.5%;
}
/deep/.el-pagination {
    padding: 19px 10px !important;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
.tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .tag {
    margin-right: 10px;
  }
}
.avue-crud__tip {
  height: 32px;
  margin-right: 6px;
}
</style>
