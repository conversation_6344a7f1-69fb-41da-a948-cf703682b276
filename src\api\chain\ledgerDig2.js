import request from '@/router/axios'

export function getPage(params) {
    return request({
        url: '/chain/ledgerDig/page',
        method: 'get',
        params
    })
}
//批量通过
export function batchConfirmByCheckStaff(data) {
    return request({
        url: '/chain/ledgerDig/batchConfirmForWorkFlow',
        method: 'post',
        data
    })
}
//批量驳回
export function batchRejectByCheckStaff(data) {
    return request({
        url: '/chain/ledgerDig/batchRejectForWorkFlow',
        method: 'post',
        data
    })
}
//核算员批量删除
export function delObj(data) {
    return request({
        url: '/chain/ledgerDig/batchDeleteByCheckStaff',
        method: 'post',
        data
    })
}
//获取权限
export function getSettingAll(params) {
    return request({
        url: 'chain/companyledgersetting/getSettingAll',
        method: 'get',
        params
    })
}
//获取权限
export function editPcLedgerRemark(data) {
    return request({
        url: 'chain/ledgerDig/editPcLedgerRemark',
        method: 'post',
        data
    })
}
//获取权限
export function getHistoryByLedgerDigId(params) {
    return request({
        url: '/chain/companyledgerdighistory/getHistoryByLedgerDigId',
        method: 'get',
        params
    })
}

export function batchEditPrice(data) {
    return request({
        url: 'chain/ledgerDig/batchEditPrice',
        method: 'post',
        data
    })
}
export function getWorkTimeStatistic(data) {
    return request({
        url: '/chain/ledgerDig/getWorkTimeStatistic',
        method: 'post',
        data
    })
}
export function getWorkFlowPage(data) {
    return request({
        url: '/chain/ledgerDig/getWorkFlowPage',
        method: 'post',
        data
    })
}
// 获取异常申请流程信息
export function getFlowNode(type,id) {
  return request({
      url: `/chain/companyapprovenode/getFlowNode/${type}/`+id,
      method: 'get',
  })
}

// 新增台班
export function addForWorkflow(data) {
    return request({
        url: `/chain/ledgerDig/addForWorkflowByBuilders`,
        method: 'post',
        data
    })
  }
