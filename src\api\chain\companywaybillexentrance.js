import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: 'chain/companywaybillexentrance/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companywaybillexentrance',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companywaybillexentrance/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companywaybillexentrance/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companywaybillexentrance',
        method: 'put',
        data: obj
    })
}
