import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/delivery/page',
        method: 'get',
        params: query
    })
}
export function getPage2(query) {
    return request({
        url: '/chain/deliveryItem/page',
        method: 'get',
        params: query
    })
}
// 修改收单人
export function putObj(params) {
    return request({
        url: '/chain/delivery/updateAcc',
        method: 'get',
        params
    })
}
// 确认付款
export function updateObj(data) {
    return request({
        url: '/chain/delivery/update',
        method: 'post',
        data
    })
}
// 撤销付款
export function revoke(data) {
    return request({
        url: '/chain/delivery/revoke',
        method: 'post',
        data
    })
}



