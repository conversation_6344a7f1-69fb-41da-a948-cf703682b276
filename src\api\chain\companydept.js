import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companydept/page',
        method: 'get',
        params: query
    })
}

export function tree() {
  return request({
      url: '/chain/companydept/tree',
      method: 'get',
  })
}

export function addObj(obj) {
    return request({
        url: '/chain/companydept',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companydept/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companydept/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companydept',
        method: 'put',
        data: obj
    })
}
