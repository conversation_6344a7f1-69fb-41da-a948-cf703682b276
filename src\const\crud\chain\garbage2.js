﻿export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: true,     // 显示搜索字段
  // excelBtn: true,
  // printBtn: true,
  labelWidth: 150,
  searchLabelWidth: 100,
  dialogClickModal: false,
  viewBtn: true,
  searchSpan:8,
  searchMenuSpan: 6,
  menuWidth:140,
  searchCustom:2,
  routerName:"garbage2",
  column: [
    {
      label: 'ID',
      prop: 'id',
      sortable: true,
      hide: true,   //列表页字段隐藏
      disabled: true,  //弹窗表单字段不允许输入
      display: false,  //弹窗表单字段隐藏
      rules: [
        {
          required: true,
          message: '请输入ID',
          trigger: 'blur'
        },
        {
          max: 36,
          message: '长度在不能超过36个字符'
        },
      ],
      minWidth:160,
      overHidden:true,
    },
    {
      label: '泥尾归属',
      prop: 'belong',
      sortable: true,
      hide: true,   //列表页字段隐藏
      showColumn:false,
      disabled: true,  //弹窗表单字段不允许输入
      display: false,  //弹窗表单字段隐藏
      rules: [
        {
          required: true,
          message: '请泥尾归属类型',
          trigger: 'blur'
        },
        {
          max: 5000,
          message: '长度在不能超过100个字符'
        },
      ]
    },
    {
      label: '消纳场所类型',
      prop: 'garbageType',
      type: 'select',   // 下拉选择
      search: true,
      order: 5,
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=garbage_record_type',
      rules: [
        {
          required: true,
          message: '请输入消纳场所类型',
          trigger: 'blur'
        },
        {
          max: 255,
          message: '长度在不能超过255个字符'
        },
      ],
      width:100,
      overHidden:true,
    },
    // {
    //   label: '代理商',
    //   prop: 'agentInfoId',
    //   sortable: true,
    //   hide: true,   //列表页字段隐藏
    //   disabled: true,  //弹窗表单字段不允许输入
    //   display: false,  //弹窗表单字段隐藏
    //   rules: [
    //     {
    //       required: true,
    //       message: '请输入代理商ID',
    //       trigger: 'blur'
    //     },
    //     {
    //       max: 36,
    //       message: '长度在不能超过36个字符'
    //     },
    //   ],
    // },
    // {
    //   label: '企业名称',
    //   prop: 'companyAuthId',
    //   sortable: true,
    //   hide: true,   //列表页字段隐藏
    //   disabled: true,  //弹窗表单字段不允许输入
    //   display: false,  //弹窗表单字段隐藏
    //   rules: [
    //     {
    //       required: true,
    //       message: '请输入企业认证编号',
    //       trigger: 'blur'
    //     },
    //     {
    //       max: 36,
    //       message: '长度在不能超过36个字符'
    //     },
    //   ]
    // },
    {
      label: '泥尾名称',
      prop: 'names',
      order: 1,
      sortable: true,
      search:true,
      rules: [
        {
          required: true,
          message: '请输入泥尾名称',
          trigger: 'blur'
        },
        {
          max: 500,
          message: '长度在不能超过500个字符'
        },
      ],
      minWidth:160,
      overHidden:true,
    },
    {
      label: '泥尾地址',
      prop: 'address',
      order: 2,
      sortable: true,
      rules: [
        {
          required: false,
          message: '请选择泥尾地址',
          trigger: 'change'
        },
        {
          max: 3000,
          message: '长度在不能超过3000个字符'
        },
      ],
      minWidth:160,
      overHidden:true,
    },
    {
      label: '入口坐标',
      prop: 'fence',
      order: 5,
      placeholder: '请在地图选择定位',
      disabled: true,
      sortable: true,
      rules: [
        {
          required: true,
          message: '请选择坐标',
          trigger: 'change'
        },
        {
          max: 3000,
          message: '长度在不能超过3000个字符'
        },
      ],
      width:160,
      overHidden:true,
    },
    {
      label: '',
      prop: 'map',
      order: 99,
      span: 24,
      hide: true,
      disabled: true,
      sortable: true,
      showColumn:false,
    },
    {
      label: '泥土类型',
      prop: 'soilTypes',
      order: 3,
      sortable: true,
      type: 'select',   // 下拉选择
      multiple: true,
      props: {
        label: "itemName",
        value: "itemValue",
      },
      // search:true,
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=soil_type',
      rules: [
        {
          required: true,
          message: '请选择泥土类型',
          trigger: 'change'
        },
      ],
      width:160,
      overHidden:true,
    },
    {
      label: '是否有效',
      prop: 'isDel',
      order: 4,
      sortable: true,
      type: 'select',   // 下拉选择
      search: true,
      dicData: [
        {
          label: '有效',
          value: '0'
        },
        {
          label: '无效',
          value: '1'
        },
      ],
      value: '0',
      rules: [
        {
          required: true,
          message: '逻辑删除',
          trigger: 'blur'
        },
        {
          max: 1,
          message: '长度在不能超过1个字符',
        },
      ],
      minWidth:94,
      overHidden:true,
    }
    ,
    {
      label: '创建时间',
      prop: 'createDatetime',
      sortable: true,
      hide: true,   //列表页字段隐藏
      disabled: true,  //弹窗表单字段不允许输入
      display: false,  //弹窗表单字段隐藏
      rules: [
        {
          required: true,
          message: '请输入创建日期时间',
          trigger: 'blur'
        },
      ],
      minWidth:170,
      overHidden:true,
    },
    // {
    //   label: '创建ID',
    //   prop: 'createId',
    //   sortable: true,
    //   hide: true,   //列表页字段隐藏
    //   disabled: true,  //弹窗表单字段不允许输入
    //   display: false,  //弹窗表单字段隐藏
    //   rules: [
    //     {
    //       required: true,
    //       message: '请输入创建ID',
    //       trigger: 'blur'
    //     },
    //     {
    //       max: 36,
    //       message: '长度在不能超过36个字符'
    //     },
    //   ],
    //   minWidth:94,
    //   overHidden:true,
    // },
    {
      label: '修改时间',
      prop: 'updateDatetime',
      sortable: true,
      disabled: true,  //弹窗表单字段不允许输入
      display: false,  //弹窗表单字段隐藏
      hide:true,
      rules: [
        {
          required: true,
          message: '请输入修改日期时间',
          trigger: 'blur'
        },
      ],
      width:140,
      overHidden:true,
    },
    // {
    //   label: '修改ID',
    //   prop: 'updateId',
    //   sortable: true,
    //   hide: true,   //列表页字段隐藏
    //   disabled: true,  //弹窗表单字段不允许输入
    //   display: false,  //弹窗表单字段隐藏
    //   rules: [
    //     {
    //       required: true,
    //       message: '请输入修改ID',
    //       trigger: 'blur'
    //     },
    //     {
    //       max: 36,
    //       message: '长度在不能超过36个字符'
    //     },
    //   ],
    //   minWidth:80,
    //   overHidden:true,
    // },
    {
      label: '泥尾票设置',
      prop: 'garbageSet',
      type: 'radio',
      order: 6,
      span:24,
      search:true,
      dicData: [{
        label: '无票出场',
        value: '0'
      }
      ,{
        label: '内部票出场',
        value: '1'
      }
      ,{
        label: '外部票出场',
        value: '2'
      }
    ],
      minWidth:94,
      overHidden:true,
      value: '0'
    },
    {
      label: '手动创建时间',
      prop: 'manualDatetime',
      span:12,
      order: 6,
      addDisplay:false,
      editDisplay:false,
      hide:true,
      showColumn:false,
    },
    {
      label: '撮合成功时间',
      prop: 'matchDatetime',
      span:12,
      order: 6,
      addDisplay:false,
      editDisplay:false,
      hide:true,
      showColumn:false,
    },
  ]
}
