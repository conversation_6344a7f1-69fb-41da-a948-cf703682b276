<template>
  <div class="captainManage">
    <basic-container>
      <my-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 v-model="form"
                 @on-load="getPage"
                 @refresh-change="refreshChange"
                 @sort-change="sortChange"
                 @search-change="searchChange">
                 <template slot="menuLeft" slot-scope="scope">
                  <el-button
                    type="primary"
                    v-if="permissions['chain:captainManage:select']"
                    icon="el-icon-plus"
                    size="small"
                    @click="add">
                  选择车队长</el-button>
                </template>
                 <template slot="menu" slot-scope="{row}">
                  <el-button
                    type="text"
                    v-if="permissions['chain:captainManage:authorizedProject']"
                    icon="el-icon-connection"
                    size="small"
                    plain
                    @click="getProjectList(row)">
                  授权项目</el-button>
                </template>
      </my-crud>
    </basic-container>
    <!-- 选择车队长 -->
    <el-dialog
      title="选择车队长"
      width="500px"
      center
      :visible.sync="visible"
      :close-on-click-modal="false"
    >
    <avue-form ref="form2"
      v-model="form2"
      :option="option2">
      <template slot-scope="{disabled,size}" slot="mobile">
        <el-input  :size="size" maxlength="11" v-model="form2.mobile" placeholder="请输入 手机号">
          <template slot="append">
            <!-- <span style="color: #66b1ff; cursor: pointer">查找</span> -->
            <el-button type="primary" :loading="btnLoading" @click="getInfo">查找</el-button>
          </template>
        </el-input>
      </template>
    </avue-form>
    <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="submitForm('form2')"
          >确 认</el-button
        >
        <el-button size="small" @click="visible = false">取 消</el-button>
      </span>
  </el-dialog>
    <!-- 选择车队长 -->
    <el-dialog
      title="授权项目"
      width="500px"
      center
      :visible.sync="projectVisible"
      :close-on-click-modal="false"
    >
      <el-table
      v-if="projectData&&projectData.length>0"
      :data="projectData"
      style="width: 100%">
      <el-table-column
        label="序号"
        width="180">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column
        prop="projectName"
        label="项目名称"
        width="180">
        <!-- <template slot-scope="{row,index}">
          <span>{{row.projectName}}</span>
        </template> -->
      </el-table-column>
    </el-table>
    <el-empty :image-size="100" description="暂无授权项目" v-else></el-empty>

    <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="projectVisible = false">确 定</el-button>
      </span>
  </el-dialog>

  </div>
</template>



<script>
import {getPage,putObj,findCaptain,getAuthorizationProjectList} from '@/api/chain/captainManage'
import {tableOption} from '@/const/crud/chain/captainManage'
import {mapGetters} from 'vuex'
import {isMobileNumber,mobileReg} from '@/util/validate'

export default {
  name: 'captainManage',
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      visible:false,
      btnLoading:false,
      form2: {},
      option2: {
        submitBtn:false,
        emptyBtn:false,
        column: [
          {
            label: "手机号",
            prop: "mobile",
            span:24,
            maxlength:11,
            formslot:true,
            rules: [
              {
                required: true,
                message: "请输入手机号",
                trigger: "blur",
              },
              {
                validator: isMobileNumber,
                trigger: "blur",
              },
            ]
          },
          {
            label: "车队长",
            prop: "name",
            span:24,
            disabled:true,
            rules: [{
              required: true,
              message: '车队长不能为空',
              trigger: "change"
            }]
          },
          {
            label: "是否实名",
            prop: "realNameAuthentication",
            type:'select',
            span:24,
            disabled:true,
            dicData: [
              {
                label: '否',
                value: '0'
              },
              {
                label: '是',
                value: '1'
              },
            ],
            rules: [{
              required: true,
              message: '不能为空',
              trigger: "change"
            }]
          },
          {
            label: "身份证号",
            prop: "idCard",
            span:24,
            disabled:true,
            rules: [{
              required: true,
              message: '不能为空',
              trigger: "change"
            }]
          },
          {
            label: "银行卡号",
            prop: "bindingBankNo",
            span:24,
            disabled:true,
            rules: [{
              required: true,
              message: '不能为空',
              trigger: "change"
            }]
          },
        ]
      },
      projectVisible:false,
      projectData:[]
    }
  },
  created() {
  },
  mounted() {
    //如果没有一个按钮就要干掉操作栏
      this.tableOption.menu = this.permissions['chain:captainManage:authorizedProject']
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['chain:captainManage:add'] ? true : false,
        delBtn: this.permissions['chain:captainManage:del'] ? true : false,
        editBtn: this.permissions['chain:captainManage:edit'] ? true : false,
        viewBtn: this.permissions['chain:captainManage:get'] ? true : false
      };
    }
  },
  methods: {
    searchChange(params,done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    add(){
      this.visible = true
    },
    getInfo(){
      let isMobile =  mobileReg.test(this.form2.mobile)
      if(!isMobile){
        this.$message.error('请输入正确的手机号码')
        return false
      }
      this.btnLoading = true
      findCaptain(this.form2.mobile).then(res=>{
        this.form2.id = res.data.data.id
        this.form2.name = res.data.data.name
        this.form2.realNameAuthentication = res.data.data.realNameAuthentication||'0'
        this.form2.idCard = res.data.data.idCard||''
        this.form2.bindingBankNo = res.data.data.bindingBankNo||''
        this.btnLoading = false
      }).catch(err=>{
        this.form2.id = ''
        this.form2.name = ''
        this.form2.realNameAuthentication = ''
        this.form2.idCard = ''
        this.form2.bindingBankNo = ''
        this.btnLoading = false
      })
    },
    submitForm(formName){
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let form = {id:this.form2.id}
          putObj(form).then((res) => {
            this.$message.success("提交成功");
            this.$refs.form2.resetForm()
            this.visible = false;
            this.getPage(this.page);
          });
        } else {
          return false;
        }
      });
    },
    getProjectList(row){
      this.projectData =[]
      getAuthorizationProjectList(row.captainId).then(res=>{
        this.projectVisible = true
        this.projectData = res.data.data
      })
    },
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body .tagName{
  margin-right: 6px;
  margin-bottom: 10px;
}
</style>
