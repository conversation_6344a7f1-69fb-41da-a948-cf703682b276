<template>
  <div class="driverTaskDetail">
    <el-drawer size="600px"
               title="修改计划"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <basic-container>
        <avue-form :option="option"
                   v-model="form"
                   @submit="submit"
                   ref="form">
          <template slot="customPrice">
            <!-- 勾选了土质 -->
            <span v-if="form.matchSoilSetDtoList&&form.matchSoilSetDtoList.length>0">
              <div v-for="(item2,index2) in form.matchSoilSetDtoList"
                   :key="index2"
                   class="flex flex-items-center;"
                   style="margin-bottom:10px">
                <span style="display: inline-block;width: 70px;">{{item2.soil}}</span>
                <avue-input-number style="flex:1;margin:0px 10px;max-width:200px"
                                   :precision="2"
                                   size="small"
                                   :controls="false"
                                   :min-rows="0"
                                   :max-rows="999999999.99"
                                   v-model="item2.unitPrice"></avue-input-number>
                <el-radio-group v-model="item2.weightUnit">
                  <el-radio-button label="车"></el-radio-button>
                  <el-radio-button label="方"></el-radio-button>
                  <el-radio-button label="吨"></el-radio-button>
                </el-radio-group>
              </div>
            </span>
            <span v-else></span>

          </template>
          <template slot="menuForm">
            <el-button icon="el-icon-close"
                       @click="cancelModal">取消</el-button>
          </template>
        </avue-form>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { editPlan,getCountByPlanId } from "@/api/chain/planManage";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  data () {
    return {
      form: {},
      option: {
        submitBtn: true,
        emptyBtn: false,
        labelWidth: 120,
        //弹窗区分
        column: [
          {
            label: "计划名称",
            prop: "planName",
            span: 24,
            row: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "出土方量",
            prop: "outSoilCube",
            type: "number",
            formslot: true,
            append: 'm³',
            placeholder: "请输入 出土方量,单位m³",
            controls: false,
            minRows: 0,
            maxRows: 999999999.99,
            precision: 2,
            span: 24,
            row: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "土质",
            prop: "goSoilType",
            multiple: true,
            type: 'select',   // 下拉选择
            props: {
              label: "itemName",
              value: "itemValue",
            },
            span: 24,
            row: true,
            dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=match_soil_type',
            filterable: true,  //是否可以搜索
            change: ({ value,column }) => {
              console.log(value);
              console.log(column,"column");
              this.filterSoilData(value)
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "出土时间段",
            prop: "outSoilTime",
            type: "daterange",
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            span: 24,
            row: true,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "不区分土质/车",
            prop: "flatPrice",
            type: "number",
            append: '元',
            placeholder: "请输入 价格,单位元",
            controls: false,
            minRows: 0,
            maxRows: 999999999.99,
            precision: 2,
            span: 18,
            row: true,
            rules: [
              {
                required: true,
                message: "请输入价格",
                trigger: "change",
              },
            ],
          },
          {
            label: "",
            prop: "customPrice",
            span: 24,
            row: true,
          },
        ]
      },
      first:true,
    };
  },
  created () {
    this.form = Object.assign({}, this.info)
    this.form.matchSoilSetDtoList = this.form.matchSoilSettVoList
    this.form.goSoilType = this.form.matchSoilSettVoList&&this.form.matchSoilSettVoList.map(item => item.soil)
    if (this.form.outSoilTimeStart && this.form.outSoilTimeEnd) {
      this.form.outSoilTime = [this.form.outSoilTimeStart, this.form.outSoilTimeEnd]
    }
  },
  mounted () {
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    filterSoilData (val) {
      console.log(val);
      if (val && val.length > 0) {
        //判断新增还是删除
        let beforeSoil = this.form.matchSoilSetDtoList.map(item => item.soil)
        let diff = [];
        if (beforeSoil && beforeSoil.length > 0) {
          let set = new Set(val);
          diff = beforeSoil.filter((v) => !set.has(v));
          console.log('diff', diff);
        }
        let add = []
        console.log(beforeSoil);
        if (val && val.length > 0) {
          let set = new Set(beforeSoil);
          add = val.filter((v) => !set.has(v));
          console.log('add', add);
        }
        if (diff.length > 0) {
          //删除
          console.log('删除');
          diff.forEach(item2 => {
            let index2 = this.form.matchSoilSetDtoList.findIndex(item3 => item3.soil == item2)
            this.form.matchSoilSetDtoList.splice(index2, 1)
          })
        }
        if (add.length > 0) {
          //新增
          console.log('新增');
          add.forEach(item2 => {
            this.form.matchSoilSetDtoList.push({
              soil: item2,
              unitPrice: "",
              weightUnit: "吨",
            })
          })
        }

      } else {
        console.log(111);
        //第一次请求一进来是空的
        if(this.first){
          this.first = false
        }else{
          this.form.matchSoilSetDtoList = []
        }
      }
      // console.log(this.editableTabs);
      // console.log(arr);
      // return arr.length>0
    },
    submit (form, done) {
      //根据计划查询是否存在可操作的队列
      getCountByPlanId({ matchProjectPlanId: this.info.id }).then(res => {
        if (res.data.data && res.data.data != 0) {
          this.$confirm('修改计划会自动取消当前等待确认的撮合队列', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.editPlan(form,done)
          }).catch(() => {
            done()
          });
        }else{
          this.editPlan(form,done)
        }
      })

    },
    editPlan (form, done) {
      form.outSoilTimeStart = form.outSoilTime[0]
      form.outSoilTimeEnd = form.outSoilTime[1]
      editPlan(form).then(res => {
        done()
        this.$message.success("操作成功！")
        this.$emit("update:visible", false);
        this.$emit("refreshChange");
      }).catch(() => {
        done()
      })
    }
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
</style>
