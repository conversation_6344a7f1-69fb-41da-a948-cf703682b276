<template>
  <div class="outputValueProcessSettings">
    <basic-container>
      <el-tabs v-model="activeName">
        <el-tab-pane label="流程设置" name="1">
          <el-form label-width="54px"
                  v-loading="loading"
                  element-loading-text="拼命加载中"
                  element-loading-spinner="el-icon-loading">
            <el-form-item label="是否开启审核流程："
                          labelWidth="150"
                          v-if="permissions['chain:outputValueProcessSettings:open']">
              <el-switch v-model="isOpen"></el-switch>
              <span style="color:red;margin-left:20px;font-size:14px">更改状态后请记得提交哦!</span>
            </el-form-item>
            <div class="steps">
              <div class="steps-label">
                配置产值审批流程：
              </div>
              <div class="step_box">
                <div class="step"
                    v-for="(v, i) in list"
                    :key="i"
                    v-if="list&&list.length>0">
                  <div class="list">
                    <div class="title">第{{nums[i]}}步</div>
                    <div class="content">
                      <el-form-item label="职位：">
                        <avue-select v-model="v.companyPositionId"
                                    placeholder="请选择职位"
                                    type="tree"
                                    multiple
                                    :tags="true"
                                    dataType="string"
                                    :dic="positionList"></avue-select>
                      </el-form-item>
                      <i class="el-icon-user" @click="showPopover(v,i)" style="cursor: pointer;position: absolute;right: 8px;top: 28px;font-size: 18px;"></i>
                      <div class="btn">
                        <el-button type="text"
                                  @click="del('list',i)">删除</el-button>
                      </div>
                    </div>
                  </div>
                  <i class="el-icon-arrow-right"
                    v-if="i<8"></i>
                </div>
                <div class="add"
                    @click="addBefore('list')"
                    v-if="list&&list.length<9">
                  <i class="el-icon-plus"></i>
                </div>
              </div>
            </div>
            <div class='confirm-box'
                v-if="permissions['chain:outputValueProcessSettings:save']">
              <el-button type='success'
                        size='medium'
                        style='padding:8px 18px; font-size:14px;'
                        @click="confirm">提交</el-button>
            </div>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="修改记录" name="2">
          <editLog @viewFlow="viewFlow" :approveType="5" ref="editLog">
          </editLog>
        </el-tab-pane>
      </el-tabs>

    </basic-container>
    <poPerson v-if="personVisible" :personNames="personNames" :visible.sync="personVisible"></poPerson>
    <flowView v-if="flowVisible" :flowList="flowList" :visible.sync="flowVisible"></flowView>
  </div>
</template>

<script>
import { getPage, addObj, getPositionList } from '@/api/chain/outputValueProcessSettings'
import { getCompanyStaffByPositionId } from '@/api/chain/waybillProcessSettings'
import { mapGetters } from 'vuex'
import poPerson from "@/views/chain/waybillProcessSettings/poPerson"
import editLog from "@/views/chain/waybillProcessSettings/editLog"
import flowView from "@/views/chain/waybillProcessSettings/flowView"
export default {
  name: 'outputValueProcessSettings',
  components: {
    poPerson,
    editLog,
    flowView,
  },
  data () {
    return {
      list: [],
      positionList: [],
      isOpen: false,
      nums: ["一", "二", "三", "四", "五", "六", "七", "八", '九'],
      loading: false,
      personNames:"",
      personVisible:false,
      activeName:"1",
      flowList:[],
      flowVisible:false,
    }
  },
  created () {
    //获取流程列表
    this.getPage()
    //获取职位选择列表
    this.getPositionList()
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    getPage () {
      this.loading = true
      getPage().then(res => {
        this.loading = false
        this.list = res.data.data.outputSumFlowList || []
        this.isOpen = res.data.data.outputSumApprove == 1
      }).catch(() => { this.loading = false })
    },
    getPositionList () {
      getPositionList().then(res => {
        this.positionList = res.data.data.map(item => {
          return {
            label: item.positionName,
            value: item.id,
          }
        })
      })
    },
    addBefore (list) {
      this[list].push({ companyPositionId: '' })
    },
    del (list, i) {
      this[list].splice(i, 1)
    },
    confirm () {
      if(this.isOpen==1&&this.list.length==0){
        this.$message.error(`开启审批流程时，审批流程必需添加审批职位!`)
        return false
      }
      let listIsEmpty = true

      if (this.list && this.list.length > 0) {
        listIsEmpty = this.list.every((item, index) => {
          if (item.companyPositionId == '') {
            this.$message.error(`请选择配置台班审批流程第${index + 1}步的审批职位`)
          }
          return item.companyPositionId != ''
        })
      }
      if (!listIsEmpty) return false

      let param = {
        outputSumFlowList: this.list.map((item, index) => {
          return {
            companyPositionId: item.companyPositionId,
            approveStep: index + 1,
          }
        }),

        outputSumApprove: this.isOpen ? 1 : 0
      }
        this.loading = true
        addObj(param).then(res => {
          this.loading = false
          this.$message.success('更新成功')
          this.getPage()
          //更新记录
          this.$refs.editLog.refreshChange()
        }).catch(() => { this.loading = false })

    },
    showPopover(item,index){
      if(!item.companyPositionId){
        this.$message.error("请选择职位!")
        return false
      }
      let param = {
        companyPositionIdList:item.companyPositionId.split(",")
      }
      getCompanyStaffByPositionId(param).then(res=>{
        this.personNames = res.data.data
        this.personVisible = true
      })
    },
    viewFlow(list,done){
      this.flowList = list
      this.flowVisible = true
      done()
    },
  }

}
</script>

<style lang="scss" scoped>
.outputValueProcessSettings {
  height: 100%;
  .steps {
    display: flex;
    align-items: flex-start;
    border-bottom: 1px dotted #bbb;
    margin-bottom: 20px;
    .steps-label {
      min-width: 140px;
      font-size: 14px;
      color: #606266;
    }
  }
  .step_box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    /deep/ .step {
      display: flex;
      align-items: center;
      margin-bottom: 14px;
      .title {
        background-color: #1890ff;
        color: #fff;
        text-align: center;
        height: 34px;
        line-height: 34px;
      }
      .list {
        width: 280px;
        border: 1px solid #ccc;
        border-radius: 4px;
        position: relative;
        .content {
          padding-right: 30px;
          padding-top: 20px;
          position: relative;
        }
        .btn {
          position: relative;
          height: 34px;
          text-align: center;
          left: 0;
          bottom: 0;
        }
      }
      .el-icon-arrow-right {
        font-size: 40px;
        color: #666;
      }
      .el-form-item {
        margin-bottom: 0;
        .el-form-item__label {
          padding-right: 0px;
        }
      }
    }
    .add {
      width: 276px;
      height: 122px;
      border: 2px dotted #ccc;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      margin-bottom: 14px;
      cursor: pointer;
      .el-icon-plus {
        font-size: 40px;
        color: #666;
      }
    }
  }
  .confirm-box {
    text-align: center;
    margin-top: 30px;
  }
}
</style>
