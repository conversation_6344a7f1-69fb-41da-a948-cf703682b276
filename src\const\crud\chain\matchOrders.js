export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  index: true,
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  dialogClickModal: false,
  dialogWidth: 500,
  excelBtn: false,
  printBtn: false,
  addBtn: false,
  viewBtn: false,
  searchSpan: 6,
  searchLabelWidth:86,
  searchMenuSpan: 6,
  labelWidth: 100,
  column: [
    // {
    //   label: "创建时间",
    //   prop: "createDatetime",
    //   sortable: "custom",
    //   minWidth: 160,
    //   overHidden: true,
    // },
    {
      label: "订单编号",
      prop: "orderNo",
      sortable: "custom",
      search:true,
      minWidth: 160,
      overHidden: true,
    },
    {
      label: "出土计划编号",
      prop: "outPlanNo",
      sortable: "custom",
      minWidth: 160,
      overHidden: true,
    },
    {
      label: "项目名称",
      prop: "projectName",
      search: true,
      minWidth: 160,
      overHidden: true,
    },
    {
      label: "出土计划方量(m³)",
      prop: "outSoilCube",
      minWidth: 120,
      overHidden: true,
    },
    {
      label: "出土土质",
      prop: "outSoil",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "收土计划编号",
      prop: "inPlanNo",
      sortable: "custom",
      minWidth: 160,
      overHidden: true,
    },
    // {
    //   label: "公司名称",
    //   prop: "companyName",
    //   search: true,
    //   minWidth: 160,
    //   overHidden: true,
    // },
    {
      label: "泥尾点名称",
      prop: "garbageName",
      search: true,
      minWidth: 160,
      overHidden: true,
    },
    {
      label: "泥尾地址",
      prop: "garbageAddress",
      // search: true,
      minWidth: 160,
      overHidden: true,
    },
    {
      label: "结算周期",
      prop: "settleCycle",
      search: true,
      sortable: "custom",
      // type: "select",
      // dicData: [
      //   {
      //     label: "日结",
      //     value: "日结",
      //   },
      //   {
      //     label: "周结",
      //     value: "周结",
      //   },
      //   {
      //     label: "月结",
      //     value: "月结",
      //   },
      //   {
      //     label: "季结",
      //     value: "季结",
      //   },
      //   {
      //     label: "年",
      //     value: "年",
      //   },
      // ],
      minWidth: 160,
      overHidden: true,
    },
    {
      label: "收土计划方量(m³)",
      prop: "recycleSoilCube",
      minWidth: 120,
      overHidden: true,
    },
    {
      label: "收土土质",
      prop: "receiveSoil",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "企业确定时间",
      prop: "projectConfirmDatetime",
      minWidth: 140,
      overHidden: true,
    },
    {
      label: "泥尾确定时间",
      prop: "garbageConfirmDatetime",
      minWidth: 140,
      overHidden: true,
    },
    // {
    //   label: "日期",
    //   prop: "searchDate",
    //   type: "select",
    //   search: true,
    //   display: false,
    //   type: "date",
    //   valueFormat: "yyyy-MM-dd",
    //   searchRange: true,
    //   hide: true,
    //   showColumn: false,
    // },
    {
      label: "订单状态",
      prop: "orderStatus",
      search: true,
      type: "select",
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=match_order_status',
      minWidth: 80,
      overHidden: true,
    },
  ],
};
