<template>
  <div class="searchInfo">
    <slot name="count"></slot>
    <div class="search flex flex-between">
      <el-radio-group v-model="form.checkDynamic" size="small" style="margin-right:10px;min-width:160px"
        @change="changeStatus">
        <el-radio-button label="">
          全部项目
        </el-radio-button>
        <el-radio-button label="1">
          活跃项目
        </el-radio-button>
      </el-radio-group>
      <slot name="searchLeft"></slot>
      <div class="searchContent">
        <slot name="searchRight" class="item"></slot>
        <div>
          <span style="font-size:14px;">运单修改项：</span>
          <el-select v-model="form.itemFieldValue" class="item" size="small" filterable placeholder="请选择运单修改项" clearable
            style="width:260px;margin-right:10px">
            <el-option v-for="item in editList" :key="item.itemValue" :label="item.itemName" :value="item.itemValue">
            </el-option>
          </el-select>
        </div>
        <el-radio-group v-model="radio" size="small" @change="changeTime" class="item">
          <el-radio-button label="week">最近一周</el-radio-button>
          <el-radio-button label="month">最近一个月</el-radio-button>
          <el-radio-button label="year">最近一年</el-radio-button>
        </el-radio-group>
        <div class="item">
          <span style="font-size:14px;margin-left:8px">申请时间：</span>
          <el-date-picker style="margin-right: 8px;width:300px" :editable="false" v-model="form.searchTime" type="daterange" :pickerOptions="pickerOptions"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" :clearable="false">
          </el-date-picker>
        </div>
        <div class="item">
          <el-button type="primary" size="small" @click="search" :loading="btnLoading">搜索</el-button>
          <el-button type="primary" size="small" @click="exOut" :loading="btnLoading">导出</el-button>
        </div>
      </div>
    </div>
    <projectList v-if="projectList && projectList.length > 0" text="异常运单数" @changeProject="changeProject"
      :active="form.projectInfoId" :projectList="projectList" :defaultProp="defaultProp">
    </projectList>
  </div>
</template>

<script>
import projectList from './projectList';
import { getWaybillUpdateHitTargetHeadCount } from "@/api/chain/board";
import { listDictionaryItem} from "@/api/chain/projectinfo";
export default {
  props: {
    //传进来的数据
    info: {},
  },
  components: {
    projectList
  },
  data () {
    return {
      projectList: [],
      form: {},
      projectInfo: {},
      radio: "",
      btnLoading: false,
      defaultProp: {
        label: "projectName", value: "projectInfoId", cnt: "waybillCnt"
      },
      itemFieldValue: "",
      editList: [],
      // 时间跨度为之前一年
      pickerOptions: {
        disabledDate: () => false,
        onPick: ({ maxDate, minDate }) => {
          if (minDate && !maxDate) {
            const oneYear = 365 * 24 * 60 * 60 * 1000;
            this.pickerOptions.disabledDate = time => {
              return time.getTime() < minDate.getTime() || time.getTime() > minDate.getTime() + oneYear;
            };
          } else {
            this.pickerOptions.disabledDate = () => false;
          }
        },
      },
    }
  },
  created () {
    this.form = {
      checkDynamic: this.info.checkDynamic,
      projectInfoId: this.info.projectInfoId || '',
      itemFieldValue:"",
      searchTime:[],
    }
    this.radio = this.info.radio
    if (this.info.startDate) {
      this.form.searchTime = [this.info.startDate, this.info.endDate]
    }
    this.getProjectList()
    this.getList()
  },
  mounted () {
  },
  methods: {
    getProjectList () {
      let param = {
        auditDatetimeBegin: this.form.searchTime[0],  //开始时间
        auditDatetimeEnd: this.form.searchTime[1],  //结束时间
        isHProjectInfoCount: this.form.checkDynamic,
        sourceType: 1,
        itemFieldValue: this.form.itemFieldValue,
      }
      getWaybillUpdateHitTargetHeadCount(param).then(res => {
        let arr = [
          {
            projectInfoId: "",
            projectName: this.form.checkDynamic == 1 ? "活跃项目" : '全部项目',
            waybillCnt: this.form.checkDynamic == 1 ? res.data.data.hcompanyWaybillCount : res.data.data.allCompanyWaybillCount,
          }
        ]
        this.projectList = arr.concat(res.data.data.list || [])
        console.log(param);
      })
    },
    getList () {
      listDictionaryItem({ dictionary: 'waybill_update_hit_target' }).then(res => {
        this.editList = res.data.data
      })
    },
    changeStatus (val) {
      //更换项目列表  有些表格需要变换数据
      this.getProjectList()
      this.form.projectInfoId = ""
      this.searchData()
    },
    changeProject (val) {
      this.form.projectInfoId = val
      console.log(this.form.projectInfoId);
      this.searchData()
    },
    changeTime (val) {
      let startDate = ""
      let endDate = this.$moment().format('YYYY-MM-DD')
      switch (val) {
        case 'week':
          startDate = this.$moment().subtract(7, 'days').format('YYYY-MM-DD');
          break;
        case 'month':
          startDate = this.$moment().subtract(1, 'months').format('YYYY-MM-DD');
          break;
        case 'year':
          startDate = this.$moment().subtract(1, 'years').format('YYYY-MM-DD');
          break;

        default:
          break;
      }
      this.form.searchTime = [startDate, endDate]
      console.log(val);
    },
    search () {
      this.searchData()
      this.getProjectList()
    },
    searchData () {
      let param = {
        startDate: this.form.searchTime[0],  //开始时间
        endDate: this.form.searchTime[1],  //结束时间
        checkDynamic: this.form.checkDynamic,  //是否活跃项目
        projectInfoId: this.form.projectInfoId,
        itemFieldValue: this.form.itemFieldValue,
      }
      this.btnLoading = true
      this.$emit('searchChange', param, this.stopLoading)
    },
    stopLoading () {
      this.btnLoading = false
    },
    exOut () {
      let param = {
        startDate: this.form.searchTime[0],  //开始时间
        endDate: this.form.searchTime[1],  //结束时间
        checkDynamic: this.form.checkDynamic,  //是否活跃项目
        projectInfoId: this.form.projectInfoId,
        itemFieldValue: this.form.itemFieldValue,
      }
      this.$emit("exOut", param, this.stopLoading)
    },
  },
}
</script>

<style lang="scss" scoped>
.search {
  .searchContent {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .item {
      flex-grow: 1;
      margin-bottom: 10px;
    }
  }
}
</style>
