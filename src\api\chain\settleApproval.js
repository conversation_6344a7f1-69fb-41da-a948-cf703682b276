import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companysettle/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companysettle',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companysettle/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companysettle/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companysettle',
        method: 'put',
        data: obj
    })
}
//获取结算单的运单信息
export function getSettleWaybill(obj) {
    return request({
        url: '/chain/companysettlewaybill/getSettleWaybill',
        method: 'post',
        data: obj
    })
}
//提交核算明细
// export function submitSettleWaybill(obj) {
//     return request({
//         url: '/chain/companysettlewaybill/submitSettleWaybill',
//         method: 'post',
//         data: obj
//     })
// }
//驳回申请
export function rejectSettle(query) {
    return request({
        url: '/chain/companysettle/rejectSettle',
        method: 'get',
        params: query
    })
}
//获取泥尾类型
export function getGarbageList(query) {
    return request({
        url: '/chain/companysettlewaybill/getGarbage',
        method: 'get',
        params: query
    })
}
//查看核算单
export function printSettle(query) {
    return request({
        url: '/chain/companysettle/printSettle',
        method: 'get',
        params: query
    })
}
//结算单的运单详情
export function printWaybillDetail(query) {
    return request({
        url: '/chain/companysettle/printWaybillDetail',
        method: 'get',
        params: query
    })
}
//查询我的待审批，已审批
export function getSettleList(params) {
  return request({
      url: '/chain/companysettle/getSettleList',
      method: 'get',
      params: params
  })
}
//结算单详情(可用于审批详情)
export function getSettleInfo(id) {
  return request({
      url: '/chain/companysettle/getSettleInfo/' + id,
      method: 'get'
  })
}
//结算单审核
export function flowDeal(obj) {
  return request({
      url: '/chain/companysettle/flowDeal',
      method: 'post',
      data: obj
  })
}
// 查询我的待审批，已审批
export function getMySettleFlow(params) {
  return request({
      url: '/chain/companysettle/getMySettleFlow',
      method: 'get',
      params: params
  })
}
export function getCompanyWaybillKuaiQianEvidence(data) {
  return request({
      url: '/chain/companyWaybillPage/getCompanyWaybillKuaiQianEvidence',
      method: 'post',
      data
  })
}
export function downPaymentApplyPdf(params) {
  return request({
      url: '/chain/companysettle/downPaymentApplyPdf',
      method: 'get',
      params,
      responseType: 'blob',
  })
}
export function downCompanySettleAuditPdf(params) {
  return request({
      url: '/chain/companysettle/downCompanySettleAuditPdf',
      method: 'get',
      params,
      responseType: 'blob',
  })
}
export function getSettleWaybillDetailPage(params) {
  return request({
      url: '/chain/companysettle/getSettleWaybillDetailPage',
      method: 'get',
      params,
  })
}
