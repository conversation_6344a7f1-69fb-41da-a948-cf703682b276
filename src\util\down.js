import store from '@/store'
import { Message } from "element-ui";
import request from '@/router/axios'


export function expotOut (searchList, pathName, xlsName) {
  return new Promise((resolve, reject) => {
    let token = store.getters.access_token ||''
    let user_md5 = store.getters.user_md5 ||''
    console.log(searchList);
    // 获取时间，这⼀步是在下载时⽂件名带下载⽇期，例如：⽤户信息2020-04-27.xls,如⽆需要可以去掉
    // let d = new Date()
    // let month = (d.getMonth() + 1)
    // let day = d.getDate()
    // let time = d.getFullYear() + '-' + (String(month).length > 1 ? month : '0' + month) + '-' + (String(day).length > 1 ? day : '0' + day)
    // 地址
    // let baseURL = 'http://xxxxxxx:8080' // 域名
    // const PATH = {
    //  userList：'vue/export/userList', // 后台接⼝地址
    // }
    // 参数
    let params = '?'
    for (let key in searchList) {
      if(key.includes('$')||searchList[key]==undefined){
        delete searchList[key]
      }else{
        params = params + key + '=' + searchList[key] + '&'
      }
    }
    console.log(params);
    function createObjectURL (object) { return (window.URL) ? window.URL.createObjectURL(object) : window.webkitURL.createObjectURL(object) }
    var xhr = new XMLHttpRequest()
    var formData = new FormData()
    console.log(111);
    xhr.open('get', pathName + params) // url填写后台的接⼝地址，如果是post，在formData append参数（参考原⽂地址）
    xhr.setRequestHeader('Authorization', 'Bearer ' + token)// token)
    xhr.setRequestHeader('AuthorizationMD5', user_md5)// user_md5)
    xhr.responseType = 'blob'
    xhr.onload = function (e) {
      console.log(e);
      console.log(this.status);
    if (this.status === 200) {
      var blob = this.response
      // xls⽂件名称
      var filename = `${xlsName}.xls`
      if (window.navigator.msSaveOrOpenBlob) {
        window.navigator.msSaveBlob(blob, filename)
      } else {
      var a = document.createElement('a')
      var url = createObjectURL(blob)
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      }
      resolve(e)
    }else{
      reject(e)
    }
    }
    xhr.timeout = 2*60*1000;
    xhr.ontimeout = function (event) {
      reject(event)
      Message({
        message: "请求超时,请稍后重试",
        type: "error",
        duration: 5 * 1000,
      });
    };
    xhr.send(formData)
  })
 }

export function exportOut (data={}, url, xlsName,method="get") {
  return new Promise((resolve, reject) => {
    let obj = {
      url: url,
      method: method,
      responseType:"blob",
    }
    method=='get'?obj.params=data:obj.data=data
    request(obj).then(res=>{
      console.log(res);
      // let reader = new FileReader();
      // reader.readAsText(res.data);
      // reader.onload = function (result) {
      //   try {
      //     let resData = JSON.parse(result.target.result); // 解析对象成功，说明是json数据
      //     console.log('解析对象成功',resData);
      //     reject(resData)
      //   } catch (err) {
      //     // 解析成对象失败，说明是正常的文件流
      //   }
      // };
        const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' }) //设置文件流
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          // 兼容IE/Edge
          window.navigator.msSaveOrOpenBlob(blob, `${xlsName}.xls`)
        }else{
          const link = document.createElement('a')  //创建a标签
          link.style.display = 'none'
          // 设置连接
          link.href = URL.createObjectURL(blob)  //将文件流转化为blob地址
          link.download = xlsName
          document.body.appendChild(link)
          // 模拟点击事件
          link.click()
        }
        resolve(res)
    }).catch(err=>{
      reject(err)
    })
  })
 }


