<template>
  <div class="addCompanySettle">
    <el-drawer size="100%" title="新增结算单" :data="visible" :visible.sync="visible" :before-close="cancelModal"  :wrapperClosable="false">
      <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :search.sync="searchForm"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        @selection-change="handleSelectionChange"
        @refresh-change="refreshChange"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <template slot="inPicture" slot-scope="{ row }">
          <el-image
            v-if="row.inPicture"
            style="width: 50px; height: 50px"
            :src="filterImg(row.inPicture)"
            :preview-src-list="filterImgs(row.inPicture)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="goPicture" slot-scope="{ row }">
          <el-image
            v-if="row.goPicture"
            style="width: 50px; height: 50px"
            :src="filterImg(row.goPicture)"
            :preview-src-list="filterImgs(row.goPicture)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="header" slot-scope="{ row }">
          <div style="display: inline-block;position: relative;top: -3px;margin-left: 10px;">
            <el-button
                icon="el-icon-check"
                size="mini"
                type="primary"
                :disabled="multipleSelection.length==0"
                :loading = 'tableLoading'
                @click="batchCreate"
              >
              批量选择生成结算单
            </el-button>
          </div>
        </template>
      </avue-crud>
    </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import {getCaptainWaybill as getPage} from "@/api/chain/companywaybill";
import {batchAddWaybillSettle} from "@/api/captain/companySettle";
import {tableOption} from "@/const/crud/captain/waybill";
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";
// import tags from '../debitCard/tags.vue'
export default {
  name: "garbage",
  components: {
    // tags
  },
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        pageSizes: [10,20,50,100, 500, 1000,5000],
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: 'go_datetime', //降序字段
      },
      paramsSearch: {},
      searchForm: {
      },
      tableLoading: false,
      trackVisible: false,
      tableHeight:'100px',
      tableOption: tableOption(true),
      marker: null,
      map: null,
      polyline: null,
      speed: 50,
      firstArr: [113.98074, 22.55251],
      lineArr: [
        [121.5389385, 31.21515044],
        [121.5389385, 31.29615044],
        [121.5273285, 31.21515044],
      ],
      graspRoad: null, //轨迹纠偏
      graspRoadList: [], //轨迹纠偏数据
      multipleSelection:[], //选择的
    };
  },
  created() {},
  mounted: function () {

  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        // addBtn: this.permissions['chain:companywaybill:add'] ? true : false,
        // delBtn: this.permissions['chain:companywaybill:del'] ? true : false,
        // editBtn: this.permissions['chain:companywaybill:edit'] ? true : false,
        // viewBtn: this.permissions["chain:companywaybill:get"] ? true : false,
      };
    },
  },
  methods: {
    expotOut,
    searchChange(params, done) {
      // if(!params.projectInfoId||params.projectInfoId==''){
      //   this.$message.error('请先选择项目')
      //   done();
      //   return false
      // }
      params = this.filterForm(params);
      this.paramsSearch = params;
      console.log(this.paramsSearch);
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      if (params) {
        if (params.hasOwnProperty("inDatetime")) {
          params.inDatetimeStart = params.inDatetime[0];
          params.inDatetimeEnd = params.inDatetime[1];
          delete params.inDatetime;
        }
        if (params.hasOwnProperty("goDatetime")) {
          params.goDatetimeStart = params.goDatetime[0];
          params.goDatetimeEnd = params.goDatetime[1];
          delete params.goDatetime;
        }
        if (params.hasOwnProperty("completeDatetime")) {
          params.completeDatetimeStart = params.completeDatetime[0];
          params.completeDatetimeEnd = params.completeDatetime[1];
          delete params.completeDatetime;
        }
      }
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
            agentStatus: 1,  //查询未结算的
            status:4,   //运单已完成
            isDel:0,   //是否删除的
            searchType:1,  //过滤电子卡结算
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    filterImg(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? null : url[0];
    },
    filterImgs(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? [] : url;
    },
    cancelModal() {
      this.$emit("update:visible", false);
      this.$emit("searchData");
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    batchCreate(){
      this.$confirm(`确定将${this.multipleSelection.length}条运单提交结算？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let arr = this.multipleSelection.map((item) => {
            return item.id ;
          });
          this.tableLoading = true
          batchAddWaybillSettle(arr).then(res=>{
            this.tableLoading = false
            this.$message.success(res.data.msg)
            this.getPage(this.page)
          }).catch(err=>{
            this.tableLoading = false
          })
        }).catch(() => {});

    },
  },
};
</script>

<style lang="scss" scoped>
</style>
