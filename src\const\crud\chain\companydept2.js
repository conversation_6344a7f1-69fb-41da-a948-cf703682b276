export const tableOption = {
  dialogType: "drawer",
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: false,
  dialogWidth: "40%",
  // excelBtn: true,
  // printBtn: true,
  delBtn: true,
  // viewBtn: true,
  defaultExpandAll:true,
  searchMenuSpan: 6,
  page:false,
  routerName:"companydept2",
  column: [
    {
      label: "部门名称",
      prop: "deptName",
      // sortable: true,
      expand: true,
      maxlength: 30,
      max:30,
      rules: [
        {
          required: true,
          message: "请输入部门名称",
          trigger: "blur",
        },
        {
          max: 30,
          message: "长度在不能超过10个字符",
        },
      ],
      overHidden:true,
    },
    {
      label: "部门负责人",
      prop: "leadingId",
      type: "select",
      props: {
        label: "name",
        value: "id",
      },
      dicUrl: "/chain/companystaff/list",
      filterable: true,
      hide:true,
      showColumn:false,
    },
    {
      label: "部门负责人",
      prop: "leadingName",
      display:false,
      overHidden:true,
    },
    {
      label: "部门人数",
      addDisplay: false,
      editDisplay: false,
      prop: "count",
      // sortable: true,
      overHidden:true,
    },
    {
      label: "上级部门",
      hide: true,
      prop: "parentDeptId",
      // sortable: true,
      rules: [
        {
          required: true,
          message: "请选择机构",
          trigger: "change",
        },
      ],
      type: "tree",
      props: {
        label: "deptName",
        value: "id",
      },
      defaultExpandAll: true,
      // dicUrl: "/chain/companydept/tree",
      dicData:[],
    }

  ],
};
