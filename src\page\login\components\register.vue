<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
  <div class="code-phone-div">


    <el-form :rules="formRules" ref="form" :model="formData" label-width="0">
      <el-form-item prop="phone">
        <el-input v-model="formData.phone" :disabled="type == '3'" placeholder="请输入手机号码" :maxlength="11">
          <!-- <i slot="prefix" class="el-input__icon" style="margin-top: 6px"></i> -->
          <img class="iphoneImg" slot="prefix" src="@/static/login/dl_phone.png" alt="">
          <template slot="append">
            <span @click="skipPopup"  style=" color: #66b1ff;cursor:pointer;" :class="[{ display: msgKey }]">{{ msgText
            }}</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input class="code" v-model="formData.code" @keyup.enter.native="handleDone" placeholder="请输入验证码">
          <img class="codeImg" slot="prefix" src="@/static/login/yzm.png" alt="">
        </el-input>
      </el-form-item>
      <div class="check" style="margin: 30px 0;">
        <el-checkbox v-model="checked" checked></el-checkbox>
        <span style="margin-left: 10px">我已阅读并同意<a
            href="https://jyb.szyl8.cn/h5/#/pages/privacyAgreement/privacyAgreement" target="_blank">《隐私协议》</a>和<a
            href="https://jyb.szyl8.cn/h5/#/pages/customerService/customerService" target="_blank">《软件使用服务协议》</a></span>

      </div>
      <div class="btn">
        <el-row>
          <el-button type="primary" style="width: 100%" :loading="btnLoading" @click="nextStep">下一步</el-button>
        </el-row>
      </div>
    </el-form>
    <!-- 图形验证码 -->
    <div class="popup" v-show="popupShow">
      <div class="box">
        <div class="popup_title">请输入验证码并确认</div>
        <div class="iptBox">
          <span class="iptDesc">验证码：</span>
          <el-input v-model="imgCode" class="elInput" placeholder="请输入验证码，不区分大小写" clearable></el-input>
        </div>

        <div id="v_container" @click="btncode"></div>
        <div class="instructions">看不清？点击图片换一张</div>
        <div class="btnBox">
          <el-button @click="popupShow = false">取消</el-button>
          <el-button type="primary" @click="next">确认</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { validatePhone } from "@/util/validate";
import { sendCode, codeCheck } from "@/api/upms/phone";
import { GVerify } from "@//util/GVerify";
import { encryptDes, decryptDes } from '@/util/des.js'
const MSGINIT = "发送验证码",
  MSGSCUCCESS = "${time}秒后可重发",
  MSGTIME = 60;
export default {
  name: "codePhoneDiv",
  props: {
    //1、登录；2、绑定；3、解绑
    type: {
      type: String,
    },
    phone: {
      type: String,
    },
  },
  data() {
    const validatorPhone = (rule, value, callback) => {
      if (validatePhone(value)[0]) {
        callback(new Error(validatePhone(value)[1]));
      } else {
        callback();
      }
    };
    const validatorCode = (rule, value, callback) => {
      if (value.length != 6) {
        callback(new Error("请输入6位数的验证码"));
      } else {
        callback();
      }
    };
    return {
      isFogertPass: false,
      checked: false,
      msgText: MSGINIT,
      msgTime: MSGTIME,
      msgKey: false,
      tabPosition: 1,
      formData: {
        phone: "",
        code: "",
      },
      formRules: {
        phone: [{ required: true, trigger: "blur", validator: validatorPhone }],
        code: [{ required: true, trigger: "blur", validator: validatorCode }],
      },
      btnLoading: false,
      verifyCode: "",
      imgData: "", //图片验证码
      imgCode: "",
      popupShow: false,
    };
  },
  created() {
    this.formData.phone = this.phone;
  },
  destroyed() { },
  mounted() { },
  beforeDestroy() { },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    skipPopup() {
      if (this.msgKey) return;
      if (validatePhone(this.formData.phone)[0]) {
        this.$message.error("请输入正确的手机号");
        return;
      }
      // 图片验证码
      this.verifyCode = new GVerify("v_container");
      this.imgData = this.verifyCode.options.code;
      this.popupShow = true
    },
    // 切换图片验证码
    btncode() {
      this.imgData = this.verifyCode.options.code.substring(
        this.verifyCode.options.code.length - 4
      );
    },
    next() {

      var imgCode = this.toLower(this.imgCode)
      var imgData = this.toLower(this.imgData)
      console.log(imgCode, imgData)
      if (imgCode != imgData) {
        this.$message.error('图形验证码错误,请重新输入');
        return
      }
      this.popupShow = false
      this.imgCode = ""
      this.handleSend()
    },
    // 字母转小写
    toLower(str) {
      var arr = ''
      for (var i = 0; i < str.length; i++) {
        arr = arr + str[i].toLowerCase();
      }
      return arr
    },
    // 设置cookie
    setCookie(c_name, c_pwd, c_state, exdays) {
      const exdate = new Date()
      exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays) // 保存的天数
      window.document.cookie = 'username' + '=' + c_name + ';path=/;expires=' + exdate.toGMTString()
      window.document.cookie = 'password' + '=' + c_pwd + ';path=/;expires=' + exdate.toGMTString()
      window.document.cookie = 'state' + '=' + c_state + ';path=/;expires=' + exdate.toGMTString()
    },
    // 读取cookie
    getCookie() {
      if (document.cookie.length > 0) {
        const arr = document.cookie.split('; ')
        for (let i = 0; i < arr.length; i++) {
          const arr2 = arr[i].split('=')
          console.log(arr[2])
          if (arr2[0] === 'username') {
            this.username = arr2[1]
          } else if (arr2[0] === 'password') {
            this.password = arr2[1]
          } else if (arr2[0] === 'state') {
            this.checked = Boolean(arr2[1])
          }
        }
      }
    },
    // 清除cookie
    clearCookie: function () {
      this.setCookie('', '', false, -1)
    },
    handleSend() {
      if (this.msgKey) return;
      if (validatePhone(this.formData.phone)[0]) {
        this.$message.error("请输入正确的手机号");
        return;
      }
      this.msgKey = true;
      var key = Math.floor(Date.now() / 3000000) + 168;
      var mobile = encryptDes(this.formData.phone, key)
      var deviceNewNo = encryptDes("AB288758-C1D0-1089-B513-YILU98DB5216", key)
      sendCode({
        mobile: mobile,
        type: this.type,
        roleType: "2",
        deviceNewNo: deviceNewNo,
      })
        .then((response) => {
          this.msgKey = false;
          if (response.data.code == "0") {
            this.$message.success("验证码发送成功");
            this.msgText = MSGSCUCCESS.replace("${time}", this.msgTime);
            this.msgKey = true;
            const time = setInterval(() => {
              this.msgTime--;
              this.msgText = MSGSCUCCESS.replace("${time}", this.msgTime);
              if (this.msgTime == 0) {
                this.msgTime = MSGTIME;
                this.msgText = MSGINIT;
                this.msgKey = false;
                clearInterval(time);
              }
            }, 1000);
          } else {
            this.$message.error(response.data.msg);
          }
        })
        .catch(() => {
          this.msgKey = false;
        });
    },
    nextStep() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.checked) {
            this.$message({
              showClose: true,
              message: "请先勾选协议",
              type: "success",
            });
            return;
          }
          console.log(this.formData);
          let param = {
            code:this.formData.code,
            mobile:this.formData.phone
          }
          this.btnLoading = true
          codeCheck(param).then(res=>{
            this.btnLoading = false
            this.$emit("nextStep");
          }).catch(err=>{
            this.btnLoading = false
          })
          // if (this.isFogertPass === true) {
          //   this.setCookie(this.username, this.password, true, 7)
          // } else {
          //   this.clearCookie()
          // }
          // this.$emit("handleDone", this.formData, this.type);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
   a{
          color: #108EE9;
        }
.code-phone-div {
  margin-top: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.register {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// .code-phone-submit {
//   display: block !important;
//   margin: 40px auto 10px auto !important;
//   width: 200px;
//   height: 48px;
//   font-size: 14px !important;
//   text-align: center;
//   border-radius: 50px !important;
//   border: 0px;
//   box-shadow: rgba(152, 22, 244, 0.19) 0px 5px 10px 2px;
// }
.login-containers {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  display: flex;

  .left {
    width: 70%;
    padding-left: 0.3rem;
    height: 100%;
    position: relative;

    // background:url('../../static/login/dlzc_t.png')bottom left no-repeat no-repeat;
    .top {
      height: 3.8rem;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      .mark {
        width: 1.5rem;
        height: 3.8rem;
        background: linear-gradient(180deg, #108EE9 0%, rgba(16, 142, 233, 0) 100%);
        opacity: 0.17;
        margin-right: 0.6rem;
      }

      .logo {
        display: flex;
        align-items: center;
        margin-top: 0.5rem;

        img {
          width: 0.8rem;
          height: 0.8rem;
          margin-right: 0.2rem;
        }

        .dec {
          h1 {
            font-size: 0.28rem;
            font-family: PingFang SC;
            font-weight: 400;
            line-height: 0.44rem;
            color: #353347;
            opacity: 1;
          }

          h2 {
            font-family: PingFang SC;
            font-weight: 400;
            line-height: 0.44rem;
            opacity: 1;
            font-size: 0.21rem;
            color: #817f8b;
          }
        }
      }
    }

    .bottom {
      position: absolute;
      bottom: 0;
      left: 0.3rem;

      img {
        width: 6.3rem;
        height: 6.3rem;
        // width: 89.3vh;
        // height: 89.3vh;

      }

      .content {
        position: absolute;
        top: -0.5rem;
        right: -2.5rem;
        font-size: 0.16rem;
        font-family: PingFang SC;
        font-weight: bold;
        line-height: 0.44rem;
        color: #353347;

        span {
          color: #108EE9;
        }

        .line {
          width: 0.6rem;
          height: 0.1rem;
          background: rgba(16, 142, 233, 1);
          margin-top: 0.3rem;
        }
      }
    }
  }

  .code-phone-div {


    img {
      position: absolute;
      right: 0.3rem;
      top: 0.2rem;
      width: 0.85rem;
      height: 0.9rem;
    }

    .code-phone-title {
      padding-top: 0.7rem;
      margin-bottom: 0.6rem;
      width: 4.04rem;
      height: 0.94rem;
      font-size: 0.24rem;
      font-family: PingFang SC;
      font-weight: bold;
      line-height: 0.46rem;
      color: #353347;

      div {
        font-size: 0.24rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: #353347;

      }

      .code-phone-subtitle {
        // font-size: 0.20rem;
        font-weight: 400;
      }
    }

  }



}

.footer {
  position: absolute;
  bottom: 15px;
  text-align: center;
  width: 100%;
  color: #999aaa;
  font-size: 14px;

  a {
    color: blue;
  }
}
.popup {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .6);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  font-size: 16px;
  color: #353347;

  .box {
    width: 50%;
    height: 50%;
    background: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -15% 0 0 -25%;
    padding: 0 20px;

    .popup_title {
      width: 100%;
      font-weight: 600;
      line-height: 120px;
      text-align: center;
    }

    .iptBox {
      display: flex;
      justify-content: left;
      align-items: center;

      .iptDesc {
        width: 120px;
      }
    }
  }

}

#v_container {
  width: 160px;
  height: 60px;
  margin: 12px auto;

}

.instructions {
  font-size: 10px;
  color: #a9a6a6;
  text-align: center;
}

.btnBox {
  display: flex;
  justify-content: right;
  align-items: center;
  margin-top: 30px;
}

.elInput {
  width: 50%;
}
</style>
