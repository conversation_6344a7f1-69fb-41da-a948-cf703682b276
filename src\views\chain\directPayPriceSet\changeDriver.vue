<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="70%"
      title="选择车队长和司机"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <avue-crud
          ref="driverChange"
          :data="tableData"
          :table-loading="tableLoading"
          :option="tableOption"
          @on-load="getPage"
          @refresh-change="refreshChange"
          @search-change="searchChange"
          @selection-change="selectionChange"
        >
          <template
            slot="projectNameSearch"
            slot-scope="{ item, value, label }"
          >
            <el-input v-model="info.projectName" disabled></el-input>
          </template>
          <template slot="header" slot-scope="{ item, value, label }">
              <el-button size="small" :disabled="selectList.length==0" style="margin-left:6px" type="primary" @click="confirm">
                选择
              </el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { getCaptain } from "@/api/chain/directPayPriceSet";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      dialogForm: {},
      tableOption: {
        searchLabelWidth: 76,
        submitText: "确定选择",
        position: "left",
        cancelBtn: true,
        submitBtn: true,
        selection: true,
        addBtn: false,
        menu: false,
        searchSpan: 8,
        searchMenuSpan: 8,
        selectable:(row)=>{
          return row.isOpenWallet!='0'
        },
        column: [
          {
            label: "项目名称",
            prop: "projectName",
            disabled: true,
            search: true,
          },
          {
            label: "车队长",
            prop: "fleetCaptainName",
            search: true,
            formatter: (val) => {
              return val.captainName
            }
          },
          {
            label: "司机",
            prop: "driverName",
            search: true,
          },
          {
            label: "是否开通钱包",
            prop: "isOpenWallet",
            formatter: (val) => {
              let text = "";
              switch (val.isOpenWallet) {
                case "0":
                  text = "否";
                  break;
                case "1":
                  text = "是";
                  break;
              }
              return text;
            },
          },
        ],
      },
      selectList: [],
    };
  },
  created() {},
  mounted() {
    // this.dialogForm = this.info;
  },
  methods: {
    searchChange(params, done) {
      params = this.filterForm(params);
      this.getPage(params);
      done();
    },

    getPage(params = {}) {
      if(params.pageSizes){
        delete params.pageSizes;
      }
      this.tableLoading = true;
      params = Object.assign(params, this.info);
      delete params.captainAndDriver

      getCaptain(params)
        .then((response) => {
          this.tableData = response.data.data
          let captainAndDriver = this.info.captainAndDriver
          if(captainAndDriver&&captainAndDriver.length>0){
            captainAndDriver.forEach(item=>{
              this.tableData.forEach(item2=>{
                if(item.captainId==item2.captainId&&item.driverId==item2.driverId){
                  console.log(this.$refs.driverChange);
                  setTimeout(()=>{
                  this.$refs.driverChange.toggleRowSelection(item2,true);
                  },100)

                }
              })
            })
          }
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange() {
      this.getPage();
    },
    selectionChange(e) {
      this.selectList = e;
    },
    cancelModal() {
      this.$emit("update:visible", false);
    },
    confirm() {
      this.$emit("confirm", this.selectList);
    },
  },
};
</script>

<style lang="scss" scoped></style>
