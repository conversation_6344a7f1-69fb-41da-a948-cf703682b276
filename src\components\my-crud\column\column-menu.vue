<template>
  <!-- 操作栏 -->
  <u-table-column class="operate"
                   prop="menu"
                   key="menu"
                   :fixed="vaildData(crud.tableOption.menuFixed,config.menuFixed)"
                   v-if="vaildData(crud.tableOption.menu,config.menu)&&crud.getPermission('menu')"
                   :label="crud.tableOption.menuTitle||'操作'"
                   :align="crud.tableOption.menuAlign || config.menuAlign"
                   :header-align="crud.tableOption.menuHeaderAlign || config.menuHeaderAlign"
                   :width="crud.tableOption.menuWidth || config.menuWidth">
    <template slot="header"
              slot-scope="scope">
      <slot name="menuHeader"
            :size="crud.isMediumSize"
            v-bind="scope"
            v-if="crud.getSlotName({prop:'menu'},'H',crud.$scopedSlots)"></slot>
      <span v-else>{{crud.tableOption.menuTitle || '操作'}}</span>
    </template>
    <template slot-scope="{row,$index}" class="operateBtn">
      <template v-if="['button','text','icon'].includes(menuType)">
          <!-- 行编辑的暂时不用 后期有时间再弄 -->
        <!-- <template v-if="vaildData(crud.tableOption.cellBtn,config.cellBtn)">
          <el-button :type="menuText('primary')"
                     :icon="crud.getBtnIcon('editBtn')"
                     :size="crud.isMediumSize"
                     :disabled="crud.btnDisabledList[$index]"
                     @click.stop="crud.rowCell(row,$index)"
                     v-if="vaildData(crud.tableOption.editBtn,config.editBtn)&&!row.$cellEdit"
                     v-permission="crud.getPermission('editBtn',row,$index)">
            <template v-if="!isIconMenu">
              {{crud.tableOption.editBtnText||"编辑"}}
            </template>
          </el-button>
          <el-button :type="menuText('primary')"
                     :icon="crud.getBtnIcon('saveBtn')"
                     :size="crud.isMediumSize"
                     :disabled="crud.btnDisabledList[$index]"
                     @click.stop="crud.rowCell(row,$index)"
                     v-else-if="vaildData(crud.tableOption.saveBtn,config.saveBtn)&&row.$cellEdit"
                     v-permission="crud.getPermission('saveBtn',row,$index)">
            <template v-if="!isIconMenu">
              {{crud.tableOption.saveBtnText||"保存"}}
            </template>
          </el-button>
          <el-button :type="menuText('danger')"
                     :icon="crud.getBtnIcon('cancelBtn')"
                     :size="crud.isMediumSize"
                     :disabled="crud.btnDisabledList[$index]"
                     @click.stop="crud.rowCancel(row,$index)"
                     v-if="row.$cellEdit">
            <template v-if="!isIconMenu">
              {{crud.tableOption.cancelBtnText||"取消"}}
            </template>
          </el-button>
        </template> -->
        <el-button :type="menuText('success')"
                   :icon="crud.getBtnIcon('viewBtn')"
                   class="operateBtnPadding"
                   :size="crud.isMediumSize"
                   :disabled="crud.btnDisabled"
                   @click.stop="crud.rowView(row,$index)"
                   v-permission="crud.getPermission('viewBtn',row,$index)"
                   v-if="vaildData(crud.tableOption.viewBtn,config.viewBtn)">
          <template v-if="!isIconMenu">
            {{crud.tableOption.viewBtnText||"查看"}}
          </template>
        </el-button>
        <el-button :type="menuText('primary')"
                   :icon="crud.getBtnIcon('editBtn')"
                    class="operateBtnPadding"
                   :size="crud.isMediumSize"
                   :disabled="crud.btnDisabled"
                   @click.stop="crud.rowEdit(row,$index)"
                   v-permission="crud.getPermission('editBtn',row,$index)"
                   v-if="vaildData(crud.tableOption.editBtn,config.editBtn)&&!crud.tableOption.cellBtn">
          <template v-if="!isIconMenu">
            {{crud.tableOption.editBtnText||"编辑"}}
          </template>
        </el-button>
        <el-button :type="menuText('primary')"
                   :icon="crud.getBtnIcon('copyBtn')"
                    class="operateBtnPadding"
                   :size="crud.isMediumSize"
                   :disabled="crud.btnDisabled"
                   @click.stop="crud.rowCopy(row)"
                   v-permission="crud.getPermission('copyBtn',row,$index)"
                   v-if="vaildData(crud.tableOption.copyBtn,config.copyBtn)">
          <template v-if="!isIconMenu">
            {{crud.tableOption.copyBtnText||"复制"}}
          </template>
        </el-button>
        <el-button :type="menuText('danger')"
                   :icon="crud.getBtnIcon('delBtn')"
                    class="operateBtnPadding"
                   :size="crud.isMediumSize"
                   :disabled="crud.btnDisabled"
                   @click.stop="crud.rowDel(row,$index)"
                   v-permission="crud.getPermission('delBtn',row,$index)"
                   v-if="vaildData(crud.tableOption.delBtn,config.delBtn) && !row.$cellEdit">
          <template v-if="!isIconMenu">
            {{crud.tableOption.delBtnText||"删除"}}
          </template>
        </el-button>

      </template>
      <slot name="menu"
            :row="row"
            :type="menuText('primary')"
            :disabled="crud.btnDisabled"
            :size="crud.isMediumSize"
            :index="$index"></slot>
    </template>
  </u-table-column>
</template>

<script>
import config from "../config.js";
import permission from '../permission';
export default {
  name: "crud",
  data () {
    return {
      config: config,
    }
  },
  inject: ["crud"],
  directives: {
    permission
  },
  mounted(){
  },
  computed: {
    menuType () {
      return this.crud.tableOption.menuType || 'text';
    },
    isIconMenu () {
      return this.menuType === "icon"
    },
    isTextMenu () {
      return this.menuType === "text"
    },
  },
  methods: {
    menuText (value) {
      return ['text', 'menu'].includes(this.menuType) ? "text" : value;
    },
  }
}
</script>
<style lang="scss" scoped>
  .operateBtnPadding{
    padding-top: 0;
    padding-bottom: 0;
  }
</style>
