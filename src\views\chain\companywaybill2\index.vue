<template>
  <div class="companywaybill2">
    <basic-container>
      <my-crud
        ref="crud"
        :page.sync="page"
        :search.sync="search"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        @row-update="handleUpdate"
        @selection-change="handleSelectionChange"
        @row-save="handleSave"
        @row-del="handleDel"
        @sort-Change="sortChange"
        @search-change="searchChange"
      >
        <!-- <template slot="settleCardNoSearch" slot-scope="{ row }">
          <tags v-model="statements"></tags>
        </template> -->
        <template slot="inPicture" slot-scope="{ row }">
          <el-image
            v-if="row.inPicture"
            style="width: 50px; height: 50px"
            :src="filterImg(row.inPicture)"
            :preview-src-list="filterImgs(row.inPicture)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="goPicture" slot-scope="{ row }">
          <el-image
            v-if="row.goPicture"
            style="width: 50px; height: 50px"
            :src="filterImg(row.goPicture)"
            :preview-src-list="filterImgs(row.goPicture)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="iotInPicture" slot-scope="{ row }">
          <el-image
            v-if="row.iotInPicture"
            style="width: 50px; height: 50px"
            :src="filterImg(row.iotInPicture)"
            :preview-src-list="filterImgs(row.iotInPicture)"
          >
          </el-image>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="entrancePictureForm" slot-scope="{ row }">
          <span v-if="row.entrancePicture">
            <el-tooltip
              class="item"
              effect="dark"
              content="此处只显示一张，有多张请点击图片预览"
              placement="top-start"
            >
              <el-image
                style="width: 50px; height: 50px"
                :src="filterImg(row.entrancePicture)"
                :preview-src-list="filterImgs(row.entrancePicture)"
              >
              </el-image>
            </el-tooltip>
          </span>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="inPictureForm" slot-scope="{ row }">
          <span v-if="row.inPicture">
            <el-tooltip
              class="item"
              effect="dark"
              content="此处只显示一张，有多张请点击图片预览"
              placement="top-start"
            >
              <el-image
                style="width: 50px; height: 50px"
                :src="filterImg(row.inPicture)"
                :preview-src-list="filterImgs(row.inPicture)"
              >
              </el-image>
            </el-tooltip>
          </span>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="goPictureForm" slot-scope="{ row }">
          <span v-if="row.goPicture">
            <el-tooltip
              class="item"
              effect="dark"
              content="此处只显示一张，有多张请点击图片预览"
              placement="top-start"
            >
              <el-image
                style="width: 50px; height: 50px"
                :src="filterImg(row.goPicture)"
                :preview-src-list="filterImgs(row.goPicture)"
              >
              </el-image>
            </el-tooltip>
            <span v-if="filterImgs(row.goPicture).length > 1"
              >多张图片点击图片进行预览</span
            >
          </span>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="ticketImgForm" slot-scope="{ row }">
          <span v-if="row.ticketImg">
            <el-tooltip
              class="item"
              effect="dark"
              content="此处只显示一张，有多张请点击图片预览"
              placement="top-start"
            >
              <el-image
                style="width: 50px; height: 50px"
                :src="filterImg(row.ticketImg)"
                :preview-src-list="filterImgs(row.ticketImg)"
              >
              </el-image>
            </el-tooltip>
            <span v-if="filterImgs(row.ticketImg).length > 1"
              >多张图片点击图片进行预览</span
            >
          </span>
          <span v-else>暂无图片信息</span>
        </template>
        <template slot="menu" slot-scope="scope">
          <!-- <el-button
              type="text"
              v-if="permissions['chain:companywaybill:track']"
              icon="el-icon-document"
              size="small"
              plain
              @click="track(scope.row, scope.index)"
            > 轨迹</el-button> -->
          <el-button
            type="text"
            v-if="permissions['chain:companywaybill:track']"
            icon="el-icon-view"
            size="small"
            plain
            @click="trackAll(scope.row, scope.index)"
          >
            轨迹</el-button
          >
          <el-dropdown
            size="mini"
            style="margin-left: 4px"
            v-if="
              permissions['chain:companywaybill:get'] ||
              permissions['chain:companywaybill:model'] ||
              permissions['chain:companywaybill:remark'] ||
              permissions['chain:companywaybill:setGarbagePrice']
            "
          >
            <el-button type="text" size="mini"
              >更多功能<i class="el-icon-arrow-down el-icon--right"></i
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <div style="display: flex; padding: 2px 10px">
                <el-button
                  type="text"
                  v-if="permissions['chain:companywaybill:get']"
                  icon="el-icon-view"
                  size="small"
                  plain
                  @click="viewInfo(scope.row, scope.index)"
                >
                  查看</el-button
                >

                <el-button
                  type="text"
                  v-if="permissions['chain:companywaybill:model']"
                  icon="el-icon-document-add"
                  size="small"
                  plain
                  @click="setVehicleType(scope.row, scope.index)"
                >
                  设置出口签单车型</el-button
                >
                <el-button
                  type="text"
                  v-if="permissions['chain:companywaybill:remark']"
                  icon="el-icon-document-add"
                  size="small"
                  plain
                  @click="setRemark(scope.row, scope.index)"
                >
                  设置PC后台备注</el-button
                >
                <el-button
                  type="text"
                  v-if="permissions['chain:companywaybill:setGarbagePrice']"
                  icon="el-icon-document-add"
                  size="small"
                  plain
                  @click="batchSetGarbagePrice(scope.row, scope.index)"
                >
                  设置泥尾价</el-button
                >
              </div>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <!-- <template slot="searchMenu" slot-scope="scope">
          <el-button
              class="el-button--small"
              type="primary"
              icon="el-icon-caret-top"
              @click="getmoreshow(2)"
              >隐藏</el-button>
        </template>
        <template slot="menuLeft" slot-scope="{ row }">
          <el-button
            icon="el-icon-document-add"
            v-if="permissions['chain:companywaybill:model']"
            size="mini"
            type="primary"
            :disabled="multipleSelection.length == 0"
            @click="batchSetVehicleType"
          >
            批量设置出口签单车型
          </el-button>
          <el-button
            icon="el-icon-document-add"
            v-if="permissions['chain:companywaybill:remark']"
            size="mini"
            type="primary"
            :disabled="multipleSelection.length == 0"
            @click="batchSetRemark"
          >
            批量设置PC后台备注
          </el-button>
          <el-button
            icon="el-icon-setting"
            v-if="permissions['chain:companywaybill:batchSetFleetName']"
            size="mini"
            type="primary"
            :disabled="multipleSelection.length == 0"
            @click="batchSetFleetName"
          >
            批量设置车队名称
          </el-button>
          <el-button
            icon="el-icon-setting"
            v-if="permissions['chain:companywaybill:batchSetShift']"
            size="mini"
            type="primary"
            :disabled="multipleSelection.length == 0"
            @click="batchSetShift"
          >
            批量设置班次
          </el-button>
        </template> -->
        <template slot="menuLeft" slot-scope="{ row }">
          <div>
            <el-tooltip
              class="item"
              effect="dark"
              content="请先选择运单再操作"
              placement="top"
              v-if="
                permissions['chain:companywaybill:setGarbagePrice'] ||
                permissions['chain:companywaybill:model'] ||
                permissions['chain:companywaybill:remark'] ||
                permissions['chain:companywaybill:batchSetFleetName'] ||
                permissions['chain:companywaybill:batchSetShift']
              "
            >
              <el-dropdown
                :disabled="multipleSelection.length < 1"
                @command="(event) => handleClick(event)"
              >
                <el-button type="primary" size="small">
                  批量设置<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-if="permissions['chain:companywaybill:model']"
                    command="batchSetVehicleType"
                    >批量设置出口签单车型</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-if="permissions['chain:companywaybill:remark']"
                    command="batchSetRemark"
                    >批量设置PC后台备注</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-if="permissions['chain:companywaybill:batchSetFleetName']"
                    command="batchSetFleetName"
                    >批量设置车队名称</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-if="permissions['chain:companywaybill:batchSetShift']"
                    command="batchSetShift"
                    >批量设置班次</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-if="permissions['chain:companywaybill:setGarbagePrice']"
                    command="batchSetGarbagePrice"
                    >批量设置泥尾价</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </el-tooltip>
            <el-button
              style="margin-left: 10px"
              icon="el-icon-download"
              v-if="permissions['chain:companywaybill:exc']"
              size="small"
              :loading="btnLoading"
              type="primary"
              @click.stop.prevent="exOutExcel"
            >
              导出
            </el-button>
            <el-button
              icon="el-icon-download"
              v-if="permissions['chain:companywaybill:downImg']"
              size="small"
              type="primary"
              :loading="btnLoading"
              @click="dialogTableVisible = true"
            >
              导出图片
            </el-button>
             <el-button
                icon="el-icon-download"
                v-if="permissions['chain:companywaybill:downImg']"
                size="small"
                type="primary"
                :loading="btnLoading"
                @click="exportWaybillImage"
              >
                导出运单图片
              </el-button>
              <el-button
                icon="el-icon-download"
                v-if="permissions['chain:companywaybill:downImg']"
                size="small"
                type="primary"
                @click="openUrl"
              >
                导出图片工具
              </el-button> 
          </div>
        </template>
      </my-crud>
    </basic-container>
    <!-- 任务弹窗 -->
    <el-dialog
      width="60%"
      :fullscreen="isFullscreen"
      title="运单轨迹"
      center
      :visible="trackVisible"
      :before-close="closeVisible"
      :close-on-click-modal="false"
    >
      <i
        class="el-icon-full-screen"
        @click="isFullscreen = !isFullscreen"
        style="position: absolute; right: 46px; top: 22px; cursor: pointer"
      ></i>
      <div id="maps" style="height: 100%; min-height: 500px"></div>
      <div class="input-card">
        <h4>轨迹回放控制</h4>
        <span
          ><el-slider v-model="speed" :min="30" :max="100000"></el-slider
        ></span>
        <div class="input-item">
          <el-row class="my-row" :gutter="12">
            <el-col :span="12">
              <el-button type="primary" size="small" @click="startAnimation"
                >开始动画</el-button
              >
            </el-col>
            <el-col :span="12">
              <el-button type="primary" size="small" @click="pauseAnimation"
                >暂停动画</el-button
              >
            </el-col>
          </el-row>
          <el-row class="my-row" :gutter="12">
            <el-col :span="12">
              <el-button type="primary" size="small" @click="resumeAnimation"
                >继续动画</el-button
              >
            </el-col>
            <el-col :span="12">
              <el-button type="primary" size="small" @click="stopAnimation"
                >停止动画</el-button
              >
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
    <!-- 运单详情 -->
    <waybill-detail
      v-if="detailVisible"
      :detailForm="detailForm"
      :option="detailOption"
      :visible.sync="detailVisible"
    ></waybill-detail>
    <!-- 批量设置班次 -->
    <batch-set-shift
      v-if="setShiftVisible"
      :selectList="multipleSelection"
      @refreshChange="refreshChange"
      :visible.sync="setShiftVisible"
    ></batch-set-shift>
    <!-- 全部轨迹 -->
    <track-all-gps
      v-if="trackAllVisible"
      :visible.sync="trackAllVisible"
      :trackList="trackAllList"
    ></track-all-gps>
    <!-- 导出图片 -->
    <el-dialog title="导出图片选择" :visible.sync="dialogTableVisible">
      <div style="margin-bottom: 20px">请选择需要导出的图片:</div>
      <el-checkbox-group v-model="customePhoto">
        <el-checkbox
          v-for="(item, index) in customePhotoList"
          :label="index"
          :key="index"
          >{{ item.label }}</el-checkbox
        >
      </el-checkbox-group>
      <div style="margin: 20px 0">导出方式:</div>
      <el-checkbox-group v-model="photoType">
        <el-checkbox
          v-for="(item, index) in photoTypeList"
          :label="index"
          :key="index"
          >{{ item.label }}</el-checkbox
        >
      </el-checkbox-group>
      <div class="btnBox">
        <el-button @click="dialogTableVisible = false">取消</el-button>
        <el-button type="primary" @click="exportImage">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBuilderPage,
  getObj,
  addObj,
  putObj,
  delObj,
  getGpsList,
  getGpsListNew,
  editPcRemark,
  batchFleetName,
  batchUpdateGoVehicleType,
  batchUpdateGarbagePreparePrice,
  postCreateByCode,
  listDictionaryItem,
  exportPNG,
} from "@/api/chain/companywaybill";
import { tableOption } from "@/const/crud/chain/companywaybill2";
import { mapGetters } from "vuex";
import { exportOut } from "@/util/down.js";
import tags from "./tags.vue";
import WaybillDetail from "./detail.vue";
import batchSetShift from "./batchSetShift.vue";
import trackAllGps from "./trackAllGps.vue";

export default {
  name: "companywaybill2",
  data() {
    return {
      dialogTableVisible: false,
      // statements: [],
      form: {},
      tableData: [],
      page: {
        pageSizes: [10, 20, 50, 100, 500, 1000, 5000],
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: "go_datetime", //降序字段
      },
      paramsSearch: {
        status: "2,3,4,11,12,21,22",
      },
      searchForm: {},
      tableLoading: false,
      trackVisible: false,
      tableHeight: "100px",
      tableOption: tableOption(this),
      marker: null,
      map: null,
      polyline: null,
      speed: 50,
      firstArr: [113.98074, 22.55251],
      lineArr: [
        [121.5389385, 31.21515044],
        [121.5389385, 31.29615044],
        [121.5273285, 31.21515044],
      ],
      // graspRoad: null, //轨迹纠偏
      // graspRoadList: [], //轨迹纠偏数据
      searchDom: {},
      btnLoading: false,
      isOne: true,
      search: {
        // projectInfoId: "",
      },
      trackList: [],
      // carWindow:null,
      pathSimplifierIns: null,
      navg1: null,
      moreshow: false,
      detailVisible: false, //详情弹窗
      detailForm: {},
      detailOption: {
        detail: true,
        labelWidth: 114,
        group: [
          {
            label: "基本信息",
            prop: "group",
            column: [
              {
                label: "运单号",
                prop: "no",
                span: 12,
                placeholder: " ",
              },
              {
                label: "项目名称",
                prop: "projectName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "项目合作方",
                prop: "agentName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "状态",
                prop: "statusName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "地块",
                prop: "landParcel",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: "入场信息",
            prop: "group",
            column: [
              {
                label: "入场拍照凭证",
                prop: "iotInPicture",
                placeholder: " ",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: "挖机签单信息",
            prop: "group1",
            column: [
              {
                label: "挖机签单员",
                prop: "inStaffName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机土质",
                prop: "inSoilType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机车牌",
                prop: "inDriverTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机签单时间",
                prop: "inDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机班次",
                prop: "inShiftTypeName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机备注",
                prop: "inRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "凭证类型",
                prop: "inWeightUnit",
                span: 12,
                placeholder: " ",
              },
              {
                label: "挖机拍照凭证",
                prop: "inPicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "称重仪器照片",
                prop: "inWeighInstrumentPicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "挖机签单地址",
                prop: "inAddr",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: "出场签单信息",
            prop: "group2",
            column: [
              {
                label: "运输类型",
                prop: "tpModeName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "车队长",
                prop: "fleetCaptainName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾",
                prop: "garbageName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场签单员",
                prop: "goStaffName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场土质",
                prop: "goSoilType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场车牌",
                prop: "goDriverTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场签单时间",
                prop: "goDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场班次",
                prop: "goShiftTypeName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "入场重量",
                prop: "inWeight",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场重量",
                prop: "outWeight",
                span: 12,
                placeholder: " ",
              },
              {
                label: "单位",
                prop: "weightUnit",
                span: 12,
                placeholder: " ",
              },
              {
                label: "单价",
                prop: "unitPrice",
                span: 12,
                placeholder: " ",
              },
              {
                label: "数量",
                prop: "weightTons",
                span: 12,
                placeholder: " ",
              },
              {
                label: "价格",
                prop: "payeePrice",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾票土质",
                prop: "goTicketSoilType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾票号",
                prop: "ticketNo",
                span: 12,
                placeholder: " ",
              },
              {
                label: "泥尾票单价",
                prop: "ticketPrice",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场拍照凭证",
                prop: "goPicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "磅单票据",
                prop: "poundbillUrl",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "出场拍照泥尾票",
                prop: "ticketImg",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "出场备注",
                prop: "goRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "电子结算卡",
                prop: "settleCardNo",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出口签单车型",
                prop: "goVehicleType",
                span: 12,
                placeholder: " ",
              },
              {
                label: "出场签单地址",
                prop: "goAddr",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: "司机信息",
            prop: "group3",
            column: [
              {
                label: "司机",
                prop: "driverName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "车牌",
                prop: "goDriverTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机出场时间",
                prop: "confirmGoDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机卸土时间",
                prop: "completeDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机卸土地址",
                prop: "completeAddr",
                span: 12,
                placeholder: " ",
              },
              {
                label: "直付类型",
                prop: "isPlatformDirectPayName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "司机联系方式",
                prop: "driverMobile",
                span: 12,
                placeholder: " ",
              },
              {
                label: "行驶证车辆车型",
                prop: "brandType",
                span: 12,
                placeholder: " ",
              },
            ],
          },
          {
            label: "空车入场签单",
            prop: "group4",
            column: [
              {
                label: "入场车牌",
                prop: "entranceTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "入场签单人",
                prop: "entranceStaffName",
                span: 12,
                placeholder: " ",
                overHidden: true,
              },
              {
                label: "入场签单时间",
                prop: "entranceDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "入场备注",
                prop: "entranceRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "入场拍照",
                prop: "entrancePicture",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
            ],
          },
          {
            label: "回填入场签单",
            prop: "group5",
            column: [
              {
                label: "回填项目",
                prop: "backfillProjectName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "回填车牌",
                prop: "backfillTruckCode",
                span: 12,
                placeholder: " ",
              },
              {
                label: "票号",
                prop: "backfillTicketNo",
                span: 12,
                placeholder: " ",
                overHidden: true,
              },
              {
                label: "备注",
                prop: "backfillRemark",
                span: 12,
                placeholder: " ",
              },
              {
                label: "回填签单人",
                prop: "backfillSignName",
                span: 12,
                placeholder: " ",
              },
              {
                label: "回填签单时间",
                prop: "backfillSignDatetime",
                span: 12,
                placeholder: " ",
              },
              {
                label: "拍照车辆",
                prop: "backfillPicUrl",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
              {
                label: "拍照泥尾票",
                prop: "backfillTicketNoUrl",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                span: 24,
                placeholder: " ",
              },
            ],
          },
        ],
      },
      multipleSelection: [],
      setShiftVisible: false,
      isFullscreen: true,
      trackAllVisible: false,
      trackAllList: [],
      photoTypeList: [],
      photoType: [0, 1],
      customePhotoList: [],
      customePhoto: [0, 1],
    };
  },
  components: {
    tags,
    WaybillDetail,
    batchSetShift,
    trackAllGps,
  },
  created() {},
  mounted() {
    this.listDictionaryItem();
    this.listDictionaryItem2();
    // console.log(this.$refs.crud);
    // this.searchDom = this.$refs.crud.$refs.table.$refs.headerSearch;
    // window.addEventListener("resize", this.func);
  },
  activated() {
    // setTimeout(() => {
    //   let tableHeight = document.querySelector(".el-table").offsetTop;
    //   this.$refs.crud.$refs.table.tableHeight = window.innerHeight - tableHeight - 182;
    //   this.$refs.crud.$refs.table.tableOption.height =
    //     window.innerHeight - tableHeight - 182;
    //   this.$refs.crud.$refs.table.doLayout();
    // }, 500);
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        // addBtn: this.permissions['chain:companywaybill:add'] ? true : false,
        // delBtn: this.permissions['chain:companywaybill:del'] ? true : false,
        // editBtn: this.permissions['chain:companywaybill:edit'] ? true : false,
        viewBtn: this.permissions["chain:companywaybill:get"] ? true : false,
      };
    },
  },
  // watch: {
  //   "searchDom.searchShow": {
  //     handler(newVal, oldVal) {
  //       console.log(newVal);
  //       setTimeout(() => {
  //         let tableHeight = document.querySelector(".el-table").offsetTop;
  //         this.$refs.crud.$refs.table.tableHeight = window.innerHeight - tableHeight - 182;
  //         this.$refs.crud.$refs.table.tableOption.height =
  //           window.innerHeight - tableHeight - 182;
  //         this.$refs.crud.$refs.table.doLayout();
  //       }, 500);
  //     },
  //     deep: true,
  //   },
  // },
  methods: {
    listDictionaryItem() {
      listDictionaryItem("costome_photo_type").then((res) => {
        this.photoTypeList = res.data.data.map((v) => {
          return {
            label: v.itemName,
            value: v.itemValue,
          };
        });
      });
    },
    listDictionaryItem2() {
      listDictionaryItem("custome_photo").then((res) => {
        this.customePhotoList = res.data.data.map((v) => {
          return {
            label: v.itemName,
            value: v.itemValue,
          };
        });
      });
    },
    exportImage() {
      if (this.customePhoto.length == 0) {
        this.$message.error("请选择需要导出的图片");
        return;
      }
      if (this.photoType.length == 0) {
        this.$message.error("请选择导出方式");
        return;
      }
      let params = Object.assign({}, this.paramsSearch);
      if (params) {
        if (params.hasOwnProperty("generationTime")) {
          params.generationTimeStart = params.generationTime[0];
          params.generationTimeEnd = params.generationTime[1];
          delete params.generationTime;
        }
        if (params.hasOwnProperty("inDatetime")) {
          params.inDatetimeStart = params.inDatetime[0];
          params.inDatetimeEnd = params.inDatetime[1];
          delete params.inDatetime;
        }
        if (params.hasOwnProperty("goTime")) {
          params.goDatetimeStart = params.goTime[0];
          params.goDatetimeEnd = params.goTime[1];
          delete params.goTime;
        }
        if (params.hasOwnProperty("completeTime")) {
          params.completeDatetimeStart = params.completeTime[0];
          params.completeDatetimeEnd = params.completeTime[1];
          delete params.completeTime;
        }
        if (params.hasOwnProperty("entranceDatetime")) {
          params.entranceDatetimeStart = params.entranceDatetime[0];
          params.entranceDatetimeEnd = params.entranceDatetime[1];
          delete params.entranceDatetime;
        }
        if (params.hasOwnProperty("goShiftTime")) {
          params.goShiftTimeStart = params.goShiftTime[0];
          params.goShiftTimeEnd = params.goShiftTime[1];
          delete params.goShiftTime;
        }
        params.projectInfoId =
          Array.isArray(params.projectInfoId) && params.projectInfoId.length > 0
            ? params.projectInfoId.join(",")
            : params.projectInfoId || "";
      }
      let type = [];
      let photos = [];
      for (let i = 0; i < this.photoType.length; i++) {
        type.push(this.photoTypeList[this.photoType[i]].value);
      }
      for (let i = 0; i < this.customePhoto.length; i++) {
        photos.push(this.customePhotoList[this.customePhoto[i]].value);
      }
      console.log(type);
      console.log(photos);
      console.log(params);
      params.type = type.join(",");
      params.photos = photos.join(",");
      params.code = 1;
      exportPNG(params).then((res) => {
        console.log(res);
        this.dialogTableVisible = false;
        // 1. 把 arraybuffer 转成 Blob
        const blob = new Blob([res.data], { type: "application/zip" });
        // 2. 生成临时 URL
        const url = window.URL.createObjectURL(blob);
        // 3. 构造 <a> 标签并触发下载
        const a = document.createElement("a");
        a.href = url;
        a.download = "download.zip"; // 下载文件名
        document.body.appendChild(a);
        a.click();
        // 4. 清理
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      });
    },
    batchUpdateGarbagePreparePrice(data) {
      batchUpdateGarbagePreparePrice(data).then((res) => {
        this.$message.success("操作成功");
        this.getPage(this.page);
      });
    },

    batchSetGarbagePrice(row) {
      this.$prompt("设置泥尾价", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "number",
        // inputPattern:/^[0-9]+(\.[0-9]{1,2})?$/,
        inputPlaceholder: "请设置泥尾价",
        inputValidator: (value) => {
          if (!value) {
            return "泥尾价不能为空";
          } else if (!/^[0-9]+(\.[0-9]{1,2})?$/.test(value)) {
            return "请输入两位小数";
          }
        },
        // inputErrorMessage: "请输入泥尾价",
      })
        .then(({ value }) => {
          let data = {};
          if (row && row.id) {
            data = {
              garbagePreparePrice: value,
              ids: [row.id],
            };
          } else {
            data = {
              garbagePreparePrice: value,
              ids: this.multipleSelection.map((v) => v.id),
            };
          }

          this.batchUpdateGarbagePreparePrice(data);
        })
        .catch(() => {});
    },
    exportOut,
    getmoreshow(type) {
      console.log(this.$refs.crud);
      this.searchDom.searchShow = type == 1;
      // console.log(this.$refs.crud.$refs.headerSearch.$refs.form);
      // if (type == 1) {
      //   this.$refs.crud.$refs.headerSearch.$refs.form.$el.style.height = "47px";
      //   this.moreshow = false;
      // } else {
      //   this.moreshow = true;
      //   this.$refs.crud.$refs.headerSearch.$refs.form.$el.style.height = "100%";
      // }
    },
    searchData() {
      this.page.currentPage = 1;
      let params = this.filterForm(this.search);
      console.log(params);
      console.log(this.paramsSearch);
      this.paramsSearch = params;
      this.getPage(this.page, params);
    },
    func() {
      let tableHeight = document.querySelector(".el-table").offsetTop;
      this.$refs.crud.$refs.table.tableHeight =
        window.innerHeight - tableHeight - 182;
      this.$refs.crud.$refs.table.tableOption.height =
        window.innerHeight - tableHeight - 182;
      this.$refs.crud.$refs.table.doLayout();
    },
    searchChange(params, done) {
      console.log(params);
      params = this.filterForm(params);
      this.paramsSearch = params;
      console.log(this.paramsSearch);
      console.log(this.search);
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params = {}) {
      console.log(this.paramsSearch);
      console.log(this.search);
      this.tableLoading = true;
      //第一次调用
      console.log(params);
      if (this.isOne && this.$route.query.info) {
        let info = JSON.parse(this.$route.query.info);
        this.search.goTime = [info.start, info.end];
        this.search.projectInfoId = info.projectInfoId;
        this.search.garbageName = info.garbageName;
        this.search.goTicketSoilType = info.goTicketSoilType;
        this.search.isTicket = info.isTicket;
      }
      if (this.isOne && this.$route.query.statements) {
        this.search.settleCardNo = this.$route.query.statements;
      }
      Object.assign(params, this.search);
      if (params) {
        if (params.hasOwnProperty("generationTime")) {
          params.generationTimeStart = params.generationTime[0];
          params.generationTimeEnd = params.generationTime[1];
          delete params.generationTime;
        }
        if (params.hasOwnProperty("inDatetime")) {
          params.inDatetimeStart = params.inDatetime[0];
          params.inDatetimeEnd = params.inDatetime[1];
          delete params.inDatetime;
        }
        if (params.hasOwnProperty("goTime")) {
          params.goDatetimeStart = params.goTime[0];
          params.goDatetimeEnd = params.goTime[1];
          delete params.goTime;
        }
        if (params.hasOwnProperty("completeTime")) {
          params.completeDatetimeStart = params.completeTime[0];
          params.completeDatetimeEnd = params.completeTime[1];
          delete params.completeTime;
        }
        if (params.hasOwnProperty("entranceDatetime")) {
          params.entranceDatetimeStart = params.entranceDatetime[0];
          params.entranceDatetimeEnd = params.entranceDatetime[1];
          delete params.entranceDatetime;
        }
        if (params.hasOwnProperty("goShiftTime")) {
          params.goShiftTimeStart = params.goShiftTime[0];
          params.goShiftTimeEnd = params.goShiftTime[1];
          delete params.goShiftTime;
        }
        console.log(params.projectInfoId);
        params.projectInfoId =
          Array.isArray(params.projectInfoId) && params.projectInfoId.length > 0
            ? params.projectInfoId.join(",")
            : params.projectInfoId || "";
      }
      if (Array.isArray(params.settleCardNo))
        params.settleCardNo = params.settleCardNo.join(",");
      getBuilderPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.isOne = false;
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.isOne = false;
          this.tableData = [];
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    filterImg(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? null : url[0];
    },
    filterImgs(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? [] : url;
    },
    closeVisible() {
      this.trackVisible = false;
    },
    track(row) {
      this.lineArr = [];
      this.firstArr = [];
      this.trackList = [];
      // this.graspRoadList = [];
      this.marker = null;
      getGpsList({ companyWaybillId: row.id }).then((res) => {
        if (res.data.data && res.data.data.length > 0) {
          // this.trackList = res.data.data
          this.trackList = [{ name: "轨迹时间", points: [] }];
          res.data.data.forEach((item, index) => {
            if (item.gps) {
              let arr = item.gps.split(",");
              this.lineArr.push(arr);
              this.trackList[0].points.push({
                name: item.createDatetime,
                lnglat: arr,
              });
              // if (index % 10 == 0) {
              //   this.graspRoadList.push({
              //     x: arr[0],
              //     y: arr[1],
              //     sp: 20,
              //     ag: 0,
              //     tm: new Date(item.createDatetime).getTime() / 1000,
              //   });
              // }
            }
          });
          console.log(this.lineArr);
          this.firstArr = this.lineArr[0];
          this.trackVisible = true;
          console.log(this.lineArr);
          setTimeout(() => {
            this.initMap();
            this.initroad();
          }, 500);
        } else {
          this.$message.error("暂无轨迹信息");
        }
      });
    },
    trackAll(row) {
      //获取GPS
      let param = {
        companyWaybillId: row.id,
      };
      console.log(row);
      this.tableLoading = true;
      getGpsListNew(param)
        .then((res) => {
          this.tableLoading = false;
          this.trackAllList = [];
          console.log(res);
          let data = res.data.data;
          //APP收集的轨迹 建运宝轨迹
          if (data && (data.truckGps || data.zjxlGps)) {
            if (data.truckGps && data.truckGps.length > 1) {
              //最少要两个点
              this.trackAllList.push({
                name: "建运宝",
                label: "建运宝轨迹",
                speed: 80,
                points: [],
                lngLatArr: [],
              });
              data.truckGps.forEach((item2) => {
                let arr = item2.gps.split(",");
                this.trackAllList[this.trackAllList.length - 1].lngLatArr.push(
                  arr
                );
                this.trackAllList[this.trackAllList.length - 1].points.push({
                  name: item2.createDatetime,
                  lnglat: arr,
                });
              });
            }
            //中交兴路轨迹
            if (data.zjxlGps && data.zjxlGps.length > 1) {
              this.trackAllList.push({
                name: "中交兴路",
                label: "中交兴路轨迹",
                speed: 80,
                points: [],
                lngLatArr: [],
              });
              data.zjxlGps.forEach((item2) => {
                let arr = item2.gps.split(",");
                this.trackAllList[this.trackAllList.length - 1].lngLatArr.push(
                  arr
                );
                this.trackAllList[this.trackAllList.length - 1].points.push({
                  name: item2.createDatetime,
                  lnglat: arr,
                });
              });
            }
            // //自定义轨迹
            // if (data.customGps && data.customGps.length > 1) {
            //   this.trackAllList.push({
            //     name: "自定义",
            //     label: "自定义轨迹",
            //     speed:80,
            //     points: [],
            //     lngLatArr: [],
            //   });
            //   data.customGps.forEach((item2) => {
            //     let arr = item2.gps.split(",");
            //     this.trackAllList[this.trackAllList.length - 1].lngLatArr.push(arr)
            //     this.trackAllList[this.trackAllList.length - 1].points.push({
            //       name: item2.createDatetime,
            //       lnglat: arr,
            //     });
            //   });
            // }
            // //标准轨迹
            // if (data.standardGps && data.standardGps.length > 1) {
            //   this.trackAllList.push({
            //     name: "标准",
            //     label: "标准轨迹",
            //     speed:80,
            //     points: [],
            //     lngLatArr: [],
            //   });
            //   data.standardGps.forEach((item2) => {
            //     let arr = item2.gps.split(",");
            //     this.trackAllList[this.trackAllList.length - 1].lngLatArr.push(arr)
            //     this.trackAllList[this.trackAllList.length - 1].points.push({
            //       name: item2.createDatetime,
            //       lnglat: arr,
            //     });
            //   });
            // }
            console.log(this.trackAllList);
            this.trackAllVisible = true;
          } else {
            this.$message.error("暂无轨迹信息");
          }
          // this.trackList = [
          // {
          //   name: "粤BLK787中交兴路轨迹",
          //   speed:100,
          //   label:'粤BLK787',
          //   points: [
          //     {
          //       lnglat: [113.98074, 22.55251],
          //       name: "2023-04-18 15:23:11",
          //     },
          //     {
          //       lnglat: [114.117679, 22.543603],
          //       name: "2023-04-18 15:23:09",
          //     },
          //     {
          //       lnglat: [114.917679, 22.553303],
          //       name: "2023-04-18 15:23:51",
          //     },
          //   ],
          // },
          // {
          //   name: "粤BLK787建运宝轨迹",
          //   label:'粤BLK787',
          //   speed:100,
          //   points: [
          //     {
          //       lnglat: [113.96074, 22.55651],
          //       name: "2023-04-18 15:23:11",
          //     },
          //     {
          //       lnglat: [114.112679, 22.548603],
          //       name: "2023-04-18 15:23:09",
          //     },
          //     {
          //       lnglat: [114.911679, 22.549303],
          //       name: "2023-04-18 15:23:51",
          //     },
          //   ],
          // },
          // {
          //   name: "粤BLK788中交兴路轨迹",
          //   label:'粤BLK788',
          //   speed:100,
          //   points: [
          //     {
          //       lnglat: [113.98074, 22.55351],
          //       name: "2023-04-18 15:23:11",
          //     },
          //     {
          //       lnglat: [114.117679, 22.544603],
          //       name: "2023-04-18 15:23:09",
          //     },
          //     {
          //       lnglat: [114.917679, 22.544303],
          //       name: "2023-04-18 15:23:51",
          //     },
          //   ],
          // },
          // {
          //   name: "粤BLK788建运宝轨迹",
          //   label:'粤BLK788',
          //   speed:100,
          //   points: [
          //     {
          //       lnglat: [113.96074, 22.55451],
          //       name: "2023-04-18 15:23:11",
          //     },
          //     {
          //       lnglat: [114.112679, 22.547603],
          //       name: "2023-04-18 15:23:09",
          //     },
          //     {
          //       lnglat: [114.911679, 22.548303],
          //       name: "2023-04-18 15:23:51",
          //     },
          //   ],
          // },
          // ];
        })
        .catch((err) => {
          this.tableLoading = false;
        });
      // trackAllVisible
      // trackAllList
    },
    calcAngle(start, end) {
      console.log(start);
      console.log(end);
      var p_start = this.map.lngLatToContainer(start),
        p_end = this.map.lngLatToContainer(end);
      var diff_x = p_end.x - p_start.x,
        diff_y = p_end.y - p_start.y;
      return (360 * Math.atan2(diff_y, diff_x)) / (2 * Math.PI) + 90;
    },
    //初始化地图
    initMap() {
      this.map = new AMap.Map("maps", {
        resizeEnable: true, //窗口大小调整
        center: this.firstArr, //中心 firstArr: [116.478935, 39.997761],
        zoom: 17,
      });
      // var historyItem = null
      // this.graspRoadList.forEach((item) => {
      //   console.log(historyItem);
      //   console.log(item);
      //   item.ag = historyItem ? this.calcAngle(historyItem, [item.x, item.y]) : 0;
      //   historyItem = [item.x, item.y];
      // });
      // this.graspRoad = new AMap.GraspRoad();
      // this.graspRoad.driving(this.graspRoadList, function (error, result) {
      // console.log(result);
      // if(!error){
      //   var path2 = [];
      //   var newPath = result.data.points;
      //   for(var i =0;i<newPath.length;i+=1){
      //     path2.push([newPath[i].x,newPath[i].y])
      //   }
      //   var newLine = new AMap.Polyline({
      //     path:path2,
      //     strokeWeight:8,
      //     strokeOpacity:0.8,
      //     strokeColor:'#0091ea',
      //     showDir:true
      //   })
      //   map.add(newLine)
      //   map.setFitView()
      // }
      // });
      // 创建一个车 Icon
      // var car = new AMap.Icon({
      //   // 图标尺寸
      //   size: new AMap.Size(70, 69),
      //   // 图标的取图地址
      //   image: require("../../../static/trucks.png"),
      //   // 图标所用图片大小
      //   imageSize: new AMap.Size(69, 39),
      //   angle: 90,
      // });
      // this.marker = new AMap.Marker({
      //   map: this.map,
      //   position: this.firstArr,
      //   icon: car,
      //   offset: new AMap.Pixel(-20, -20),
      //   autoRotation: true,
      //   angle: -270,
      // });
      //创建车运行时信息
      // this.carWindow = new AMap.InfoWindow({
      //   offset:new AMap.Pixel(6,-25),
      //   content:''
      // })
      // 创建一个起点 Icon
      var startIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(25, 34),
        // 图标的取图地址
        image:
          "//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(135, 40),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(-9, -3),
      });
      let startMarker = new AMap.Marker({
        map: this.map,
        position: this.firstArr,
        icon: startIcon,
        offset: new AMap.Pixel(-13, -30),
        label: {
          // offset: new AMap.Pixel(10, 10), //设置文本标注偏移量
          content: `<div class='left-label'><img src='https://s3.bmp.ovh/imgs/2022/04/18/96628058ce87c40a.png' />(起) ${this.trackList[0].points[0].name}`, //设置文本标注内容
          direction: "top", //设置文本标注方位
        },
      });
      // 创建一个终点 Icon
      var endIcon = new AMap.Icon({
        size: new AMap.Size(25, 34),
        image:
          "//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
        imageSize: new AMap.Size(135, 40),
        imageOffset: new AMap.Pixel(-95, -3),
      });
      let endMarker = new AMap.Marker({
        map: this.map,
        position: this.lineArr[this.lineArr.length - 1],
        icon: endIcon,
        offset: new AMap.Pixel(-13, -30),
        label: {
          // offset: new AMap.Pixel(10, 10), //设置文本标注偏移量
          content: `<div class='left-label'><img src='https://s3.bmp.ovh/imgs/2022/04/18/96628058ce87c40a.png' />(终) ${
            this.trackList[0].points[this.trackList[0].points.length - 1].name
          }</div>`, //设置文本标注内容
          direction: "top", //设置文本标注方位
        },
      });

      AMapUI.load(["ui/misc/PathSimplifier", "lib/$"], (PathSimplifier, $) => {
        if (!PathSimplifier.supportCanvas) {
          alert("当前环境不支持 Canvas！");
          return;
        }
        this.pathSimplifierIns = new PathSimplifier({
          zIndex: 100,
          //autoSetFitView:false,
          map: this.map, //所属的地图实例
          getPath: function (pathData, pathIndex) {
            var points = pathData.points,
              lnglatList = [];

            for (var i = 0, len = points.length; i < len; i++) {
              lnglatList.push(points[i].lnglat);
            }

            return lnglatList;
          },
          getHoverTitle: function (pathData, pathIndex, pointIndex) {
            if (pointIndex >= 0) {
              //point
              return pathData.name + "，" + pathData.points[pointIndex].name;
            }

            return "轨迹点数量" + pathData.points.length;
          },
          renderOptions: {
            renderAllPointsIfNumberBelow: 1000000, //绘制路线节点，如不需要可设置为-1
            pathLineStyle: {
              strokeStyle: "#28F", //线颜色
              lineWidth: 6, //线宽
            },
          },
        });
        console.log(this.trackList);
        //设置数据
        this.pathSimplifierIns.setData(this.trackList);
        let that = this;
        //对第一条线路（即索引 0）创建一个巡航器
        this.navg1 = this.pathSimplifierIns.createPathNavigator(0, {
          loop: false, //循环播放
          speed: 10000, //巡航速度，单位千米/小时
          pathNavigatorStyle: {
            width: 69,
            height: 39,
            initRotateDegree: 270,
            autoRotate: true,
            pathLinePassedStyle: {
              strokeStyle: "#AF5", //线颜色
              lineWidth: 6, //线宽
            },
            content: PathSimplifier.Render.Canvas.getImageContent(
              require("../../../static/trucks.png"),
              () => {
                that.pathSimplifierIns.renderLater();
              },
              () => {
                alert("图片加载失败！");
              }
            ),
          },
        });
        // var $markerContent = $('<div class="markerInfo" style="background-color: rgba(0,0,0,0.4);color: #fff;border-radius: 4px;padding: 5px 10px;text-align: center;white-space: nowrap;"></div>');
        // console.log(this.pathSimplifierIns.getPathData(0));
        // $markerContent.html(this.pathSimplifierIns.getPathData(0).points[0].name);
        // console.log($markerContent);
        // this.navg1.marker = new AMap.Marker({
        //     offset: new AMap.Pixel(12, -10),
        //     content: $markerContent.get(0),
        //     map: this.map
        // });
        // this.navg1.marker.setPosition(this.navg1.getPosition());
        // this.navg1.on('move',(e)=>{
        //   console.log(e);
        //   $markerContent.html(this.pathSimplifierIns.getPathData(0).points[e.target.cursor.idx].name);
        //   this.navg1.marker.setContent($markerContent.get(0));
        //   this.navg1.marker.setPosition(this.navg1.getPosition());
        // });
        // this.navg1.onDestroy(()=>{
        //     this.navg1 = null;
        //     this.navg1.marker.setMap(null);
        // });
        this.map.setFitView(); //合适的视口
      });
    },
    //初始化轨迹
    initroad(row) {
      // //绘制还未经过的路线
      // this.trackList.forEach((item,index,arr)=>{
      //   if(item.gps&&index!=0){
      //     let gps = item.gps.split(',')
      //     let lastgps = arr[index-1].gps.split(',')
      //     let lineGps = [gps,lastgps]
      //    let polyline =  new AMap.Polyline({
      //       map: this.map,
      //       path: lineGps,
      //       showDir: true,
      //       strokeColor: "#28F", //线颜色--蓝色
      //       // strokeOpacity: 1,     //线透明度
      //       strokeWeight: 6, //线宽
      //       // strokeStyle: "solid"  //线样式
      //       strokeDasharray: [0,0,0],
      //       lineJoin: 'round',
      //       lineCap: 'round',
      //       zIndex: 20
      //     });
      //     polyline.on('mouseover',(e)=>{
      //       this.carWindow.open(this.map,gps)
      //       this.carWindow.setContent(item.createDatetime)
      //     })
      //     polyline.on('mouseout',(e)=>{
      //       console.log(111);
      //       this.carWindow.close()
      //     })
      //   }
      // })
      // let that = this
      // function showCarWindow(e){
      //   that.carWindow.open(that.map,gps)
      //   that.carWindow.setContent(item.createDatetime)
      // }
      // this.polyline = new AMap.Polyline({
      //   map: this.map,
      //   path: this.lineArr,
      //   showDir: true,
      //   strokeColor: "#28F", //线颜色--蓝色
      //   strokeWeight: 6, //线宽
      // });
      // // 绘制路过了的轨迹
      // var passedPolyline = new AMap.Polyline({
      // map: this.map,
      // strokeColor: "#AF5", //线颜色-绿色
      //path: this.lineArr,
      // strokeOpacity: 1,     //线透明度
      // strokeWeight: 6, //线宽
      // strokeStyle: "solid"  //线样式
      // });
      // this.navg1.on("moving", () => {
      // passedPolyline.setPath(e.passedPath);
      // });
    },
    startAnimation() {
      // this.marker.moveAlong(this.lineArr, this.speed);
      this.navg1.setSpeed(this.speed);
      this.navg1.start();
    },
    pauseAnimation() {
      this.navg1.pause();
      // this.marker.pauseMove();
    },
    resumeAnimation() {
      this.navg1.setSpeed(this.speed);
      this.navg1.resume();
      // this.marker.resumeMove();
    },
    stopAnimation() {
      // this.navg1.getPathStartIdx();
      this.navg1.moveToPoint(this.navg1.getPathStartIdx());
      this.navg1.stop();
      // this.marker.stopMove();
    },
    // exOut(value) {
    //   this.$router.push({ path: "/exportExc/exportExc" });
    // },
    exOutExcel() {
      let params = Object.assign({}, this.paramsSearch);

      console.log(params);
      if (params) {
        if (params.hasOwnProperty("generationTime")) {
          params.generationTimeStart = params.generationTime[0];
          params.generationTimeEnd = params.generationTime[1];
          delete params.generationTime;
        }
        if (params.hasOwnProperty("inDatetime")) {
          params.inDatetimeStart = params.inDatetime[0];
          params.inDatetimeEnd = params.inDatetime[1];
          delete params.inDatetime;
        }
        if (params.hasOwnProperty("goTime")) {
          params.goDatetimeStart = params.goTime[0];
          params.goDatetimeEnd = params.goTime[1];
          delete params.goTime;
        }
        if (params.hasOwnProperty("completeTime")) {
          params.completeDatetimeStart = params.completeTime[0];
          params.completeDatetimeEnd = params.completeTime[1];
          delete params.completeTime;
        }
        if (params.hasOwnProperty("entranceDatetime")) {
          params.entranceDatetimeStart = params.entranceDatetime[0];
          params.entranceDatetimeEnd = params.entranceDatetime[1];
          delete params.entranceDatetime;
        }
        if (params.hasOwnProperty("goShiftTime")) {
          params.goShiftTimeStart = params.goShiftTime[0];
          params.goShiftTimeEnd = params.goShiftTime[1];
          delete params.goShiftTime;
        }
        params.projectInfoId =
          Array.isArray(params.projectInfoId) && params.projectInfoId.length > 0
            ? params.projectInfoId.join(",")
            : params.projectInfoId || "";
      }
      for (const key in params) {
        if (key.includes("$") || params[key] == undefined) {
          delete params[key];
        }
      }
      params.code = "BuilderCompanyWaybillExcelExport";
      this.btnLoading = true;
      postCreateByCode(params)
        .then((res) => {
          this.btnLoading = false;
          // this.$message.success('操作123成功')
          this.$store.commit("SET_DOWN_EXCEL_SHOW", true);
        })
        .catch(() => {
          this.btnLoading = false;
          this.$message.error("导出失败");
        });
    },
    viewInfo(row, index) {
      this.tableLoading = true;
      getObj(row.id)
        .then((res) => {
          console.log(" res.data.data", res.data.data);
          this.tableLoading = false;
          this.detailForm = res.data.data;
          this.detailForm.ticketNo =
            this.detailForm.ticketNo &&
            this.detailForm.manualSelectTicket == "0"
              ? `${this.detailForm.ticketNo}(系统分配)`
              : this.detailForm.ticketNo || "";
          this.detailVisible = true;
        })
        .catch((err) => {
          this.tableLoading = false;
        });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    batchSetRemark() {
      this.$prompt("PC后台备注", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入PC后台备注",
        inputValidator: (value) => {
          if (!value) {
            return "PC后台备注不能为空";
          }
        },
        inputErrorMessage: "请输入PC后台备注",
      })
        .then(({ value }) => {
          let arr = this.multipleSelection.map((item) => {
            return {
              companyWaybillId: item.id,
              pcRemark: value,
            };
          });
          this.editPcRemark(arr);
        })
        .catch(() => {});
    },
    batchSetFleetName() {
      this.$prompt("车队名称", "批量设置车队名称", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入车队名称",
        inputValidator: (value) => {
          if (!value) {
            return "车队名称不能为空";
          }
        },
        inputErrorMessage: "请输入车队名称",
      })
        .then(({ value }) => {
          let ids = this.multipleSelection.map((item) => item.id);
          let param = {
            ids,
            captainFleetName: value,
          };
          batchFleetName(param).then((res) => {
            this.$message.success("操作成功");
            this.getPage(this.page);
          });
        })
        .catch(() => {});
    },
    setRemark(row) {
      this.$prompt("PC后台备注", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入PC后台备注",
        inputValue: row.pcRemark || "",
        inputValidator: (value) => {
          if (!value) {
            return "PC后台备注不能为空";
          }
        },
        inputErrorMessage: "请输入PC后台备注",
      })
        .then(({ value }) => {
          this.editPcRemark([
            {
              companyWaybillId: row.id,
              pcRemark: value,
            },
          ]);
        })
        .catch(() => {});
    },
    editPcRemark(arr) {
      editPcRemark(arr).then((res) => {
        this.$message.success("操作成功");
        this.getPage(this.page);
      });
    },
    batchSetVehicleType() {
      this.$prompt("出口签单车型", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入出口签单车型",
        inputValidator: (value) => {
          if (!value) {
            return "出口签单车型不能为空";
          }
        },
        inputErrorMessage: "请输入出口签单车型",
      })
        .then(({ value }) => {
          let arr = this.multipleSelection.map((item) => item.id);
          this.batchUpdateGoVehicleType({
            ids: arr,
            goVehicleType: value,
          });
        })
        .catch(() => {});
    },
    setVehicleType(row) {
      this.$prompt("出口签单车型", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入出口签单车型",
        inputValue: row.goVehicleType || "",
        inputValidator: (value) => {
          if (!value) {
            return "出口签单车型不能为空";
          }
        },
        inputErrorMessage: "请输入出口签单车型",
      })
        .then(({ value }) => {
          this.batchUpdateGoVehicleType({
            ids: [row.id],
            goVehicleType: value,
          });
        })
        .catch(() => {});
    },
    batchSetShift() {
      let same = this.multipleSelection.every((item) => {
        return item.projectInfoId == this.multipleSelection[0].projectInfoId;
      });
      if (!same) {
        this.$message.error("请选择相同项目的运单进行设置");
        return false;
      }
      this.setShiftVisible = true;
    },
    batchUpdateGoVehicleType(arr) {
      batchUpdateGoVehicleType(arr).then((res) => {
        this.$message.success("操作成功");
        this.getPage(this.page);
      });
    },
    handleClick(val) {
      this[val]();
    },
    openUrl() {
      window.open(
        "https://jyb-app.obs.cn-south-1.myhuaweicloud.com:443/%E8%BF%90%E5%8D%95%E4%B8%8B%E8%BD%BD%E5%9B%BE%E7%89%87%E5%B7%A5%E5%85%B7.zip"
      );
    },
    exportWaybillImage() {
      this.$confirm("导出图片需要先下载旁边的导出图片工具,继续?", "提示", {
        confirmButtonText: "继续",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = Object.assign({}, this.paramsSearch);
          if (params) {
            if (params.hasOwnProperty("generationTime")) {
              params.generationTimeStart = params.generationTime[0];
              params.generationTimeEnd = params.generationTime[1];
              delete params.generationTime;
            }
            if (params.hasOwnProperty("inDatetime")) {
              params.inDatetimeStart = params.inDatetime[0];
              params.inDatetimeEnd = params.inDatetime[1];
              delete params.inDatetime;
            }
            if (params.hasOwnProperty("goTime")) {
              params.goDatetimeStart = params.goTime[0];
              params.goDatetimeEnd = params.goTime[1];
              delete params.goTime;
            }
            if (params.hasOwnProperty("completeTime")) {
              params.completeDatetimeStart = params.completeTime[0];
              params.completeDatetimeEnd = params.completeTime[1];
              delete params.completeTime;
            }
            if (params.hasOwnProperty("entranceDatetime")) {
              params.entranceDatetimeStart = params.entranceDatetime[0];
              params.entranceDatetimeEnd = params.entranceDatetime[1];
              delete params.entranceDatetime;
            }
            if (params.hasOwnProperty("goShiftTime")) {
              params.goShiftTimeStart = params.goShiftTime[0];
              params.goShiftTimeEnd = params.goShiftTime[1];
              delete params.goShiftTime;
            }
            params.projectInfoId =
              Array.isArray(params.projectInfoId) &&
              params.projectInfoId.length > 0
                ? params.projectInfoId.join(",")
                : params.projectInfoId || "";
          }
          for (const key in params) {
            if (key.includes("$") || params[key] == undefined) {
              delete params[key];
            }
          }
          let url = "/chain/excelExport/postCreateByCode";
          params.code = "CompanyWaybillImageExcelExport";
          this.btnLoading = true;
          exportOut(params, url, "运单图片", "post")
            .then((res) => {
              this.btnLoading = false;
            })
            .catch((err) => {
              this.btnLoading = false;
            });
        })
        .catch(() => {});
    },
  },
  destroyed() {
    window.removeEventListener("resize", this.func);
  },
};
</script>

<style lang="scss" scoped>
.tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .tag {
    margin-right: 10px;
  }
}
.input-card {
  position: absolute;
  right: 40px;
  bottom: 40px;
  background-color: #fff;
  padding: 10px;
  .my-row {
    margin-top: 15px;
  }
}
/deep/ .amap-marker-label {
  background: rgba(0, 0, 0, 0.4);
}
/deep/.left-label {
  padding: 2px;
  display: flex;
  align-items: center;
  color: #ffff;
}

/deep/ .left-label img {
  width: 14px;
  height: 14px;
  margin-right: 10px;
}
.companywaybill2 {
  /deep/ .el-dialog__wrapper .el-dialog .el-dialog__body {
    height: 100%;
    // max-height: 100vh;
    // padding: 0;
    max-height: none;
  }
}
.btnBox {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 30px 0 0;
}
</style>
