export const tableOption = (value)=>{
  let that = value
  console.log(that);
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    index:true,
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    dialogClickModal:false,
    dialogWidth:600,
    excelBtn: false,
    printBtn: false,
    addBtn: false,
    viewBtn: false,
    searchSpan:6,
    searchLabelWidth:110,
    searchMenuSpan: 6,
    labelWidth:120,
    menuWidth:100,
    column: [
      {
        label: "客户手机号码",
        prop: "garbageCustomerMobile",
        sortable: true,
        search:true,
        minWidth:120,
        overHidden:true,
      },
      {
        label: "客户名称",
        prop: "garbageCustomerName",
        sortable: true,
        search:true,
        minWidth:96,
        overHidden:true,
      },
      {
        label: "项目名称(泥尾)",
        prop: "projectName",
        sortable: true,
        search: true,
        // type:'select',
        // props: {
        //   label: 'projectName',
        //   value: 'id'
        // },
        // dicUrl: "/chain/garbagecustomersales/getProjectInfoList",
        minWidth:140,
        overHidden:true,
      },
      {
        label: "销售总数",
        prop: "qty",
        sortable: true,
        minWidth:96,
        overHidden:true,
      },
      {
        label: "核销总数",
        prop: "checkedQty",
        sortable: true,
        minWidth:96,
        overHidden:true,
      },
      {
        label: "退票总数",
        prop: "returnQty",
        sortable: true,
        minWidth:96,
        overHidden:true,
      },
      {
        label: "剩余总数",
        prop: "remainQty",
        sortable: true,
        minWidth:96,
        overHidden:true,
      },
    ],
  };
}

