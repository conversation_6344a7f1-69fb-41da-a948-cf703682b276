<template>
  <el-drawer
    lock-scroll
    :modal-append-to-body="false"
    append-to-body
    class="avue-dialog"
    title="列显隐"
    size="800px"
    :visible.sync="columnBox"
    :before-close="cancelModal"
  >
    <!-- <el-table
      :data="list"
      ref="table"
      height="100%"
      :key="Math.random()"
      size="small"
      border
    >
      <el-table-column
        align="center"
        width="100"
        header-align="center"
        prop="label"
        key="label"
        label="列名"
      >
      </el-table-column>
      <template v-for="(item, index) in defaultColumn">
        <el-table-column
          :prop="item.prop"
          :key="item.prop"
          align="center"
          header-align="center"
          v-if="item.hide != true"
          :label="item.label"
        >
          <template slot-scope="{ row }">
            <el-checkbox
              v-model="crud.objectOption[row.prop][item.prop]"
            ></el-checkbox>
          </template>
        </el-table-column>
      </template>
    </el-table> -->
    <ul
      v-loading="loading"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      v-if="list&&list.length>0&&crud.objectOption"
    >
      <li class="listHeader">
        <span>列名</span>
        <span>隐藏</span>
        <span>冻结</span>
        <span>过滤</span>
        <span>排序</span>
        <span>宽度</span>
      </li>
      <el-scrollbar style="height:calc(100vh - 160px)">
      <draggable
        v-model="list"
        @update="datadragEnd"
        :options="{ animation: 500 }"
        handle=".left"
      >
        <li v-for="(item, index) in list" :key="index">
          <span class="left"><i class="el-icon-rank"></i>{{ item.label }}</span>
          <span
            ><el-checkbox
              @change="changCheckbox('hide',$event)"
              v-model="crud.objectOption[item.prop]['hide']"
            ></el-checkbox
          ></span>
          <span
            ><el-checkbox
              v-model="crud.objectOption[item.prop]['fixed']"
            ></el-checkbox
          ></span>
          <span
            ><el-checkbox
              @change="changCheckbox('filters',$event)"
              v-model="crud.objectOption[item.prop]['filters']"
            ></el-checkbox
          ></span>
          <span
            ><el-checkbox
              v-model="crud.objectOption[item.prop]['sortable']"
            ></el-checkbox
          ></span>
          <span
            >
            <!-- <el-slider
              :min="0"
              :max="2000"
              size="small"
              @change="changCheckbox('width')"
              v-model="crud.objectOption[item.prop].width"
            ></el-slider
          > -->
          <avue-input-number :precision="0" :min="0"  size="small" :max="2000" v-model="crud.objectOption[item.prop].width"></avue-input-number>
          </span>
          <!-- <div class="right">
                <i class="el-icon-upload2" v-if="index!=0" @click="sortList(index,index-1)"></i>
                <span v-if="index!=0&&index!=defaultColumn.length-1">|</span>
                <i class="el-icon-download" v-if="index!=defaultColumn.length-1" @click="sortList(index,index+1)"></i>
              </div> -->
        </li>
      </draggable>
    </el-scrollbar>

    </ul>
  </el-drawer>
</template>
<script>
import config from "../config.js";
import draggable from "vuedraggable";
import {saveOrUpdate} from '@/api/chain/companycustompage'
import { arraySort } from "@/util/util";

export default {
  name: "crud",
  inject: ["crud"],
  data() {
    return {
      bindList: {},
      loading: false,
      list:[],
      columnBox:false,
    };
  },
  components: {
    draggable,
  },
  computed: {
    defaultColumn() {
      return [
        {
          label: "隐藏",
          prop: "hide",
        },
        {
          label: "冻结",
          prop: "fixed",
        },
        {
          label: "过滤",
          prop: "filters",
        },
        {
          label: "排序",
          prop: "sortable",
        },
        {
          label: "序号",
          prop: "index",
          hide: true,
        },
        {
          label: "宽度",
          prop: "width",
          hide: true,
        },
      ];
    },
    routerName(){
      return this.crud.option.routerName||""
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      let list = [];
      this.crud.propOption.forEach((ele) => {
        if (ele.showColumn != false) {
          list.push(ele);
        }
      });
      list = arraySort(list, 'order', (a, b) => this.crud.objectOption[a.prop]?.order - this.crud.objectOption[b.prop]?.order)
      this.list = list
    },
    changCheckbox(prop,val){
      console.log(prop);
      console.log(val);
      if(prop=='hide'||prop=='filters'){
        console.log(this.crud.objectOption);
        this.$nextTick(()=>{
          this.crud.refreshTable()
        })
      }
    },
    datadragEnd(evt) {
      this.list.forEach((item,index)=>{
        this.crud.objectOption[item.prop]['order'] = index+1
      })
      evt.preventDefault();
      this.crud.refreshTable()
    },
    cancelModal() {
      this.columnBox = false
      if(this.routerName){
        saveOrUpdate({pageRoute:this.routerName,pageTableOption:JSON.stringify(this.crud.objectOption)}).then(res=>{
          console.log(res);
        })
      }
    },
    showBox(){
      this.columnBox = true
    },
  },
};
</script>
<style lang="scss" scoped>
ul {
  border-top: 1px solid #ebeef5;
  border-left: 1px solid #ebeef5;
  li {
    border-right: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    text-align: center;
    line-height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #303133;
    // font-weight: 700;
    cursor: pointer;
    .left:hover{
      cursor: move;
    }
    span {
      display: inline-block;
      width: 13%;
      text-align: center;
      &:first-child {
        width: 28%;
        padding-left: 10px;
      text-align: left;
      }
      &:last-child {
        width: 20%;
        padding-right: 10px;
      }
    }
    &:hover {
      background-color: #ebeef5;
    }
    i {
      margin-right: 10px;
      cursor: pointer;
      color: #409eff;
      font-weight: 700;
    }
    div {
      text-align: right;
      span {
        margin-right: 10px;
        color: #ccc;
      }
    }
  }
}
</style>
