import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/garbagecustomersales/statics',
        method: 'get',
        params: query
    })
}
export function getPage2(query) {
    return request({
        url: '/chain/garbagecustomersales/staticsDetail',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/garbagecustomerinventory',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/garbagecustomerinventory/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/garbagecustomerinventory/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/garbagecustomerinventory',
        method: 'put',
        data: obj
    })
}
