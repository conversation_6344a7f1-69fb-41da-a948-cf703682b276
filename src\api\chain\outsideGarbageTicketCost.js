import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companyticketpurchase/purchaseAvgMonthCostPage',
        method: 'get',
        params: query
    })
}
export function getPage2(query) {
  return request({
    url: '/chain/companyticketpurchase/purchasePage',
    method: 'get',
    params: query
  })
}

export function recount(query) {
    return request({
        url: '/chain/companyticketpurchase/calculatePurchaseAvgPrice',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companyticketprojectinventory',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companyticketprojectinventory/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companyticketprojectinventory/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companyticketprojectinventory',
        method: 'put',
        data: obj
    })
}
