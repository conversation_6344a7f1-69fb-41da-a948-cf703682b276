<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="70%"
      title="查看明细"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <avue-crud
          ref="crud"
          :data="tableData"
          :table-loading="tableLoading"
          :option="tableOption"
          @on-load="getPage"
          @refresh-change="refreshChange"
          @search-change="searchChange"
          @search-reset="searchReset"
        >
          <template slot="platformBranchNsrmcSearch" slot-scope="scope">
            <el-input disabled :value="info.platformBranchNsrmc"></el-input>
          </template>
          <template slot="amountSearch" slot-scope="scope">
            <!-- amount -->
            <div style="display: flex;">
              <el-input-number style="margin-right:2px;" v-model="minAmount" :min="0" :precision="2" :controls="false" size="small" :step="0.01" step-strictly></el-input-number>
              至
              <el-input-number style="margin-left: 2px;" v-model="maxAmount" :min="0" :precision="2" :controls="false" size="small" :step="0.01" step-strictly></el-input-number>
            </div>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {getDetail} from "@/api/chain/companyWalletStatistics";
import { expotOut } from "@/util/down.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  data() {
    return {
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: true,
        labelWidth: 150,
        searchLabelWidth: 86,
        searchSpan: 8,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchMenuSpan: 8,
        menu: false,
        column: [
          {
            label: "类型",
            prop: "type",
            type: "select", // 下拉选择
            search: true,
            width:"50px",
            dicData: [
              {
                value: "1",
                label: "充值",
              },
              {
                value: "2",
                label: "提现",
              },
              {
                value: "3",
                label: "直付运单",
              },
            ],
          },
          {
            label: "收款企业",
            prop: "platformBranchNsrmc",
            search:true,
            formatter: (val) => {
              return val.type==2?val.companyName:val.platformBranchNsrmc
            },
          },
          {
            label: "收款银行",
            prop: "platformBranchBankAccountName",
            formatter: (val) => {
              return val.type==2?val.withdrawBankAccountName:val.platformBranchBankAccountName
            },
          },
          {
            label: "收款账户",
            prop: "platformBranchBankAccount",
            formatter: (val) => {
              return val.type==2?val.withdrawBankAccount:val.platformBranchBankAccount
            },
          },
          {
            label: "项目名称",
            prop: "projectName",
          },
          // {
          //   label: "企业名称",
          //   prop: "companyName",
          // },
          {
            label: "金额",
            prop: "amount",
            width:"70px",
            search:true,
          },
          {
            label: "手续费",
            prop: "platformFee",
            width:"70px",
          },
          {
            label: "银行流水号",
            prop: "frontLogNo",
            search:true,
            width:"100px",
          },
          {
            label: "银行凭证",
            prop: "frontLogPic",
            type: "upload",
            width:"70px",
          },
          {
            label: "申请日期",
            prop: "searchDate",
            type:'date',
            search:true,
            searchRange:true,
            hide: true,
            showColumn:false,
            valueFormat: 'yyyy-MM-dd',
          },
          {
            label: "申请时间",
            prop: "applyDatetime",
            type:'datetime',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            width:"84px",
          },
          {
            label: "申请人",
            prop: "applyName",
            width:"70px",
          },
          {
            label: "审核时间",
            prop: "auditDatetime",
            width:"84px",
          },
          {
            label: "审核人",
            prop: "auditName",
            width:"70px",
          },
        ],
      },
      minAmount:'',
      maxAmount:undefined,
    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    searchReset(event){
      this.minAmount = ''
      this.maxAmount = undefined
    },
    cancelModal() {
      this.$emit("update:visible", false);
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.getPage(params);
      done();
    },
    getPage(params = {}) {
      console.log(params);
      console.log(this.paramsSearch);
      params.minAmount = this.minAmount
      params.maxAmount = this.maxAmount
      params.platformBranchId = this.info.platformBranchId
      params.companyAuthId = this.info.companyAuthId
      params.projectInfoId = this.info.projectInfoId
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
      }
      this.tableLoading = true;
      getDetail(params)
        .then((response) => {
          console.log('response',response);
          this.tableData = response.data.data.records;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
     refreshChange() {
      this.getPage();
    },
  },
};
</script>

<style lang="scss" scoped>

/deep/ .avue-crud__pagination{
  display: none;
}
.count{
  display: flex;
  margin-top: 20px;
  color: #000;
  li{
    margin-right: 50px;
  }
}
</style>
