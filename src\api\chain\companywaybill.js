import request from '@/router/axios'

export function getPage(params) {
  return request({
    url: '/chain/companywaybill/page',
    method: 'get',
    params
  })
}
export function queryExcavatorLoadingPage(data) {
  return request({
    url: '/chain/companywaybill/queryExcavatorLoadingPage',
    method: 'post',
    data
  })
}
export function getBuilderPage(query) {
  return request({
    url: '/chain/companyWaybillBuilderPage/postBuilderPage',
    method: 'post',
    data: query
  })
}
//车队长运单查看
export function getCaptainWaybill(query) {
  return request({
    url: '/chain/captainWaybill/getPage',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/chain/companywaybill',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/chain/companywaybill/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/chain/companywaybill/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/chain/companywaybill',
    method: 'put',
    data: obj
  })
}
export function getGpsList(query) {
  return request({
    url: '/chain/drivertruckgps/getGpsList',
    method: 'get',
    params: query
  })
}
export function getGpsListNew(query) {
  return request({
    url: '/chain/drivertruckgps/getGpsListNew',
    method: 'get',
    params: query
  })
}
//通过id获取 签单员 土质 泥尾
export function infoList(query) {
  return request({
    url: '/chain/addWaybill/infoList',
    method: 'get',
    params: query
  })
}
//获取资源地
export function listByDriver(params) {
  return request({
    url: '/chain/resourcecustomerinventory/listByDriver',
    method: 'get',
    params
  })
}
//泥尾获取泥尾票土质
export function querySoilTypeByGarbage(query) {
  return request({
    url: '/chain/companyticketprojectinventory/querySoilTypeByGarbage',
    method: 'get',
    params: query
  })
}
//异常申请审核
export function auditUpdateHistory(data) {
  return request({
    url: '/chain/companywaybillupdatehistory/auditForWorkFlow',
    method: 'post',
    data
  })
}

//获取车队长列表
export function listDriverCaptainTaskByProject(query) {
  return request({
    url: '/chain/drivercaptaintask/listDriverCaptainTaskByProject',
    method: 'get',
    params: query
  })
}
//运单台账导出Exc
export function queryByCode(query) {
  return request({
    url: '/chain/garbageBaseQuery/queryByCode',
    method: 'get',
    params: query
  })
}
//PC后台备注
export function editPcRemark(query) {
  return request({
    url: '/chain/companywaybillext/editPcRemark',
    method: 'post',
    data: query
  })
}
//挖机装车关联PC后台备注
export function batchUpdateRemark(query) {
  return request({
    url: '/chain/companywaybillext/batchUpdateRemark',
    method: 'post',
    data: query
  })
}
//批量更新出口签单车型
export function batchUpdateGoVehicleType(query) {
  return request({
    url: '/chain/companywaybill/batchUpdateGoVehicleType',
    method: 'post',
    data: query
  })
}
//批量设置车队名称
export function batchFleetName(query) {
  return request({
    url: '/chain/companywaybill/batchFleetName',
    method: 'post',
    data: query
  })
}
//批量修改班次
export function batchSetShiftTime(query) {
  return request({
    url: '/chain/companyWaybillPage/batchSetShiftTime',
    method: 'post',
    data: query
  })
}

export function batchAddWaybillSettle(data) {
  return request({ //type2 为结算卡
    url: '/chain/companysettle/batchAddWaybillSettle?type=2',
    method: 'post',
    data
  })
}


export function batchUpdateInPreparePrice(data) {
  return request({
    url: '/chain/companywaybillext/batchUpdateInPreparePrice',
    method: 'post',
    data
  })
}

export function batchUpdateGarbagePreparePrice(data) {
  return request({
    url: '/chain/companywaybillext/batchUpdateGarbagePreparePrice',
    method: 'post',
    data
  })
}
//导出
export function postCreateByCode(obj) {
  return request({
    url: '/chain/excelExport/postCreateByCode',
    method: 'post',
    data: obj
  })
}
//方法名：根据条件,查询项目 or 撮合泥尾点 相关的撮合订单
export function getProjectNewMatchOrderList(obj) {
  return request({
    url: 'chain/matchorder/getProjectNewMatchOrderList',
    method: 'get',
    params: obj
  })
}
//导出挖机装车管理
export function exportExcavatorLoadingInfo(obj) {
  return request({
    url: '/chain/companywaybill/exportExcavatorLoadingInfo',
    method: 'post',
    data: obj
  })
}
//获取导入情况
export function thirdImportTaskMessage(query) {
  return request({
    url: '/chain/thirdImport/thirdImportTaskMessage',
    method: 'get',
    params: query
  })
}
//导入EXC
export function importThirdWaybillExcel(query) {
  return request({
    url: '/chain/thirdImport/importThirdWaybillExcel',
    method: 'post',
    data: query
  })
}

//导入EXC
export function exportPNG(query) {
  return request({
    url: '/chain/excelExport/exportPNG',
    method: 'post',
    data: query,
     responseType: 'arraybuffer',
  })
}
export function listDictionaryItem(type) {
  return request({
    url: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=' + type,
    method: 'get',

  })
}