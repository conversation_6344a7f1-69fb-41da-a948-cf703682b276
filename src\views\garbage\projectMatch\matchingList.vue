<template>
  <div class="projectMatchList">
    <el-drawer size="1200px"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <basic-container>
        <div style="position:absolute;left:32px;top:16px">
          <el-button type="primary"
                     icon="el-icon-refresh"
                     size="small"
                     @click="matchRefresh">
            更新匹配</el-button>
        </div>
        <div>
          <el-radio-group style="margin-bottom:10px"
                          v-model="tabPosition"
                          @change="changeTab">
            <el-radio-button label="1">项目推荐池</el-radio-button>
            <el-radio-button label="2">待我确认</el-radio-button>
            <el-radio-button label="3">待项目确认</el-radio-button>
            <el-radio-button label="4">匹配成功</el-radio-button>
            <el-radio-button label="5">已取消</el-radio-button>
          </el-radio-group>
          <avue-crud ref="tableList"
                     :data="tableData"
                     :table-loading="tableLoading"
                     :option="tableOption"
                     v-model="form"
                     :page.sync="page"
                     @on-load="getPage">
            <template slot="menu"
                      slot-scope="{ row }">
              <el-button type="text"
                         icon="el-icon-share"
                         v-if="permissions['garbage:projectMatch:invite']&&tabPosition==1"
                         size="small"
                         :loading="tableLoading"
                         plain
                         @click="invite(row)">邀请</el-button>
              <el-button type="text"
                         icon="el-icon-check"
                         v-if="permissions['garbage:projectMatch:invite']&&tabPosition==2"
                         size="small"
                         plain
                         @click="acceptInvite(row)">接受邀请</el-button>
              <el-button type="text"
                         icon="el-icon-close"
                         v-if="permissions['garbage:projectMatch:invite']&&(tabPosition==2||tabPosition==3)"
                         size="small"
                         plain
                         @click="cancelInvite(row)">取消邀请</el-button>
            </template>
          </avue-crud>
        </div>
      </basic-container>
    </el-drawer>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getPoolPage,invite,acceptInvite,cancelInvite,matchRefresh} from "@/api/garbage/projectMatch";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tabPosition: '1',
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: false,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: false,
        refreshBtn: false,
        columnBtn: false,
        header: false,
        menuWidth: 180,
        column: [
          {
            label: "匹配值",
            prop: "planMatchValue",
            minWidth:66,
            overHidden: true,
            formatter: (val) => {
              return val.planMatchValue +'%'
            },
          },
          {
            label: "项目地址",
            prop: "targetGpsAddress",
            minWidth:110,
            overHidden: true,
          },
          {
            label: "联系人",
            prop: "targetContactPerson",
            minWidth:90,
            overHidden: true,
          },
          {
            label: "联系电话",
            prop: "targetContactPhone",
            minWidth:96,
            overHidden: true,
          },
          {
            label: "车数/天",
            prop: "targetCarsNumber",
            minWidth:70,
            overHidden: true,
          },
          {
            label: "收土土质",
            prop: "targetSoils",
            minWidth:70,
            overHidden: true,
          },
          {
            label: "直线距离",
            prop: "matchDistance",
            formatter: (val) => {
              return val.matchDistance&&val.matchDistance +'km'
            },
            minWidth:70,
            overHidden: true,
          },
          {
            label: "价格",
            prop: "targetPrice",
            minWidth:90,
            overHidden: true,
          },
          {
            label: "账期",
            prop: "targetSettleCycle",
            minWidth:70,
            overHidden: true,
          },
          {
            label: "匹配成功时间",
            prop: "updateDatetime",
            hide: true,
            minWidth: 140,
            overHidden: true,
          },
        ],
      },
      btnLoading: false,
    }
  },
  created () {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
      this.$emit("refreshChange")
    },
    getPage (page, params = {}) {
      if(this.info.type){
        this.tabPosition = this.info.type
        delete this.info.type
      }
      let planPoolStatus = 1
      switch (this.tabPosition) {
        case '2':
          planPoolStatus = 3
          break;
        case '3':
          planPoolStatus = 2
          break;
        case '4':
          planPoolStatus = 5
          this.$nextTick(()=>{
            this.tabActive()
          })
          break;
        case '5':
          planPoolStatus = 6
          break;
        default:
          break;
      }
      let data = {selectMatchPlanId: this.info.id,planPoolStatus}
      getPoolPage(
      Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          data
      )).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total;
        this.page.currentPage = page.currentPage;
        this.page.pageSize = page.pageSize;
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    matchRefresh () {
      this.$confirm("更新匹配后，平台将更新所有土石方发布新的撮合计划，重新为您匹配推荐", "提示", {
        confirmButtonText: "更新匹配",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableLoading = true
          matchRefresh({garbageProjectPlanId:this.info.id}).then((response) => {
            this.tableLoading = false
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });
            this.tabPosition=1
            this.changeTab()
          })
          .catch(() => {
            this.tableLoading = false
          });
        })
        .catch((err) => {});
    },
    tabActive(){
      let updateDatetime = this.findObject(this.tableOption.column, 'updateDatetime');
      console.log(updateDatetime);
        updateDatetime.hide = true
        switch (this.tabPosition) {
          case "4":
            updateDatetime.hide = false
            updateDatetime.label = "匹配成功时间"
            this.tableOption.menu = false
            break;
          case "5":
            updateDatetime.hide = false
            updateDatetime.label = "取消邀请时间"
            this.tableOption.menu = false
            break;
          default:
            this.tableOption.menu = true
            break;
        }
        this.$refs.tableList.columnInit();
    },
    changeTab () {
      let updateDatetime = this.findObject(this.tableOption.column, 'updateDatetime');
      updateDatetime.hide = true
      switch (this.tabPosition) {
        case "4":
          updateDatetime.hide = false
          updateDatetime.label = "匹配成功时间"
          this.tableOption.menu = false
          break;
        case "5":
          updateDatetime.hide = false
          updateDatetime.label = "取消邀请时间"
          this.tableOption.menu = false
          break;
        default:
          this.tableOption.menu = true
          break;
      }
      this.page.currentPage = 1
      this.tableData = []
      this.getPage(this.page)
      this.$refs.tableList.columnInit();
    },
    // 邀请
    invite (row) {
      this.$confirm("发起邀请后，该土石方将收到邀请，待土石方接受邀请后，撮合完成。", "提示", {
        confirmButtonText: "发起邀请",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableLoading = true
          invite(row.id,5).then((response) => {
            this.tableLoading = false
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });
            this.getPage(this.page);
          })
          .catch(() => {
            this.tableLoading = false
          });
        })
        .catch((err) => {});
    },
    //接受邀请
    acceptInvite (row) {
      this.$confirm("接受邀请后，双方撮合完成。请到匹配成功查看记录。", "提示", {
        confirmButtonText: "接受邀请",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableLoading = true
          acceptInvite(row.id,5).then((response) => {
            this.tableLoading = false
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });
            this.getPage(this.page);
          })
          .catch(() => {
            this.tableLoading = false
          });
        })
        .catch((err) => {});
    },
    //取消邀请
    cancelInvite (row) {
      this.$confirm("取消邀请后，该土石方撮合计划将不再匹配到，请到已取消查看记录。", "提示", {
        confirmButtonText: "取消邀请",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableLoading = true
          cancelInvite(row.id).then((response) => {
            this.tableLoading = false
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });
            this.getPage(this.page);
          })
          .catch(() => {
            this.tableLoading = false
          });
        })
        .catch((err) => {});
    },
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 0px;
  padding-bottom: 20px;
}
</style>
