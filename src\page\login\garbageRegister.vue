<template>
  <div class="register">
    <div class="register-title flex flex-items-center">
      <i class="title-text"></i>
    </div>
    <div class="register-body">
      <div class="content">
        <div class="title">注册账号</div>
        <div class="registerForm">
          <avue-form :option="option" v-model="form" @submit="submit" ref="form">
            <template slot="menuForm">
              <div class="btns" style="padding:20px 100px 0px;">
                <el-button icon="el-icon-user" style="width:100%;" type="primary" size="medium"
                  @click="$refs.form.submit()">注 册</el-button>
              </div>
            </template>
            <template slot="contactPhone">
              <el-input size="small" v-model="form.contactPhone" placeholder="请输入 管理员手机号码" maxlength="11">
                <template slot="append">
                  <span @click="skipPopup" style="color: #66b1ff; cursor: pointer"
                    :class="[{ display: msgKey }]">{{ msgText }}</span>
                </template>
              </el-input>
            </template>
          </avue-form>
          <div class="back-to-login">
            <span @click="$router.push({ path: '/login' })">已有账号，立即登录</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 图形验证码 -->
    <div class="popup" v-show="popupShow">
      <div class="box">
        <div class="popup_title">请输入验证码并确认</div>
        <div class="iptBox">
          <span class="iptDesc">验证码：</span>
          <el-input v-model="imgCode" class="elInput" placeholder="请输入验证码，不区分大小写" clearable></el-input>
        </div>

        <div id="v_container" @click="btncode"></div>
        <div class="instructions">看不清？点击图片换一张</div>
        <div class="btnBox">
          <el-button @click="popupShow = false">取消</el-button>
          <el-button type="primary" @click="next">确认</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { isMobileNumber, mobileReg } from "@/util/validate";
import { createAssetsMatchCompanyInfoZC } from "@/api/chain/companyauth.js";
import { sendCode } from "@/api/upms/phone";
import { GVerify } from "@//util/GVerify";
import { encryptDes, decryptDes } from '@/util/des.js'
const MSGINIT = "发送验证码",
  MSGSCUCCESS = "${time}秒后可重发",
  MSGTIME = 60;
export default {
  name: "register",
  data() {
    return {
      form: {},
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 140,
        column: [
          {
            label: "企业名称",
            prop: "companyName",
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入公司名称",
                trigger: "blur",
              },
              { pattern: /\s*\S+?/, message: '请输入非空字符', trigger: 'blur' },
              {
                max: 20,
                message: "长度在不能超过20个字符",
              },
            ],
          },

          {
            label: "统一社会信用代码",
            prop: "companyNo",
            span: 24,
            rules: [
              {
                max: 50,
                message: "长度在不能超过50个字符",
              },
            ],
          },
          {
            label: "注册地址",
            prop: "registerAddress",
            span: 24,
            rules: [
              {
                max: 100,
                message: "长度在不能超过100个字符",
              },
            ],
          },
          {
            label: "营业执照",
            prop: "companyUrl",
            span: 12,
            row: true,
            type: "upload",
            listType: "picture-img",
            // imgWidth:300,
            // imgHigh:150,
            action: "/chain/common/upload?fileType=image&dir=register/",
            propsHttp: {
              url: "link",
            },
            loadText: "图上上传中，请稍等",
          },
          {
            label: "身份证国徽面",
            prop: "idCardPositiveUrl",
            span: 12,
            type: "upload",
            listType: "picture-img",
            // imgWidth:300,
            // imgHigh:150,
            action: "/chain/common/upload?fileType=image&dir=register/",
            propsHttp: {
              url: "link",
            },
            loadText: "图上上传中，请稍等",
          },
          {
            label: "身份证人像面",
            prop: "idCardNegativeUrl",
            span: 12,
            type: "upload",
            listType: "picture-img",
            // imgWidth:300,
            // imgHigh:150,
            action: "/chain/common/upload?fileType=image&dir=register/",
            propsHttp: {
              url: "link",
            },
            loadText: "图上上传中，请稍等",
          },
          {
            label: "身份证号码",
            prop: "idNo",
            span: 24,
          },
          {
            label: "管理员姓名",
            prop: "contactName",
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入管理员姓名",
                trigger: "blur",
              },
              { pattern: /\s*\S+?/, message: '请输入非空字符', trigger: 'blur' },
              {
                max: 10,
                message: "长度在不能超过10个字符",
              },
            ],
          },
          {
            label: "管理员手机号码",
            prop: "contactPhone",
            span: 24,
            maxlength: 11,
            rules: [
              {
                required: true,
                message: "请输入管理员手机号码",
                trigger: "blur",
              },
              {
                validator: isMobileNumber,
                trigger: "blur",
              },
            ],
          },
          {
            label: "验证码",
            prop: "code",
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入验证码",
                trigger: "blur",
              },
            ],
          },

        ],
      },
      msgText: MSGINIT,
      msgTime: MSGTIME,
      msgKey: false,
      time: null, //定时器
      verifyCode: "",
      imgData: "", //图片验证码
      imgCode: "",
      popupShow: false,
    };
  },
  props: {},
  created() {

  },
  mounted() {

  },
  methods: {
    skipPopup() {
      if (this.msgKey) return;
      this.$refs.form.validateField('companyName')
      this.$refs.form.validateField('contactName')
      this.$refs.form.validateField('contactPhone')
      if (!this.form.companyName) {
        this.$message.error('请检查公司名称');
        return
      }
      if (!this.form.contactName ) {
        this.$message.error('请检查管理员姓名');
        return
      }
      //手机号验证
      if (!this.form.contactPhone ) {
        this.$message.error('请检查手机号');
        return
      }
      // 图片验证码
      this.verifyCode = new GVerify("v_container");
      this.imgData = this.verifyCode.options.code;
      this.popupShow = true
    },
    // 切换图片验证码
    btncode() {
      this.imgData = this.verifyCode.options.code.substring(
        this.verifyCode.options.code.length - 4
      );
    },
    next() {

      var imgCode = this.toLower(this.imgCode)
      var imgData = this.toLower(this.imgData)
      console.log(imgCode, imgData)
      if (imgCode != imgData) {
        this.$message.error('图形验证码错误,请重新输入');
        return
      }
      this.popupShow = false
      this.imgCode = ""
      this.getCode()
    },
    // 字母转小写
    toLower(str) {
      var arr = ''
      for (var i = 0; i < str.length; i++) {
        arr = arr + str[i].toLowerCase();
      }
      return arr
    },
    getCode() {
      if (this.msgKey) return;
      this.$refs.form.validateField('contactPhone')
      //手机号验证
      if (!(this.form.contactPhone && mobileReg.test(this.form.contactPhone))) {
        return false
      }
      var key = Math.floor(Date.now() / 3000000) + 168;
      var mobile = encryptDes(this.form.contactPhone, key)
      var deviceNewNo = encryptDes("AB288758-C1D0-1089-B513-YILU98DB5217", key)
      let param = {
        mobile: mobile,
        roleType: 7,
        deviceNewNo: deviceNewNo,
      };
      sendCode(param).then((res) => {
        this.$message.success("验证码发送成功");
        this.msgText = MSGSCUCCESS.replace("${time}", this.msgTime);
        this.msgKey = true;
        this.time = setInterval(() => {
          this.msgTime--;
          this.msgText = MSGSCUCCESS.replace("${time}", this.msgTime);
          if (this.msgTime == 0) {
            this.msgTime = MSGTIME;
            this.msgText = MSGINIT;
            this.msgKey = false;
            clearInterval(this.time);
          }
        }, 1000);
      });
    },
    submit(form, done) {
      console.log(form);
      form.companyType = 1
      createAssetsMatchCompanyInfoZC(form).then(response => {
        this.$message({
          showClose: true,
          message: '注册成功',
          type: 'success'
        })
        done()
        setTimeout(() => {
          this.$router.push({ path: '/login' })
        }, 1000)
      }).catch(() => {
        done()
      })
    },
  }
};
</script>
<style lang="less" scoped>
.register {
  //已有账号
  overflow-y: auto;

  .back-to-login {
    height: 14px;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: rgba(13, 127, 239, 1);
    cursor: pointer;
    text-decoration: underline;
  }

  .back-to-login span:hover {
    opacity: 0.7;
  }

  width: 100%;
  height: 100%;
  background: #fff;
  text-align: center;
  position: relative;

  .register-title {
    width: 100%;
    height: 80px;
    line-height: 80px;
    background: #194180;
    overflow: hidden;

    i.title-text {
      margin-left: 120px;
      width: 231px;
      height: 41px;
      background-image: url('../../static/login/logo2.png');
      background-size: cover;
      background-position: center;
      display: block;
    }
  }

  .register-body {
    background-image: url('../../static/login/register_bg.png');
    background-size: cover;
    background-position: center;
    width: 100%;
    text-align: center;
    padding: 38px 0;

    .content {
      width: 1014px;
      margin: 0px auto;
      background-color: #fff;
      padding-top: 15px;
      padding-bottom: 20px;

      .title {
        padding-left: 10px;
        text-align: left;
        height: 18px;
        line-height: 18px;
        font-size: 18px;
        color: #000;
        border-left: 6px solid #1876e9;
        margin-left: 15px;
        margin-bottom: 46px;
      }

      .registerForm {
        width: 700px;
        margin: 0 auto;
      }
    }
  }
}

.popup {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .6);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  font-size: 16px;
  color: #353347;

  .box {
    width: 50%;
    height: 50%;
    background: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -15% 0 0 -25%;
    padding: 0 20px;

    .popup_title {
      width: 100%;
      font-weight: 600;
      line-height: 120px;
      text-align: center;
    }

    .iptBox {
      display: flex;
      justify-content: left;
      align-items: center;

      .iptDesc {
        width: 120px;
      }
    }
  }

}

#v_container {
  width: 160px;
  height: 60px;
  margin: 12px auto;

}

.instructions {
  font-size: 10px;
  color: #a9a6a6;
  text-align: center;
}

.btnBox {
  display: flex;
  justify-content: right;
  align-items: center;
  margin-top: 30px;
}

.elInput {
  width: 50%;
}
</style>
