export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  addBtn: false,
  viewBtn: true,
  menuWidth:120,
  searchMenuSpan: 6,
  column: [
    {
      label: "收款企业",
      prop: "platformBranchId",
      sortable: "custom",
      type: 'select',   // 下拉选择
      search: true,
      props: {
        label: 'nsrmc',
        value: 'id'
      },
      dicUrl: '/chain/platformbranch/getList',
      formatter: (val) => {
        return val.platformBranchNsrmc
      },
      minWidth:180,
      overHidden:true,
    },
    {
      label: "收款银行",
      prop: "platformBranchBankAccountName",
      sortable: 'custom',
      minWidth:120,
      overHidden:true,
    },
    {
      label: "收款账号",
      prop: "platformBranchBankAccount",
      sortable: 'custom',
      minWidth:120,
      overHidden:true,
    },
    {
      label: "项目名称",
      prop: "projectName",
      sortable: 'custom',
      search:true,
      minWidth:160,
      overHidden:true,
    },
    // {
    //   label: "企业名称",
    //   prop: "companyName",
    //   sortable: 'custom',
    //   search:true,
    // },
    {
      label: "充值总额",
      prop: "rechargeTotalAmount",
      sortable: 'custom',
      minWidth:96,
      overHidden:true,
    },
    {
      label: "提现总额",
      prop: "withdrawTotalAmount",
      sortable: 'custom',
      minWidth:96,
      overHidden:true,
    },
    {
      label: "付款金额",
      prop: "paymentTotalAmount",
      sortable: 'custom',
      minWidth:96,
      overHidden:true,
    },
    {
      label: "手续费",
      prop: "totalFee",
      sortable: 'custom',
      minWidth:92,
      overHidden:true,
    },
    {
      label: "余额",
      prop: "balance",
      sortable: 'custom',
      minWidth:80,
      overHidden:true,
    },
    {
      label: "充值次数",
      prop: "rechargeCount",
      sortable: 'custom',
      minWidth:96,
      overHidden:true,
    },
    {
      label: "提现次数",
      prop: "withdrawCount",
      sortable: 'custom',
      minWidth:96,
      overHidden:true,
    },
    {
      label: "已付款运单数",
      prop: "payWaybillCount",
      sortable: 'custom',
      minWidth:118,
      overHidden:true,
    },
    // {
    //   label: "企业已结算运单数",
    //   prop: "settleWaybillCount",
    //   sortable: 'custom',
    // },
  ],
};
