<template>
  <div class="projectList">
    <div :class="{'isShowProjectNum':isShowProjectNum}" class="avue-tags-box">
      <ul class="flex flex-wrap">
        <li v-for="item in projectList" :key="item[defaultProp.value]" :class="projectInfoId == item[defaultProp.value] ? 'active' : ''"
          @click="changeProject(item)">
            {{ item[defaultProp.label] }}
              <div v-if="isShowProjectNum">{{text}}{{item[defaultProp.cnt]}}{{ unit }}</div>
          </li>
      </ul>
      <el-select v-model="projectInfoId" @change="changeActive" size="mini" filterable placeholder="请选择"
        style="position: absolute;right: 0;top: 0;width:206px">
        <el-option v-for="item in projectList" :key="item[defaultProp.value]" :label="item[defaultProp.label]" :value="item[defaultProp.value]">
          <span style="float: left">{{ item[defaultProp.label] }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">{{ item[defaultProp.cnt] }}</span>
        </el-option>
      </el-select>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    //传进来的数据
    projectList: {
      type: Array,
      default: () => {
        return []
      }
    },
    //
    active: "",
    isShowProjectNum:{
      type:Boolean,
      default:()=>{
        return true
      }
    },
    text: {//数量
      type: String,
      default: () => {
        return "运单数"
      }
    },
    defaultProp: {
      type: Object,
      default: () => {
        return {
          label:"projectName",
          value:"id",
          cnt:"billCount",
        }
      }
    },
    unit: { //单位
      type: String,
      default: () => {
        return ""
      }
    },
  },
  components: {
  },
  data () {
    return {
      projectInfoId: "",
    }
  },
  created () {
    this.projectInfoId = this.active

  },
  mounted () {
  },
  computed: {
  },
  watch:{
    active: {
      handler() {
        console.log(this.active);
        this.projectInfoId = this.active;
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //选择项目
    changeProject (item) {
      if(this.projectInfoId == item[this.defaultProp.value]){
        return false
      }
      this.projectInfoId = item[this.defaultProp.value]
      this.$emit('changeProject', this.projectInfoId)
    },
    changeActive (val) {
      this.$emit('changeProject', this.projectInfoId)
    },
  },

};
</script>

<style lang="scss" scoped>
.projectList {
  .avue-tags-box {
    height: 28px;
    overflow: hidden;
    padding-right: 206px;
    box-sizing: border-box;
    position: relative;

    ul {

      // justify-content: space-between;
      li {
        box-sizing: border-box;
        text-align: center;
        padding: 4px 10px;
        font-size: 12px;
        border-right: 1px solid #dcdfe6;
        border-top: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
        max-width: 240px;
        &:first-child {
          border-left: 1px solid #dcdfe6;
        }

        // border-radius: 4px;
        flex-grow: 1;
        margin-bottom: 4px;
        cursor: pointer;
        // min-width: 200px;
      }
    }

    .el-select {
      input {
        border-radius: 0;
      }
    }
    &.isShowProjectNum{
      height: 46px;
    /deep/  .el-select input{
        height: 46px;
      }
    }
  }
}
</style>
