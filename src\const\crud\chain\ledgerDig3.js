export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  // index:true,
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  dialogClickModal:false,
  selection:true,
  excelBtn: false,
  printBtn: false,
  addBtn: false,
  viewBtn: false,
  delBtn: false,
  searchSpan:6,
  searchMenuSpan: 6,
  searchLabelWidth:116,
  menuWidth:160,
  defaultSort: {
    prop: "startDatetime",
    order: "descending",
  },
  column: [
    {
      label: "项目",
      prop: "projectName",
      sortable: true,
      search: true,
      order:1,
      width:160,
      overHidden:true,
    },
    {
      label: "台班编号",
      prop: "ledgerNo",
      search:true,
      order:2,
      minWidth:180,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "挖机车主姓名",
      prop: "ownerName",
      search: true,
      width:96,
      overHidden:true,
    },
    {
      label: "挖机车主手机号",
      prop: "ownerMobile",
      search: true,
      width:110,
      overHidden:true,
    },
    {
      label: "商务备注",
      prop: "businessAuditRemark",
      sortable: true,
      width:100,
      overHidden:true,
    },
    {
      label: "挖机员",
      prop: "staffName",
      sortable: true,
      search: true,
      width:90,
      overHidden:true,
    },
    {
      label: "作业类型",
      prop: "ledgerType",
      sortable: true,
      // searchMultiple:true,
      dataType:'string',
      // search: true,
      type: "select", // 下拉选择=
      dicUrl: "/chain/projectinfoext/getCompanyLedgerType",
      width:100,
      overHidden:true,
    },
    {
      label: "状态",
      prop: "ledgerStatus",
      sortable: true,
      search: true,
      type: "select", // 下拉选择
      searchValue:'2',
      dicData: [
        // {
        //   label: "项目负责人未审核",
        //   value: "1",
        // },
        {
          label: "项目负责人审核通过",
          value: "2",
        },
        {
          label: "核算员审核通过",
          value: "5",
        },
        {
          label: "核算员驳回",
          value: "6",
        },
        {
          label: "核算员删除",
          value: "7",
        },
        {
          label: "商务审核通过",
          value: "9",
        },
        {
          label: "商务驳回",
          value: "10",
        },
      ],
      width:90,
      overHidden:true,
    },
    {
      label: "挖机班次",
      prop: "inShiftType",
      sortable: true,
      search: true,
      type:"select",
      dicUrl:"/chain/projectinfo/getShiftOfProject",
      dicFormatter: (res) => {
        return res.data.map((item)=>{
          return {
            label:item,
            value:item,
          }
        })
      },
      formatter:(value)=>{
        return value.inShiftTypeName
      },
      width:110,
      overHidden:true,
      searchFilterable:true,
    },
    {
      label: "挖机类型",
      prop: "inType",
      sortable: true,
      search: true,
      type: "select", // 下拉选择
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: "/chain/systemdictionaryitem/listDictionaryItem?dictionary=in_type",
      width:100,
      overHidden:true,
    },
    {
      label: "机械型号",
      prop: "machineCode",
      width:120,
      overHidden:true,
      search: true,
    },
    {
      label: "完成量",
      prop: "scheduleWork",
      width:80,
      overHidden:true,
    },
    {
      label: "单位",
      prop: "unitWork",
      type: "select", // 下拉选择
      dicData: [
        {
          label: "车",
          value: "1",
        },
        {
          label: "方",
          value: "2",
        },
      ],
      width:80,
      overHidden:true,
    },
    {
      label: "地块",
      prop: "landName",
      search:true,
      width:120,
      overHidden:true,
    },
    {
      label: "作业地点",
      prop: "address",
      width:120,
      overHidden:true,
    },
    {
      label: "开始时间",
      prop: "startDatetime",
      sortable: true,
      width:140,
      overHidden:true,
    },
    {
      label: "结束时间",
      prop: "endDatetime",
      sortable: true,
      width:140,
      overHidden:true,
    },
    {
      label: "时长",
      prop: "workTime",
      sortable: true,
      width:80,
      overHidden:true,
    },
    {
      label: "转换时长",
      prop: "workHour",
      width:80,
      overHidden:true,
    },
    {
      label: "是否删除",
      prop: "isDel",
      sortable: true,
      search: true,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "是",
          value: "1",
        },
        {
          label: "否",
          value: "0",
        },
      ],
      width:94,
      overHidden:true,
    },
    {
      label: "是否签证",
      prop: "isVisa",
      search: false,
      type: "select", // 下拉选择
      dicData: [
        {
          label: "是",
          value: "1",
        },
        {
          label: "否",
          value: "0",
        },
      ],
      hide:true,
      showColumn:false,
      width:80,
      overHidden:true,
    },
    {
      label: "备注",
      prop: "ledgerRemark",
      width:120,
      overHidden:true,
      search: true,
    },
    {
      label: "开始码表拍照",
      prop: "beginClockUrl",
      hide:true,
      showColumn:false,
      type:'upload',
      dataType: 'string',
      width:100,
      overHidden:true,
    },
    {
      label: "开始码表",
      prop: "beginClock",
      hide:true,
      showColumn:false,
      width:80,
      overHidden:true,
    },
    {
      label: "结束码表拍照",
      prop: "endClockUrl",
      hide:true,
      showColumn:false,
      type:'upload',
      dataType: 'string',
      width:100,
      overHidden:true,
    },
    {
      label: "结束码表",
      prop: "endClock",
      hide:true,
      showColumn:false,
      width:80,
      overHidden:true,
    },
    {
      label: "加油升数",
      prop: "refuelingLitres",
      hide:true,
      showColumn:false,
      width:80,
      overHidden:true,
    },
    {
      label: "费用(元)",
      prop: "cost",
      hide:true,
      showColumn:false,
      width:80,
      overHidden:true,
    },
    {
      label: "是否餐补",
      prop: "isSubsidizedMeals",
      hide:true,
      showColumn:false,
      width:80,
      overHidden:true,
    },
    {
      label: "加班时长(小时)",
      prop: "overtimeHours",
      hide:true,
      showColumn:false,
      width:110,
      overHidden:true,
    },
    {
      label: "项目审核人",
      prop: "confirmStaffName",
      width:90,
      overHidden:true,
    },
    {
      label: "项目审核时间",
      prop: "confirmDatetime",
      sortable: true,
      width:140,
      overHidden:true,
    },
    {
      label: "核算审核人",
      prop: "checkStaffName",
      sortable: true,
      width:110,
      overHidden:true,
    },
    {
      label: "核算审核时间",
      prop: "checkDatetime",
      width:140,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "创建日期",
      prop: "searchDate",
      type:'date',
      valueFormat: 'yyyy-MM-dd',
      searchRange:true,
      search:true,
      showColumn:false,
      hide:true,
      display:false,
    },
    {
      label: "班次日期",
      prop: "inShiftTime",
      type:'date',
      sortable: 'custom',
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd',
      minWidth:140,
      overHidden:true,
      hide:true,
      showColumn:false,
    },
    {
      label: "PC后台备注",
      prop: "pcLedgerRemark",
      width:120,
      overHidden:true,
      sortable: 'custom',
      search: true,
    },
    {
      label: "审批备注",
      prop: "pcAuditRemark",
      width:120,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "台班价",
      prop: "isSet",
      type: "select", // 下拉选择
      search:true,
      dicData: [
        {
          label: '未设置',
          value: '0'
        },
        {
          label: '已设置',
          value: '1'
        },

     ],
      minWidth:96,
      overHidden:true,
      sortable: 'custom',
      formatter:(val)=>{
        return val.preparePrice
      }
    },
  ],
};
