<template>
  <div class="setPayee">
    <el-dialog width="500px"
               title="设置承运人"
               center
               :visible.sync="visible"
               :before-close="cancelModal"
               :close-on-click-modal="false">
      <el-button type="primary" size="small" style="position: absolute;right: 30px;top: 67px;z-index: 2;" @click="selectDialog = true">常用收款人</el-button>
      <avue-form ref="editForm"
                 v-model="editForm"
                 :option="editOption">
        <template slot-scope="{ disabled, size }"
                  slot="mobile">
          <span>{{ editForm.mobile }}</span>
        </template>
        <template slot-scope="{ disabled, size }"
                  slot="bindingBankNo">
          <span>{{ editForm.bindingBankNo }}</span>
        </template>
        <template slot-scope="{ disabled, size }"
                  slot="bindingBankName">
          <span>{{ editForm.bindingBankName }}</span>
        </template>
        <template slot-scope="{ disabled, size }"
                  slot="status">
          <span>{{ editForm.status }}</span>
        </template>
      </avue-form>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="cancelModal">取消</el-button>
        <el-button type="primary"
                   :loading="loading"
                   :disabled="editForm.bindingBankName == '' || editForm.bindingBankNo == ''"
                   @click="submitPayee">提交</el-button>
      </span>
    </el-dialog>
    <selectCommonPayees v-if="selectDialog" @submit="selectComplete" :visible.sync="selectDialog"></selectCommonPayees>
  </div>
</template>

<script>
import selectCommonPayees from '@/views/chain/waybillPreset/selectCommonPayees'

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },

  },
  components:{
    selectCommonPayees
  },
  data () {
    return {
      tableLoading: false,
      editForm: {},
      editOption: {
        column: [
          {
            label: "收款人",
            prop: "payeeId",
            type: "select",
            span: 24,
            remote: true,
            props: {
              label: "payeeId",
              value: "payeeId",
            },
            typeformat (item, label, value) {
              return `${item["carrierName"]}-${item["mobile"]}`;
            },
            dicFormatter: (res) => {
              this.payeeDic = res.data;
              return res.data || [];
            },
            dicUrl: `/chain/companywaybill/selectCarrierListByNameOrMobile?param={{key}}`,
            // allowCreate:true,
            filterable: true,
            change: ({ value }) => {
              console.log(value);
              if (value) {
                let tmp = this.payeeDic.filter((v) => v.payeeId == value)[0];
                console.log(tmp);
                if (tmp) {
                  this.editForm.mobile = tmp.mobile;
                  this.editForm.bindingBankNo = tmp.bindingBankNo;
                  this.editForm.bindingBankName = tmp.bindingBankName;
                  this.editForm.status = tmp.status;
                }
              }
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "手机号码",
            prop: "mobile",
            span: 24,
          },
          {
            label: "收款卡号",
            prop: "bindingBankNo",
            span: 24,
          },
          {
            label: "收款银行",
            prop: "bindingBankName",
            span: 24,
          },
          {
            label: "是否签约合同",
            prop: "status",
            span: 24,
          },
        ],
        labelWidth: 120,
        submitBtn: false,
        position: "left",
        emptyBtn: false,
        cancelBtn: true,
      },
      loading: false,
      selectDialog:false,
    };
  },
  created () { },
  mounted: function () { },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    changLoading () {
      this.loading = false
    },
    submitPayee () {
      this.loading = true
      this.$emit("submit", this.editForm, this.changLoading);
    },
    selectComplete(arr){
      if(arr&&arr.length>0){
        let tmp = arr[0]
        let form = {
          payeeId:tmp.systemUserId,
          carrierName:tmp.payeeName,
          mobile:tmp.payeeMobile,
          bindingBankNo:tmp.bindingBankNo,
          bindingBankName:tmp.bindingBankName,
          status:tmp.isContract,
        }
        this.$refs.editForm.updateDic('payeeId',[form])
        this.payeeDic = [form]
        setTimeout(()=>{
          this.editForm.payeeId = form.payeeId
        },100)
      }
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
