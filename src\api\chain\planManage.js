import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/matchProjectPlan/getPage',
        method: 'get',
        params: query
    })
}
//开启计划
export function openMatch(data) {
    return request({
        url: '/chain/matchProjectPlan/openMatch',
        method: 'post',
        data
    })
}
// 取消计划
export function cancelPlan(data) {
    return request({
        url: '/chain/matchProjectPlan/cancelPlan',
        method: 'post',
        data
    })
}
// 根据计划id查看单个计划
export function getMatchProjectPlanDetailById(data) {
    return request({
        url: '/chain/matchProjectPlan/getMatchProjectPlanDetailById',
        method: 'post',
        data
    })
}
// 根据计划id修改单个计划
export function editPlan(data) {
    return request({
        url: '/chain/matchProjectPlan/editPlan',
        method: 'post',
        data
    })
}
// 确认匹配列表
export function confirmMatchPage(params) {
    return request({
        url: '/chain/matchgarbageprojectplan/getPage',
        method: 'get',
        params
    })
}
// 批量确认
export function batchConfirm(data) {
    return request({
        url: '/chain/matchgarbageprojectplan/batchConfirm',
        method: 'post',
        data
    })
}
// 复制新增添加一条计划
export function copyPlanByPlanId(data) {
    return request({
        url: '/chain/matchProjectPlan/copyPlanByPlanId',
        method: 'post',
        data
    })
}
// 根据计划id获取进行中的订单数
export function getUnderwayOrderCount(data) {
    return request({
        url: '/chain/matchProjectPlan/getUnderwayOrderCount',
        method: 'post',
        data
    })
}
// 根据计划id查询订单进度
export function getProgress(data) {
    return request({
        url: '/chain/matchProjectPlan/getProgress',
        method: 'post',
        data
    })
}
//根据计划查询是否存在可操作的队列
export function getCountByPlanId(data) {
  return request({
      url: '/chain/matchgarbageprojectplan/getCountByPlanId',
      method: 'post',
      data
  })
}
