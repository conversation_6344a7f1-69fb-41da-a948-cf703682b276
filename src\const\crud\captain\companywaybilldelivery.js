export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  delBtn: false,
  addBtn: false,
  editBtn: false,
  menu: false,
  searchMenuSpan: 6,
  searchLabelWidth:100,
  selection:true,
  selectable:(row)=>{
    return row.isRight==1
  },
  column: [
    {
      label: "交单编号",
      prop: "acceptNo",
      search: true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "运单编号",
      prop: "waybillNo",
      minWidth:160,
      overHidden:true,
    },
    {
      label: "施工单位",
      prop: "companyAuthName",
      minWidth:180,
      overHidden:true,
    },
    {
      label: "项目名称",
      prop: "projectName",
      search: true,
      minWidth:180,
      overHidden:true,
    },
    {
      label: "任务车队长",
      prop: "fleetName",
      minWidth:90,
      overHidden:true,
    },
    {
      label: "运输类型",
      prop: "tpMode",
      type: "select", // 下拉选择
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode',
      minWidth:80,
      overHidden:true,
    },
    {
      label: "运单出场时间",
      prop: "goDatetime",
      minWidth:140,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "司机",
      prop: "driverName",
      search:true,
      minWidth:80,
      overHidden:true,
    },
    {
      label: "车牌",
      prop: "truckCode",
      minWidth:90,
      overHidden:true,
    },
    {
      label: "交单时间",
      prop: "createDatetime",
      minWidth:140,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "收单人",
      prop: "acceptName",
      minWidth:80,
      overHidden:true,
    },
    {
      label: "收款状态",
      prop: "deliveryStatus",
      type: "select",
      search:true,
      dicData: [
        {
          label: "未付款",
          value: "1",
        },
        {
          label: "已付款",
          value: "2",
        },
      ],
      minWidth:80,
      overHidden:true,
    },
    {
      label: "收款确认时间",
      prop: "payDatetime",
      minWidth:140,
      overHidden:true,
      sortable: "custom",
    },
    {
      label: "是否交单",
      prop: "isRight",
      type: "select",
      search:true,
      hide: true,
      showColumn: false,
      dicData: [
        {
          label: "否",
          value: "0",
        },
        {
          label: "是",
          value: "1",
        },
      ],
      minWidth:80,
      overHidden:true,
    },
    {
      label: "运单出场时间",
      prop: "searchDate",
      type: "date",
      searchRange: true,
      search: true,
      valueFormat: "yyyy-MM-dd",
      hide: true,
      showColumn: false,
    },
  ],
};
