export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  addBtn: false,
  viewBtn: true,
  menuWidth: 80,
  searchMenuSpan: 6,
  searchLabelWidth:86,
  defaultSort: {
    prop: "applyDatetime",
    order: "ascending",
  },
  column: [
    {
      label: "类型",
      prop: "applyType",
      sortable: "custom",
      type: "select", // 下拉选择
      search: true,
      dicData: [
        {
          value: "1",
          label: "充值",
        },
        {
          value: "2",
          label: "提现",
        },
      ],
      width:70,
      overHidden:true,
    },
    {
      label: "收款企业",
      prop: "platformBranchNsrmc",
      sortable: "custom",
      search: true,
      formatter: (val) => {
        return val.applyType==1?val.platformBranchNsrmc:val.companyName
      },
      minWidth:160,
      overHidden:true,
    },
    {
      label: "收款银行",
      prop: "platformBranchBankAccountName",
      sortable: true,
      formatter: (val) => {
        return val.applyType==1?val.platformBranchBankAccountName:val.withdrawBankAccountName
      },
      minWidth:130,
      overHidden:true,
    },
    {
      label: "收款账户",
      prop: "platformBranchBankAccount",
      sortable: true,
      formatter: (val) => {
        return val.applyType==1?val.platformBranchBankAccount:val.withdrawBankAccount
      },
      minWidth:100,
      overHidden:true,
    },
    {
      label: "项目名称",
      prop: "projectName",
      sortable: "custom",
      search: true,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "金额",
      prop: "amount",
      sortable: "custom",
      search:true,
      overHidden:true,
    },
    {
      label: "手续费",
      prop: "platformFee",
      sortable: "custom",
      minWidth:86,
      overHidden:true,
    },
    {
      label: "银行流水号",
      prop: "frontLogNo",
      sortable: "custom",
      search:true,
      minWidth:106,
      overHidden:true,
    },
    {
      label: "银行凭证",
      prop: "frontLogPic",
      sortable: "custom",
      type: "upload",
      minWidth:96,
      overHidden:true,
    },
    {
      label: "状态",
      prop: "applyStatus",
      sortable: "custom",
      search:true,
      type:'select',
      searchValue: '1',
      dicData: [
        {
          value: "1",
          label: "确认中",
        },
        {
          value: "2",
          label: "已确认",
        },
        {
          value: "3",
          label: "已拒绝",
        },
      ],
      minWidth:80,
      overHidden:true,
    },
    {
      label: "日期",
      prop: "searchDate",
      sortable: true,
      type:'date',
      search:true,
      searchRange:true,
      hide: true,
      showColumn:false,
      valueFormat: 'yyyy-MM-dd',
    },
    {
      label: "充值时间",
      prop: "applyDatetime",
      sortable: true,
      type:'datetime',
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:94,
      overHidden:true,
    },
    {
      label: "充值人",
      prop: "applyName",
      sortable: "custom",
      minWidth:82,
      overHidden:true,
    },
    {
      label: "确认时间",
      prop: "auditDatetime",
      sortable: "custom",
      minWidth:94,
      overHidden:true,
    },
    {
      label: "确认人",
      prop: "auditName",
      sortable: "custom",
      minWidth:82,
      overHidden:true,
    },

  ],
};
