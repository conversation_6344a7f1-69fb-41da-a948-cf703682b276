import request from '@/router/axios'

export function getPhaseListByProjectInfoId(query) {
    return request({
        url: '/chain/projectinfobiphase/getPhaseListByProjectInfoId',
        method: 'get',
        params: query
    })
}

export function getBiManageVoByProjectInfoBiPhaseId(query) {
    return request({
        url: '/chain/projectinfobiphase/getBiManageVoByProjectInfoBiPhaseId',
        method: 'get',
        params: query
    })
}

export function getTruckTpModeTitle(query) {
    return request({
        url: '/chain/projectinfobiphase/getTruckTpModeTitle',
        method: 'get',
        params: query
    })
}

export function transportUnitCost(query) {
    return request({
        url: '/chain/projectinfobiphase/transportUnitCost',
        method: 'get',
        params: query
    })
}

export function machineCost(query) {
    return request({
        url: '/chain/projectinfobiphase/machineCost',
        method: 'get',
        params: query
    })
}

export function garbageCost(query) {
    return request({
        url: '/chain/projectinfobiphase/garbageCost',
        method: 'get',
        params: query
    })
}

export function getSoilTypeStatistic(query) {
    return request({
        url: '/chain/projectinfobiphase/getSoilTypeStatistic',
        method: 'get',
        params: query
    })
}

export function getTpModeStatistic(query) {
    return request({
        url: '/chain/projectinfobiphase/getTpModeStatistic',
        method: 'get',
        params: query
    })
}

export function getMachineStatistic(id) {
    return request({
        url: `/chain/projectinfobiphase/getMachineStatistic/${id}`,
        method: 'get'
    })
}

export function getGarbageStatistic(id) {
    return request({
        url: `/chain/projectinfobiphase/getGarbageStatistic/${id}`,
        method: 'get'
    })
}

export function getPlanAchievementStatistic(id) {
    return request({
        url: `/chain/projectinfobiphase/getPlanAchievementStatistic/${id}`,
        method: 'get'
    })
}

