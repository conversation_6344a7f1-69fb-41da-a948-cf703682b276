<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%"
               :title="isDetail?'查看计划':'设置出土计划'"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <basic-container>
        <el-descriptions title="土石方信息">
          <el-descriptions-item label="项目名称">{{info.projectName}}</el-descriptions-item>
          <el-descriptions-item label="土质">{{info.listSoilType}}</el-descriptions-item>
          <el-descriptions-item label="工程地址">{{info.projectAddress}}</el-descriptions-item>
        </el-descriptions>
        <el-divider></el-divider>
        <el-button icon="el-icon-plus"
                   style="margin-bottom:10px"
                   v-if="!isDetail"
                   @click="handleTabsAdd"
                   type="primary"
                   size="small">
          新增计划
        </el-button>
        <el-tabs v-model="editableTabsValue"
                 type="card"
                 @tab-remove="del">
          <el-tab-pane :key="item.name"
                       v-for="(item, index) in editableTabs"
                       :label="item.title"
                       :closable="item.closable"
                       :name="item.name">
            <template #label>
              <span @dblclick.stop="editTab(item, index)">{{item.title}}</span>
            </template>
            <avue-form :option="getOption(item,index)"
                       v-model="item.form"
                       ref="form">
              <template slot="outSoilCube">
                <avue-input-number :precision="2"
                                   :disabled="item.isDisabled||isDetail"
                                   placeholder="请输入 出土方量"
                                   :controls="false"
                                   :min-rows="0"
                                   :max-rows="999999999.99"
                                   v-model="item.form.outSoilCube"></avue-input-number>
                <span class="formValueTip">m³</span>
              </template>
              <template slot="customPrice">
                <!-- 勾选了土质 -->
                <span v-if="item.form&&item.form.goSoilType&&item.form.goSoilType.length>0&&item.form.matchSoilSetDtoList&&item.form.matchSoilSetDtoList.length>0">
                  <div v-for="(item2,index2) in item.form.matchSoilSetDtoList"
                       :key="index2"
                       class="flex flex-items-center;"
                       style="margin-bottom:10px">
                    <span style="display: inline-block;width: 70px;">{{item2.soil}}</span>
                    <avue-input-number style="flex:1;margin:0px 10px;max-width:200px"
                                       :precision="2"
                                       :disabled="item.isDisabled||isDetail"
                                       size="small"
                                       :controls="false"
                                       :min-rows="0"
                                       :max-rows="999999999.99"
                                       v-model="item2.unitPrice"></avue-input-number>
                    <el-radio-group v-model="item2.weightUnit"
                                    :disabled="item.isDisabled||isDetail">
                      <el-radio-button label="车"></el-radio-button>
                      <el-radio-button label="方"></el-radio-button>
                      <el-radio-button label="吨"></el-radio-button>
                    </el-radio-group>
                  </div>
                </span>
                <span v-else></span>

              </template>
            </avue-form>
          </el-tab-pane>
        </el-tabs>
      </basic-container>
      <div class="btns"
           v-if="!isDetail">
        <el-button @click="cancelModal"
                   icon="el-icon-close"
                   size="small">
          取 消
        </el-button>
        <el-button @click="submit"
                   :loading="btnLoading"
                   icon="el-icon-checked"
                   type="primary"
                   size="small">
          确 定
        </el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { saveOrUpdate, getMatchProjectPlanDetail } from "@/api/chain/excavationManage";
import { listDictionaryItem } from "@/api/garbage/garbage";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
    isDetail: {
      type: Boolean,
      default: false,
    },
  },
  data () {
    return {
      editableTabsValue: '1',
      editableTabs: [{
        title: '计划1',
        name: '1',
        closable: true,
        form: {
          matchSoilSetDtoList: []
        }
      },],
      tabIndex: 1,
      btnLoading: false,
      soilList: [],
    };
  },
  created () { },
  mounted () {
    this.getSoilList()
    this.getData()
  },
  computed: {

  },
  watch: {

  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    getSoilList () {
      listDictionaryItem({ dictionary: 'match_soil_type' }).then(res => {
        this.soilList = res.data.data
      })
    },
    getOption (item, index) {
      return {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 120,
        disabled: item.isDisabled || this.isDetail,
        //弹窗区分
        column: [
          {
            label: "出土方量",
            prop: "outSoilCube",
            type: "number",
            formslot: true,
            append: 'm³',
            placeholder: "请输入 出土方量,单位m³",
            controls: false,
            minRows: 0,
            maxRows: 999999999.99,
            precision: 2,
            span: 18,
            row: true,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "土质",
            prop: "goSoilType",
            multiple: true,
            type: 'select',   // 下拉选择
            props: {
              label: "itemName",
              value: "itemValue",
            },
            span: 18,
            row: true,
            dicData: this.soilList,
            filterable: true,  //是否可以搜索
            change: ({ value }) => {
              this.filterSoilData(value, item, index)
            },
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "出土时间段",
            prop: "outSoilTime",
            type: "daterange",
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            span: 18,
            row: true,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
          {
            label: "不区分土质/车",
            prop: "flatPrice",
            type: "number",
            append: '元',
            placeholder: "请输入 价格,单位元",
            controls: false,
            minRows: 0,
            maxRows: 999999999.99,
            precision: 2,
            span: 18,
            row: true,
            rules: [
              {
                required: true,
                message: "请输入价格",
                trigger: "change",
              },
            ],
          },
          {
            label: "",
            prop: "customPrice",
            span: 24,
            row: true,
          },
        ]
      }
    },
    getData () {
      getMatchProjectPlanDetail({ projectInfoId: this.info.id }).then(res => {
        console.log(res, 'res');
        if (res.data.data && res.data.data.length > 0) {
          //已经设置过了
          let arr = []
          this.tabIndex = res.data.data.length
          res.data.data.forEach((item, index) => {
            console.log(item);
            let obj = JSON.parse(JSON.stringify(item))
            obj.title = item.planName
            obj.name = index + 1 + ''
            obj.closable = !obj.id
            obj.isDisabled = !!obj.id
            obj.form = {
              id: obj.id,
              planNo: obj.planNo,
              outSoilCube: obj.outSoilCube,
              goSoilType: obj.matchSoilSettVoList && obj.matchSoilSettVoList.map(item => item.soil),
              outSoilTime: [obj.outSoilTimeStart, obj.outSoilTimeEnd],
              flatPrice: obj.flatPrice,
              matchSoilSetDtoList: obj.matchSoilSettVoList,
            }
            // setTimeout(()=>{
            //   obj.form.matchSoilSetDtoList = obj.matchSoilSettVoList
            // },100)
            arr.push(obj)
          })
          this.editableTabs = arr
          console.log(this.editableTabs);
        }
      })
    },
    filterSoilData (val, item, index) {
      console.log(val);
      console.log(item)
      if (val && val.length > 0) {
        //判断新增还是删除
        let beforeSoil = item.form.matchSoilSetDtoList.map(item3 => item3.soil)
        let diff = [];
        if (beforeSoil && beforeSoil.length > 0) {
          let set = new Set(val);
          diff = beforeSoil.filter((v) => !set.has(v));
          console.log('diff', diff);
        }
        let add = []
        console.log(beforeSoil);
        if (val && val.length > 0) {
          let set = new Set(beforeSoil);
          add = val.filter((v) => !set.has(v));
          console.log('add', add);
        }
        if (diff.length > 0) {
          //删除
          console.log('删除');
          diff.forEach(item2 => {
            let index2 = item.form.matchSoilSetDtoList.findIndex(item3 => item3.soil == item2)
            item.form.matchSoilSetDtoList.splice(index2, 1)
          })
        }
        if (add.length > 0) {
          //新增
          console.log('新增');
          add.forEach(item2 => {
            item.form.matchSoilSetDtoList.push({
              soil: item2,
              unitPrice: "",
              weightUnit: "吨",
            })
          })
        }

      } else if (!item.isDisabled) {
        item.form.matchSoilSetDtoList = []
      }
      // console.log(this.editableTabs);
      // console.log(arr);
      // return arr.length>0
    },
    del (targetName) {
      let tabs = this.editableTabs;
      let activeName = this.editableTabsValue;
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.name;
            }
          }
        });
      }
      this.editableTabsValue = activeName;
      this.editableTabs = tabs.filter(tab => tab.name !== targetName);
    },
    handleTabsAdd () {
      this.$prompt('请输入计划名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: "请输入计划名称",
        inputPattern: /\S/,
        inputErrorMessage: '请输入计划名称'
      }).then(({ value }) => {
        let newTabName = ++this.tabIndex + '';
        this.editableTabs.push({
          title: value,
          name: newTabName,
          closable: true,
          form: {
            matchSoilSetDtoList: []
          },
        },)
        this.editableTabsValue = newTabName;
      }).catch(() => { });
    },
    editTab (item) {
      this.$prompt('请输入计划名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: "请输入计划名称",
        inputValue: item.title,
        inputPattern: /\S/,
        inputErrorMessage: '请输入计划名称'
      }).then(({ value }) => {
        item.title = value
      }).catch(() => { });
    },
    async submit () {
      console.log(this.$refs.form);
      let flag = true;
      let refItems = this.$refs.form;
      if (Array.isArray(refItems)) {
        for (let i = 0; i < refItems.length; i++) {
          await refItems[i].validate((valid, loading) => {
            loading()
            if (!valid) {
              flag = false;
            }
          })
        }
      }
      if (!flag) return;
      console.log('通过了');
      console.log(this.editableTabs);
      let arr = []
      this.editableTabs.forEach((item, index) => {
        let items = JSON.parse(JSON.stringify(item.form))
        items.planName = item.title
        items.outSoilTimeStart = items.outSoilTime[0]
        items.outSoilTimeEnd = items.outSoilTime[1]
        items.projectInfoId = this.info.id
        delete items.outSoilTime
        arr.push(items)
      })
      console.log(arr);
      let param = {
        projectInfoId: this.info.id,
        matchProjectPlanDtoList: arr
      }
      this.btnLoading = true
      saveOrUpdate(param).then(res => {
        this.btnLoading = false
        this.$message.success('操作成功')
        this.$emit("update:visible", false);
      }).catch(() => {
        this.btnLoading = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
.btns {
  position: absolute;
  left: 0px;
  width: 100%;
  bottom: 40px;
  text-align: center;
}
</style>
