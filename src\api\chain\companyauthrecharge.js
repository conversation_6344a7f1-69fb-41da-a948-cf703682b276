import request from "@/router/axios";

//查询企业充值记录
export function getPages(query) {
  return request({
    url: "/chain/companyauthrecharge/page",
    method: "get",
    params: query,
  });
}
//按支付单查询充值记录
export function getRechargePage(query) {
  return request({
    url: "/chain/companyauthrecharge/getRechargePage",
    method: "get",
    params: query,
  });
}
//按支付单查询银行信息
export function getProjectBankInfoByPaymentId(query) {
  return request({
    url: "/chain/companyauthrecharge/getProjectBankInfoByPaymentId",
    method: "get",
    params: query,
  });
}
//根据支付单ID查询点击付款时，验证全部运单必须存在收款人，存在未设置收款人，提示“存在{x}单没有设置收款人
export function checkPaymentExistNotSetPayeeId(query) {
  return request({
    url: "/chain/companyauthrecharge/checkPaymentExistNotSetPayeeId",
    method: "get",
    params: query,
  });
}
//根据支付单ID查询需要付款承运人分组信息
export function getPayeeGroupByPaymentId(query) {
  return request({
    url: "/chain/companypayment/getPayeeGroupByPaymentId",
    method: "get",
    params: query,
  });
}
//根据支付单批量付款承运人
export function batchTrans4047ByPayment(obj) {
  return request({
    url: "/chain/companypayment/batchTrans4047ByPayment",
    method: "post",
    data: obj,
  });
}
//根据凭证ID查询银行交易状态
export function getTransStatusByThirdVoucher(params) {
  return request({
    url: "/chain/companypayment/getTransStatusByThirdVoucher",
    method: "get",
    params,
  });
}
// 支付明细-查询支付信息
export function getBankVoucherPayInfo(params) {
  return request({
    url: "/chain/bankVoucher/getBankVoucherPayInfo",
    method: "get",
    params,
  });
}
// 支付明细-添加付款信息
export function saveBankVoucher(obj) {
  return request({
    url: "/chain/bankVoucher/saveBankVoucher",
    method: "post",
    data: obj,
  });
}
//根据凭证ID查询银行交易状态
export function getTransStatus4047ByPaymentId(params) {
  return request({
    url: "/chain/companypayment/getTransStatus4047ByPaymentId",
    method: "get",
    params,
  });
}
export function recharge(obj) {
  return request({
    url: "/chain/companyauthrecharge",
    method: "post",
    data: obj,
  });
}
//获取当前用户的子账号
export function getBankAccount(query) {
  return request({
    url: "/chain/companyauth/getBankAccount",
    method: "get",
    params: query,
  });
}
//查询子账号余额
export function getSubAccount(query) {
  return request({
    url: "/chain/bankinfo/searchSubAccountBalance",
    method: "get",
    params: query,
  });
}
//账户明细
export function searchSubAccountBalance(query) {
  return request({
    url: "/chain/bankinfo/searchSubAccountBalance",
    method: "get",
    params: query,
  });
}
//项目列表
export function getProjectInfo(query) {
  return request({
    url: "/chain/companyauthrecharge/getProjectInfo",
    method: "get",
    params: query,
  });
}

export function getObj(id) {
  return request({
    url: "/chain/companyauthrecharge/" + id,
    method: "get",
  });
}

export function delObj(id) {
  return request({
    url: "/chain/companyauthrecharge/" + id,
    method: "delete",
  });
}

export function putObj(obj) {
  return request({
    url: "/chain/companyauthrecharge",
    method: "put",
    data: obj,
  });
}
export function getPayeeGroupByPaymentIdList(data) {
  return request({
    url: "/chain/companypayment/getPayeeGroupByPaymentIdList",
    method: "post",
    data,
  });
}
export function getPayeePayInfoByPaymentIdList(data) {
  return request({
    url: "/chain/companypayment/getPayeePayInfoByPaymentIdList",
    method: "post",
    data,
  });
}
export function getPayeeGroupByPaymentIdListNew(data) {
  return request({
    url: "/chain/companypayment/getPayeeGroupByPaymentIdListNew",
    method: "post",
    data,
  });
}
// 根据支付单ID查询税费信息
export function getPayTaxInfoByPaymentIdList(data) {
  return request({
    url: "/chain/companypayment/getPayTaxInfoByPaymentIdList",
    method: "post",
    data,
  });
}
// 根据支付单ID集合查询凭证集合信息
export function getEvidenceByPaymentIdList(data) {
  return request({
    url: "/chain/companypayment/getEvidenceByPaymentIdList",
    method: "post",
    data,
  });
}
// 支付单ID查询支付明细信息
export function getBatchTransDetailByPaymentIdList(data) {
  return request({
    url: "/chain/companypayment/getBatchTransDetailByPaymentIdList",
    method: "post",
    data,
  });
}
// 根据支付单ID和承运人ID查询支付单信息和已支付的运单信息
export function getPaymentPayeeInfo(params) {
  return request({
    url: "/chain/companypayment/getPaymentVoucher",
    method: "get",
    params,
  });
}
// 根据支付单ID和承运人ID查询支付单信息和未支付的运单信息
export function getNotPaymentPayeeInfo(params) {
  return request({
    url: "/chain/companypayment/getNotPaymentPayeeInfo",
    method: "get",
    params,
  });
}
// 根据支付单ID查询需要付款承运人分组信息（支付成功的--打印凭证）
export function getEvidenceWhere(data) {
  return request({
    url: "/chain/bankVoucher/getBankVoucher",
    method: "post",
    data,
  });
}
// 批量支付同一个支付单的多个运单金额
export function batchTrans4047ByPaymentWaybill(data) {
  return request({
    url: "/chain/companypayment/batchTrans4047ByPaymentWaybill",
    method: "post",
    data,
  });
}
// 下载PDF
export function getEvidenceWherePdf(data) {
  return request({
    url: "/chain/bankVoucher/getBankVoucherPdf",
    method: "post",
    data,
    responseType: "blob",
  });
}
// 五证是否齐全
export function checkPayment(params) {
  return request({
    url: "/chain/companypayment/checkPaymentExistDocIncomplete",
    method: "get",
    params,
  });
}
// 检查是否需要支付密码或者手机
export function getCompanyPayAuthority(params) {
  return request({
    url: "/chain/companypayauthority/getCompanyPayAuthority",
    method: "get",
    params,
  });
}

export function getCode(query) {
  return request({
    url: "/chain/uniapp/codeByPay",
    method: "get",
    params: query,
  });
}
//付款详情
export function getDetail(id) {
  return request({
    url: "/chain/companypayment/view/" + id,
    method: "get",
  });
}
//下载运费付款单pdf
export function downloadFreightPaymentPdf(data) {
  return request({
    url: "/chain/companypayment/downloadFreightPaymentPdf",
    method: "post",
    data,
    responseType: "blob",
  });
}
//下载税费付款单pdf
export function downloadTaxFeePaymentPdf(data) {
  return request({
    url: "/chain/companypayment/downloadTaxFeePaymentPdf",
    method: "post",
    data,
    responseType: "blob",
  });
}
//下载支付单明细pdf
export function downloadPaymentDetailPdf(data) {
  return request({
    url: "/chain/companypayment/downloadPaymentDetailPdf",
    method: "post",
    data,
    responseType: "blob",
  });
}
// 根据支付单ID查询批量查询充值信息
export function getBatchChargeByPaymentIdList(data) {
  return request({
    url: "/chain/companyauthrecharge/getBatchChargeByPaymentIdList",
    method: "post",
    data,
  });
}
// 批量企业认证充值新增
export function batchCharge(data) {
  return request({
    url: "/chain/companyauthrecharge/batchCharge",
    method: "post",
    data,
  });
}

export function getDocCompleteRateByCompanyPaymentIdList(params) {
  return request({
    url: "/chain/companypayment/getDocCompleteRateByCompanyPaymentIdList",
    method: "get",
    params,
  });
}
//根据支付单查询资金支付计划
export function getPlanByPaymentId(params) {
  return request({
    url: "/chain/companypaymentplansettle/getPlanByPaymentId",
    method: "get",
    params,
  });
}
//付款打款驳回资金支付计划
export function rejectPaymentPlan(data) {
  return request({
    url: "/chain/companypaymentplansettle/rejectPaymentPlan",
    method: "post",
    data,
  });
}
//根据支付单号查询税费付款信息
export function getPayTaxInfoByPaymentNo(params) {
  return request({
    url: "/chain/companypayment/getPayTaxInfoByPaymentNo",
    method: "get",
    params,
  });
}
//付款检查余额
export function paymentCheck(data) {
  return request({
    url: "/chain/companypayment/paymentCheck",
    method: "post",
    data,
  });
}
//支付单税费支付
export function payTaxFee(data) {
  return request({
    url: "/chain/companypayment/payTaxFee",
    method: "post",
    data,
  });
}
//批量根据支付单号查询税费付款信息
export function batchGetPayTaxInfoByPaymentId(data) {
  return request({
    url: "/chain/companypayment/batchGetPayTaxInfoByPaymentId",
    method: "post",
    data,
  });
}
//批量根据支付单号支付税费
export function batchPayTaxInfoByPaymentId(data) {
  return request({
    url: "/chain/companypayment/batchPayTaxInfoByPaymentId",
    method: "post",
    data,
  });
}
