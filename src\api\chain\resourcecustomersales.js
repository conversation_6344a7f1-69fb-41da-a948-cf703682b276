import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/resourcecustomersales/page',
        method: 'get',
        params: query
    })
}
export function getCustomerList(query) {
  return request({
      url: '/chain/resourcecustomer/list',
      method: 'get',
      params: query
  })
}

export function addObj(obj) {
    return request({
        url: '/chain/resourcecustomersales',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/resourcecustomersales/' + id,
        method: 'get'
    })
}
export function getInventoryNum(resourceCustomerId,resourceType) {//根据资源id及退票客户id查询剩余票
  console.log(resourceType);
    return request({
        url: '/chain/resourcecustomerinventory/getInventoryNum/' + resourceCustomerId+'/'+resourceType,
        method: 'get'
    })
}
export function listByDriver(params) {//根据资源id获取资源类型列表
    return request({
        url: '/chain/resourcecustomerinventory/listByDriver',
        method: 'get',
        params,
    })
}

export function delObj(id) {
    return request({
        url: '/chain/resourcecustomersales/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/resourcecustomersales',
        method: 'put',
        data: obj
    })
}
