<template>
  <div class="walletList">
    <el-dialog width="1000px"
               center
               :data="visible"
               :visible.sync="visible"
               title="请确认付税费"
               :before-close="cancelModal">
      <basic-container>
        <avue-crud ref="walletList"
                   :data="tableData"
                   :table-loading="tableLoading"
                   :option="tableOption">
          <span slot="empty">暂无数据</span>
          <template slot="nsrmc"
                    slot-scope="{ row, index }">
            <div><span class="roundTip"
                    v-if="Number(row.walletBalance)<Number(row.unpaidTax)"></span>{{ row.nsrmc }}</div>
          </template>
          <template slot="menu"
                    slot-scope="{ row, index }">
            <span v-if="Number(row.walletBalance)<Number(row.unpaidTax)">
              <el-button type="primary"
                         icon="el-icon-wallet"
                         size="small"
                         v-if="permissions['chain:companynsrsbhwallet:recharge']"
                         @click="recharge(row)">
                充值</el-button>
              <div style="color:#d40000">钱包余额不足，请充值</div>
            </span>
            <span v-else
                  style="color: #409eff;">钱包余额充足</span>
          </template>
        </avue-crud>
      </basic-container>
      <span slot="footer"
            class="dialog-footer">
        <el-button type="primary"
                    :disabled="countTotal<=0"
                   :loading="btnLoading" @click="pay">一键付款</el-button>
        <el-button @click="cancelModal">取消</el-button>
        <span style="margin-left:10px">可支付税费{{countTotal}}元</span>
      </span>
    </el-dialog>
    <!-- 充值 -->
    <addRecharge v-if="addVisible"
                 :detailForm="addForm"
                 :option="addOption"
                 :showCancel="true"
                 size="634px"
                 :visible.sync="addVisible"
                 @submit="submit"
                 title="账户余额充值"></addRecharge>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {  batchGetPayTaxInfoByPaymentId,batchPayTaxInfoByPaymentId } from '@/api/chain/companyauthrecharge'
import { saveForWallet } from '@/api/chain/companynsrsbhwallet'
import addRecharge from '@/components/formDetail/index.vue';
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    selectList: {
      type: Array,
      default: () => {
        return []
      }
    },
  },
  components: {
    addRecharge,
  },
  data () {
    return {
      tableOption: {
        header: false,
        page: false,
        editBtn: false,
        delBtn: false,
        menuWidth: 140,
        column: [
          {
            label: "益路银行企业",
            prop: "nsrmc",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "钱包余额(元)",
            prop: "walletBalance",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "待付税费(元)",
            prop: "unpaidTax",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "收款人",
            prop: "inAcctName",
            minWidth: 80,
            overHidden: true,
          }]
      },
      tableData: [],
      tableLoading: false,
      logVisible: false,
      addVisible: false, //充值
      addForm: {},
      addOption: {
        labelWidth: 100,
        emptyBtn: false,
        submitBtnText: "创建",
        column: [
          {
            label: "充值金额(¥)",
            prop: "money",
            span: 24,
            type: "number",
            minRows: 0.01,
            maxRows: *********.99,
            precision: 2,
            controls: false,
            rules: [
              {
                required: true,
                message: "请输入 充值金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "充值企业银行",
            prop: "platformBranchId",
            span: 24,
            disabled: true,
            type: "select",
            props: {
              label: "nsrmc",
              value: "platformBranchId",
            },
            dicData: this.tableData,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 3,
            maxRows: 5,
            span: 24,
          },
          {
            label: "上传凭证",
            prop: "pic",
            type: "upload",
            listType: "picture-card",
            action: "/upms/file/upload?fileType=image&dir=batchRecharge/",
            dataType: 'string',
            propsHttp: {
              url: "link",
            },
            loadText: "附件上传中，请稍等",
            span: 24,
            accept: ".jpg,.png",
            tip: "只能上传jpg/png文件",
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
        ],
      },
      platformBranchId: "",
      btnLoading:false,
    };
  },
  created () {
    this.getData()
  },
  mounted () {
  },
  computed: {
    ...mapGetters(["permissions"]),
    countTotal(){
      return this.tableData.map((row) => row.needCharge? 0: row.unpaidTax).reduce((acc, cur) => parseFloat(cur) + acc, 0);
    },
  },
  methods: {
    getData () {
      let ids = this.selectList.map(item=>{
       return item.id
      })
      batchGetPayTaxInfoByPaymentId(ids).then(res => {
        this.tableData = res.data.data || []
        this.addOption.column.forEach(item=>{
          if(item.prop=='platformBranchId'){
            item.dicData = this.tableData
          }
        })
        // this.$refs.walletList.updateDic("platformBranchId", res.data.data);
      })
    },
    cancelModal () {
      this.$emit("update:visible", false);
    },
    recharge (row) {
      this.addForm.platformBranchId = row.platformBranchId
      this.addVisible = true
    },
    submit (form, done) {
      console.log(form);
      let param = Object.assign({}, form)
      saveForWallet(param).then(res => {
        this.$message.success("充值成功")
        this.addVisible = false
        // 记得需要更新税洼地金额
        // this.$emit('saveForWallet')
        done()
      }).catch(() => {
        done()
      })
    },
    pay(){
      let ids = this.selectList.map(item=>{
       return item.id
      })
      this.btnLoading = true
      batchPayTaxInfoByPaymentId(ids).then(res => {
        this.btnLoading = false
        this.$message.success("付款成功")
        this.$emit("refreshChange")
        this.cancelModal()
      }).catch(()=>{
        this.btnLoading = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .roundTip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #d40000;
  margin-right: 3px;
}
</style>
