<template>
  <div class="setPayee">
    <el-dialog width="300px"
               title="批量调整增减值"
               center
               :visible.sync="visible"
               :before-close="cancelModal"
               :close-on-click-modal="false">
      <div>
        <div style="margin-bottom: 6px">请输入增减值</div>
        <el-input placeholder="请输入增减值"
                  oninput="value=value.replace(/[^\d.\-]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
                  @blur="handleInput"
                  size="small"
                  maxlength="7"
                  v-model="unitPrice">
        </el-input>
        <div style="color:red;margin-top: 4px;">调整每一单增减值,旧的增减值将会被覆盖除不尽的余值将增加在最后一单增减值上!</div>

        <div class="remind">
          <div>
            您确认修改这
            <span>{{ list.length }}</span> 单？
          </div>
          <p>
            预计每单增减值：<span>{{divideByAverage(unitPrice,list.length)}}</span>
          </p>
          <p>
            最后一单增减值：<span>{{divideByAverageWithRemainder(unitPrice,list.length)}}</span>
          </p>
        </div>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="cancelModal">取消</el-button>
        <el-button type="primary"
                   :loading="loading"
                   @click="submit">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { clearNoNum } from "@/util/util.js";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  components: {
  },
  data () {
    return {
      unitPrice: 0,
      loading: false,
      averageValue:0,
      remainder:0,
    };
  },
  created () { },
  mounted: function () { },
  methods: {
    divideByAverageWithRemainder(number, average) {
      const quotient  = this.divideByAverage(number, average)
      let remainder = (number - quotient*average)
      this.remainder = remainder?(quotient+remainder).toFixed(2):quotient
      return this.remainder
    },
    divideByAverage (number, average) {
      const result = number / average;
      this.averageValue = Math.floor(result * 100) / 100;
      return this.averageValue  // 保留两位小数并删除多余的小数
    },
    cancelModal () {
      this.loading = false
      this.$emit("update:visible", false);
    },
    handleInput (obj, value) {
      this.unitPrice = clearNoNum(this.unitPrice + "");
      if (this.unitPrice > 999999999) {
        this.unitPrice = 999999999;
      }
      this.unitPrice = this.unitPrice.toFixed(2);
    },
    submit () {
      this.$emit("submit", this.averageValue,this.remainder, this.cancelModal)
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
