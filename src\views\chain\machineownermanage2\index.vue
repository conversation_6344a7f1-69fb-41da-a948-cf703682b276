<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        :before-open="beforeOpen"
        @refresh-change="refreshChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="handleDel"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <template slot="companyAuthIdForm" slot-scope="scope">
            <el-select v-model="form.companyAuthId" placeholder="请选择" @change="changCompanyAuth">
              <el-option
                v-for="item in companyAuthList"
                :key="item.id"
                :label="item.companyName"
                :value="item.id">
              </el-option>
            </el-select>
        </template>
        <template slot="inStaffIdForm" slot-scope="scope">
            <el-select v-model="form.inStaffId" placeholder="请选择">
              <el-option
                v-for="item in inStaffList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {
  getPage,
  getCompanyAuthList,
  getInStaffList,
  queryMachineOwnerByCompanyAuthId,
  mergeMachine,
  getObj,
  addObj,
  putObj,
  delObj,
} from "@/api/chain/machineownermanage";
import { tableOption } from "@/const/crud/chain/machineownermanage2";
import { mapGetters } from "vuex";

export default {
  name: "machineownermanage2",
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: 'update_datetime', //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      companyAuthList:[],
      inStaffList:[],
      queryMachineOwnerList:[],
      ruleForm:{
        companyAuthId:'',
        sourceMachineId:'',
        targetMachineId:'',
      },
      visible:false,
      rules:{
        companyAuthId: [
          { required: true, message: "请选择属在企业", trigger: "change" },
        ],
        inStaffId: [
          { required: true, message: "请选择挖机司机", trigger: "change" },
        ],
        sourceMachineId: [{ required: true, message: "请选择", trigger: "change" }],
        targetMachineId: [{ required: true, message: "请选择", trigger: "change" }],
      },
      loading:false,
    };
  },
  created() {},
  mounted: function () {
    this.getCompanyAuthList()
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:machineownermanage2:add"] ? true : false,
        delBtn: this.permissions["chain:machineownermanage2:del"] ? true : false,
        editBtn: this.permissions["chain:machineownermanage2:edit"]? true: false,
        viewBtn: this.permissions["chain:machineownermanage2:get"]? true: false,
      };
    },
  },
  methods: {
    getCompanyAuthList(){
      getCompanyAuthList().then(res=>{
        this.companyAuthList = res.data.data
      })
    },
    getInStaffList(companyAuthId){
      getInStaffList({companyAuthId}).then(res=>{
        this.inStaffList = res.data.data
      })
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    beforeOpen(done, type) {
      if (type == "edit") {
        this.getInStaffList(this.form.companyAuthId)
      }
      done();
    },
    changCompanyAuth(value){
      this.form.inStaffId = ''
      this.inStaffList = []
      this.getInStaffList(value)
    },
    changCompany(value){
      this.ruleForm.inStaffId = ''
      this.ruleForm.sourceMachineId = ''
      this.ruleForm.targetMachineId = ''
      this.inStaffList = []
      this.getInStaffList(value)
    },
    changInstaff(value){
      this.ruleForm.sourceMachineId = ''
      this.ruleForm.targetMachineId = ''
      this.queryMachineOwnerList = []
      this.getOwner(value)
    },
    getOwner(inStaffId){
      queryMachineOwnerByCompanyAuthId({companyAuthId:this.ruleForm.companyAuthId,inStaffId}).then(res=>{
        this.queryMachineOwnerList = res.data.data
      })
    },
    migration(){
      this.ruleForm = {
        companyAuthId:'',
        inStaffId:'',
        sourceMachineId:'',
        targetMachineId:'',
      },
      this.queryMachineOwnerList = []
      this.inStaffList = []
      this.visible = true
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true
          mergeMachine(this.ruleForm).then((res) => {
            this.loading = false
            this.visible = false;
            this.$message.success("提交成功");
            this.getPage(this.page);
          }).catch(err=>{
            this.loading = false
          })
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
