<template>
  <div class="execution">
    <basic-container>
      <el-container>
        <el-aside width="180px">
          <el-tabs tab-position="left" v-model="activeName" @tab-click="tabClick">
            <el-tab-pane
              :label="item"
              v-for="(item, index) in tabs"
              :key="index"
              :name="index + ''"
            >
            </el-tab-pane>
          </el-tabs>
        </el-aside>
        <el-main>
          <avue-form
            :option="option"
            v-model="form"
            @submit="submit"
            @empty="empty"
          >
          </avue-form>
          <avue-crud
            ref="crud"
            :data="tableData"
            :table-loading="tableLoading"
            :option="tableOption"
          >
          <template slot="index" slot-scope="{ row }">
            <span>{{row.$index+1}}</span>
          </template>
          </avue-crud>
        </el-main>
      </el-container>
    </basic-container>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { expotOut } from "@/util/down.js";
import { queryByCode } from "@/api/chain/companywaybill.js";
import { tableOption1 } from "@/const/crud/chain/exportExc.js";

export default {
  name: "exportExc",
  data() {
    return {
      tabs: [
        "车辆进出台账",
        "挖机装车台账",
        "泥尾台账",
        "泥尾统计运费",
        "土票领发统计",
        "机械台班装车数统计",
        "挖机装车台班费用统计",
        "挖机装车和台班",
      ],
      form: {},
      option: {
        labelWidth: 100,
        border: true,
        align: "center",
        menuAlign: "center",
        submitText: "查询并下载",
        emptyText: "重置",
        column: [
          {
            label: "项目名称",
            prop: "projectInfoId",
            type: "select",
            props: {
              label: "projectName",
              value: "id",
            },
            dicUrl: "/chain/projectinfo/list",
            filterable: true, //是否可以搜索
          },
          {
            label: "签单时间",
            prop: "goDatetime",
            type: "datetimerange",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
        ],
      },
      activeName: "0",
      tableLoading: false,
      tableOption: tableOption1,
      tableData: [],
      preName:'0',
    };
  },
  created() {},
  mounted() {
    window.addEventListener("resize", this.func);
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        // addBtn: this.permissions['chain:companywaybill:add'] ? true : false,
        // delBtn: this.permissions['chain:companywaybill:del'] ? true : false,
        // editBtn: this.permissions['chain:companywaybill:edit'] ? true : false,
        viewBtn: this.permissions["chain:companywaybill:get"] ? true : false,
      };
    },
  },
  methods: {
    func() {
      let tableHeight = document.querySelector(".el-table").offsetTop;
      this.$refs.crud.tableHeight = window.innerHeight - tableHeight - 130;
      this.$refs.crud.tableOption.height =
        window.innerHeight - tableHeight - 130;
      this.$refs.crud.doLayout();
    },
    expotOut,
    submit(form, done) {
      if (form.projectInfoId == "") {
        this.$message.error("请选择项目名称");
        done();
        return false;
      }
      this.queryByCode(Number(this.activeName) + 1, form);
      // this.exOut(Number(this.activeName) + 1, form);
      setTimeout(() => {
        done();
      }, 3000);
    },
    queryByCode(value, form) {
      let params = Object.assign({}, form);
      if (params.goDatetime && params.goDatetime.length > 0) {
        params.goDatetimeStart = params.goDatetime[0];
        params.goDatetimeEnd = params.goDatetime[1];
      }
      delete params.goDatetime;
      console.log(this.form);
      params.code = "truckVisit";
      switch (value) {
        case 1:
          // params.code = "truckVisit";
          this.$set(tableOption1.column,1,{
            label: this.form.$projectInfoId,
            prop: "test8",
            children: [
              {
                label: "车牌号",
                prop: "goTruckCode",
              },
              {
                label: "进场时间",
                prop: "test3",
              },
              {
                label: "挖机签单日期",
                prop: "inDate",
              },
            ],
          })
          this.tableOption = JSON.parse(JSON.stringify(tableOption1));
          this.$refs.crud.doLayout()
          this.$refs.crud.refreshTable()
          break;
        case 2:
          params.code = "truckSoil";
          break;
        case 3:
          params.code = "garbageExcel";
          break;
        case 4:
          params.code = "garbageCountExcel";
          break;
        case 5:
          params.code = "landStampCountExcel";
          break;
        case 6:
          params.code = "mechanicalLoadingCountExcel";
          break;
        case 7:
          params.code = "excavatorCostCountExcel";
          break;
        case 8:
          params.code = "machineOwnerAndLegerExcel";
          break;
      }
      this.tableLoading = true;
      queryByCode(params)
        .then((res) => {
          this.tableLoading = false;
          this.tableData = res.data.data;
          this.func()
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    empty() {
      console.log("qingkong");
    },
    exOut(value, form) {
      console.log(value);
      let params = Object.assign({}, form);
      if (params.goDatetime && params.goDatetime.length > 0) {
        params.goDatetimeStart = params.goDatetime[0];
        params.goDatetimeEnd = params.goDatetime[1];
      }
      delete params.goDatetime;
      let url = "/chain/excelExport/createByCode";
      let name = "车辆进出台账";
      params.code = "truckVisit";
      switch (value) {
        case 2:
          params.code = "truckSoil";
          url = "/chain/excelExport/createByCode";
          name = "挖机装车台账";
          break;
        case 3:
          params.code = "garbageExcel";
          url = "/chain/excelExport/createByCode";
          name = "泥尾台账";
          break;
        case 4:
          params.code = "garbageCountExcel";
          url = "/chain/excelExport/createByCode";
          name = "泥尾统计运费";
          break;
        case 5:
          params.code = "landStampCountExcel";
          url = "/chain/excelExport/createByCode";
          name = "土票领发统计";
          break;
        case 6:
          params.code = "mechanicalLoadingCountExcel";
          url = "/chain/excelExport/createByCode";
          name = "机械台班装车数统计";
          break;
        case 7:
          params.code = "excavatorCostCountExcel";
          url = "/chain/excelExport/createByCode";
          name = "挖机装车台班费用统计";
          break;
        case 8:
          params.code = "machineOwnerAndLegerExcel";
          url = "/chain/excelExport/createByCode";
          name = "挖机装车和台班";
          break;
      }

      this.expotOut(params, url, name);
    },
    tabClick(tab,event){
      console.log(tab);
      console.log(event);
      console.log(this.activeName);
      if(tab.index==this.preName){
        return false
      }
      // this.preName = tab.index
      // switch (Number(this.preName)) {
      //   case 1:
      //     this.tableOption = JSON.parse(JSON.stringify(tableOption1));
      //     this.tableData = []
      //     this.$refs.crud.refreshTable()
      //     break;
      //   case 2:
      //     break;
      //   case 3:
      //     break;
      //   case 4:
      //     break;
      //   case 5:
      //     break;
      //   case 6:
      //     break;
      //   case 7:
      //     break;
      //   case 8:
      //     break;
      // }
    },
  },
  destroyed() {
    window.removeEventListener("resize", this.func);
  },
};
</script>

<style lang="scss" scoped></style>
