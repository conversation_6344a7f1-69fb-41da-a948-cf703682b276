import request from '@/router/axios'

export function getPage(data) {
    return request({
        url: '/chain/companyentranceupdatehistory/getEntranceWorkFlowPage',
        method: 'post',
        data
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companywaybillupdatehistory',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companywaybillupdatehistory/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companywaybillupdatehistory/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companywaybillupdatehistory',
        method: 'put',
        data: obj
    })
}
// 根据企业运单ID查询运单的超时,结算,支付状态
export function queryWaybillUpdateStatus(params) {
    return request({
        url: '/chain/companywaybillupdatehistory/queryWaybillUpdateStatus',
        method: 'get',
        params
    })
}
// 批量审核异常申请
export function batchAudit(data) {
    return request({
        url: '/chain/companyentranceupdatehistory/batchConfirmForWorkFlow',
        method: 'post',
        data
    })
}
// 获取异常申请流程信息
export function getFlowNode(id) {
    return request({
        url: '/chain/companyapprovenode/getFlowNode/1/'+id,
        method: 'get',
    })
}
// 获取出场相关信息
export function getEntranceDetail(params) {
    return request({
        url: '/chain/companyentranceupdatehistory/getEntranceDetail',
        method: 'get',
        params
    })
}
