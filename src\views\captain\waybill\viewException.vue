<template>
  <div class="viewException">
    <el-drawer size="70%" title="查看异常" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <avue-crud
          ref="crud"
          :page.sync="page"
          :data="tableData"
          :table-loading="tableLoading"
          :option="tableOption"
          v-model="form"
          @on-load="getPage"
          @refresh-change="refreshChange"
          @sort-change="sortChange"
          @search-change="searchChange"
        >
          <template slot="pictureUrl" slot-scope="{ row }">
            <el-image
              v-if="row.pictureUrl"
              style="width: 50px; height: 50px"
              :src="filterImg(row.pictureUrl)"
              :preview-src-list="filterImgs(row.pictureUrl)"
            >
            </el-image>
            <span v-else>暂无图片信息</span>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>

  </div>
</template>

<script>
import {getPage} from "@/api/chain/companywaybillex";
import { tableOption } from "@/const/crud/captain/companywaybillex2";
import { mapGetters } from "vuex";
import { clearNoNum } from "@/util/util.js";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {},
  },
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: 'update_datetime', //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
    }
  },
  created() {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
            companyWaybillId:this.info.id
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    filterImg(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? null : url[0];
    },
    filterImgs(imgUrl) {
      let url = null;
      if (!!imgUrl && imgUrl != "") {
        url = imgUrl.split(",");
      }
      return url == null ? [] : url;
    },
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .drawerHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  li {
    color: rgba(0, 0, 0, .85);
    font-size: 14px;
    font-weight: 700;
    margin-right: 30px;
  }
}

.demo-block-control {
  border-top: 1px solid #eaeefb;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;
  background-color: #fff;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  text-align: center;
  margin-top: -1px;
  color: #d3dce6;
  cursor: pointer;
  position: relative;

  &:hover {
    color: #409eff;
    background-color: #f9fafc;
  }
}

</style>
