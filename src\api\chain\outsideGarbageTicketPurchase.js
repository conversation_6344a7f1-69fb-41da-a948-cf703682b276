import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companyticketpurchase/purchasePage',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
  return request({
    url: '/chain/companyticketpurchase',
    method: 'post',
    data: obj
  })
}
export function queryInventoryByGarbageId(query) {
    return request({
        url: '/chain/companyticketinventory/queryInventoryByGarbageId',
        method: 'get',
        params: query
    })
}
export function querySoilTypeByGarbage(query,val) {
  return request({
      url: val==1?'/chain/garbage/listSoilTypeByGarbageId':'/chain/companyticketprojectinventory/querySoilTypeByGarbage',
      method: 'get',
      params: query
  })
}
//获取退票泥尾票
export function getTicketNoPage(query) {
    return request({
        url: '/chain/companyticketno/getTicketNoPage',
        method: 'post',
        data: query
    })
}
//根据项目泥尾土质查询可用库存前缀
export function getTicketPrefix(query) {
    return request({
        url: '/chain/companyticketno/getTicketPrefix',
        method: 'post',
        data: query
    })
}
//根据退票采购id查询退票号
export function getRefundTicketNo(query) {
    return request({
        url: '/chain/companyticketno/getRefundTicketNo',
        method: 'get',
        params: query
    })
}

