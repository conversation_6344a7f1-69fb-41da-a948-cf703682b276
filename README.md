### [![输入图片说明](https://images.gitee.com/uploads/images/2021/0622/110750_59b42ced_5079715.jpeg "n_community_0l.jpg")](https://promotion.aliyun.com/ntms/yunparter/invite.html?userCode=ktp7i3ac) <br>[阿里云领取￥2000红包，服务器低至￥91.80/年](https://promotion.aliyun.com/ntms/yunparter/invite.html?userCode=ktp7i3ac)

### 注意🈲
1. 本软件为 www.joolun.com 开发研制，未经购买不得使用
1. 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
1. 一经发现盗用、分享等行为，将追究法律责任，后果自负

### 重要信息
1. ⚡技术问题和bug，请提交【工单】；如问题解决请自行关闭工单，问题请带截图和操作步骤，问题没描述清楚的不处理
1. 前期项目部署时遇到问题可以联系技术人员QQ帮忙解决；其他非部署问题请直接发【工单】，QQ只处理部署方面的问题
2. 版本升级时要重新导入新的脚本，并清理redis缓存
3. 代码请拉master分支，其他分支不要管
3. 🔊请按照自己业务需求进行测试后再使用，因框架问题造成的任何损失JooLun概不负责

### 软著（受法律保护）
* ![](https://joolun-blog.oss-cn-zhangjiakou.aliyuncs.com/%E8%BD%AF%E8%91%97.jpg)

[elementUI](https://element.eleme.cn/#/zh-CN)<p>
[Avue](https://avue.top)