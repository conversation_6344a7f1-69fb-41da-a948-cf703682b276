<template>
  <div class="history">
    <el-drawer
      size="60%"
      title="历史记录"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
    <div v-if="list&&list.length>0">
      <basic-container class="lists" v-for="(item,index) in list" :key="index">
        <div >
          <el-row v-for="(item2,index2) in item" :key="index2" type="flex" :class="index2>13?'customRow rowBg':'customRow'" >
            <el-col v-for="(item3,index3) in item2" :key="index3" :class="index3==0?'col1 myCol':'myCol'">
              <el-image
                v-if="(item3.key=='beginClockUrl'||item3.key=='endClockUrl')&&index3!=0&&item3.label"
                style="width: 100px; height: 100px"
                :src="item3.label"
                :preview-src-list="[item3.label]">
              </el-image>
              <!-- <el-tooltip class="item" effect="dark" :content="item3" placement="top-start"> -->
                <span v-else class="text" :class="{'customText':index2!=0&&index3!=0,'isAbnormal':index2!=0&&index3!=0&&!item3.isAbnormal}">{{item3.label}}</span>
              <!-- </el-tooltip> -->
            </el-col>
          </el-row>

        </div>

      </basic-container>
    </div>
    <el-empty v-else :image-size="200"></el-empty>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {getHistoryByLedgerDigId,getSettingAll } from '@/api/chain/ledgerDig2'

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  data() {
    return {
      originalList:[
        {
          ledgerValue:"",
          key:"custom"
        },
        {
          ledgerValue:"班次",
          key:"inShiftType"
        },
        {
          ledgerValue:"挖机类型",
          key:"inType"
        },
        {
          ledgerValue:"机械型号",
          key:"machineCode"
        },
        {
          ledgerValue:"开始时间",
          key:"startDatetime"
        },
        {
          ledgerValue:"结束时间",
          key:"endDatetime"
        },
        {
          ledgerValue:"时长",
          key:"workTime"
        },
        {
          ledgerValue:"作业地点",
          key:"address"
        },
        {
          ledgerValue:"作业类型",
          key:"ledgerType"
        },
        {
          ledgerValue:"单位",
          key:"unitWork"
        },
        {
          ledgerValue:"完成量",
          key:"scheduleWork"
        },
        // {
        //   ledgerValue:"是否签证",
        //   key:"isVisa"
        // },
        {
          ledgerValue:"备注",
          key:"ledgerRemark"
        },
        {
          ledgerValue:"修改时间",
          key:"updateDatetime"
        },
      ],
      list:[]
    };
  },
  created() {},
 async mounted() {
   await this.getSettingAll()
    this.getHistoryByLedgerDigId()
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    getSettingAll(){
     return getSettingAll().then(res=>{
        // fuelMeterUrl
        let arr = res.data.data
        arr.forEach((value,index,array)=>{
          if(value.key == "fuelMeterUrl"){
            array.splice(index,1)
          }
        })
        this.list = []
        this.list = [...this.originalList,...arr]
        return this.list
      }).catch(()=>{
        return []
      })
    },
    getHistoryByLedgerDigId(){
      getHistoryByLedgerDigId({id:this.info.id}).then(res=>{
        let arr = []
        res.data.data&&res.data.data.forEach((element,index) => {
          let ledgerDigInJson = JSON.parse(element.ledgerDigInJson)
          let ledgerDigLeaderJson = JSON.parse(element.ledgerDigLeaderJson)
          arr.push([])
          this.list&&this.list.forEach((item,index2)=>{
            arr[index].push([{label:item.ledgerValue,value:item.key}])
            if(index2==0){
              arr[index][index2].push({label:`挖机员(${this.info.staffName})`,value:"custom"})
              arr[index][index2].push({label:`项目经理(${this.info.confirmStaffName})`,value:"custom"})
            }else{
              arr[index][index2].push({label:ledgerDigInJson[item.key]||'',key:item.key,isAbnormal:ledgerDigInJson[item.key]==ledgerDigLeaderJson[item.key]})
              arr[index][index2].push({label:ledgerDigLeaderJson[item.key]||'',key:item.key,isAbnormal:ledgerDigInJson[item.key]==ledgerDigLeaderJson[item.key]})
            }
          });
        })
        this.list = arr
        console.log(this.list);
      })
    }
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
/deep/ .customRow{
  // height: 40px;
  line-height: 40px;
  &.rowBg{
    background-color: #f5f5f5;
  }
  .myCol{
    min-width: 200px;
    &.col1{
      min-width: 120px;
    }
  }
  .text{
    display: inline-block;
    text-overflow: ellipsis;
    overflow: hidden;
    vertical-align: middle;
    &.customText{
      color: #999;
      font-size: 14px;
    }
    &.isAbnormal{
      color: red;
      font-size: 14px;
    }
  }
}
</style>
