import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companymachineowner/queryMachineOwnerPage',
        method: 'get',
        params: query
    })
}

export function getCompanyAuthList(query) {
    return request({
        url: '/chain/companyauth/list',
        method: 'get',
        params: query
    })
}
export function getInStaffList(query) {
    return request({
        url: '/chain/companymachineowner/queryInStaffByCompanyAuthId',
        method: 'get',
        params: query
    })
}
export function queryMachineOwnerByCompanyAuthId(query) {
    return request({
        url: '/chain/companymachineowner/queryMachineOwnerByCompanyAuthId',
        method: 'get',
        params: query
    })
}
export function mergeMachine(query) {
    return request({
        url: '/chain/companymachineowner/mergeMachine',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companymachineowner',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companymachineowner/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companymachineowner/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companymachineowner',
        method: 'put',
        data: obj
    })
}
