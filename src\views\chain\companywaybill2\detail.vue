<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%" title="运单详情" :append-to-body="true" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <!-- <basic-container> -->
        <avue-form :option="option" v-model="form"></avue-form>
      <!-- </basic-container> -->
    </el-drawer>

  </div>
</template>

<script>
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    detailForm: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    option: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
  },
  data() {
    return {
      form:{}
    }
  },
  created() {
  },
  mounted: function () {
    this.form = this.detailForm
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
/deep/ .avue-upload .el-icon-plus{
  display: none;
}
/deep/ .avue-upload--list .el-upload{
  border: none;
  .avue-upload__avatar{
    width: 100px;
    height: 100px;
  }
}
</style>
