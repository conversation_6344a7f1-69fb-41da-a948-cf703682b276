import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companyWaybillBuilderPage/postBuilderPreSetPricePage',
        method: 'post',
        data: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companywaybill',
        method: 'post',
        data: obj
    })
}
export function updatePrice(obj) {
    return request({
        url: '/chain/companywaybill/updatePrice',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companywaybill/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companywaybill/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companywaybill',
        method: 'put',
        data: obj
    })
}
export function getGpsList(query) {
    return request({
        url: '/chain/drivertruckgps/getGpsList',
        method: 'get',
        params: query
    })
}

export function batchAddWaybillSettlePre(obj) {
    return request({
        url: '/chain/companysettle/batchAddWaybillSettlePre',
        method: 'post',
        data: obj
    })
}

export function updateCarrierName(obj) {
    return request({
        url: '/chain/companywaybill/updateCarrierName',
        method: 'post',
        data: obj
    })
}

export function postCreateByCode(obj) {
  return request({
      url: '/chain/excelExport/postCreateByCode',
      method: 'post',
      data: obj
  })
}
