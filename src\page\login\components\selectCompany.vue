<template>
  <div class="selectPersonnel">
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="420px"
      title="选择我的企业"
      :before-close="oncancel"
      append-to-body
    >
      <ul v-if="list && list.length > 0">
      <li v-for="item in list" :key="item.companyAuthId">
        <div class="top flex flex-items-center">
          <div class="photo flex flex-items-center">
            <img src="../../../static/login/jyb.png" alt="" />
            <span class="overflow_1">{{ item.companyAuthName }}</span>
          </div>
          <!-- <div class="remark">最近访问</div> -->
        </div>
        <div class="accessIdentity">访问身份:{{ item.positionName }}</div>
        <div class="btns">
          <el-button type="primary"  @click="handle(item)">进入企业</el-button>
        </div>
      </li>
    </ul>
    </el-dialog>
  </div>
</template>

<script>
import {switchCompanyAuthCode} from "@/api/login.js"
import { mapGetters } from "vuex";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    roleType: {
      type: [Number,String],
      default: "2",
    },
  },
  data() {
    return {
      list:[]
    };
  },
  mounted() {
    this.list = JSON.parse(sessionStorage.getItem('companyUser'))
  },
  computed: {
    ...mapGetters(["tagWel"]),
  },
  methods: {
    handle(item){
      switchCompanyAuthCode().then(res=>{
        let data = res.data.data
        let loginForm ={
          phone:item.phone,
          code:data,
          roleType:this.roleType,
          company_auth_id:item.companyAuthId,
        }
        this.$store.dispatch("LoginByPhone", loginForm).then(() => {
          console.log(loginForm);
          this.$store.commit("SET_TOP_MENU_INDEX", 0);
          this.$store.dispatch("SetRoleType",loginForm.roleType)
          this.$store.commit("DEL_ALL_TAG");
          this.$emit("update:visible", false);
          this.$router.push({ path: this.tagWel.value });
        });
      })
    },
    oncancel() {
      console.log(1111);
      this.$store.commit('SET_ACCESS_TOKEN', '')
      this.$store.commit('SET_USER_MD5', '')
      this.$store.commit('SET_REFRESH_TOKEN', '')
      this.$store.commit('SET_EXPIRES_IN', '')
      this.$store.commit('CLEAR_LOCK')
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style scoped lang="less">
/deep/ .el-dialog__header{
  padding: 0;
  height: 50px;
  line-height: 50px;
  text-align: center;
  .el-dialog__title{
     display: inherit;
     line-height: 50px;
  }
}
/deep/ .el-dialog__body{
  background-color: #f3f3f3;
}
ul {
  margin: 0 auto;
  width: 350px;
  li {
    height: 214px;
    background: #fdfdfd;
    box-shadow: 0px 10px 15px 0px rgba(102, 102, 102, 0.1);
    border-radius: 8px;
    border: 1px solid #eeeeee;
    box-sizing: border-box;
    padding: 28px 25px 24px;
    margin-bottom: 10px;
    .top {
      justify-content: space-between;
      img {
        width: 32px;
        height: 32px;
        margin-right: 9px;
      }
      .photo {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #666666;
        line-height: 20px;
        text-shadow: 0px 10px 15px rgba(102, 102, 102, 0.1);
      }
      // .remark {
      //   width: 52px;
      //   margin-left: 10px;
      //   font-size: 12px;
      //   font-family: PingFangSC-Regular, PingFang SC;
      //   font-weight: 400;
      //   color: #bbbbbb;
      //   line-height: 17px;
      //   text-shadow: 0px 10px 15px rgba(102, 102, 102, 0.1);
      // }
    }
    .accessIdentity {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 20px;
      text-shadow: 0px 10px 15px rgba(102, 102, 102, 0.1);
      margin-top: 26px;
      margin-bottom: 40px;
    }
    .btns {
      // text-align: center;
      .el-button{
        width: 100%;
      }
    }
  }
}
</style>
