<template>
  <div class="Echarts" id="echarts">
    <div class="subTitle">
      泥尾成本<el-tooltip
        class="item"
        effect="dark"
        placement="top-start"
        ><i class="el-icon-question"></i>
        <template #content>
          单位为非车方吨{{titleData.unitNotCorrectCount}}单不参与统计<br>
          单位为吨土质不设置单位换算{{titleData.soilTypeNotCorrectCount}}单不参与统计<br>
          未设置泥尾价格的{{titleData.garbagePriceNotCorrectCount}}单不参与统计
        </template></el-tooltip
      >
    </div>

    <div ref="soilChart" class="echart"></div>
  </div>
</template>
  
  <script>
import {
  getGarbageStatistic,
  getTruckTpModeTitle,
} from "@/api/chain/analysis.js";
const colors = ["#EE6666", "#91CC75", "#5470C6", "#73c0de"];
export default {
  name: "soilChart",
  data() {
    return {
      chart: null,
      id: "",
      titleData: {
        truckTotalCount: 0, // 共出车N单
        truckCorrectCount: 0, // 运输方式-N单参与统计
        squareCorrectCount: 0, // 出土情况-N单参与统计
        unitNotCorrectCount: 0, // 单位为非车方吨N单不参与统计
        soilTypeNotCorrectCount: 0, // 单位为吨土质不设置单位换算N单不参与统计
        tpModeInnerNotCorrectCount: 0, // 回填，内转回填运单N单不参与统计
        tpModeResourceNotCorrectCount: 0, // 资源运单N单不参与统计
        priceNotCorrectCount: 0, // 未设置装车价/泥尾价/核算价/预设价N单不参与统计
        ledgerDigPriceNotCorrectCount: 0, // 未设置台班费N项不参与统计
      },
    };
  },
  activated() {
    var myEvent = new Event("resize");
    window.dispatchEvent(myEvent);
  },
  methods: {
    myEcharts(data) {
      // 基于准备好的dom，初始化echarts实例
      this.chart = this.$echarts.init(this.$refs.soilChart);
      let option = {
        color: colors,
        
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
          formatter: function (params) {
            let str = "<span>" + params[0].name + "</span><br/>";
            for (let i = 0; i < params.length; i++) {
             let unit= params[i].seriesName == "费用"?'元':params[i].seriesName=="装车数"?'车':params[i].seriesName=="出土均费"?'元/车':'方/车'
              str +=
                params[i].marker +
                params[i].seriesName +
                '：<span style="float:right;font-weight:600">' +
                params[i].value +
                `${unit}</span><br/>`;
            }
            return str;
          },
        },
        grid: {
          right: "20%",
        },
        toolbox: {
          feature: {
            dataView: { show: false, readOnly: false },
            restore: { show: false },
            saveAsImage: { show: true },
          },
        },
        // legend: {
        //   data: ["出车数", "土质出土方量", "均方/车"],
        // },
        xAxis: [
          {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            // prettier-ignore
            data: data.xAxis,
          },
        ],
        yAxis: [
        {
            type: "value",
            name: "费用",
            position: "right",
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[0],
              },
            },
            axisLabel: {
              formatter: "{value} 元",
            },
          },
          {
            type: "value",
            name: "装车数",
            position: "right",
            alignTicks: true,
            offset: 80,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[1],
              },
            },
            axisLabel: {
              formatter: "{value} 车",
            },
          },
        {
            type: "value",
            name: "出土方量",
            position: "left",
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[2],
              },
            },
            axisLabel: {
              formatter: "{value} 方/车",
            },
          },
         
         
          
          {
            type: "value",
            name: "出土均费",
            position: "right",
            alignTicks: true,
            offset: 160,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[3],
              },
            },
            axisLabel: {
              formatter: "{value} 元/车",
            },
          },
        ],
        series: [
          // {
          //   name: "出车数",
          //   type: "bar",
          //   data: data.yTruckAxisData,
          // },
          // {
          //   name: "土质出土方量",
          //   type: "bar",
          //   yAxisIndex: 1,
          //   data: data.ySquareAxisData,
          // },
          // {
          //   name: "均方/车",
          //   type: "line",
          //   yAxisIndex: 2,
          //   data: data.yAvgSquareTruckAxisData,
          // },
        ],
      };
      let legend = data.series.map((v) => v.name);
      let series = data.series.map((v) => {
        if (v.name == "费用") {
          v.type = "bar";
          v.yAxisIndex = 0;
        } else if (v.name == "装车数") {
          v.type = "bar";
          v.yAxisIndex = 1;
        } else if (v.name == "出土均费") {
          v.type = "line";
          v.yAxisIndex = 3;
        } else if (v.name == "出土方量") {
          v.type = "bar";
          v.yAxisIndex = 2;
        }
        return v;
      });
      option.series = series;
      option.legend = {
        data: legend,
      };
      // 使用刚指定的配置项和数据显示图表。
      this.chart.setOption(option);
    },
    resizeHanlder() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  },
  mounted() {
    this.id = this.$route.query.id;
    getTruckTpModeTitle({
      projectInfoBiPhaseId: this.id,
    })
      .then((res) => {
        this.titleData = res.data.data;
      })
      .catch(() => {});
    getGarbageStatistic(this.id).then((res) => {
      this.myEcharts(res.data.data);
      window.addEventListener("resize", this.resizeHanlder);
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    window.removeEventListener("resize", this.resizeHanlder);
    this.chart.dispose();
    this.chart = null;
  },
  watch: {},
};
</script>
  
  <style lang="scss" scoped>
.Echarts {
  border-radius: 8px;
  margin: 0 15px;
  width: auto;
  background-color: #fff;
  //   overflow: hidden;
  height: 85vh;
  //   display: flex;
  //   flex-direction: column;
  //   justify-content: flex-start;
  .echart {
    padding: 15px 0;
    width: 100%;
    height: 100%;
  }
}
.subTitle {
  padding: 0 15px;
  padding-top: 15px;
  text-align: center;
  line-height: 30px;
  color: #464646;
    font-size: 16px;
  .link {
    margin-left: auto;
  }
}
</style>
  