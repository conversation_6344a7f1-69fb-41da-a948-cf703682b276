import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/systemarea/page',
        method: 'get',
        params: query
    })
}

export function getTree(query) {
    return request({
        url: '/chain/systemarea/tree',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/systemarea',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/systemarea/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/systemarea/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/systemarea',
        method: 'put',
        data: obj
    })
}
