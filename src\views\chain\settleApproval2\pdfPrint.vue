<template>
  <div class="pdfPrint">
    <el-drawer
      size="900px"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <template slot="title">
        <div
          style="font-size: 28px; color: #333333"
          class="flex flex-items-center"
        >
          打印
          <el-radio-group v-model="tabPosition" style="margin-left: 20px">
            <el-radio-button label="1">结算审批单</el-radio-button>
            <el-radio-button label="2">付款审批单</el-radio-button>
          </el-radio-group>
          <el-tooltip
            class="item"
            effect="dark"
            content="打印"
            placement="bottom"
          >
            <i
              class="el-icon-printer"
              color="red"
              style="margin: 0 20px; color: #333333; cursor: pointer"
              @click="printPDF"
            ></i>
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="下载PDF"
            placement="bottom"
          >
            <i
              class="el-icon-download"
              style="color: #333333; cursor: pointer"
              @click="downPdf"
            ></i>
          </el-tooltip>
        </div>
      </template>
      <pdfPreview
        :pdfUrl="newSettleUrl || settleUrl"
        v-show="tabPosition == 1"
      ></pdfPreview>
      <pdfPreview
        :pdfUrl="newPaymentUrl || paymentUrl"
        v-show="tabPosition == 2"
      ></pdfPreview>
    </el-drawer>
  </div>
</template>

<script>
import pdfPreview from "./pdfPreview";
import { print } from "@/util/util.js";
import $Print from "avue-plugin-print";
import printJS from "print-js";
import {
  downCompanySettleAuditPdf,
  downPaymentApplyPdf,
} from "@/api/chain/settleApproval";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    settleUrl: {
      type: String,
      default: () => {
        return "";
      },
    },
    paymentUrl: {
      type: String,
      default: () => {
        return "";
      },
    },
    curSettleNo: {
      type: String,
      default: () => {
        return "";
      },
    },
  },
  components: {
    pdfPreview,
  },
  data() {
    return {
      tabPosition: "1",
      newSettleUrl: "",
      newPaymentUrl: "",
    };
  },
  created() {},
  mounted () {
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    printPDF() {
      this.downApi().then((res) => {
        printJS({
          printable: res,
          type: "pdf",
        });
      });
    },
    downPdf() {
      // window.open(this.tabPosition == 1 ? this.settleUrl : this.paymentUrl, "_blank")
      this.downApi().then((res) => {
        const link = document.createElement("a");
        link.href = res;
        let fileName =
          this.tabPosition == 1 ? "结算审批单.pdf" : "付款审批单.pdf"; //
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
      });
    },
    downApi() {
      return new Promise((resolve, reject) => {
        if (this.tabPosition == 1) {
          downCompanySettleAuditPdf({
            settleNo: this.curSettleNo,
            isDown: true,
          })
            .then((res) => {
              this.newSettleUrl = window.URL.createObjectURL(
                new Blob([res.data])
              );
              resolve(window.URL.createObjectURL(new Blob([res.data])));
            })
            .catch(() => {
              reject();
            });
        } else {
          downPaymentApplyPdf({ settleNo: this.curSettleNo, isDown: true })
            .then((res) => {
              this.newPaymentUrl = window.URL.createObjectURL(
                new Blob([res.data])
              );
              resolve(this.newPaymentUrl);
            })
            .catch(() => {
              reject();
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 0px !important;
}
/deep/ .el-drawer__body {
  padding: 0px !important;
}
</style>
