<template>
  <div class="driverTaskDetail">
    <el-drawer size="80%"
               title="确定匹配"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <basic-container>
        <header style="text-align:center;font-size:18px;margin-bottom:10px;color:#000;">出土计划编号：{{info.planNo}}</header>
        <avue-crud ref="crud"
                   :data="tableData"
                   :page.sync="page"
                   :table-loading="tableLoading"
                   :option="tableOption"
                   v-model="form"
                   @selection-change="selectionChange"
                   @on-load="getPage">
          <template slot="header"
                    slot-scope="{ size }">
            <el-button icon="el-icon-circle-check"
                       style="margin-left:10px;position: relative;top: -3px;"
                       size="mini"
                       type="primary"
                       :disabled="selectList.length==0"
                       @click="batchConfirm">批量确定匹配
            </el-button>
            <el-radio-group v-model="orderStatus"
                            @change="changeTab"
                            size="small"
                            style="display: inline-block;float: right;margin-top: 4px;">
              <el-radio-button label="1">待确认</el-radio-button>
              <el-radio-button label="2">项目待确认</el-radio-button>
              <el-radio-button label="3">泥尾待确认</el-radio-button>
              <el-radio-button label="4">匹配成功</el-radio-button>
              <el-radio-button label="6,7">已取消</el-radio-button>
            </el-radio-group>
          </template>
          <template slot="menu"
                    slot-scope="{ row }">
            <el-button icon="el-icon-check"
                       size="small"
                       type="text"
                       v-if="row.orderStatus==1||row.orderStatus==2"
                       @click="confirm(row)">确定匹配
            </el-button>
          </template>
          <template slot="expand"
                    slot-scope="{row}">
            <div style="padding: 10px;">
              <el-table :data="row.matchSoilArr"
                        border
                        style="width: 100%">
                <el-table-column prop="soilName"
                                 align='center'
                                 label="土质">
                </el-table-column>
                <el-table-column prop="garbagePrice"
                                 align='center'
                                 label="泥尾价格">
                </el-table-column>
                <el-table-column prop="projectPrice"
                                 align='center'
                                 label="项目价格">
                </el-table-column>
                <el-table-column prop="weightUnit"
                                 align='center'
                                 label="单位">
                </el-table-column>
              </el-table>
            </div>
          </template>
        </avue-crud>

      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { confirmMatchPage, batchConfirm } from "@/api/chain/planManage";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: 'order_status', //升序字段
        descs: "garbage_plan_create_time", //降序字段
      },
      form: {},
      tableData: [],
      paramsSearch: {},
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 12,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchMenuSpan: 6,
        refreshBtn: false,
        searchShowBtn: false,
        selection: true,
        selectable: (row) => {
          return row.orderStatus == 1 || row.orderStatus == 2
        },
        header: false,
        menuWidth: 100,
        expand: true,
        expandRowKeys: [1],
        rowKey: 'matchGarbageProjectPlanId',
        column: [
          {
            label: "泥尾名称",
            prop: "garbageNames",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "泥尾地址",
            prop: "garbageAddress",
            minWidth: 120,
            overHidden: true,
          },
          {
            label: "土质",
            prop: "matchGarbagePlanSoil",
            minWidth: 80,
            overHidden: true,
          },
          // {
          //   label: "参考价格",
          //   prop: "chargeWaybillTotal",
          // },
          {
            label: "签单方式",
            prop: "signingWayText",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "收土量(m³)",
            prop: "recycleSoilCube",
            minWidth: 80,
            overHidden: true,
          },
          {
            label: "结算周期",
            prop: "garbageSettleCycle",
            minWidth: 80,
            overHidden: true,
          },
          // {
          //   label: "不区分土质/车",
          //   prop: "garbageFlatPrice",
          //   minWidth:100,
          //   overHidden:true,
          // },
          {
            label: "收土开始时间",
            prop: "garbageRecycleSoilTimeStart",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "收土结束时间",
            prop: "garbageRecycleSoilTimeEnd",
            minWidth: 100,
            overHidden: true,
          },
          {
            label: "状态",
            prop: "orderStatusText",
            minWidth: 80,
            overHidden: true,
          },
        ],
      },
      selectList: [],
      orderStatus: "1",
    };
  },
  created () { },
  mounted: function () { },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },
    sortChange (val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage (page, params = {}) {
      this.tableLoading = true;
      confirmMatchPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
            matchProjectPlanId: this.info.id,
            orderStatus: this.orderStatus
          },
          params,
          this.paramsSearch
        ))
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page);
    },
    batchConfirm () {
      let ids = this.selectList.map(item => item.matchGarbageProjectPlanId)
      this.$confirm('确定匹配?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        batchConfirm(ids).then(res => {
          this.$message.success('操作成功')
          this.getPage(this.page)
        })
      }).catch(() => { });
    },
    confirm (row) {
      this.$confirm('确定匹配?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        batchConfirm([row.matchGarbageProjectPlanId]).then(res => {
          this.$message.success('操作成功')
          this.getPage(this.page)
        })
      }).catch(() => { });
    },
    selectionChange (e) {
      this.selectList = e;
    },
    changeTab (tab) {
      this.getPage(this.page);
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
</style>
