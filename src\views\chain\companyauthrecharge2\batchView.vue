<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%" title="查看承运人" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        <avue-crud ref="crud" :data="tableData"  :table-loading="tableLoading" :option="tableOption" v-model="form"
          @on-load="getPage">
          <template slot="menuLeft" slot-scope="scope">
            <el-button
              size="small"
              type="primary"
              icon="el-icon-download"
              :loading="btnLoading"
              @click="exOut">导出</el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {getPayeeGroupByPaymentIdList} from '@/api/chain/companyauthrecharge'
import { exportOut } from "@/util/down.js";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    selectList: {
      type: Array,
      default: ()=>{
        return []
      }
    },
  },
  data() {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      tableData: [],
      paramsSearch: {

      },
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: false,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        // height:'auto',
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        index: false,
        menu:false,
        showSummary: true,
        refreshBtn:false,
        sumColumnList: [
            {
              label:'合计：',
              name: 'amount',
              type: 'sum'
            }
          ],
        column: [
          {
            label: "收款人名称",
            prop: "payeeName",
            overHidden:true,
            formatter:(val)=>{
              if(val.amount>=5000000){
                return `<span style='color:red'>${val.payeeName}</span>`
              }else{
                return `<span>${val.payeeName}</span>`
              }
            }
          },
          {
            label: "收款卡号",
            prop: "bindingBankNo",
            overHidden:true,
          },
          {
            label: "收款银行",
            prop: "bindingBankName",
            overHidden:true,
          },
          {
            label: "收款金额(合计金额)",
            prop: "amount",
            formatter:(val)=>{
              if(val.amount>=5000000){
                return `<span style='color:red'>${val.amount}</span>`
              }else{
                return `<span>${val.amount}</span>`
              }
            },
            overHidden:true,
          },
          {
            label: "手机号码",
            prop: "payeeMobile",
            overHidden:true,
          },
          {
            label: "身份证号",
            prop: "idCard",
            overHidden:true,
          },
        ],
      },
      btnLoading:false,
    }
  },
  created() {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    getPage(page, params = {}) {
      this.tableLoading = true
      let data = this.selectList.map(item=>{
        return item.id
      })
      getPayeeGroupByPaymentIdList(data).then(response => {
        this.tableData = response.data.data
        // this.page.total = response.data.data.total
        // this.page.currentPage = page.currentPage
        // this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    exOut(){
      let data = this.selectList.map(item=>{
        return item.id
      })
      let url = '/chain/companypayment/exportPayee'
      this.btnLoading = true
      exportOut(data,url,'查看承运人',"post").then(res=>{
        this.btnLoading = false
      }).catch(err=>{
        this.$message.error('导出失败')
        this.btnLoading = false
      })
    },
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

</style>
