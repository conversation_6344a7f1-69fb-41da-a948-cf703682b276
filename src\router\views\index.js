import Layout from "@/page/index/";
export default [
  {
    path: "/wel",
    component: Layout,
    redirect: "/wel/index",
    children: [
      {
        path: "index",
        name:'wel',
        meta:{
          label:'首页'
        },
        component: () => import(/* webpackChunkName: "views" */ "@/page/wel"),
      },
    ],
  },
  {
    path: "/info",
    component: Layout,
    redirect: "/info/index",
    // name:'info',
    children: [
      {
        path: "index",
        name: "info",
        meta:{
          label:'个人信息'
        },
        component: () =>
          import(/* webpackChunkName: "page" */ "@/views/upms/user/info"),
      },
    ],
  },
  {
    path: "/changeCompany",
        name:'changeCompany',
        meta:{
            label:'选择企业',
            isTab:false,
        },
        component: () =>import(/* webpackChunkName: "page" */ "@/page/login/changeCompany"),

  },
  {
    path: "/waybill",
    // name:'waybillDetail',
    component: Layout,
    redirect: "/waybill/waybillDetail",
    children: [
      {
        path: "waybillDetail",
        name: "waybillDetail",
        meta:{
          label:'结算单详情'
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/createPayOrder2/waybillDetail"
          ),
      },
    ],
  },
  {
    path: "/companypayment",
    // name:'alreadyWaybill',
    component: Layout,
    redirect: "/companypayment/alreadyWaybill",
    children: [
      {
        path: "alreadyWaybill",
        name: "alreadyWaybill",
        meta:{
          label:'开票列表查看'
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/companypayment2/alreadyWaybill"
          ),
      },
    ],
  },
  // {
  //   path: "/exportExc",
  //   // name:'waybill',
  //   component: Layout,
  //   redirect: "/exportExc/exportExc",
  //   children: [
  //     {
  //       path: "exportExc",
  //       name: "数据导出",
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "page" */ "@/views/chain/companywaybill2/exportExc"
  //         ),
  //     },
  //   ],
  // },
  {
    path: "/owner/waybill",
    // name:'waybill',
    component: Layout,
    redirect: "/owner/waybill",
    children: [
      {
        path: "/owner/waybill",
        name: "waybill",
        meta:{
          label:'运单查看'
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/navvyOwner/businessStatis/waybill"
          ),
      },
    ],

  },
  {
    path: "/owner/workTime",
    name:'workTime',
    component: Layout,
    redirect: "/owner/workTime",
    children: [
      {
        path: "/owner/workTime",
        name: "ownerWorkTime",
        meta:{
          label:'挖机工时'
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/navvyOwner/navvyWorkTime/indexView"
          ),
      },
    ],
  },
  {
    path: "/scan",
    name:'scan',
    component: Layout,
    redirect: "/scan/scanCard",
    meta: {
      keepAlive: true,
    },
    children: [
      {
        path: "scanCard",
        name: "scanCard",
        meta: {
          label:'扫描结算'
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/companysettle2/scanCard"
          ),
      },
      {
        path: "scanQrCode",
        name: "scanQrCode",
        meta: {
          label:'小票结算'
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/companysettle2/scanQrCode"
          ),
      },
      {
        path: "historyCard",
        name: "historyCard",
        meta: {
          label:'历史交卡回执',
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/companysettle2/historyCard"
          ),
      },
      {
        path: "generateCardReceipt",
        name: "generateCardReceipt",
        meta: {
          label:'生成交卡回执',
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/companysettle2/generateCardReceipt"
          ),
      }

    ],
  },
  {
    path: "/useTicketsMonth",
    component: Layout,
    redirect: "/useTicketsMonth/index",
    children: [
      {
        path: "index",
        name: "useTicketsMonth",
        meta: {
          label:'本期用票(按月)',
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/outsideGarbageTicketCount/useTicketsMonth"
          ),
      },
    ],
  },
  {
    path: "/useTicketsDay",
    component: Layout,
    redirect: "/useTicketsDay/index",
    children: [
      {
        path: "index",
        name: "useTicketsDay",
        meta: {
          label:'本期用票(按日)',
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/outsideGarbageTicketCount/useTicketsDay"
          ),
      },
    ],
  },
  {
    path: "/directPayPriceSet",
    component: Layout,
    redirect: "/directPayPriceSet/index",
    children: [
      {
        path: "index",
        name: "directPayPriceSet",
        meta: {
          label:'直付价申请',
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/directPayPriceSet/index"
          ),
      },
    ],
  },
  {
    path: "/cardLedgerC",
    component: Layout,
    redirect: "/cardLedgerC/index",
    children: [
      {
        path: "index",
        name: "cardLedgerC",
        meta: {
          label:'C卡台账',
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/cardLedgerB/cardLedgerC"
          ),
      },
    ],
  },
  {
    path: "/soilChart",
    component: Layout,
    redirect: "/soilChart/index",
    children: [
      {
        path: "index",
        name: "soilChart",
        meta: {
          label:'出土构成',
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/projectCostAnalysis/detail/soilChart.vue"
          ),
      },
    ],
  },
  {
    path: "/transportChart",
    component: Layout,
    redirect: "/transportChart/index",
    children: [
      {
        path: "index",
        name: "transportChart",
        meta: {
          label:'运输成本',
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/projectCostAnalysis/detail/transportChart.vue"
          ),
      },
    ],
  },
  {
    path: "/machineChart",
    component: Layout,
    redirect: "/machineChart/index",
    children: [
      {
        path: "index",
        name: "machineChart",
        meta: {
          label:'机械成本',
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/projectCostAnalysis/detail/machineChart.vue"
          ),
      },
    ],
  },
  {
    path: "/garbageChart",
    component: Layout,
    redirect: "/garbageChart/index",
    children: [
      {
        path: "index",
        name: "garbageChart",
        meta: {
          label:'泥尾成本',
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/projectCostAnalysis/detail/garbageChart.vue"
          ),
      },
    ],
  },
  {
    path: "/planCompleteChart",
    component: Layout,
    redirect: "/planCompleteChart/index",
    children: [
      {
        path: "index",
        name: "planCompleteChart",
        meta: {
          label:'计划完成度',
        },
        component: () =>
          import(
            /* webpackChunkName: "page" */ "@/views/chain/projectCostAnalysis/detail/planCompleteChart.vue"
          ),
      },
    ],
  },

];
