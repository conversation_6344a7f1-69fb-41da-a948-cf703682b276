<template>
  <div class="avue-crud__menu">
    <div class="my-crud__left"
         v-if="vaildData(crud.tableOption.menuLeft,true)">
         <el-tag class="my-crud__tip"
              v-if="vaildData(crud.tableOption.tip,config.tip) && crud.tableOption.selection">
        <span class="avue-crud__tip-name">
          当前表格已选择
          <span class="avue-crud__tip-count">{{crud.selectLen}}</span>
          项
        </span>
        <el-button type="text"
                   size="small"
                   @click="crud.clearSelection"
                   v-permission="crud.getPermission('selectClearBtn')"
                   v-if="vaildData(crud.tableOption.selectClearBtn,config.selectClearBtn) && crud.tableOption.selection">清空</el-button>
        <slot name="tip"></slot>
      </el-tag>
      <el-button type="primary"
                 @click="crud.rowAdd"
                 :icon="crud.getBtnIcon('addBtn')"
                 :size="crud.isMediumSize"
                 v-permission="crud.getPermission('addBtn')"
                 v-if="vaildData(crud.tableOption.addBtn,config.addBtn)">
        <template v-if="!crud.isIconMenu">
          {{crud.tableOption.addBtnText||"新增"}}
        </template>
      </el-button>
      <el-button type="primary"
                 @click="rowExcel"
                 :icon="config.excelBtnIcon"
                 v-permission="crud.getPermission('excelBtn')"
                 :size="crud.isMediumSize"
                 v-if="vaildData(crud.tableOption.excelBtn,config.excelBtn)">
        <template v-if="!crud.isIconMenu">
          {{crud.tableOption.excelBtnText||"导出"}}
        </template>
      </el-button>
      <!-- <el-button type="primary"
                 @click="crud.rowCellAdd"
                 :icon="crud.getBtnIcon('addBtn')"
                 v-permission="crud.getPermission('addRowBtn')"
                 :size="crud.isMediumSize"
                 v-if="vaildData(crud.tableOption.addRowBtn,config.addRowBtn)">
        <template v-if="!crud.isIconMenu">
          {{crud.tableOption.addBtnText||"新增"}}
        </template>
      </el-button> -->
      <slot name="menuLeft"
            :size="crud.isMediumSize"></slot>
    </div>
    <div class="avue-crud__right"
         v-if="vaildData(crud.tableOption.menuRight,true)">

      <slot name="menuRight"
            :size="crud.isMediumSize"></slot>
      <!-- <el-button :icon="crud.getBtnIcon('excelBtn')"
                 circle
                 :size="crud.isMediumSize"
                 @click="rowExcel"
                 v-permission="crud.getPermission('excelBtn')"
                 v-if="vaildData(crud.tableOption.excelBtn,config.excelBtn)"></el-button>
      </el-button>
      <el-button :icon="crud.getBtnIcon('printBtn')"
                 circle
                 :size="crud.isMediumSize"
                 @click="rowPrint"
                 v-permission="crud.getPermission('printBtn')"
                 v-if="vaildData(crud.tableOption.printBtn,config.printBtn)"></el-button>
      </el-button> -->
      <el-button :icon="crud.getBtnIcon('refreshBtn')"
                 circle
                 :size="crud.isMediumSize"
                 @click="crud.refreshChange"
                 v-permission="crud.getPermission('refreshBtn')"
                 v-if="vaildData(crud.tableOption.refreshBtn,config.refreshBtn)"></el-button>
      <el-button :icon="crud.getBtnIcon('columnBtn')"
                 circle
                 :size="crud.isMediumSize"
                 @click="crud.$refs.dialogColumn.showBox()"
                 v-permission="crud.getPermission('columnBtn')"
                 v-if="vaildData(crud.tableOption.columnBtn,config.columnBtn)"
                ></el-button>
      <el-button :icon="crud.getBtnIcon('searchBtn')"
                 circle
                 :size="crud.isMediumSize"
                 @click="crud.$refs.headerSearch.handleSearchShow()"
                 v-if="(crud.$refs.headerSearch || {}).searchFlag&&vaildData(crud.tableOption.searchShowBtn,true)"></el-button>
      <el-button :icon="crud.getBtnIcon('filterBtn')"
                 circle
                 :size="crud.isMediumSize"
                 @click="crud.$refs.dialogFilter.handleShow()"
                 v-permission="crud.getPermission('filterBtn')"
                 v-if="vaildData(crud.tableOption.filterBtn,config.filterBtn)"></el-button>
    </div>
  </div>
</template>

<script>
import config from "../config";
import permission from '../permission';

export default {
  name: "crud",
  inject: ["crud"],
  data () {
    return {
      dateCreate: false,
      pickerOptions: {
        shortcuts: [{
          text: '今日',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime());
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '昨日',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一周',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      config: config
    };
  },
  created () {
  },
  directives: {
    permission
  },
  computed: {
    data () {
      if (this.crud.tableOption.selection) {
        return this.crud.tableSelect;
      } else {
        return this.crud.list;
      }
    }
  },
  methods: {
    //日期组件回调
    dateChange (val) {
      if (this.dateCreate) {
        this.crud.$emit("date-change", val);
      } else {
        this.dateCreate = true;
      }

    },
    rowExcel () {
      if (this.validatenull(this.data)) {
        this.$message.warning("请勾选要导出的数据");
        return;
      }
      console.log(this.$export)
      console.log((this.crud.tableOption.title||'')+this.$moment(new Date()).format("YYYY-MM-DD hh:mm:ss"));
      console.log(this.crud.columnOption);
      console.log(this.handleSum());
      this.$export.excel({
        title: (this.crud.tableOption.title||'')+this.$moment(new Date()).format("YYYY-MM-DD hh:mm:ss"),
        columns: this.crud.columnOption,
        data: this.handleSum()
      });
    },
    //计算统计
    handleSum () {
      const option = this.crud.option;
      const columnOption = this.crud.propOption;
      let count = 0;
      let sumsList = [...this.crud.sumsList];
      let data = []
      this.data.forEach(ele => {
        let obj = this.deepClone(ele);
        columnOption.forEach(column => {
          if (column.bind) {
            obj[column.prop] = getAsVal(obj, column.bind);
          }
          if (!this.validatenull(obj['$' + column.prop])) {
            obj[column.prop] = obj['$' + column.prop];
          }
        })
        data.push(obj);
      })
      if (option.index) count++;
      if (option.selection) count++;
      if (option.expand) count++;
      sumsList.splice(0, count);
      sumsList.splice(sumsList.length - 1, 1);
      if (option.showSummary) {
        let sumsObj = {};
        sumsList.forEach((ele, index) => {
          if ((columnOption[index] || {}).prop) {
            sumsObj[columnOption[index].prop] = ele;
          }
        });
        data.push(sumsObj);
      }
      return data;
    },
  }
};
</script>
<style lang="scss" scoped>
  .my-crud__left{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .my-crud__tip{
      height: 32px;
      line-height: 32px;
      margin-right: 16px;
    }
  }
</style>
