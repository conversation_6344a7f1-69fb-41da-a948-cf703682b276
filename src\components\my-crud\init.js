import {getByRoute,saveOrUpdate} from '@/api/chain/companycustompage'

export default function () {
  return {
    props: {
      defaults: {
        type: Object,
        default () {
          return {};
        }
      },
      option: {
        type: Object,
        required: true,
        default: () => {
          return {};
        }
      }
    },
    watch: {
      defaults: {
        handler (val) {
          this.objectOption = val;
        },
        deep: true
      },
      objectOption: {
        handler (val) {
          this.$emit('update:defaults', val)
        },
        deep: true
      },
      option: {
        handler () {
          this.init();
        },
        deep: true,
      },
    },
    data () {
      return {
        tableOption: {},
        defaultOption:{},
        objectOption: {},
        isComplete:false,
      };
    },
    created () {
      //如果要列表自定义，就必须有路由
      if(this.routerName){
        //先获取看看有没有保存
      getByRoute({route:this.routerName}).then(res=>{
          console.log(res);
          if(res.data.data){
            let pageTableOption =  JSON.parse(res.data.data.pageTableOption)
            console.log(pageTableOption);
            for (const key in this.objectOption) {
              if(key in pageTableOption){
                  this.$set(this.objectOption[key],'hide',pageTableOption[key].hide)
                  // this.objectOption[key].hide = pageTableOption[key].hide
                  if(pageTableOption[key].width){
                    this.objectOption[key].width = pageTableOption[key].width
                  }
                  if(pageTableOption[key].order){
                  this.objectOption[key].order = pageTableOption[key].order
                }
              }
            }
          }
          //加载完成
          this.isComplete = true
          saveOrUpdate({pageRoute:this.routerName,pageTableOption:JSON.stringify(this.objectOption)}).then(res=>{
            console.log(res);
          })
        })
      }else{
        this.isComplete = true
      }
      this.defaultOption = this.deepClone(this.option)
      this.init();
    },
    methods: {
      init () {
        this.tableOption = this.option;
      },
    }
  };
}
