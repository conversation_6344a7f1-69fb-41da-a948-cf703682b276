<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="70%"
      title="本期退票"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <avue-crud
          ref="crud"
          :data="tableData"
          :table-loading="tableLoading"
          :option="tableOption"
          v-model="form"
          :search.sync="search"
          @on-load="getPage"
          @search-change="getPage"
        >
          <template slot="menuLeft" slot-scope="{ row }">
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getRefundTickets } from "@/api/chain/outsideGarbageTicketCount";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  data() {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      form: {},
      tableData: [],
      tableLoading: false,
      search:{},
      tableOption: {
        dialogDrag: false,
        border: true,
        indexLabel: "序号",
        stripe: true,
        align: "center",
        // searchShow: false,
        index: true,
        labelWidth: 150,
        searchLabelWidth: 80,
        searchSpan: 8,
        excelBtn: true,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: true,
        delBtn: false,
        menu: false,
        searchMenuSpan: 6,
        index: false,
        refreshBtn: false,
        defaultSort: {
          prop: "operateDate",
          order: "ascending",
        },
        column: [
          {
            label: "退票类型",
            prop: "sourceRefund",
            search: true,
            type: "select",
            props: {
              label: "itemName",
              value: "itemValue",
            },
            dicUrl: "/chain/systemdictionaryitem/listDictionaryItem?dictionary=apply_source",
          },
          {
            label: "项目",
            prop: "projectInfoIds",
            search: true,
            type: "select",
            hide: true,
            showColumn: false,
            dataType: "string",
            searchMultiple: true,
            props: {
              label: "projectName",
              value: "id",
            },
            dicUrl: "/chain/projectinfo/listTicket",
          },
          {
            label: "项目",
            prop: "projectName",
          },
          {
            label: "泥尾",
            prop: "garbageIds",
            search: true,
            hide: true,
            type: "select",
            dataType: "string",
            searchMultiple: true,
            showColumn: false,
            props: {
              label: "names",
              value: "id",
            },
            dicUrl: "/chain/garbage/listByCompanyAuth",
          },
          {
            label: "泥尾",
            prop: "garbageName",
          },
          {
            label: "土质",
            prop: "soilTypes",
            type: "select",
            search: true,
            dataType: "string",
            searchMultiple: true,
            showColumn: false,
            hide: true,
            props: {
              label: "soilType",
              value: "soilType",
            },
            dicUrl:"/chain/companyticketpurchase/getCompanyTicketPurchaseSoilType",
          },
          {
            label: "土质",
            prop: "soilType",
          },
          {
            label: "退票数",
            prop: "qty",
          },
          {
            label: "审核数",
            prop: "auditsQty",
          },
          {
            label: "退票人",
            prop: "applyName",
          },
          {
            label: "退票时间",
            prop: "applyDatetime",
          },
          {
            label: "退票日期",
            prop: "searchDate",
            search:true,
            type:'date',
            valueFormat: 'yyyy-MM-dd',
            searchRange:true,
            hide:true,
            showColumn:false,
            pickerOptions: {
              // disabledDate(time) {
              //   return time.getTime() < Date.now();
              // },
              shortcuts: [{
                text: '最近一周',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '最近一个月',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '最近三个月',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '最近六个月',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
                  picker.$emit('pick', [start, end]);
                }
              },]
            }
          },
          {
            label: "审核人",
            prop: "auditsName",
          },
          {
            label: "审核时间",
            prop: "auditDatetime",
          },
        ],
      },
      once:true,//更改搜索参数前
    };
  },
  created() {},
  mounted() {
    console.log(this.info);
    this.search.projectInfoIds = this.info.projectInfoIds
    this.search.garbageIds = this.info.garbageIds
    this.search.soilTypes = this.info.soilTypes
    if(this.info.startDate&&this.info.endDate){
      this.search.searchDate = [this.info.startDate,this.info.endDate]
    }
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    getPage(obj,done) {
      this.tableLoading = true;
      let params = Object.assign({},this.search)
      if (params.hasOwnProperty("searchDate")) {
        console.log(params);
        params.startDate = params.searchDate[0];
        params.endDate = params.searchDate[1];
        delete params.searchDate;
      }
      //如果搜索条件一致需要增加传值过来的garbageType
      if(this.once&&params.startDate==this.info.startDate&&params.endDate==this.info.endDate
      &&params.projectInfoIds==this.info.projectInfoIds&&params.garbageIds==this.info.garbageIds&&params.soilTypes==this.info.soilTypes){
        params.garbageType = this.info.garbageType
      }else{
        this.once = false
      }
      getRefundTickets(params)
        .then((response) => {
          this.tableData = response.data.data
          this.tableLoading = false;
          done?done():''
        })
        .catch(() => {
          this.tableLoading = false;
          done?done():''
        });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .drawerHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  li {
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-weight: 700;
    margin-right: 30px;
  }
}

.demo-block-control {
  border-top: 1px solid #eaeefb;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;
  background-color: #fff;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  text-align: center;
  margin-top: -1px;
  color: #d3dce6;
  cursor: pointer;
  position: relative;

  &:hover {
    color: #409eff;
    background-color: #f9fafc;
  }
}
</style>
