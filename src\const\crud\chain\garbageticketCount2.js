export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  index:true,
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  dialogClickModal:false,
  excelBtn: false,
  printBtn: false,
  addBtn: false,
  viewBtn: false,
  searchSpan:6,
  searchMenuSpan: 6,
  menu:false,
  column: [
    {
      label: "项目名称",
      prop: "projectName",
      // sortable: true,
      search: true,
      overHidden:true,
    },
    {
      label: "泥尾",
      prop: "garbageName",
      // sortable: true,
      search: true,
      overHidden:true,
    },
    {
      label: "日期",
      prop: "searchDate",
      // sortable: true,
      type:'date',
      valueFormat: 'yyyy-MM-dd',
      searchRange:true,
      search:true,
      formatter: (val) => {
        return val.createDateStr;
      },
      overHidden:true,
    },
    {
      label: "领用数量",
      prop: "totalNum",
      overHidden:true,
      // sortable: true,
    },
    {
      label: "消耗数量",
      prop: "useNum",
      overHidden:true,
      // sortable: true,
    },
    {
      label: "剩余数量",
      prop: "lastNum",
      overHidden:true,
      // sortable: true,
    },
  ],
};
