export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  searchMenuSpan: 6,
  column: [
    {
      label: "创建时间",
      prop: "createDatetime",
      sortable: "custom",
      minWidth: 140,
      overHidden: true,
    },
    {
      label: "计划编号",
      prop: "planNo",
      sortable: "custom",
      search: true,
      minWidth: 180,
      overHidden: true,
    },
    // {
    //   label: "公司名称",
    //   prop: "companyName",
    //   search: true,
    //   minWidth: 160,
    //   overHidden: true,
    // },
    {
      label: "泥尾名称",
      prop: "garbageNames",
      search: true,
      minWidth: 160,
      overHidden: true,
    },
    // {
    //   label: "泥尾地址",
    //   prop: "garbageAddress",
    //   search: true,
    //   minWidth: 160,
    //   overHidden: true,
    // },
    {
      label: "结算周期",
      prop: "settleCycle",
      // search: true,
      sortable: "custom",
      type: "select",
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl:"/chain/systemdictionaryitem/listDictionaryItem?dictionary=match_settle_cycle",
      minWidth: 96,
      overHidden: true,
    },

    // {
    //   label: "工地地址",
    //   prop: "projectAddress",
    //   search: true,
    //   minWidth: 160,
    //   overHidden: true,
    // },
    {
      label: "计划收土方量(m³)",
      prop: "recycleSoilCube",
      minWidth: 120,
      overHidden: true,
    },
    {
      label: "土质类型",
      prop: "planSoil",
      search: true,
      minWidth: 120,
      overHidden: true,
    },
    // {
    //   label: "泥尾区域",
    //   prop: "garbageName",
    //   search: true,
    //   hide: true,
    //   showColumn: false,
    // },
    // {
    //   label: "车号",
    //   prop: "vehicleNumber",
    //   search: true,
    //   hide: true,
    //   showColumn: false,
    // },
    // {
    //   label: "运输方式",
    //   prop: "vehicleNumber",
    //   search: true,
    //   type: "select", // 下拉选择
    //   search: true,
    //   props: {
    //     label: "itemName",
    //     value: "itemValue",
    //   },
    //   dicUrl:
    //     "/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode",
    //   hide: true,
    //   showColumn: false,
    // },
    // {
    //   label: "单位",
    //   prop: "weightUnit",
    //   minWidth: 80,
    //   overHidden: true,
    // },
    // {
    //   label: "价格",
    //   prop: "price",
    //   minWidth: 80,
    //   overHidden: true,
    // },
    {
      label: "收土开始时间",
      prop: "recycleSoilTimeStart",
      minWidth: 100,
      overHidden: true,
    },
    {
      label: "收土结束时间",
      prop: "recycleSoilTimeEnd",
      minWidth: 100,
      overHidden: true,
    },
    // {
    //   label: "车型",
    //   prop: "goVehicleType",
    //   type: "select",
    //   search: true,
    //   props: {
    //     label: "itemName",
    //     value: "itemValue",
    //   },
    //   dicUrl: `/chain/systemdictionaryitem/listDictionaryItem?dictionary=go_vehicle_type`,
    //   hide:true,
    //   showColumn:false,
    //   minWidth: 100,
    //   overHidden: true,
    // },

    {
      label: "匹配状态",
      prop: "planStatus",
      search: true,
      type: "select",
      dicData: [
        {
          label: "匹配中",
          value: "2",
        },
        {
          label: "待确认",
          value: "3",
        },
        {
          label: "匹配成功",
          value: "4",
        },
        {
          label: "已取消",
          value: "5",
        },
      ],
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "撮合时间",
      prop: "matchStart",
      minWidth: 140,
      overHidden: true,
    },
    {
      label: "收土日期",
      prop: "searchDate",
      type: "select",
      search: true,
      display: false,
      type: "date",
      valueFormat: "yyyy-MM-dd",
      searchRange: true,
      hide: true,
      showColumn: false,
    },
  ],
};
