<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud" :page="page" :data="tableData" :permission="permissionList" :table-loading="tableLoading"
        :option="tableOption" v-model="form" @on-load="getPage" @refresh-change="refreshChange" @sort-change="sortChange"
        @search-change="searchChange">
        <template slot="menuLeft" slot-scope="{ size }">
          <el-button icon="el-icon-download" size="small" :loading="btnLoading" type="primary" @click="exOut">
            导出
          </el-button>
        </template>
        <template slot="menu" slot-scope="{ row }">
          <el-button type="text" icon="el-icon-notebook-2" v-if="permissions['chain:companyoutputsummary:view']" size="small" plain
            @click="getFlow(row)">
            查看</el-button>
        </template>
      </avue-crud>
    </basic-container>
    <flowView
        v-if="flowVisible"
        :visible.sync="flowVisible"
        :info="info"
      ></flowView>
  </div>
</template>

<script>
import { getPage,getObj} from '@/api/chain/companyoutputsummary'
import { tableOption } from '@/const/crud/chain/companyoutputsummary'
import { mapGetters } from 'vuex'
import { exportOut } from "@/util/down.js";
import flowView from "./flowView.vue";

export default {
  name: 'companyoutputsummary',
  components: {flowView },
  data () {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      btnLoading: false,
      info:{},
      flowVisible:false,
    }
  },
  created () {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList () {
      return {
        addBtn: this.permissions['chain:companyoutputsummary:add'] ? true : false,
        delBtn: this.permissions['chain:companyoutputsummary:del'] ? true : false,
        editBtn: this.permissions['chain:companyoutputsummary:edit'] ? true : false,
        viewBtn: this.permissions['chain:companyoutputsummary:get'] ? true : false
      };
    }
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage (page, params) {
      this.tableLoading = true
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
      }
      getPage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * 刷新回调
     */
    refreshChange (page) {
      this.getPage(this.page)
    },
    exOut () {
      let params = Object.assign({}, this.paramsSearch)
      if (params) {
        if (params.hasOwnProperty("searchDate")) {
          params.startDate = params.searchDate[0];
          params.endDate = params.searchDate[1];
          delete params.searchDate;
        }
      }
      let url = '/chain/companyoutputsummary/export'
      this.btnLoading = true
      exportOut(params, url, '产值记录').then(res => {
        this.btnLoading = false
      }).catch(() => {
        this.btnLoading = false
      })
    },
    getFlow(row) {
      getObj(row.id).then(res=>{
        this.info = res.data.data
        this.flowVisible = true;
      })
    },
  }
}
</script>

<style lang="scss" scoped></style>
