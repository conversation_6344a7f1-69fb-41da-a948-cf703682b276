<template>
  <div class="selectPersonnel">
    <el-dialog :visible.sync="visible"
               title="新增泥尾"
               :close-on-click-modal="false"
               width="700px"
               :before-close="oncancel">
      <avue-form :option="option"
                 ref="fleetForm"
                 v-model="form"
                 @submit="submit">
        <template slot="map"
                  slot-scope="{type}">
          <div style="padding-bottom: 20px">
            <el-amap-search-box class="search-box"
                                :search-option="searchOption"
                                :on-search-result="onSearchResult">
            </el-amap-search-box>
          </div>
          <el-amap vid="amapDemo2"
                   :center="center"
                   :amap-manager="amapManager"
                   :zoom="zoom"
                   :events="events"
                   class="amap-demo"
                   style="height: 300px; width: 100%">
            <el-amap-marker ref="marker"
                            vid="component-marker"
                            :position="marker.position"
                            :events="marker.events"
                            :visible="marker.visible"
                            :draggable="marker.draggable"></el-amap-marker>
          </el-amap>
        </template>
      </avue-form>
      <div slot="footer"
           class="dialog-footer">
        <el-button @click="oncancel">取 消</el-button>
        <el-button type="primary"
                   @click="$refs.fleetForm.submit()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {addObj} from "@/api/garbage/garbage";
import { AMapManager } from "vue-amap";
const amapManager = new AMapManager();
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    checkList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data () {
    return {
      form: {},
      option: {
        emptyBtn: false,
        submitBtn: false,
        labelWidth: 110,
        column: [
          {
            label: '消纳场所类型',
            prop: 'garbageType',
            type: 'select',   // 下拉选择
            search: true,
            order: 5,
            props: {
              label: "itemName",
              value: "itemValue",
            },
            dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=garbage_record_type',
            rules: [
              {
                required: true,
                message: '请输入消纳场所类型',
                trigger: 'blur'
              },
              {
                max: 255,
                message: '长度在不能超过255个字符'
              },
            ],
            width: 100,
            overHidden: true,
          },
          {
            label: '泥尾名称',
            prop: 'names',
            order: 1,
            sortable: true,
            search: true,
            rules: [
              {
                required: true,
                message: '请输入泥尾名称',
                trigger: 'blur'
              },
              {
                max: 500,
                message: '长度在不能超过500个字符'
              },
            ],
            minWidth: 160,
            overHidden: true,
          },
          {
            label: '泥尾地址',
            prop: 'address',
            order: 2,
            sortable: true,
            rules: [
              {
                required: false,
                message: '请选择泥尾地址',
                trigger: 'change'
              },
              {
                max: 3000,
                message: '长度在不能超过3000个字符'
              },
            ],
            minWidth: 160,
            overHidden: true,
          },
          {
            label: '入口坐标',
            prop: 'fence',
            order: 5,
            placeholder: '请在地图选择定位',
            disabled: true,
            sortable: true,
            rules: [
              {
                required: true,
                message: '请选择坐标',
                trigger: 'change'
              },
              {
                max: 3000,
                message: '长度在不能超过3000个字符'
              },
            ],
            width: 160,
            overHidden: true,
          },
          {
            label: '',
            prop: 'map',
            order: 99,
            span: 24,
            hide: true,
            disabled: true,
            sortable: true,
            showColumn: false,
          },
          {
            label: '泥土类型',
            prop: 'soilTypes',
            order: 3,
            sortable: true,
            type: 'select',   // 下拉选择
            multiple: true,
            dataType: "string",
            props: {
              label: "itemName",
              value: "itemValue",
            },
            dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=match_soil_type',
            rules: [
              {
                required: true,
                message: '请选择泥土类型',
                trigger: 'change'
              },
            ],
            width: 160,
            overHidden: true,
          },
          {
            label: '是否有效',
            prop: 'isDel',
            order: 4,
            sortable: true,
            type: 'select',   // 下拉选择
            search: true,
            dicData: [
              {
                label: '有效',
                value: '0'
              },
              {
                label: '无效',
                value: '1'
              },
            ],
            value: '0',
            rules: [
              {
                required: true,
                message: '逻辑删除',
                trigger: 'blur'
              },
              {
                max: 1,
                message: '长度在不能超过1个字符',
              },
            ],
            minWidth: 94,
            overHidden: true,
          },
          {
            label: '泥尾票设置',
            prop: 'garbageSet',
            type: 'radio',
            order: 6,
            span: 24,
            search: true,
            dicData: [{
              label: '无票出场',
              value: '0'
            }
              , {
              label: '内部票出场',
              value: '1'
            }
              , {
              label: '外部票出场',
              value: '2'
            }
            ],
            minWidth: 94,
            overHidden: true,
            value: '0'
          },
        ]
      },
      searchOption: {
        // 限制搜索城市的范围
        citylimit: false,
      },
      zoom: 14,
      center: [113.98074, 22.55251],
      marker: {
        position: [113.98074, 22.55251],
        visible: true,
        draggable: false,
      },
      amapManager,
      events: {
        click: (e) => {
          this.marker.position = [e.lnglat.lng, e.lnglat.lat];
          this.form.fence = e.lnglat.lng + "," + e.lnglat.lat;
        },
      },
    };
  },
  mounted () {
  },
  methods: {
    oncancel () {
      this.$emit("update:visible", false);
    },
    onSearchResult(pois) {
      this.center = [pois[0].location.lng, pois[0].location.lat];
      this.marker.position = [pois[0].location.lng, pois[0].location.lat];
      this.form.fence = pois[0].location.lng + "," + pois[0].location.lat;
    },
    submit (form, done) {
      console.log(form);
      addObj(form).then(res => {
        this.$emit('complete')
      })
      done()
    },
  },
};
</script>

<style scoped lang="less">
</style>
