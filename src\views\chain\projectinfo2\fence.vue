<template>
  <el-dialog title="获取位置信息" :data="visible" :visible.sync="visible" :close-on-click-modal="false"
    :close-on-press-escape="true" append-to-body center width="50%" :before-close="handleClose">
    <div class="gd-map">
      <avue-form :option="option" ref="form" v-model="form"> </avue-form>

      <div style="padding-bottom: 20px">
        <el-amap-search-box class="search-box" :search-option="searchOption" :on-search-result="onSearchResult">
        </el-amap-search-box>
      </div>
      <el-amap vid="amapDemo" :center="center" :amap-manager="amapManager" :zoom="zoom" :events="events"
        class="amap-demo" style="height: 300px; width: 100%">
        <el-amap-marker ref="marker" vid="component-marker" :position="marker.position" :events="marker.events"
          :visible="marker.visible" animation="AMAP_ANIMATION_BOUNCE" :draggable="marker.draggable"></el-amap-marker>
        <el-amap-circle :center="marker.position" :radius="form.fenceRange" :fillOpacity="0.2" :bubble='true'
          fillColor="blue" strokeColor="blue"></el-amap-circle>
        <el-amap-marker ref="projectMarker" vid="project-marker" :position="projectMarker.position"
          :events="projectMarker.events" :visible="projectMarker.visible"
          :draggable="projectMarker.draggable"></el-amap-marker>
        <el-amap-circle :center="projectMarker.position" :radius="projectRange" :fillOpacity="0.2"></el-amap-circle>
        <el-amap-info-window :position="projectMarker.position" :visible="isVisible"
          :content="this.isFence ? '<div>项目地址与新增围栏请勿重叠</div>' : '<div>项目地址与新增装货起点请勿重叠</div>'"
          :offset="[5, -15]"></el-amap-info-window>
        <div v-if="fenceList && fenceList.length > 0">
          <div v-for="(item, index) in fenceList" :key="index">
            <el-amap-marker vid="component-marker" :position="item.fenceGps.split(',')"
              :label="{ content: item.fenceName, offset: [0, 5] }" :visible="true" :draggable="false">
            </el-amap-marker>
            <el-amap-circle :center="item.fenceGps.split(',')" :radius="item.fenceRange" :fillOpacity="0.2"
              :bubble='true' fillColor="blue" strokeColor="blue"></el-amap-circle>
          </div>
        </div>
      </el-amap>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" style="height: 40px; margin-top: 20px">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { AMapManager } from "vue-amap";
const amapManager = new AMapManager();
export default {
  props: {
    //格式'113.94448,22.55736'
    coords: {
      type: String,
      default () {
        return "";
      },
    },
    //项目的gps
    projectFence: {
      type: String,
      default () {
        return "";
      },
    },
    //项目的gps范围
    projectRange: {
      type: Number,
      default () {
        return 500;
      },
    },
    //是否电子围栏  否则是装货起点
    isFence: {
      type: Boolean,
      default () {
        return false;
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
    fenceList: {
      type: Array,
      default () {
        return [];
      },
    },
  },
  data () {
    return {
      searchOption: {
        // 限制搜索城市的范围
        citylimit: false,
      },
      form: {
        fenceName: "",
        fenceAddress: "",
        fenceRange: 500,
        fenceGps: "",
      },
      option: {
        labelWidth: 110,
        position: "left",
        emptyBtn: false,
        submitBtn: false,
        emptyIcon: "el-icon-close",
        column: [
          {
            label: this.isFence ? "围栏名称" : "装货起点名称",
            prop: "fenceName",
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "blur",
              },
            ],
          },
          {
            label: "范围(米)",
            prop: "fenceRange",
            span: 24,
            controlsPosition: "",
            type: "number",
            minRows: 100,
            maxRows: 99999999,
            precision: 0,
            rules: [
              {
                required: true,
                message: "请输入",
                trigger: "change",
              },
            ],
          },
          {
            label: "经纬度",
            prop: "fenceGps",
            span: 24,
            disabled: true,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
          {
            label: "地址",
            prop: "fenceAddress",
            span: 24,
            disabled: true,
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      zoom: 14,
      center: [113.98074, 22.55251],
      marker: {
        position: [113.98074, 22.55251],
        visible: true,
        draggable: false,
      },
      projectMarker: {
        position: [113.98074, 22.55251],
        visible: true,
        draggable: false,
      },
      amapManager,
      events: {
        click: (e) => {
          this.center = [e.lnglat.lng, e.lnglat.lat];
          this.marker.position = [e.lnglat.lng, e.lnglat.lat];
          this.form.fenceGps = e.lnglat.lng + "," + e.lnglat.lat;
          this.getFormattedAddress();
        },
      },
      isVisible: true,
      winInfo: {},
    };
  },
  created () { },
  destroyed () { },
  mounted () {
    let coord = this.coords;
    if (coord && coord != "" && coord.split(",").length == 2) {
      this.center = coord.split(",");
      this.marker.position = this.center;
      this.form.fenceGps = this.center[0] + "," + this.center[1];
      this.getFormattedAddress();
    }
    if (
      this.projectFence &&
      this.projectFence != "" &&
      this.projectFence.split(",").length == 2
    ) {
      this.projectMarker.position = this.projectFence.split(",");
    }
    setTimeout(() => {
      this.isVisible = false
    }, 5000)
  },
  beforeDestroy () { },
  computed: {},
  methods: {
    onSearchResult (pois) {
      this.marker.position = [pois[0].location.lng, pois[0].location.lat];
      this.form.fenceGps = pois[0].location.lng + "," + pois[0].location.lat;
      this.isVisible = false
      setTimeout(() => {
        this.center = [pois[0].location.lng, pois[0].location.lat];
      }, 100)
    },
    getFormattedAddress () {
      AMap.plugin("AMap.Geocoder", () => {
        let GeocoderOptions = {
          city: "全国",
        };
        let geocoder = new AMap.Geocoder(GeocoderOptions);
        geocoder.getAddress(this.center, (status, result) => {
          if (status === "complete" && result.info === "OK") {
            this.form.fenceAddress = result.regeocode.formattedAddress;
          } else {
            this.form.fenceAddress = "";
          }
        });
      });
    },
    // 计算距离
    getDistance: (lat1, lng1, lat2, lng2) => {
      var radLat1 = (lat1 * Math.PI) / 180.0;
      var radLat2 = (lat2 * Math.PI) / 180.0;
      var a = radLat1 - radLat2;
      var b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
      var s =
        2 *
        Math.asin(
          Math.sqrt(
            Math.pow(Math.sin(a / 2), 2) +
            Math.cos(radLat1) *
            Math.cos(radLat2) *
            Math.pow(Math.sin(b / 2), 2)
          )
        );
      s = s * 6378.137; // EARTH_RADIUS;
      s = Math.round(s * 10000) / 10000;
      return s;
    },
    handleClose () {
      this.$emit("close");
    },
    handleSubmit () {
      this.$refs.form.validate((valid, loading) => {
        loading()
        if (valid) {
          //计算距离不能与项目地址重叠
          let distL = this.getDistance(this.marker.position[1], this.marker.position[0], this.projectMarker.position[1], this.projectMarker.position[0])
          let range = this.form.fenceRange + this.projectRange
          if (distL * 1000 <= range) {
            this.$message.error(this.isFence ? '围栏' : '装货起点' + "地址不能与项目地址重叠")
            return false
          }
          this.$emit("getLocation", JSON.stringify(this.form));
        } else {
          this.$message.error("请完善信息");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.gd-map {
  margin-bottom: 20px;
}

/deep/ .el-dialog__footer {
  padding-top: 0;
}

/deep/ .el-dialog__body {
  padding-bottom: 0px;
}
</style>
