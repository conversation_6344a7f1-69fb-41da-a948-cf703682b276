export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  searchShow: true,
  excelBtn: false,
  printBtn: false,
  viewBtn: true,
  searchSpan: 8,
  searchMenuSpan: 6,
  index:true,
  menuWidth:120,
  column: [
    {
      label: "项目",
      prop: "projectInfoId",
      type: "select", // 下拉选择
      search: true,
      props: {
        label: "projectName",
        value: "id",
      },
      dicUrl: "/chain/projectinfo/list",
      searchFilterable: true, //是否可以搜索
      minWidth: 150,
      overHidden: true,
      formatter:(val)=>{
        return val.projectName
      }
    },
    {
      label: "班次时间",
      prop: "shiftTime",
      minWidth: 140,
      overHidden: true,
      sortable: "custom",
    },
    {
      label: "班次类型",
      prop: "shiftType",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "产值合计(元)",
      prop: "totalPrice",
      minWidth: 80,
      overHidden: true,
    },
    {
      label: "发起时间",
      prop: "createDatetime",
      minWidth: 140,
      overHidden: true,
      sortable: "custom",
    },
    {
      label: "修改日期时间",
      prop: "updateDatetime",
      hide: true,
      minWidth: 140,
      overHidden: true,
    },
    {
      label: "产值状态",
      prop: "status",
      search:true,
      type: "select",
      dicData: [
        {
          label: "待审核",
          value: "1",
        },
        {
          label: "已审核",
          value: "2",
        },
        {
          label: "已驳回",
          value: "3",
        },
      ],
    },
    {
      label: "我的审核",
      prop: "approveStatus",
      search:true,
      type: "select",
      hide:true,
      showColumn:false,
      dicData: [
        {
          label: "待审核",
          value: "1",
        },
        {
          label: "已审核",
          value: "2",
        },
        {
          label: "已驳回",
          value: "3",
        },
      ],
    },
    {
      label: "发起时间",
      prop: "searchDate",
      type: "date",
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd',
      hide:true,
      showColumn:false,
    },
  ],
};
