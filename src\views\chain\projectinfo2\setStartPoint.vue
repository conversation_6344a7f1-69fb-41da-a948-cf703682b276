<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%" title="设置装货起点" :data="visible" :visible.sync="visible" :before-close="cancelModal">
      <basic-container>
        装货起点自动签单：
        <el-switch v-model="setValue" active-value="1" inactive-value="0" @change="changeSwitch" active-text="是" inactive-text="否">
        </el-switch>
        <div style="margin-top: 30px;">
          自动装车间隔时长：
          <el-input-number v-model="generalCargoInterval" @blur="handleChange" :step="1" step-strictly size="small" :min="0" :max="9999999" :controls="false" label="请输入 自动装车间隔时长"></el-input-number>
        </div>
      </basic-container>
      <basic-container>
        <avue-crud ref="crud" :page.sync="page" :data="tableData" :table-loading="tableLoading" :option="option"
          @on-load="getPage" @refresh-change="refreshChange" @sort-change="sortChange" @search-change="searchChange">
          <template slot="menuLeft" slot-scope="scope">
            <el-button icon="el-icon-plus" type="primary" size="small" @click="add">新增装货起点</el-button>
          </template>
          <template slot="menu" slot-scope="scope">
            <el-button icon="el-icon-delete" type="text" size="small" @click="delObj(scope.row)">删除</el-button>
          </template>
        </avue-crud>
      </basic-container>
      <fence v-if="mapVisible" :visible.sync="mapVisible" @close="closePosition" @getLocation="getLocation"
        :isFence="false" :fenceList="fenceList" :projectFence="this.info.electronicFence" :projectRange="this.info.electronicRange"></fence>
    </el-drawer>
  </div>
</template>

<script>
import {
  getProjectInfoSdkFencePage,
  saveGeneralCargoSdkFence,
  delFence,
} from "@/api/chain/projectinfo";
import fence from "./fence.vue";
import { updateGeneralCargoMode,updateGeneralCargoInterval } from "../../../api/chain/projectinfo";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  components: {
    fence,
  },
  data () {
    return {
      dialogForm: {},
      tableData: [],
      tableLoading: false,
      option: {
        labelWidth: 110,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchMenuSpan: 6,
        column: [
          {
            label: "装货起点名称",
            prop: "fenceName",
            search: true,
            minWidth: 200,
            searchSpan: 8,
            searchLabelWidth: 110,
            overHidden: true,
          },
          {
            label: "经纬度",
            prop: "fenceGps",
            minWidth: 130,
            overHidden: true,
          },
          {
            label: "地址",
            prop: "fenceAddress",
            minWidth: 180,
            overHidden: true,
          },
          {
            label: "范围",
            prop: "fenceRange",
            minWidth: 80,
            overHidden: true,
          },

        ],
      },
      mapVisible: false,
      btnLoading: false,
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch: {},
      setValue:"0",
      fenceList:[],
      generalCargoInterval:30,
    };
  },
  created () { },
  mounted () {
    this.dialogForm = this.info;
    this.generalCargoInterval = this.info.generalCargoInterval
    this.setValue = this.info.generalCargoMode
  },
  methods: {
    searchChange (params, done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange (val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage (page, params) {
      this.tableLoading = true
      getProjectInfoSdkFencePage(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        projectInfoId: this.info.id
      }, params, this.paramsSearch)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * 刷新回调
     */
     refreshChange(page) {
      this.getPage(this.page);
    },
    cancelModal () {
      this.$emit("refreshChange");
      this.$emit("update:visible", false);
    },
    changeSwitch(){
      console.log(this.setValue);
      let param = {
        projectInfoId: this.info.id,
        generalCargoMode: this.setValue,
      };
      updateGeneralCargoMode(param).then(res=>{
        this.$message.success("操作成功");
      }).catch(()=>{
        this.setValue= this.setValue=="1"?"0":"1"
      })
    },
    add(){
      this.tableLoading = true
      getProjectInfoSdkFencePage(Object.assign({
        size: -1,
        projectInfoId: this.info.id
      })).then(response => {
        this.fenceList = response.data.data.records
        this.tableLoading = false
        this.mapVisible = true
      }).catch(() => {
        this.tableLoading = false
      })
    },
    getLocation (obj) {
      let data = JSON.parse(obj);
      let param = {
        projectInfoId: this.info.id,
        companyAuthId: this.info.companyAuthId,
        fenceName: data.fenceName,
        fenceRange: data.fenceRange,
        fenceGps: data.fenceGps,
        fenceAddress: data.fenceAddress,
      };
      //添加装货点
      saveGeneralCargoSdkFence(param)
        .then((res) => {
          this.$message.success("新增装货点成功");
          this.getPage(this.page)
          this.mapVisible = false;
        })
    },
    closePosition () {
      this.mapVisible = false;
    },
    delObj (row) {
      // delFence
      let _this = this;
      this.$confirm("是否确认删除此装货点？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delFence(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page)
        })
        .catch(function (err) { });
    },
    handleChange(val){
      console.log(val,'val');
      // generalCargoInterval
      console.log(this.generalCargoInterval);
      let param = {
        projectInfoId: this.info.id,
        generalCargoInterval: this.generalCargoInterval,
      };
      updateGeneralCargoInterval(param).then(res=>{
        this.$message.success("自动装车间隔时间修改成功");
      })
    },
  },
};
</script>

<style lang="scss" scoped></style>
