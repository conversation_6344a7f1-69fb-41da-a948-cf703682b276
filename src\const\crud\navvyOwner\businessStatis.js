export const tableOption = {
  dialogDrag: true,
  border: true,
  index: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  menu: false,
  align: "center",
  menuType: "text",
  searchShow: true,
  searchSpan: 8,
  searchLabelWidth: 100,
  excelBtn: false,
  printBtn: false,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchMenuSpan: 6,
  defaultSort: {
    prop: "paymentNo",
    order: "descending",
  },
  column: [
    {
      label: "项目",
      prop: "projectInfoName",
      search: true,
      sortable: "custom",
      minWidth:180,
      overHidden:true,
    },
    {
      label: "挖机车主",
      prop: "ownerName",
      search: true,
      minWidth:90,
      overHidden:true,
    },
    {
      label: "挖机车主手机",
      prop: "ownerMobile",
      minWidth:100,
      overHidden:true,
    },
    {
      label: "机械型号",
      prop: "machineCode",
      search: true,
      minWidth:120,
      overHidden:true,
    },
    {
      label: "挖机司机",
      prop: "inStaffName",
      search: true,
      minWidth:80,
      overHidden:true,
    },
    {
      label: "上班时间",
      type:'date',
      prop: "dateRange",
      valueFormat: 'yyyy-MM-dd',
      searchRange:true,
      search:true,
      showColumn:false,
      hide:true,
      display:false,
    },
    {
      label: "属在企业",
      prop: "companyAuthName",
      search: true,
      minWidth:180,
      overHidden:true,
    },
    {
      label: "运单数",
      prop: "waybillCount",
      slot: true,
      minWidth:80,
      overHidden:true,
    },
    {
      label: "工时(小时)",
      prop: "workTimeCount",
      slot: true,
      minWidth:80,
      overHidden:true,
    },

  ],
};
