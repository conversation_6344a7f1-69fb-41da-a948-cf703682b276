<template>
  <div class="dashboard">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="margin-right: 20px">当月数据</span>
        <el-date-picker
          @change="getBoard"
          v-model="yearMonth"
          type="month"
          format="yyyy-MM"
          value-format="yyyy-MM"
          placeholder="选择月"
        >
        </el-date-picker>
      </div>
      <panel-group :info="monthInfo" />
    </el-card>
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>累计数据（所有数据）</span>
      </div>
      <panel-group :info="info" />
    </el-card>
  </div>
</template>

<script>
import PanelGroup from "./components/PanelGroup";
import { garbageManagerStatistics } from "@/api/chain/board";
import moment from "moment";
export default {
  name: "wel",
  components: {
    PanelGroup,
  },
  data() {
    return {
      info: {},
      monthInfo: {},
      // options: [],
      // time: "",
      yearMonth: "",
    };
  },
  computed: {},
  watch: {},
  created() {
    document.documentElement.style.fontSize = "16px";
    var date = new Date();
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    month = month < 10 ? "0" + month : month;
    this.yearMonth = year.toString() + "-" + month.toString();
    // this.options = Array.apply(0, Array(12)).map(function(_,i){return year.toString() +'-'+(i<9?'0'+(i+1):i+1)})
    this.getBoard(this.yearMonth);
    // this.getBoardAll()
  },
  methods: {
    getBoard(yearMonth) {
      console.log(yearMonth);
      garbageManagerStatistics({ yearMonth }).then((res) => {
        console.log(res);
        // this.monthInfo = res.data.data;
        this.monthInfo = {
          waybillCount: res.data.data.currentWaybill || 0,
          garbageCount: res.data.data.currentGarbage || 0,
        };
        this.info = {
          waybillCount: res.data.data.totalWaybill || 0,
          garbageCount: res.data.data.totalGarbage || 0,
        };
      });

    },
    // getBoardAll(yearMonth) {
    //   queryMachineOwnerPcIndexStat().then((res) => {
    //     console.log(res);
    //     // this.info = res.data.data;
    //     this.info = {
    //       waybillCount: res.data.data.totalWaybill || 0,
    //       garbageCount: res.data.data.totalGarbage || 0,
    //     };
    //   });
    // },
  },
};
</script>

<style lang="scss">
.dashboard {
  padding: 0px 10px;
  .el-card__body {
    background-color: #f5f5f5;
    padding: 20px 20px 10px;
  }
}
</style>
