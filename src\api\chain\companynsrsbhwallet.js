import request from '@/router/axios'

export function getPage(data) {
    return request({
        url: '/chain/companynsrsbhwalletdetail/getInfoByWhere',
        method: 'post',
        data
    })
}
//查询企业所有税洼地
export function getBranchWalletList(obj) {
    return request({
        url: '/chain/companynsrsbhwallet/getBranchWalletList',
        method: 'post',
        data: obj
    })
}
//钱包明细
export function getCompanyWalletUnInfo(obj) {
    return request({
        url: '/chain/companynsrsbhwallet/getCompanyWalletUnInfo',
        method: 'get',
        params: obj
    })
}
//充值
export function saveForWallet(obj) {
    return request({
        url: '/chain/companyauthrecharge/saveForWallet',
        method: 'post',
        data: obj
    })
}
//查看充值，退款凭证
export function getProof(obj) {
    return request({
        url: '/chain/companynsrsbhwallet/getProof',
        method: 'get',
        params: obj
    })
}
//查看充值，退款凭证
export function getEvidence(obj) {
    return request({
        url: '/chain/companynsrsbhwalletdetail/getEvidence',
        method: 'post',
        data: obj
    })
}
