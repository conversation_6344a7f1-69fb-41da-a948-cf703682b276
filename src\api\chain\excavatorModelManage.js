import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companymachine/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companymachine',
        method: 'post',
        data: obj
    })
}


export function delObj(id) {
    return request({
        url: '/chain/companymachine/disable/' + id,
        method: 'get'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companymachine',
        method: 'put',
        data: obj
    })
}
export function importExcel(data) {
  return request({
      url: '/chain/companymachine/import',
      method: 'post',
      data
  })
}
