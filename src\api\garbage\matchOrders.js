import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/matchorder/page',
        method: 'get',
        params: query
    })
}

export function getObj(id) {
  return request({
    url: '/chain/matchorder/getProgress/' + id,
    method: 'get'
  })
}

export function getWaybillList(query) {
    return request({
        url: '/chain/matchorder/getWaybillList',
        method: 'get',
        params: query
    })
}
export function cancelOrder(id) {
  return request({
      url: '/chain/matchorder/cancel/'+id,
      method: 'get',
  })
}
