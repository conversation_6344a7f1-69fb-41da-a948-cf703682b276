<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        :row-class-name="cellClassNameFn"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="handleDel"
        @sort-change="sortChange"
        @search-change="searchChange"
        @selection-change="handleSelectionChange"
      >
        <template slot="searchMenu" slot-scope="scope">
          <el-button
            v-if="permissions['chain:companysettle:export']"
            icon="el-icon-download"
            size="small"
            @click="exportExc(scope.row, scope.index)"
          >
            导出</el-button
          >
          <el-button
            v-if="permissions['chain:companysettle:scan']"
            icon="el-icon-view"
            type="primary"
            size="small"
            @click="scanCard"
          >
            扫描电子结算卡二维码结算</el-button
          >
          <el-button
            v-if="permissions['chain:companysettle:scan']"
            icon="el-icon-view"
            type="primary"
            size="small"
            @click="scanQrCode"
          >
            扫描小票二维码结算</el-button
          >
        </template>
        <template slot="menuLeft" slot-scope="scope">
          <div class="statistics">
            <label>运单总数：</label><span>{{ statisForm.total }}</span>
            <label>已核算运单数：</label><span>{{ statisForm.settle }}</span>
            <label>已核算运单金额(元)：</label
            ><span>{{ statisForm.amtSettle }}</span>
            <label>待核算运单数：</label><span>{{ statisForm.unsettle }}</span>
            <label>驳回运单数：</label><span>{{ statisForm.reject }}</span>
          </div>
        </template>
        <template slot="header" slot-scope="{ row }">
          <div
            style="
              display: inline-block;
              position: relative;
              top: -3px;
              margin-left: 10px;
            "
          >
            <el-button
              icon="el-icon-share"
              size="mini"
              type="primary"
              :disabled="multipleSelection.length == 0"
              :loading="tableLoading"
              @click="batchMerge"
            >
              合并结算单
            </el-button>
          </div>
        </template>
        <template slot="projectInfoId" slot-scope="scope">
          <span>{{ scope.row.projectName }}</span>
        </template>
        <template slot="waybillCnt" slot-scope="scope">
          <div
            style="color: #409eff; cursor: pointer"
            @click="waybillDetail(scope.row)"
          >
            {{ scope.row.waybillCnt }}
          </div>
        </template>
        <template slot="menu" slot-scope="scope">
          <el-button
            type="text"
            v-if="
              permissions['chain:companysettle:check'] &&
              (scope.row.status == 1 || scope.row.status == 5)
            "
            icon="el-icon-document"
            size="small"
            plain
            @click="billAccount(scope.row, scope.index, 1)"
          >
            账单核算</el-button
          >
          <el-button
            type="text"
            v-if="
              permissions['chain:companysettle:check'] &&
              (scope.row.status == 2 ||
                scope.row.status == 3 ||
                scope.row.status == 9)
            "
            icon="el-icon-document"
            size="small"
            plain
            @click="billAccount(scope.row, scope.index, 2)"
          >
            查看账单</el-button
          >
          <el-button
            type="text"
            v-if="
              permissions['chain:companysettle:check']
            "
            icon="el-icon-document"
            size="small"
            plain
            @click="viewSettleAccountSheet(scope.row, scope.index, 2)"
          >
            查看结算凭证</el-button
          >
          <el-dropdown
            size="mini"
            style="margin-left: 4px"
            v-if="
              (permissions['chain:companysettle:get'] &&
                (scope.row.status == 2 ||
                  scope.row.status == 3 ||
                  scope.row.status == 9)) ||
              (permissions['chain:companysettle:reject'] &&
                (scope.row.status == 1 || scope.row.status == 5)) ||
              (permissions['chain:companysettle:receiptInfo'] && isCheckOpen) ||
              (permissions['chain:companysettle:createPlan'] &&
                (scope.row.status == 1 ||
                  scope.row.status == 2 ||
                  scope.row.status == 3 ||
                  scope.row.status == 9)) ||
              permissions['chain:companysettle:viewPlan'] ||
              (permissions['chain:companysettle:viewProcess'] &&
                (scope.row.status == 2 ||
                  scope.row.status == 3 ||
                  scope.row.status == 9))
            "
          >
            <el-button type="text" size="mini"
              >更多功能<i class="el-icon-arrow-down el-icon--right"></i
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <div style="display: flex; padding: 2px 10px">
                <!-- <el-button type="text"
                           v-if="permissions['chain:companysettle:reject'] &&(scope.row.status == 1 || scope.row.status == 5)"
                           icon="el-icon-document-remove"
                           size="small"
                           plain
                           @click="paymentDocument(scope.row, scope.index)">
                  自主支付凭证登记</el-button> -->
                <el-button
                  type="text"
                  v-if="
                    permissions['chain:companysettle:get'] &&
                    (scope.row.status == 2 ||
                      scope.row.status == 3 ||
                      scope.row.status == 9)
                  "
                  icon="el-icon-view"
                  size="small"
                  plain
                  @click="viewAccountSheet(scope.row, scope.index)"
                >
                  查看核算单</el-button
                >
                <el-button
                  type="text"
                  v-if="
                    permissions['chain:companysettle:reject'] &&
                    (scope.row.status == 1 || scope.row.status == 5)
                  "
                  icon="el-icon-document-remove"
                  size="small"
                  plain
                  @click="rejectReq(scope.row, scope.index)"
                >
                  驳回申请</el-button
                >
                <el-button
                  type="text"
                  v-if="
                    permissions['chain:companysettle:receiptInfo'] &&
                    isCheckOpen
                  "
                  icon="el-icon-document-remove"
                  size="small"
                  plain
                  @click="receiptInfo(scope.row, scope.index)"
                >
                  回执信息</el-button
                >
                <el-button
                  icon="el-icon-plus"
                  size="small"
                  type="text"
                  plain
                  :loading="tableLoading"
                  v-if="
                    permissions['chain:companysettle:createPlan'] &&
                    (scope.row.status == 1 ||
                      scope.row.status == 2 ||
                      scope.row.status == 3 ||
                      scope.row.status == 9)
                  "
                  @click="createPlan(scope.row)"
                >
                  创建计划
                </el-button>
                <el-button
                  icon="el-icon-view"
                  size="small"
                  type="text"
                  plain
                  :loading="tableLoading"
                  v-if="permissions['chain:companysettle:viewPlan']"
                  @click="viewPlanInfo(scope.row)"
                >
                  查看计划
                </el-button>
                <el-button
                  icon="el-icon-view"
                  size="small"
                  type="text"
                  plain
                  :loading="tableLoading"
                  v-if="
                    permissions['chain:companysettle:viewProcess'] &&
                    (scope.row.status == 2 ||
                      scope.row.status == 3 ||
                      scope.row.status == 9)
                  "
                  @click="viewProcess(scope.row)"
                >
                  查看流程
                </el-button>
              </div>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </avue-crud>
    </basic-container>
    <!-- 查看运单 -->
    <el-dialog title="运单详情" :visible.sync="viewVisible">
      <el-table :data="waybillData">
        <el-table-column
          v-for="(item, index) in column"
          :key="index"
          :property="item.property"
          :label="item.label"
          width="105px"
        ></el-table-column>
      </el-table>
    </el-dialog>
    <!-- 驳回申请 -->
    <el-dialog
      title="驳回结算"
      class="rejectDialog"
      :visible.sync="rejectVisible"
      width="50%"
      center
      :close-on-click-modal="false"
    >
      <el-form
        :model="rejectForm"
        ref="rejectForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="结算单号：">
              <span>{{ rejectForm.settleNo }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结算申请人：">
              <span>{{ rejectForm.agentName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="项目名称：">
              <span>{{ rejectForm.projectName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运单数量：">
              <span>{{ rejectForm.waybillCnt }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="操作人：">
              <span>{{ rejectForm.settleName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item :required="true" label="驳回原因："> </el-form-item>
          </el-col>
        </el-row>
        <el-form-item
          prop="rejectedRemark"
          :rules="{
            required: true,
            message: '请输入驳回原因',
            trigger: 'blur',
          }"
          label-width="0px"
        >
          <el-input
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 12 }"
            placeholder="请输入内容"
            v-model="rejectForm.rejectedRemark"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <div style="text-align: center">
          <el-button
            :loading="visibleBtnLoading"
            icon="el-icon-circle-check"
            size="small"
            type="primary"
            @click="confirmReject"
          >
            确认驳回</el-button
          >
          <el-button
            icon="el-icon-circle-close"
            size="small"
            @click="rejectVisible = false"
          >
            取消</el-button
          >
        </div>
      </template>
    </el-dialog>
    <!-- 账单核算 -->
    <bill-account
      v-if="billVisible"
      :infoForm="infoForm"
      v-on:searchData="refreshChange"
      :visible.sync="billVisible"
    ></bill-account>
    <!-- 查看核算单 -->
    <account-sheet
      v-if="accountVisible"
      :accountForm="accountForm"
      v-on:viewWaybill="viewWaybill"
      :visible.sync="accountVisible"
    ></account-sheet>
    <!-- 回执信息 -->
    <receipt-info
      v-if="receiptVisible"
      :info="info"
      :visible.sync="receiptVisible"
    ></receipt-info>
    <!-- 资金计划 -->
    <createPlan
      v-if="planVisible"
      :infoForm="infoForm"
      :visible.sync="planVisible"
    ></createPlan>
    <!-- 查看核算单 -->
    <view-plan
      v-if="viewPlanVisible"
      :info="accountForm"
      :visible.sync="viewPlanVisible"
    ></view-plan>
    <!-- 结算凭证 -->
    <bill-account
      v-if="viewSettlePlanVisible"
      :settleInfoForm="settleAccountForm"
      v-on:searchData="refreshChange"
      :visible.sync="viewSettlePlanVisible"
    ></bill-account>
    <!-- 查看进度 -->
    <view-process
      v-if="viewProcessVisible"
      :accountForm="accountForm"
      :visible.sync="viewProcessVisible"
    ></view-process>
    <!-- //查看核算单特殊企业 -->
    <printPlan
      v-if="printVisible"
      fileName="结算单"
      :accountForm="accountForm"
      :visible.sync="printVisible"
    ></printPlan>
    <waybillDetail
      v-if="waybillVisible"
      :visible.sync="waybillVisible"
      :info="info"
    ></waybillDetail>
    <!-- 查看结算凭证 -->
    <settleSheet
      v-if="settleVisible"
       fileName="结算凭证"
      :visible.sync="settleVisible"
      :accountForm="settleAccountForm"
    >
    </settleSheet>
    <!--自主支付凭证登记  -->
    <el-dialog title="自主支付凭证登记" :visible.sync="dialogFormVisible">
      <el-form :model="paymentForm" :inline="true" label-width="100px">
        <!--申请付款信息  -->
        <div class="fromTitle">
          <div class="line"></div>
          <span>申请付款信息</span>
        </div>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="支付单号">
              <el-input
                v-model="paymentForm.companyPaymentId"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="企业名称">
              <el-input
                v-model="paymentForm.companyName"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12"
            ><el-form-item label="项目名称">
              <el-input
                v-model="paymentForm.projectInfoName"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款人姓名">
              <el-input
                v-model="paymentForm.payeeName"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="收款账号">
              <el-input
                v-model="paymentForm.bindingBankNo"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款人银行">
              <el-input
                v-model="paymentForm.bindingBankName"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24">
            <div style="font-size: 16px">
              应付金额<span
                style="font-weight: bold; font-size: 18px; color: red"
                >{{ paymentForm.amount }}</span
              >元，已付款<span
                style="font-weight: bold; font-size: 18px; color: red"
                >{{ paymentForm.bankPaySuccessAmount }}</span
              >，剩余未付金额<span
                style="font-weight: bold; font-size: 18px; color: red"
                >{{ paymentForm.bankNoPayAmount }}</span
              >元！
            </div>
          </el-col>
        </el-row>
        <el-form-item label="付款金额">
          <el-input
            v-model="paymentForm.tranAmount"
            autocomplete="off"
            type="number"
            @input="tranAmountInput"
            class="inputFund"
          ></el-input>
        </el-form-item>
        <!-- 平台付款信息 -->
        <div class="fromTitle">
          <div class="line"></div>
          <span>平台付款信息</span>
        </div>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="付款人姓名">
              <el-input
                v-model="paymentForm.outAcctName"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款账号">
              <el-input
                v-model="paymentForm.outAcctNo"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="付款人银行">
              <el-autocomplete
                v-model="paymentForm.outAcctBank"
                :fetch-suggestions="querySearchAsync"
                placeholder="请选择银行"
                @select="handleSelect"
              ></el-autocomplete>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行回单">
              <el-input
                v-model="paymentForm.outBankReceipt"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="付款时间">
              <el-date-picker
                v-model="paymentForm.payTime"
                type="datetime"
                :disabled="disabled == 'check'"
                placeholder="选择日期时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款用途">
              <el-input
                v-model="paymentForm.outUseRemark"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="是否跨行">
              <el-select
                v-model="paymentForm.unionFlag"
                placeholder="请选择是否跨行"
              >
                <el-option label="行内转账" value="1"></el-option>
                <el-option label="跨行转账 " value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手续费">
              <el-input
                v-model="paymentForm.outFee1"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="凭证照片（JPG、PNG）">
          <el-upload
            class="upload-demo"
            :action="actionUrl"
            :file-list="fileList"
            :on-success="handleAvatarSuccess"
            :headers="headers"
            list-type="picture-card"
            :limit="1"
          >
            <i slot="default" class="el-icon-plus"></i
          ></el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPage,
  getObj,
  addObj,
  putObj,
  delObj,
  statisticsPage,
  getSettleWaybill,
  rejectSettle,
  printSettleDoc,
  printSettle,
  mergeSettlement,
  checkOpen,
  printCompanyPaymentPlanSettle,
  listDictionaryItem,
} from "@/api/chain/companysettle";
import { getSettleInfo } from "@/api/chain/settleApproval";
import { tableOption } from "@/const/crud/chain/companysettle2";
import { mapGetters } from "vuex";
//引入账单核算
import billAccount from "./billAccount.vue";
import accountSheet from "./accountSheet.vue";
import receiptInfo from "./receiptInfo.vue";
import { expotOut } from "@/util/down.js";
import createPlan from "./createPlan.vue";
import viewPlan from "./viewPlan.vue";
import viewProcess from "./viewProcess.vue";
import printPlan from "./printPlan.vue";
import settleSheet from "./settleSheet";
import waybillDetail from "@/components/waybillDialog";
import store from "@/store";
export default {
  name: "companysettle2",
  components: {
    billAccount,
    accountSheet,
    receiptInfo,
    createPlan,
    viewPlan,
    viewProcess,
    printPlan,
    settleSheet,
    waybillDetail,
  },
  data() {
    return {
      // <----自主支付凭证登记
      dialogFormVisible: false,
      paymentForm: {
        companyPaymentId: "",
        companyName: "",
        projectInfoName: "",
        payeeName: "",
        bindingBankNo: "",
        bindingBankName: "",
        amount: 0,
        bankPaySuccessAmount: 0,
        bankNoPayAmount: 0,
        tranAmount: 0,
        outAcctName: "",
        outAcctNo: "",
        outAcctBank: "",
        outBankReceipt: "",
        payTime: "",
        outUseRemark: "",
        unionFlag: "",
        outFee1: 0,
        voucherPic: "",
      },
      formLabelWidth: "120px",
      headers: {
        Authorization: "Bearer " + store.getters.access_token,
        AuthorizationMD5: store.getters.user_md5, // user_md5
      },
      actionUrl: "/upms/file/upload?fileType=image&dir=",
      fileList: [],
      // 自主支付凭证登记---->
      selectList: [],
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      viewVisible: false, //查看运单的弹窗
      rejectVisible: false, //驳回申请的弹窗
      billVisible: false, //账单核算的弹窗
      //结算凭证
      settleVisible: false,
      accountVisible: false, //查看核算单的弹窗
      infoForm: {}, //账单核算需要的数据
      settleInfoForm: {},
      waybillData: [], //查看运单数据
      accountForm: {}, //查看核算单数据
      settleAccountForm: {}, //查看结算凭证数据
      column: [
        {
          label: "运单号",
          property: "id",
        },
        {
          label: "车牌号",
          property: "truckCode",
        },
        {
          label: "项目合作方",
          property: "projectPartner",
        },
        {
          label: "车队长",
          property: "captainName",
        },
        {
          label: "司机",
          property: "driverName",
        },
        {
          label: "挖机签单员",
          property: "inStaffId",
        },
        {
          label: "出场签单员",
          property: "goStaffId",
        },
        {
          label: "结算价",
          property: "price",
        },
        {
          label: "挖机签单时间",
          property: "inDatetime",
        },
        {
          label: "出场签单时间",
          property: "goDatetime",
        },
        {
          label: "完成时间",
          property: "createDatetime",
        },
      ], //查看运单数据列数据
      rejectForm: {}, //驳回申请的数据
      visibleBtnLoading: false, //弹窗按钮loadding
      statisForm: {
        amtSettle: 0,
        reject: 0,
        settle: 0,
        total: 0,
        unsettle: 0,
      },
      searchDom: {},
      multipleSelection: [],
      info: {},
      receiptVisible: false,
      isCheckOpen: false, //企业是否开启结算单回执
      planVisible: false, //资金计划弹窗
      viewPlanVisible: false, //查看资金计划弹窗
      viewProcessVisible: false, //查看流程弹窗
      printVisible: false, //查看流程弹窗
      waybillVisible: false,
      bankList: [
        { value: "中国银行" },
        { value: "中国工商银行" },
        { value: "中国农业银行" },
        { value: "中国建设银行" },
        { value: "中国交通银行" },
        { value: "中国邮政储蓄银行" },
        { value: "中国招商银行" },
        { value: "中国兴业银行" },
        { value: "中国民生银行" },
        { value: "中国平安银行" },
        { value: "中国中信银行" },
        { value: "中国浦发银行" },
        { value: "中国光大银行" },
        { value: "中国华夏银行" },
        { value: "中国广发银行" },
        { value: "中国兴业银行" },
        { value: "中国恒丰银行" },
        { value: "中国渤海银行" },
        { value: "中国浙商银行" },
        { value: "中国农村信用社" },
        { value: "中国农村合作银行" },
        { value: "中国城市商业银行" },
        { value: "中国农村商业银行" },
        { value: "中国村镇银行" },
        { value: "中国外资银行" },
        { value: "中国城商行" },
        { value: "中国农商行" },
        { value: "中国农信社" },
        { value: "中国农合行" },
        { value: "中国农商银行" },
        { value: "中国农村合作社" },
        { value: "中国农村信用社" },
        { value: "中国农村商业银行" },
        { value: "中国农村合作银行" },
        { value: "中国农村信用联社" },
        { value: "中国农村信用合作" },
      ],
    };
  },
  created() {},
  mounted() {
    // 检查是否开启结算单回执
    checkOpen({ companyAuthId: "" }).then((res) => {
      this.isCheckOpen = res.data.data;
    });
    this.searchDom = this.$refs.crud.$refs.headerSearch;
    window.addEventListener("resize", this.func);
  },
  activated() {
    setTimeout(() => {
      let tableHeight = document.querySelector(".el-table").offsetTop;
      this.$refs.crud.tableHeight = window.innerHeight - tableHeight - 182;
      this.$refs.crud.tableOption.height =
        window.innerHeight - tableHeight - 182;
      this.$refs.crud.doLayout();
    }, 100);
  },
  computed: {
    ...mapGetters(["permissions", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:companysettle:add"] ? true : false,
        delBtn: this.permissions["chain:companysettle:del"] ? true : false,
        editBtn: this.permissions["chain:companysettle:edit"] ? true : false,
        viewBtn: this.permissions["chain:companysettle:get"] ? true : false,
      };
    },
  },
  watch: {
    "searchDom.searchShow": {
      handler(newVal, oldVal) {
        setTimeout(() => {
          let tableHeight = document.querySelector(".el-table").offsetTop;
          this.$refs.crud.tableHeight = window.innerHeight - tableHeight - 182;
          this.$refs.crud.tableOption.height =
            window.innerHeight - tableHeight - 182;
          this.$refs.crud.doLayout();
        }, 300);
      },
      deep: true,
    },
    dialogFormVisible(val) {
      if (!val) {
        this.paymentForm = {};
        this.fileList = [];
      }
    },
  },
  methods: {
    expotOut,
    paymentDocument(row, index) {
      this.dialogFormVisible = true;
      console.log(row, index);
    },

    handleAvatarSuccess(response) {
      this.paymentForm.voucherPic = response.link;
    },
    func() {
      let tableHeight = document.querySelector(".el-table").offsetTop;
      this.$refs.crud.tableHeight = window.innerHeight - tableHeight - 182;
      this.$refs.crud.tableOption.height =
        window.innerHeight - tableHeight - 182;
      this.$refs.crud.doLayout();
    },
    statisticsPage(params = {}) {
      statisticsPage(Object.assign(params, this.paramsSearch)).then((res) => {
        this.statisForm = res.data.data;
      });
    },
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params = {}) {
      if (params) {
        if (params.hasOwnProperty("applyDatetime")) {
          params.applyDatetimeStart = params.applyDatetime[0];
          params.applyDatetimeEnd = params.applyDatetime[1];
          delete params.applyDatetime;
        }
        if (params.hasOwnProperty("moneyDatetime")) {
          params.moneyDatetimeStart = params.moneyDatetime[0];
          params.moneyDatetimeEnd = params.moneyDatetime[1];
          delete params.moneyDatetime;
        }
        if (params.hasOwnProperty("settleDatetime")) {
          params.settleDatetimeStart = params.settleDatetime[0];
          params.settleDatetimeEnd = params.settleDatetime[1];
          delete params.settleDatetime;
        }
      }

      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
      this.statisticsPage(params);
    },
    querySearchAsync(queryString, cb) {
      var reg = eval("/" + queryString + "/");
      var restaurants = this.bankList;
      var results = [];
      if (queryString) {
        restaurants.forEach((item) => {
          if (reg.test(item.value)) {
            results.push(item);
          }
        });
      } else {
        results = restaurants;
      }
      console.log(results);
      cb(results);
    },

    handleSelect(item) {
      console.log(item);
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    //账单核算
    billAccount(row, index, type) {
      this.tableLoading = true;
      this.infoForm = row;
      this.infoForm.type = type; //1核算  2查看核算
      //获取运单信息
      let param = {
        companySettleId: row.id,
      };
      getSettleWaybill(param)
        .then((res) => {
          this.billVisible = true;
          this.tableLoading = false;
          this.infoForm.tableData = res.data.data;
          console.log(res, "res");
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },

    //查看核算单
    viewAccountSheet(row, index) {
      this.tableLoading = true;
      //获取核算单信息
      this.accountForm = row;
      let param = {
        settleId: row.id,
        planId: "",
      };
      listDictionaryItem({ dictionary: this.userInfo.tenantId })
        .then((res) => {
          //在数据字典里面 特殊处理 chain:companysettle:get
          let permission = res.data.data.some((item) => {
            return item.itemValue == "chain:companysettle:get";
          });
          if (res.data.data.length > 0 && permission) {
            printCompanyPaymentPlanSettle(param)
              .then((res) => {
                this.tableLoading = false;
                this.accountForm.tableData = res.data.data;
                this.printVisible = true;
              })
              .catch(() => {
                this.tableLoading = false;
              });
          } else {
            printSettle(param)
              .then((res) => {
                this.tableLoading = false;
                this.accountForm.tableData = res.data.data;
                this.accountVisible = true;
              })
              .catch(() => {
                this.tableLoading = false;
              });
          }
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    //查看结算凭证
    viewSettleAccountSheet(row, index) {
      this.tableLoading = true;
      //获取核算单信息
      this.settleAccountForm = row;
      let param = {
        settleId: row.id,
        planId: "",
      };
      listDictionaryItem({ dictionary: this.userInfo.tenantId })
        .then((res) => {
          //在数据字典里面 特殊处理 chain:companysettle:get
          let permission = res.data.data.some((item) => {
            return item.itemValue == "chain:companysettle:get";
          });
          if (res.data.data.length > 0 && permission) {
            printCompanyPaymentPlanSettle(param)
              .then((res) => {
                this.tableLoading = false;
                this.settleAccountForm.tableData = res.data.data;
                this.settleVisible = true;
              })
              .catch(() => {
                this.tableLoading = false;
              });
          } else {
            printSettleDoc(param)
              .then((res) => {
                this.tableLoading = false;
                this.settleAccountForm.tableData = res.data.data;
                this.settleVisible = true;
              })
              .catch(() => {
                this.tableLoading = false;
              });
          }
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    //查看运单
    viewWaybill(row, index) {
      this.waybillData = [];
      //获取运单信息
      this.waybillData = waybillData;
      this.viewVisible = true;
    },
    //确认打款
    confirmRemit(row, index) {
      this.$message({
        showClose: false,
        message: "打款功能暂未开发完成，请您耐心等待",
        type: "error",
      });
    },
    //驳回申请
    rejectReq(row, index) {
      //获取运单信息
      this.rejectForm = row;
      this.rejectVisible = true;
    },
    //确认驳回
    confirmReject() {
      this.$refs.rejectForm.validate((valid) => {
        if (valid) {
          this.visibleBtnLoading = true;
          let param = {
            rejectedRemark: this.rejectForm.rejectedRemark,
            settleId: this.rejectForm.id,
          };
          //下面调用接口
          rejectSettle(param)
            .then((res) => {
              this.rejectVisible = false;
              this.visibleBtnLoading = false;
              this.refreshChange();
            })
            .catch(() => {
              this.visibleBtnLoading = false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    scanCard() {
      this.$router.push({ path: "/scan/scanCard" });
    },
    scanQrCode() {
      this.$router.push({ path: "/scan/scanQrCode" });
    },
    //导出
    exportExc() {
      console.log(this.paramsSearch);
      let params = Object.assign({}, this.paramsSearch);
      if (params) {
        if (params.hasOwnProperty("applyDatetime")) {
          params.applyDatetimeStart = params.applyDatetime[0];
          params.applyDatetimeEnd = params.applyDatetime[1];
          delete params.applyDatetime;
        }
        if (params.hasOwnProperty("moneyDatetime")) {
          params.moneyDatetimeStart = params.moneyDatetime[0];
          params.moneyDatetimeEnd = params.moneyDatetime[1];
          delete params.moneyDatetime;
        }
        if (params.hasOwnProperty("settleDatetime")) {
          params.settleDatetimeStart = params.settleDatetime[0];
          params.settleDatetimeEnd = params.settleDatetime[1];
          delete params.settleDatetime;
        }
      }
      this.expotOut(params, "/chain/companysettle/exportXls", "财务结算");

      // let params = Object.assign({}, form);
      // if (params.goDatetime && params.goDatetime.length > 0) {
      //   params.goDatetimeStart = params.goDatetime[0];
      //   params.goDatetimeEnd = params.goDatetime[1];
      // }
      // delete params.goDatetime;
      // let url = '/chain/excelExport/createByCode'
      // let name = '车辆进出台账'
      // this.$refs.crud.rowExcel();
    },
    cellClassNameFn(row) {
      if (row.row.paymentNo) {
        // 通过自定义样式隐藏复选框
        return "table-column-hidden";
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    batchMerge() {
      console.log(this.multipleSelection);
      let param = this.multipleSelection.map((item) => {
        return {
          companyAuthId: item.companyAuthId,
          agentInfoId: item.agentInfoId,
          projectInfoId: item.projectInfoId,
          applyId: item.applyId,
          id: item.id,
        };
      });
      this.$confirm("确定合并结算单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableLoading = true;
          mergeSettlement(param)
            .then((res) => {
              this.tableLoading = false;
              this.$message.success(res.data.msg);
              this.getPage(this.page);
            })
            .catch(() => {
              this.tableLoading = false;
            });
        })
        .catch(function (err) {});
    },
    receiptInfo(row) {
      this.info = row;
      this.receiptVisible = true;
    },
    //创建资金计划
    createPlan(row) {
      this.tableLoading = true;
      this.infoForm = row;
      //获取运单信息
      let param = {
        companySettleId: this.infoForm.id,
        isPlan: 1,
      };
      getSettleWaybill(param)
        .then((res) => {
          this.planVisible = true;
          this.tableLoading = false;
          this.infoForm.tableData = res.data.data;
          console.log(res, "res");
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    //查看计划
    viewPlanInfo(row, index) {
      this.accountForm = row;
      this.viewPlanVisible = true;
    },
    //查看流程
    viewProcess(row, index) {
      this.tableLoading = true;
      getSettleInfo(row.id)
        .then((res) => {
          this.tableLoading = false;
          this.accountForm = res.data.data;
          this.viewProcessVisible = true;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    //查看运单详情
    waybillDetail(row) {
      //结算单号
      this.info = {
        companySettleNo: row.settleNo,
        isReject: "1", //查询已驳回的数据
      };
      this.waybillVisible = true;
    },
  },
  destroyed() {
    window.removeEventListener("resize", this.func);
  },
};
</script>

<style lang="scss" scoped>
/deep/ .rejectDialog {
  .el-dialog__body {
    padding-top: 0;
  }
  .el-form .el-form-item {
    margin-bottom: 0px;
  }
}
/deep/ .el-table .el-table__fixed-right {
  height: auto !important;
  bottom: 8px !important;
  &::before {
    background-color: transparent;
  }
}
.statistics {
  line-height: 40px;
  color: #606266;
  span {
    color: red;
    margin-right: 20px;
  }
}
::v-deep {
  .inputFund input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  .inputFund input[type="number"] {
    appearance: textfield;
    -moz-appearance: textfield;
  }
}
.fromTitle {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
}
.line {
  width: 4px;
  height: 20px;
  background-color: #409eff;
  margin-right: 10px;
}
</style>
