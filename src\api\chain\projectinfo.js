import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/projectinfo/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/projectinfo',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/projectinfo/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/projectinfo/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/projectinfo',
        method: 'put',
        data: obj
    })
}
export function projectUpdate(obj) {
    return request({
        url: '/chain/projectinfo/projectUpdate',
        method: 'put',
        data: obj
    })
}
export function getMemberList(params) {
    return request({
        url: '/chain/companystaff/list2',
        method: 'get',
        params
    })
}
export function getProjectList(params) {
    return request({
        url: '/chain/projectinfo/getGoingProjectList',
        method: 'get',
        params
    })
}
export function batchUpdatemember(data) {
    return request({
        url: '/chain/projectinfo/batchUpdateMember',
        method: 'post',
        data
    })
}

export function listDictionaryItem(params) {
    return request({
        url: '/chain/systemdictionaryitem/listDictionaryItem',
        method: 'get',
        params
    })
}
//获取员工
export function staffList(params) {
    return request({
        url: '/chain/companystaff/page',
        method: 'get',
        params
    })
}
//获取部门
export function officeList(params) {
    return request({
        url: '/chain/companydept/tree',
        method: 'get',
        params
    })
}
//选择部门员工树形
export function selectTreeStaff(params) {
    return request({
        url: '/chain/companydept/selectTreeStaff',
        method: 'get',
        params
    })
}
//设置地块
export function editLandParcel(data) {
    return request({
        url: '/chain/projectinfo/editLandParcel',
        method: 'post',
        data
    })
}


//查询项目阶段
export function getSetting(id) {
    return request({
        url: `/chain/projectinfobiext/getSetting/${id}`,
        method: 'get'
    })
}

//   添加项目阶段
export function addSetting(data) {
    return request({
        url: ' /chain/projectinfobiext/addSetting',
        method: 'post',
        data
    })
}

//删除项目阶段
export function delPhase(id) {
    return request({
        url: `/chain/projectinfobiphase/disable/${id}`,
        method: 'get'
    })
}

//添加项目阶段
export function getDefault(id) {
    return request({
        url: `/chain/projectinfobiphase/getDefault/${id}`,
        method: 'get'
    })
}

//删除单图纸单位
export function batchDelete(data) {
    return request({
        url: `/chain/projectinfobiunitconvert/batchDelete`,
        method: 'post',
        data
    })
}

//导入土质单位
export function exInSoil(obj) {
    return request({
        url: `/chain/projectinfobiphase/importSoils`,
        method: 'post',
        data: obj,
        'Content-Type': 'multipart/form-data'
    })
}

//修改车队名称和类型
export function updProFleetNames(data) {
  return request({
      url: `/chain/projectinfoext/updProFleetNames`,
      method: 'put',
      data
  })
}
//获取项目扩展 车队名称和类型
export function getProjectInfoExt(id) {
  return request({
      url: `/chain/projectinfoext/${id}`,
      method: 'get',
  })
}
//获取挖机型号列表
export function getCompanymachine(params) {
  return request({
      url: `/chain/companymachine/page`,
      method: 'get',
      params
  })
}
//批量新增挖机型号
export function batchAddProInModel(data) {
  return request({
      url: `/chain/projectinfoext/batchAddProInModel`,
      method: 'post',
      data
  })
}
//批量删除挖机型号
export function batchDelProInModel(data) {
  return request({
      url: `/chain/projectinfoext/batchDelProInModel`,
      method: 'post',
      data
  })
}
//修改挖机类型
export function updProInModelType(data) {
  return request({
      url: `/chain/projectinfoext/updProInModelType`,
      method: 'post',
      data
  })
}
//检查是否上班状态
export function checkStaffOnWork(data) {
  return request({
    url: '/chain/projectinfo/checkStaffOnWork',
    method: 'post',
    data
  })
}
//查询撮合设置
export function projectakematch(projectId,type) { //type 1车队 2泥尾 3资源 4回填
  return request({
    url: `/chain/projectmakematch/${projectId}/${type}`,
    method: 'get',
  })
}
//新增/修改撮合设置
export function projectakematchset(data) {
  return request({
    url: `/chain/projectmakematch`,
    method: 'post',
    data
  })
}
//查看人员修改记录
export function projectinfoupdatehistory(params) {
  return request({
    url: `/chain/projectinfoupdatehistory/page`,
    method: 'get',
    params
  })
}
//设置装货起点分页
export function getProjectInfoSdkFencePage(params) {
  return request({
    url: `/chain/projectinfosdkfence/getProjectInfoSdkFencePage`,
    method: 'get',
    params
  })
}
//新增装货起点分页
export function saveGeneralCargoSdkFence(data) {
  return request({
    url: `/chain/projectinfosdkfence/saveGeneralCargoSdkFence`,
    method: 'post',
    data
  })
}
//装货起点自动签单设置
export function updateGeneralCargoMode(data) {
  return request({
    url: `/chain/projectinfoext/updateGeneralCargoMode`,
    method: 'post',
    data
  })
}
//删除装货起点
export function delFence(id) {
  return request({
      url: '/chain/projectinfosdkfence/'+id,
      method: 'delete',
  })
}
//普货签单自动装车间隔时间
export function updateGeneralCargoInterval(data) {
  return request({
    url: `/chain/projectinfoext/updateGeneralCargoInterval`,
    method: 'post',
    data
  })
}
