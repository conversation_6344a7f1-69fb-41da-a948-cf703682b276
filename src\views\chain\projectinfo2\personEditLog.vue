<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%"
               :data="visible"
               :visible.sync="visible"
               title="人员修改记录"
               :before-close="cancelModal">
      <basic-container>
        <avue-crud ref="crud"
                   :page.sync="page"
                   :data="tableData"
                   :table-loading="tableLoading"
                   @on-load="getPage"
                   :option="tableOption">
          <span slot="empty">暂无数据</span>
        </avue-crud>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { projectinfoupdatehistory } from "@/api/chain/projectinfo";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {},
  },
  components:{
  },
  data () {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      tableData: [],
      tableOption: {
        header: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        column: [
          {
            label: "新增成员",
            prop: "addNames",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "减少成员",
            prop: "removeNames",
            minWidth: 160,
            overHidden: true,
          },
          {
            label: "修改人",
            prop: "createName",
            width: 80,
            overHidden: true,
          },
          {
            label: "修改时间",
            prop: "createDatetime",
            width: 140,
            overHidden: true,
          },
        ]
      },
      tableLoading: false,
    };
  },
  created () {
  },
  mounted () {
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    getPage (page) {
     let param =  Object.assign({}, {
            current: page.currentPage,
            size: page.pageSize,
            ascs: this.page.ascs,
            descs: this.page.descs,
            projectInfoId: this.info.id
          })
      projectinfoupdatehistory(param).then(res => {
        this.tableLoading = false;
        this.tableData = res.data.data.records
        this.page.total = res.data.data.total;
        this.page.currentPage = page.currentPage;
        this.page.pageSize = page.pageSize;
      })
    },
    cancelModal () {
      this.$emit("update:visible", false);
    },

  },
};
</script>

<style lang="scss" scoped>
</style>
