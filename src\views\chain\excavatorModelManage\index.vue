<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        v-model="form"
        @on-load="getPage"
        @refresh-change="refreshChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="handleDel"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
        <template slot="menuLeft" slot-scope="{ row, index }">
          <el-button
            type="primary"
            v-if="permissions['chain:excavatorModelManage:upload']"
            size="small"
            icon="el-icon-upload2"
            @click="uploadVisible=true"
            >导入</el-button
          >
        </template>
      </avue-crud>
    </basic-container>
    <uploadImport ref="uploadImport" v-if="uploadVisible" :visible.sync="uploadVisible" @imtExcel="imtExcel" :templateUrl="templateUrl"></uploadImport>
  </div>
</template>

<script>
import {
  getPage,
  addObj,
  putObj,
  delObj,
  importExcel,
} from "@/api/chain/excavatorModelManage";
import { tableOption } from "@/const/crud/chain/excavatorModelManage";
import { mapGetters } from "vuex";
import uploadImport from "@/components/uploadImport"

export default {
  name: "excavatorModelManage",
  components:{
    uploadImport
  },
  data() {
    return {
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,
      uploadVisible:false,
      templateUrl:"https://jyb-app.obs.cn-south-1.myhuaweicloud.com:443/1%2Fappdownload%2Fa34d5867-6d4b-482d-b2d4-d93243ab070f.xls?attname=挖机型号管理.xls"
    };
  },
  created() {},
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.permissions["chain:excavatorModelManage:add"]
          ? true
          : false,
        delBtn: this.permissions["chain:excavatorModelManage:del"]
          ? true
          : false,
        editBtn: this.permissions["chain:excavatorModelManage:edit"]
          ? true
          : false,
        viewBtn: this.permissions["chain:excavatorModelManage:get"]
          ? true
          : false,
      };
    },
  },
  methods: {
    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认删除此数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    //上传表格
    imtExcel(formData) {
      importExcel(formData).then(res=>{
        if(!!res.data.data){//有返回
          let arr = res.data.data.split(',')
          this.$msgbox({
                title: '提示',
                message: (
                    <div style='min-height:100px;max-height:300px;overflow-y:auto;padding:10px 0 10px 10px;'>
                        { arr.map((item,index) => <div style='line-height:30px' key={index}>{item}</div>)}
                    </div>
                ),
                customClass:"excMsg",
                confirmButtonText: '确定',
                beforeClose: (action, instance, done) => {
                        done()},
                })
        }else{
          this.$message.success("导入成功")
        }
        this.uploadVisible = false
        this.$refs.uploadImport.init()
        this.getPage(this.page)
      }).catch((err)=>{
        this.$refs.uploadImport.btnLoading = false
        this.$message.error(err.data.msg||"导入失败")
      })
    },
  },
};
</script>

<style lang="scss" scoped>
// /deep/ .excMsg{
//   width: 40;
// }
</style>
