import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companystaff/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/chain/companystaff',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/chain/companystaff/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/chain/companystaff/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/chain/companystaff',
        method: 'put',
        data: obj
    })
}

export function putObjShelf(obj) {
  return request({
    url: '/mall/goodsspu/shelf',
    method: 'put',
    params: obj
  })
}
export function getDetail(obj) {
  return request({
    url: '/chain/projectinfo/getProjectListByStaff',
    method: 'get',
    params: obj
  })
}
//检查是否上班状态
export function checkStaffOnWork(data) {
  return request({
    url: '/chain/projectinfo/checkStaffOnWork',
    method: 'post',
    data
  })
}
