export const tableOption = (value)=>{
  let that = value
  console.log(that);
  return {
    dialogDrag: true,
    border: true,
    indexLabel: "序号",
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: true,
    dialogClickModal:false,
    dialogWidth:600,
    excelBtn: false,
    printBtn: false,
    addBtn: false,
    viewBtn: false,
    searchSpan:6,
    searchMenuSpan: 6,
    labelWidth:120,
    searchLabelWidth:110,
    menuWidth:80,
    column: [
      {
        label: "客户联系方式",
        prop: "customerMobile",
        span:24,
        sortable: true,
        search: true,
        display:true,
        minWidth:120,
        overHidden:true,
      },
      {
        label: "客户名称",
        prop: "customerName",
        sortable: true,
        search:true,
        span:24,
        rules: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        minWidth:120,
        overHidden:true,
      },
      {
        label: "资源类型",
        prop: "resourceType",
        sortable: true,
        span:24,
        type:'select',
        search:true,
        props:{
          label:'itemValue',
          value:'itemValue'
        },
        dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=resource_type',
        rules: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        minWidth:96,
        overHidden:true,
      },
      {
        label: "售票总数",
        prop: "qty",
        sortable: true,
        minWidth:96,
        overHidden:true,
      },
      {
        label: "核销总数",
        prop: "checkedQty",
        sortable: true,
        minWidth:96,
        overHidden:true,
      },
      {
        label: "退票总数",
        prop: "returnQty",
        sortable: true,
        minWidth:96,
        overHidden:true,
      },
      {
        label: "剩余总数",
        prop: "remainQty",
        sortable: true,
        minWidth:96,
        overHidden:true,
      },
    ],
  };
}

