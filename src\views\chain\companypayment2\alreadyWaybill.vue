<template>
  <div class="alreadyWaybill">
    <basic-container>
      <ul>
        <li>
          支付单号：<span>{{ info.paymentNo }}</span>
        </li>
        <li>
          支付单金额：<span>{{ info.amt }}</span>
        </li>
      </ul>
      <avue-crud ref="crud" :option="option" :data="list">
        <template slot="invoiceSumCount" slot-scope="scope">
          <span
            v-if="scope.row.invoiceSumCount && scope.row.invoiceSumCount > 0"
            style="color: #409eff; cursor: pointer"
            @click="wayBillView(scope.row, scope.index)"
            >{{ scope.row.invoiceSumCount }}</span
          >
          <span v-else style="color: #409eff">{{scope.row.invoiceSumCount}}</span>
        </template>
        <template slot="invoiceUrl" slot-scope="scope">
          <span
            v-if="scope.row.invoiceUrl"
            style="color: #409eff; cursor: pointer"
            @click="viewRow(scope.row, scope.index)"
            >查看</span
          >
        </template>
      </avue-crud>
    </basic-container>
    <!-- 弹窗 -->
    <el-dialog
      width="70%"
      title="发票运单"
      :visible="dialogVisible"
      :before-close="beforeClose"
      :close-on-click-modal="false"
    >
    <ul>
        <li>
          发票号码：<span>{{ invoiceForm.invoiceSn }}</span>
        </li>
        <li>
          发票金额：<span>{{ invoiceForm.sumPrice }}</span>
        </li>
      </ul>
      <div class="table">
        <el-table :data="tableData" border>
          <el-table-column
            property="agentName"
            min-width="100px"
            label="项目合作方"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="no"
            min-width="180px"
            label="运单编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="truckCode"
            min-width="100px"
            label="车牌号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="tpMode"
            min-width="100px"
            label="运输方式"
            show-overflow-tooltip
          >
           <template slot-scope="scope">
              <span>{{filterTpMode(scope.row.tpMode)}}</span>
            </template>
          </el-table-column>
          <el-table-column
            property="names"
            min-width="150px"
            label="泥尾"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="goSoilType"
            min-width="90px"
            label="土质"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="captainName"
            label="车队长"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column property="driverName" label="司机" show-overflow-tooltip></el-table-column>
          <el-table-column
            property="inName"
            min-width="100px"
            label="挖机签单员"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="goName"
            min-width="100px"
            label="出场签单员"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="load"
            min-width="100px"
            label="容量"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="inDatetime"
            min-width="160px"
            label="挖机签单时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="goDatetime"
            min-width="160px"
            label="出场签单时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="completeDatetime"
            min-width="160px"
            label="完成时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="settlePrice"
            min-width="100px"
            label="结算价"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="exAmt"
            min-width="100px"
            label="异常金额"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="adjustAmt"
            min-width="100px"
            label="增减值"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="remark"
            min-width="100px"
            label="备注"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="receivable"
            min-width="100px"
            label="承运方应收运费"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="actual"
            min-width="100px"
            label="承运方实收运费"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="payTaxes"
            min-width="100px"
            label="企业纳税金额"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            property="actuallyPaid"
            min-width="100px"
            label="企业实付金额"
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { selectInvoiceOfWill} from "@/api/chain/companypayment";

export default {
  name: "alreadyWaybill",
  components: {},
  data() {
    return {
      info: {},
      dialogVisible: false,
      tableData: [],
      list: [],
      option: {
        addBtn: false,
        menu: false,
        border: true,
        refreshBtn: false,
        columnBtn: false,
        header: false,
        align: "center",
        column: [
          {
            label: "发票号码",
            prop: "invoiceSn",
            minWidth:160,
            overHidden:true,
          },
          {
            label: "发票代码",
            prop: "invoiceCode",
            minWidth:160,
            overHidden:true,
          },
          {
            label: "开票运单数",
            prop: "invoiceSumCount",
            minWidth:84,
            overHidden:true,
          },
          {
            label: "开票运单金额",
            prop: "invoiceSumPrice",
            minWidth:96,
            overHidden:true,
          },
          {
            label: "项目税点",
            prop: "taxPoint",
            formatter: (val) => {
              return val.taxPoint && val.taxPoint + "%";
            },
            minWidth:80,
            overHidden:true,
          },
          {
            label: "开票纳税金额",
            prop: "taxInvoiceSumPrice",
            minWidth:96,
            overHidden:true,
          },
          {
            label: "发票合计金额",
            prop: "sumPrice",
            minWidth:96,
            overHidden:true,
          },
          {
            label: "发票图片",
            prop: "invoiceUrl",
          },
          {
            label: "备注",
            prop: "remark",
            minWidth:160,
            overHidden:true,
          },
        ],
      },
       invoiceForm:{
        invoiceSn:'',
        sumPrice:''
      }
    };
  },
  created() {},
  mounted: function () {
    this.info = JSON.parse(this.$route.query.info);
    this.list = JSON.parse(this.$route.query.info).list
  },
  computed: {},
  methods: {
    viewRow(row) {
      this.$ImagePreview(
        [{ thumbUrl: row.invoiceUrl, url: row.invoiceUrl }],
        0,
        {
          closeOnClickModal: true,
        }
      );
    },
    beforeClose(){
      this.dialogVisible = false
    },
    wayBillView(row) {
      console.log(row);
      let param = {
        id: row.id,
      };
      console.log(param);
      this.invoiceForm.invoiceSn = row.invoiceSn
      this.invoiceForm.sumPrice = row.sumPrice
      selectInvoiceOfWill(param).then((res) => {
        this.tableData = res.data.data;
        this.dialogVisible = true;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.alreadyWaybill {
  ul {
    display: flex;
    align-items: center;
    li {
      margin-right: 30px;
      line-height: 50px;
      color: #666;
      span {
        color: #333;
      }
    }
  }
  /deep/ .el-dialog__body{
    padding-top: 0;
  }
}
</style>
