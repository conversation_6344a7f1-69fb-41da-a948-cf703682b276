<template>
  <div class="excavatorInfo">
    <el-drawer size="80%" :data="visible" :visible.sync="visible" :before-close="cancelModal" >
      <basic-container>
        <el-tabs v-model="activeName">
          <el-tab-pane label="挖机装车数" name="1">
            <excavatorNum :info="info" v-if="activeName==1" :tab="activeName" ref="excavatorNum"></excavatorNum>
          </el-tab-pane>
          <el-tab-pane label="台班总时长" name="2">
            <excavatorDuration :info="info" v-if="activeName==2" :tab="activeName"></excavatorDuration>
          </el-tab-pane>
        </el-tabs>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import excavatorNum from "./excavator/excavatorNum";
import excavatorDuration from "./excavator/excavatorDuration";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  components: {
    excavatorNum,
    excavatorDuration,
  },
  data () {
    return {
      activeName: "1",
    }
  },
  created () {
    console.info(this.info)
    if(!this.info.type){
      this.activeName=this.info.radio2=='挖机装车数'?"1":"2"
    }
  },
  mounted () {
  },
  computed: {
    ...mapGetters(['permissions']),
  },
  methods: {
    cancelModal () {
      this.$emit("update:visible", false);
    },

  },

};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
</style>
