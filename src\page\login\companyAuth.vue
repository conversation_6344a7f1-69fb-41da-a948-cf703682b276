<template>
  <div>
    <div class="head">
      <img src="@/static/login/logo.png" alt="" />
      建运宝
    </div>

    <div class="content-inner"   v-loading="loading"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading">
      <div class="label">
        <span>企业认证</span>
        <el-divider></el-divider>
      </div>
      <el-steps
        :active="active"
        :process-status="errMsg?'error':'wait'"
        finish-status="finish"
        simple
        style="margin-top: 20px"
      >
       <el-step title="注册信息"> </el-step>
        <el-step title="填写企业信息"> </el-step>
        <el-step title="填写法人信息"></el-step>
        <el-step title="信息核对"></el-step>
        <el-step title="系统审核"></el-step>
      </el-steps>
      <register ref="register" @nextStep="active = 1"
        v-show="active === 0"
      ></register>
      <companyInfo
        @nextStep="active = 2"
        v-if="active === 1"
        v-model="company"
      ></companyInfo>
      <peopleInfo
        @check="active = 3"
        v-if="active === 2"
        v-model="people"
      ></peopleInfo>
      <check
        v-if="active === 3"
        :companyInfo="company"
        :peopleInfo="people"
        @reWrite="active = 1"
        @check="submit"
      ></check>
      <status v-if="active===4" @rewrite="reset" :msg="errMsg"></status>
    </div>
  </div>
</template>

<script>
import companyInfo from "./components/companyInfo.vue";
import peopleInfo from "./components/peopleInfo.vue";
import check from "./components/check.vue";
import status from './components/status.vue'
import register from './components/register.vue'
import { companyReg,uploadImage2 } from "@/api/chain/companyauth.js";
export default {
  data() {
    return {
      active: 0,
      company: {},
      people: {},
      errMsg:'',
      loading:false,
    };
  },
  components: {
    companyInfo,
    peopleInfo,
    check,
    status,
    register
  },
  created(){
    document.documentElement.style.fontSize = '16px';
  },
  methods: {
    reset() {
      this.active = 1;
      this.company = {};
      this.people = {};
    },
    formatNumber(n) {
      n = n.toString();
      return n[1] ? n : "0" + n;
    },
    formatDate(date) {
      if (date) {
        date = new Date(date);
        console.info(date, "date");
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return [year, month, day].map(this.formatNumber).join("-");
      } else {
        return "";
      }
    }, //字符串转日期
  async  submit() {
      console.info("企业信息", this.companyInfo);
      console.info("法人信息", this.people);
      let {
        name,
        address,
        foundDate,
        registrationNumber,
        legalRepresentative,
        type,
        url,
        file,
      } = this.company;

      let data = {
        companyAddress: address,
        companyFound: foundDate
          ? this.formatDate(
              Date.parse(
                foundDate
                  .replace("年", "-")
                  .replace("月", "-")
                  .replace("日", "")
              )
            )
          : "",
        companyName: name,
        companyNo: registrationNumber,
        companyRepre: legalRepresentative,
        companyType: type,
        companyUrl: this.company.url,
      };
      this.loading = true
    await this.upload(file).then((res) => {
        data.companyUrl = res;
        return data
      }).catch(()=>{
        this.loading = false
      })
      if (JSON.stringify(this.people) !== "{}") {
        let {
          issue,
          birth,
          idCardNegativeUrl,
          idCardPositiveUrl,
          validTo,
          ethnicity,
          number,
          sex,
          fileFont,
          fileBack,
          validFrom,
        } = this.people;
        data = Object.assign(data, {
          idAddress: this.people.address,
          idAuthority: issue,
          idBirth: birth,
          idCardNegativeUrl,
          idCardPositiveUrl,
          idEndDate: validTo,
          idName: this.people.name,
          idNation: ethnicity,
          idNo: number,
          idSex: sex,
          idStartDate: validFrom,
          mobile:this.$refs.register.formData.phone
        });
        this.upload(fileFont).then((res) => {
          data.idCardPositiveUrl = res;
          this.upload(fileBack).then((res) => {
            data.idCardNegativeUrl = res;
            companyReg(data).then(res=>{
              this.active = 4
              this.errMsg=''
              this.loading = false
            }).catch(res=>{
               this.active = 4
               console.log('res',res)
              this.errMsg = res
              this.loading = false
            });
          }).catch(()=>{
            this.loading = false
          })
        }).catch(()=>{
          this.loading = false
        })
      }
    },
    upload(file) {
      return new Promise((resolve, reject) => {
        let param = new FormData(); // 创建form对象
        param.append("file", file);
        uploadImage2(param)
          .then((res) => {
            resolve(res.data.link);
            console.info(res.data.link, "上传图片成功");
          })
          .catch(() => {
            reject();
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 50px;
  font-weight: bold;
  margin: 30px 0 50px 0;

  img {
    margin-right: 50px;
    width: 150px;
    height: 90px;
  }
}
.content-inner {
  padding: 0 15%;
  .label {
    span {
      font-size: 30px;
      font-weight: bold;
    }
  }
}
</style>
