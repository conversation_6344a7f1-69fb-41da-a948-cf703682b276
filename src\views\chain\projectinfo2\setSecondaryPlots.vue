<template>
  <div class="driverDetail">
    <el-drawer
      size="50%"
      title="设置二级地块"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
    >
      <basic-container>
        <div style="display:flex;align-items:center">
          <label for="plots" style="width:60px">地块：</label>
          <el-input id="plots" v-model.trim="plotsName" style="width:300px" maxlength="15" size="small" placeholder="请输入地块名称">

            <el-button slot="suffix" @click="addTag" size="small" type="text">新增地块</el-button>
          </el-input>

        </div>
        <div class="tags" style="margin:20px 0px">
          <el-tag  v-for="(tag,index) in tags" :key="index" closable @close="handleClose(index)" style="margin-right:6px;margin-bottom:10px">
            {{ tag }}
          </el-tag>
        </div>
        <div class="btns" style="text-align: center; margin-top: 20px">
          <el-button @click="cancelModal" size="small" style="margin-left: 20px">取 消</el-button>
          <el-button type="primary" size="small" :loading="btnLoading" @click="handleUpdate">确 定</el-button>
        </div>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getMemberList,
  getProjectList,
  editLandParcel,
} from "@/api/chain/projectinfo";
export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  data() {
    return {
      btnLoading: false,
      plotsName:'',
      tags:[]
    };
  },
  created() {},
  mounted() {
    this.tags = this.info.landParcel
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
    },
    addTag(){
      if(!this.plotsName) return false
      if(this.tags.includes(this.plotsName)){
        this.$message.error("地块名称已存在")
      }else{
        this.tags.push(this.plotsName)
      }
    },
    handleClose(index){
      this.tags.splice(index,1)
    },
    handleUpdate() {
      this.btnLoading = true;
      editLandParcel({
        projectInfoId: this.info.id,
        landParcel: this.tags,
      })
        .then((res) => {
          this.btnLoading = false;
          this.$emit("update:visible", false);
          this.$emit("getPage");
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

</style>
