<template>
  <div>
    <div class="title">企业营业执照<span>*</span></div>
    <div class="upload">
      <img :src="imgUrl" alt="" class="pic" v-if="imgUrl" />
      <el-upload
        v-else
        class="upload-demo"
        drag
        :headers="setHeaders"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :show-file-list="false"
        :multiple="false"
        :on-error="uploadFail"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
        action="/chain/common/getOcrBusinessLicense"
      >
        <div class="upload-info">
          <img src="@/static/login/yyzz.png" alt="" class="yyzz" />
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </div>
        <div class="el-upload__tip" slot="tip">
          请上传单位证件原件照片或彩色扫描件
        </div>
      </el-upload>
      <div class="btn">
        <el-row>
          <el-button type="primary" style="width: 100%" @click="nextStep"
            >保存并下一步</el-button
          >
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  data() {
    return {
      setHeaders: {
        Authorization:  this.$store.getters.access_token,
        AuthorizationMD5: this.$store.getters.user_md5// user_md5
      },
      fileList: [],
      imgUrl: "",
      companyInfo: {},
    };
  },
  watch: {
    value: {
      handler(val) {
        this.companyInfo = val;
      },
      immediate: true,
      deep: true,
    },
    companyInfo: {
      handler(val) {
        this.$emit("input", val);
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    nextStep() {
      if (!this.imgUrl) {
        return this.$message.error("请上传营业执照");
      }
      this.$emit("nextStep");
    },
    beforeUpload(file) {
      const isPic =
        file.type === "image/jpeg" ||
        file.type === "image/png" ||
        file.type === "image/jpg";
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isPic) {
        this.$message.error("上传图片只能是 JPG、JPEG、PNG格式!");
        return false;
      }

      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
      }
      return isPic && isLt2M;
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    uploadSuccess(res, file, fileList) {
      if (res.code === 0) {
        try {
          this.imgUrl = URL.createObjectURL(file.raw);
        } catch {
          this.imgUrl = window.URL.createObjectURL(file.raw);
        }
        this.companyInfo = JSON.parse(res.data);
        console.info(this.companyInfo);
        this.companyInfo.file = file.raw;
        console.info(this.imgUrl);
        this.$message({
          showClose: true,
          message: "营业执照上传成功",
          type: "success",
        });
      } else {
        this.$message({
          showClose: true,
          message: res.msg,
          type: "error",
        });
      }
      console.log(res);
      console.log(file);
      console.log(fileList);
    },
    uploadFail(res, file, fileList) {
      console.log(res);
      console.log(file);
      console.log(fileList);
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  margin: 20px 0;
  font-size: 20px;
  font-weight: 400;
  span {
    color: #ff531a;
    margin-left: 10px;
  }
}
.upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .pic {
    width: 360px;
    height: 180px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
  }

  .upload-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    .yyzz {
      display: flex;
      align-items: center;
      width: 170px;
      height: 100px;
      margin-bottom: 20px;
    }
  }
}
.btn {
  margin-top: 100px;
  width: 30%;
}
</style>
