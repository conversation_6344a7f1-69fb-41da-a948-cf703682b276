<template>
  <div class="execution">
    <basic-container class="clearfix">
      <div class="clearfix">
        <div class="aside" style="width:270px;margin-right:10px;float: left;">
          <el-card class="box-card" shadow="never">
            <avue-tree
              :option="treeOption"
              :data="treeOrganData"
              @node-click="nodeClick"
            ></avue-tree>
          </el-card>
        </div>
        <div class="user__main" style="float: left;width:calc(100% - 280px);padding-bottom:20px">
          <my-crud
            ref="crud"
            :page.sync="page"
            :data="tableData"
            :permission="permissionList"
            :table-loading="tableLoading"
            :option="tableOption"
            v-model="form"
            @on-load="getPage"
            @refresh-change="refreshChange"
            @row-update="handleUpdate"
            @row-save="handleSave"
            @row-del="handleDel"
            @sort-change="sortChange"
            @search-change="searchChange"
            @selection-change="selectionChange" >

            <!-- <template slot="menuLeft">
              <el-button type="success"
                         @click="batchShelf('1')"
                         size="small"
                         icon="el-icon-document"
                         v-if="permissions['chain:companystaff:edit']">批量设置部门</el-button>
              <el-button type="warning"
                         @click="batchShelf('0')"
                         size="small"
                         icon="el-icon-document"
                         v-if="permissions['chain:companystaff:edit']">批量设置职位</el-button>
            </template> -->
            <template slot="menu" slot-scope="scope" >
              <el-button
                  type="text"
                  v-if="permissions['chain:companystaff:manage']"
                  icon="el-icon-s-management"
                  size="small"
                  plain
                  @click="projectManage(scope.row, scope.index)"
                > 管理项目</el-button>
            </template>
          </my-crud>

          <el-dialog
            title="批量设置部门"
            :visible.sync="dialogLogistics"
            width="30%">
            <avue-form :option="logisticsOption" v-model="logisticsForm" @submit="delivery"></avue-form>
          </el-dialog>

        </div>
      </div>
    </basic-container>
         <!-- 批量设置班次 -->
    <project-manage
      v-if="manageVisible"
      :info="info"
      :visible.sync="manageVisible"
    ></project-manage>
  </div>
</template>

<script>
import {
  getPage,
  getObj,
  addObj,
  putObj,
  delObj,
  putObjShelf
} from "@/api/chain/companystaff";
import { tree } from "@/api/chain/companydept";
import { tableOption } from "@/const/crud/chain/companystaff2";
import { mapGetters } from "vuex";
import projectManage from "./projectManage.vue"

export default {
  name: "companystaff",
  components:{
    projectManage
  },
  data() {
    return {
      treeOption: {   //树状数据显示
        nodeKey: "id",
        addBtn: false,
        menu: false,
        defaultExpandAll: true,
        props: {
          label: "deptName",
          value: "id",
        },
      },
      treeOrganData: [],
      form: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      paramsSearch: {},
      tableLoading: false,
      tableOption: tableOption,

      selectionData: '', //批量选择数据

      dialogLogistics: false,    //批量操作弹窗
      logisticsForm: {
        // orgId
      },
      logisticsOption: {
        emptyBtn: false,
        card: true,
        group: [
          {
            icon: 'el-icon-user',
            label: '部门名称',
            prop: 'group1',
            column: [{
              label: '收货人名字',
              prop: 'userName',
              span: 24,
              readonly: true
            }]
          }]
      },
      manageVisible:false,
      info:{},
    };
  },
  created() {
    this.getTree();
  },
  mounted: function () {},
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    permissionList(key, row, index) {
        if(key==='editBtn'){
            return this.permissions["chain:companystaff:edit"]&&row.isAdmin==0 ? true : false
        }else if(key==='delBtn'){
          return this.permissions["chain:companystaff:del"]&&row.isAdmin==0 ? true : false
        }else if(key==='addBtn'){
          return this.permissions["chain:companystaff:add"] ? true : false
        }
        return true;
      // return {
      //   addBtn: this.permissions["chain:companystaff:add"] ? true : false,
      //   delBtn: this.permissions["chain:companystaff:del"]&&row.isAdmin==0 ? true : false,
      //   editBtn: this.permissions["chain:companystaff:edit"]&&row.isAdmin==0 ? true : false,
      //   viewBtn: this.permissions["chain:companystaff:get"] ? true : false,
      // };
    },
    //表格记录的批量选择操作
    selectionChange(list){
      this.selectionData = list
    },
    batchShelf(shelf){   //批量处理按钮操作
      if(this.selectionData.length<=0){
        this.$message.error('请选择员工！')
        return
      }
      let selectionIds = ''
      this.selectionData.forEach(item => {
        selectionIds += item.id+ ','
      })
      this.showDialogLogistics(selectionIds)
      // this.putObjShelf(selectionIds, shelf)
    },
    changeShelf(row){
      this.putObjShelf(row.id, row.shelf)
    },
    putObjShelf(ids, shelf){
      putObjShelf({
        ids: ids,
        shelf: shelf
      }).then(data => {
        this.getPage(this.page)
      })
    },
    showDialogLogistics(selectionIds) {
      this.dialogLogistics = true
    },
    delivery(form, done) {
      let row = this.logisticsForm.row
      row.status = '2',
        row.logistics = form.logistics,
        row.logisticsNo = form.logisticsNo
      putObj(row).then(data => {
        this.$message({
          showClose: true,
          message: '发货成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
        this.dialogLogistics = false
      }).catch(() => {
        done()
      })
    },
    //==============

    //树状数据选择操作
    nodeClick(data) {
      this.page.currentPage = 1;
      this.paramsSearch.companyDeptId = data.id
      this.getPage(this.page, { companyDeptId: data.id });
    },
    getChildren(item) {
      if (item.children) {
        if (item.children.length > 0) {
          item.children.map((itss) => {
            itss.deptName = `${itss.deptName}(${itss.count})`;
            this.getChildren(itss);
          });
        }
      }
    },
    getTree() {
      tree().then((res) => {
        let arr = [];
        res.data.data.map((item) => {
          item.deptName = `${item.deptName}`;  //`${item.deptName}(${item.count})`;
          item.children&&item.children.map((its) => {
            its.deptName = `${its.deptName}`;  // `${its.deptName}(${its.count})`
            this.getChildren(its);
          });
        });
        this.treeOrganData = res.data.data;
      });
    },
    //==============

    searchChange(params, done) {
      params = this.filterForm(params);
      this.paramsSearch = params;
      this.page.currentPage = 1;
      this.getPage(this.page, params);
      done();
    },
    sortChange(val) {
      let prop = val.prop
        ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        : "";
      if (val.order == "ascending") {
        this.page.descs = [];
        this.page.ascs = prop;
      } else if (val.order == "descending") {
        this.page.ascs = [];
        this.page.descs = prop;
      } else {
        this.page.ascs = [];
        this.page.descs = [];
      }
      this.getPage(this.page);
    },
    getPage(page, params) {
      console.log(params);
      console.log(this.paramsSearch);
      this.tableLoading = true;
      getPage(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            descs: this.page.descs,
            ascs: this.page.ascs,
          },
          params,
          this.paramsSearch
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.page.currentPage = page.currentPage;
          this.page.pageSize = page.pageSize;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this;
      this.$confirm("是否确认禁用此员工", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          row.status = '0'
          return putObj(row);
        })
        .then((data) => {
          _this.$message({
            showClose: true,
            message: "禁用成功",
            type: "success",
          });
          this.getPage(this.page);
        })
        .catch(function (err) {});
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((response) => {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          done();
          this.getPage(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page);
    },
    projectManage(row){
      this.info = row
      this.manageVisible = true
    },
  },
};
</script>

<style lang="scss" scoped></style>
