import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/chain/companycard/getACardAccountPage',
        method: 'get',
        params: query
    })
}
export function getACardAccountCount(data) {
    return request({
        url: '/chain/companycard/getACardAccountCount',
        method: 'post',
        data
    })
}
export function updateStatus(obj,type=1) { //type 1禁用 2解冻
  return request({
      url: type==1?'/chain/companycard/updateStatusById':'/chain/companycard/updateStatusTwoById',
      method: 'put',
      data: obj
  })
}
