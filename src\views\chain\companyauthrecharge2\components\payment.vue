<template>
  <div class="driverTaskDetail">
    <el-drawer size="70%"
               title="付款"
               :data="visible"
               :visible.sync="visible"
               :before-close="cancelModal">
      <basic-container>
        <el-descriptions>
          <el-descriptions-item label="交易类型">支付</el-descriptions-item>
          <el-descriptions-item label="收款人姓名">{{
            form.payeeName
          }}</el-descriptions-item>
          <el-descriptions-item label="手机号码">{{
            form.payeeMobile
          }}</el-descriptions-item>
          <el-descriptions-item label="收款卡号">{{
            form.bindingBankNo
          }}</el-descriptions-item>
          <el-descriptions-item label="收款银行">{{
            form.bindingBankName
          }}</el-descriptions-item>
          <el-descriptions-item label="收款金额(合计金额)">{{ form.amount }}元</el-descriptions-item>
          <el-descriptions-item label="未付金额">{{ form.bankNoPayAmount }}元</el-descriptions-item>
          <el-descriptions-item label="未付运单数">{{ form.notWaybillCnt }}单</el-descriptions-item>
        </el-descriptions>
        <div>
          <div class="remark"
               style="margin-top: 6px">
            已确认金额：{{ form.chargedPrice }}元，已付金额：{{
              form.occupyChargedPrice
            }}元。
          </div>
          <div class="remark">
            已选运单数：{{ multipleSelection.length }}单，已选运单金额：{{
              settleAmtTotal
            }}元。
          </div>
          <div class="flex flex-items-center" style="margin-bottom:10px">
            <el-tag class="my-crud__tip" style="height:32px;line-height:32px;margin-right:10px">
            <span class="avue-crud__tip-name">
              当前表格已选择
              <span class="avue-crud__tip-count">{{ multipleSelection.length }}</span>
              项
            </span>
            <el-button type="text"
                       size="small"
                       @click="$refs.paymentCrud.clearSelection()">清空</el-button>
            <slot name="tip"></slot>
          </el-tag>
            <el-button icon="el-icon-check"
                       size="small"
                       type="success"
                       :disabled="multipleSelection.length == 0"
                       v-if="!isView"
                       :loading="tableLoading"
                       @click="payment">
              付款</el-button>
            <el-input-number style="margin-left: 10px; width: 100px"
                             v-model="start"
                             :min="1"
                             :controls="false"
                             size="small"
                             :step="1"
                             step-strictly></el-input-number>
            至
            <el-input-number style="width: 100px; margin-right: 10px"
                             v-model="end"
                             :min="1"
                             :controls="false"
                             size="small"
                             :step="1"
                             step-strictly></el-input-number>
            <el-button icon="el-icon-check"
                       size="small"
                       type="primary"
                       :disabled="!start || !end"
                       @click="checkChange(true)">
              选择</el-button>
            <el-button icon="el-icon-close"
                       size="small"
                       type="primary"
                       :disabled="!start || !end"
                       @click="checkChange(false)">
              取消</el-button>
            <el-input-number style="width: 80px; margin-left: 10px;margin-right: 10px"
                             v-model="money"
                             :min="0"
                             :controls="false"
                             placeholder="请输入金额"
                             size="small"
                             :precision="2"
                             :step="0.01"
                             clearable
                             step-strictly></el-input-number>
            <el-button icon="el-icon-check"
                       size="small"
                       type="primary"
                       :disabled="!money"
                       @click="moneyChange">选择</el-button>
          </div>
          <u-table :data="tableData"
                   ref="paymentCrud"
                   :pagination-show="false"
                   v-loading="tableLoading"
                   element-loading-text="拼命加载中"
                   element-loading-spinner="el-icon-loading"
                   :height="tableHeight"
                   :row-height="53"
                   :header-cell-style="{backgroundColor:'#f7f9fd'}"
                   header-row-class-name='tableHeader'
                   :show-header-overflow="true"
                   border
                   use-virtual
                   row-key="no"
                   :big-data-checkbox="true"
                   @selection-change="handleSelectionChange">
            <u-table-column type="selection"
                            fixed="left"
                            width="50"></u-table-column>
            <u-table-column type="index"
                            label="序号"
                            width="60"></u-table-column>
            <u-table-column prop="no"
                            label="运单号"
                            width="180"
                            show-overflow-tooltip>
            </u-table-column>
            <u-table-column prop="projectName"
                            label="项目名称"
                            minWidth="120"
                            show-overflow-tooltip>
            </u-table-column>
            <u-table-column prop="driverName"
                            label="司机"
                            minWidth="80"
                            show-overflow-tooltip>
            </u-table-column>
            <u-table-column prop="goSoilType"
                            label="出场签单土质"
                            minWidth="100"
                            show-overflow-tooltip>
            </u-table-column>
            <u-table-column prop="tpModeCn"
                            label="运输类型"
                            minWidth="80"
                            show-overflow-tooltip>
            </u-table-column>
            <u-table-column prop="truckCode"
                            label="车牌号"
                            minWidth="90"
                            show-overflow-tooltip>
            </u-table-column>
            <u-table-column prop="amount"
                            label="结算价"
                            minWidth="80"
                            show-overflow-tooltip>
            </u-table-column>
          </u-table>
          <!-- <my-crud ref="paymentCrud"
                   class="paymentCrud"
                   :data="tableData"
                   @selection-change="handleSelectionChange"
                   :table-loading="tableLoading"
                   :option="tableOption"
                   @on-load="getPage">
            <template slot="menuLeft"
                      slot-scope="scope">
              <div v-if="tableData && tableData.length > 0">
                <el-button icon="el-icon-check"
                           size="small"
                           type="success"
                           :disabled="multipleSelection.length == 0"
                           v-if="!isView"
                           :loading="tableLoading"
                           @click="payment">
                  付款</el-button>
                <el-input-number style="margin-left: 10px; width: 100px"
                                 v-model="start"
                                 :min="1"
                                 :controls="false"
                                 size="small"
                                 :step="1"
                                 step-strictly></el-input-number>
                至
                <el-input-number style="width: 100px; margin-right: 10px"
                                 v-model="end"
                                 :min="1"
                                 :controls="false"
                                 size="small"
                                 :step="1"
                                 step-strictly></el-input-number>
                <el-button icon="el-icon-check"
                           size="mini"
                           type="primary"
                           :disabled="!start || !end"
                           @click="checkChange(true)">
                  选择</el-button>
                <el-button icon="el-icon-close"
                           size="small"
                           type="primary"
                           :disabled="!start || !end"
                           @click="checkChange(false)">
                  取消</el-button>
                <el-input-number style="width: 80px; margin-left: 10px;margin-right: 10px"
                                 v-model="money"
                                 :min="0"
                                 :controls="false"
                                 placeholder="请输入金额"
                                 size="small"
                                 :precision="2"
                                 :step="0.01"
                                 clearable
                                 step-strictly></el-input-number>
                <el-button icon="el-icon-check"
                           size="small"
                           type="primary"
                           :disabled="!money"
                           @click="moneyChange">选择</el-button>
              </div>
            </template>
          </my-crud> -->
        </div>
      </basic-container>
    </el-drawer>
    <no-real-name-driver v-if="driverVisible"
                         :tableData="driverList"
                         :visible.sync="driverVisible"></no-real-name-driver>
    <confirmPass @submit="passSubmit"
                 v-if="passVisible"
                 :info="passObj"
                 :visible.sync="passVisible"></confirmPass>
    <!-- 充值 -->
    <addRecharge v-if="addVisible"
                 :detailForm="addForm"
                 :option="addOption"
                 :showCancel="true"
                 ref="addForm"
                 size="634px"
                 :visible.sync="addVisible"
                 @submit="submit"
                 :title="title"></addRecharge>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getNotPaymentPayeeInfo,
  batchTrans4047ByPaymentWaybill,
  getCompanyPayAuthority,
  paymentCheck,
  getProjectBankInfoByPaymentId,
} from "@/api/chain/companyauthrecharge";
import noRealNameDriver from "./noRealNameDriver.vue";
import confirmPass from "./confirmPass.vue";
import addRecharge from '@/components/formDetail/index.vue';
import { saveForWallet } from '@/api/chain/companynsrsbhwallet'

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      //支付id
      type: String,
      default: () => {
        return "";
      },
    },
    payeeId: {
      //承运人id
      type: String,
      default: () => {
        return "";
      },
    },
    planId: {
      //资金计划id
      type: String,
      default: () => {
        return "";
      },
    },
    //是否查看
    isView: {
      type: Boolean,
      default: false
    },
  },
  components: {
    noRealNameDriver,
    confirmPass,
    addRecharge
  },
  data () {
    return {
      form: {},
      tableData: [],
      tableLoading: false,
      tableOption: {
        dialogDrag: false,
        border: true,
        index: true,
        indexLabel: "序号",
        stripe: false,
        align: "center",
        searchShow: false,
        labelWidth: 150,
        excelBtn: true,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        refreshBtn: false,
        selection: true,
        useVirtual: true,
        page: false,
        height: "auto",
        calcHeight: 57,
        rowKey: "id",
        showBodyOverflow: true,
        column: [
          {
            label: "运单号",
            prop: "no",
            overHidden: true,
          },
          {
            label: "项目名称",
            prop: "projectName",
            overHidden: true,
          },
          {
            label: "司机",
            prop: "driverName",
            overHidden: true,
          },
          {
            label: "出场签单土质",
            prop: "goSoilType",
            overHidden: true,
          },
          {
            label: "运输类型",
            prop: "tpModeCn",
            overHidden: true,
          },
          {
            label: "车牌号",
            prop: "truckCode",
            overHidden: true,
          },
          {
            label: "结算价",
            prop: "amount",
            overHidden: true,
          },
        ],
      },
      multipleSelection: [],
      start: 1,
      end: 1,
      driverVisible: false,
      driverList: [],
      passObj: {
        isPayPwd: false,
        isPayPhoneVerification: false,
        payPhone: "",
      },
      passVisible: false,
      money: undefined,
      title: "充值",
      addVisible: false, //充值
      addForm: {},
      addOption: {
        labelWidth: 100,
        emptyBtn: false,
        column: [
          {
            label: "充值金额(¥)",
            prop: "money",
            span: 24,
            type: "number",
            minRows: 0.01,
            maxRows: 999999999.99,
            precision: 2,
            controls: false,
            rules: [
              {
                required: true,
                message: "请输入 充值金额",
                trigger: "blur",
              },
            ],
          },
          {
            label: "充值企业银行",
            prop: "platformBranchNsrmc",
            span: 24,
            disabled: true,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            minRows: 3,
            maxRows: 5,
            span: 24,
          },
          {
            label: "上传凭证",
            prop: "pic",
            type: "upload",
            listType: "picture-card",
            action: "/upms/file/upload?fileType=image&dir=batchRecharge/",
            dataType: 'string',
            propsHttp: {
              url: "link",
            },
            loadText: "附件上传中，请稍等",
            span: 24,
            tip: "只能上传jpg/png文件，且不超过500kb",
            rules: [
              {
                required: true,
                message: "请选择",
                trigger: "change",
              },
            ],
          },
        ],
      },
      tableHeight:620
    };
  },
  created () { },
  mounted () {
    this.getPage();
    window.addEventListener("resize", this.func);
    setTimeout(() => {
      let tableHeight = this.$refs.paymentCrud.$el.offsetTop;
      this.tableHeight = window.innerHeight - tableHeight - 30;
      this.$refs.paymentCrud.doLayout();
    }, 300);
    // setTimeout(() => {
    //   console.log(this.$refs.paymentCrud);
    //   if (this.tableData && this.tableData.length > 0) {
    //   //   this.tableData.forEach((row) => {
    //   //     this.$refs.paymentCrud.toggleRowSelection(row, true);
    //   //     console.log(this.multipleSelection);
    //   //   });
    //   this.$refs.paymentCrud.partRowSelections(this.tableData, true)
    //   }
    //   // this.$refs.paymentCrud.toggleSelection()
    // }, 3000);
  },
  computed: {
    ...mapGetters(["permissions"]),
    //已选运单金额
    settleAmtTotal () {
      return this.multipleSelection
        .map((row) => row.amount)
        .reduce((acc, cur) => parseFloat(cur) + acc, 0)
        .toFixed(2);
    },
  },
  methods: {
    func () {
      let tableHeight = this.$refs.paymentCrud.$el.offsetTop;
      this.tableHeight = window.innerHeight - tableHeight - 30;
      this.$refs.paymentCrud.doLayout();
    },
    cancelModal () {
      this.$emit("update:visible", false);
      if (!this.isView) {
        this.$emit("refreshChange");
      }
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },
    getPage () {
      this.tableLoading = true;
      getNotPaymentPayeeInfo({ paymentId: this.id, payeeId: this.payeeId, planId: this.planId })
        .then((response) => {
          this.form = response.data.data;
          this.tableData = response.data.data.notPaymentPayeeWaybillInfoList || [];
          this.tableLoading = false;
          // 设置表格数据
          // this.$refs.paymentCrud.reloadData(this.tableData)
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    payment () {
      //验证选择运单合计金额 < 已确认金额 - 已付金额，否则提醒“付款金额不足，请充值！”
      // if (
      //   this.settleAmtTotal >=
      //   this.form.chargedPrice - this.form.occupyChargedPrice
      // ) {
      //   this.$message.error("付款金额不足，请充值！");
      //   return false;
      // }
      // if (this.settleAmtTotal <= 0) {
      //   this.$message.error("已选运单金额太少，付款失败！");
      //   return false;
      // }
      //判断余额是否足够付钱
      paymentCheck({
        paymentId: this.id,
        amount: this.settleAmtTotal,
      }).then(res => {
        if (res.data.data == '788') {
          //已充值未到账
          this.$alert('请联系益路公司确认到账', '提示', {
            confirmButtonText: '确定',
          });

        } else if (res.data.data == '777') {
          //未充值  去充值
          getProjectBankInfoByPaymentId({ paymentId: this.id }).then(response => {
            let datas = response.data.data
            this.title = `当前余额${datas.balance || 0}，不足以付款，请充值`
            this.addForm.platformBranchId = datas.platformBranchId
            this.addForm.platformBranchNsrmc = datas.platformBranchNsrmc
            this.addVisible = true


          }).catch(err => {
            this.tableLoading = false;
          })
        } else {
          //够钱支付
          this.$confirm(
            `已选运单数:${this.multipleSelection.length}单,已选运单金额:${this.settleAmtTotal}元，是否确定付款？`,
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              closeOnClickModal: false,
              type: "warning",
            }
          )
            .then(() => {
              this.getCompanyPayAuthority()
            })
            .catch(() => { });
        }
      })
    },
    getCompanyPayAuthority () {
      getCompanyPayAuthority()
        .then((res) => {
          console.log(res);
          this.passObj = {
            isPayPwd: res.data.data.isPayPwd == 1,
            isPayPhoneVerification: res.data.data.isPayPhoneVerification == 1,
            payPhone: res.data.data.payPhone,
          };
          if (this.passObj.isPayPwd || this.passObj.isPayPhoneVerification) {
            this.passVisible = true;
          } else {
            // 不需要密码或者手机验证 批量付款
            this.batchPayment();
          }
        })
        .catch(() => {
          this.$$message.error("获取失败");
        });
    },
    passSubmit (val) {
      let obj = JSON.parse(val);
      console.log(obj);
      this.batchPayment(obj);
    },
    batchPayment (obj) {
      this.tableLoading = true;
      let param = {
        paymentId: this.id,
        payeeId: this.payeeId,
        payeeName: this.form.payeeName,
        amount: this.settleAmtTotal,
        waybillIds: this.multipleSelection.map((item) => {
          return item.companyWaybillId;
        }),
      };
      if (obj) {
        param.payPwd = obj.pass
        param.payPhoneVerificationCode = obj.code
      }
      batchTrans4047ByPaymentWaybill(param)
        .then((res) => {
          console.log(res);
          if (res.data.code == 999) {
            this.tableLoading = false;
            this.driverList = res.data.data;
            this.driverVisible = true;
          } else {
            setTimeout(() => {
              this.tableLoading = false;
              this.$message.success("操作成功");
              this.getPage();
            }, 1500);
          }
        })
        .catch((err) => {
          this.tableLoading = false;
          this.getPage();
        });
    },
    checkChange (val = false) {
      let start = this.start;
      let end = this.end;
      if (start > end) {
        [start, end] = [end, start];
      }
      if (
        this.tableData &&
        this.tableData.length > 0 &&
        start <= this.tableData.length
      ) {
        if (end > this.tableData.length) {
          end = this.tableData.length;
        }
        console.log(start);
        console.log(end);
        console.log(val);
        let data = this.tableData.slice(start - 1, end)
        console.log(data);
        this.$refs.paymentCrud.partRowSelections(data, val)
        // for (let index = start; index <= end; index++) {
        //   this.$refs.paymentCrud.toggleRowSelection(
        //     this.tableData[index - 1],
        //     val
        //   );
        // }
      }
    },
    moneyChange () {
      console.log(this.money);
      //需要先清空所有已选择
      this.$refs.paymentCrud.clearSelection();
      let money = this.money
      this.tableData.forEach((item) => {
        money = money - (parseFloat(item.amount))
        if (money >= 0) {
          this.$refs.paymentCrud.toggleRowSelection([{row:item,selected:true}]);
        }
      })
    },
    submit (form, done) {
      console.log(form);
      let param = Object.assign({}, form)
      saveForWallet(param).then(res => {
        this.$message.success("操作成功")
        this.addVisible = false
        done()
      }).catch(() => {
        done()
      })
    },
  },
  destroyed () {
    window.removeEventListener("resize", this.func);
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}
.remark {
  padding-bottom: 2px;
  color: red;
}
/deep/ .paymentCrud .avue-crud__menu {
  display: flex;
}
</style>
