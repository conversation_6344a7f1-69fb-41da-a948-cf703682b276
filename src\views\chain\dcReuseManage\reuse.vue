<template>
  <div class="driverTaskDetail">
    <el-drawer
      size="70%"
      title="电子卡扫描重用"
      :data="visible"
      :visible.sync="visible"
      :before-close="cancelModal"
      :wrapperClosable="false"
    >
      <basic-container>
        <avue-crud
          ref="crud"
          :data="tableData"
          :table-loading="tableLoading"
          :option="tableOption"
          v-model="form"
          :search.sync="search"
          @selection-change="selectionChange"
          @on-load="getPage"
          @search-change="getPage"
          @row-del="(item, index) => tableData.splice(index, 1)"
        >
          <template slot="header" slot-scope="{ row }">
            <div
              style="
                display: inline-block;
                position: relative;
                top: -3px;
                margin-left: 10px;
              "
            >
              <el-button
                icon="el-icon-check"
                size="mini"
                type="primary"
                :disabled="selectList.length < 1"
                @click="confirm"
              >
                确认重用
              </el-button>
              <el-button
                icon="el-icon-delete"
                size="mini"
                type="primary"
                :disabled="selectList.length < 1"
                @click="delSelect"
              >
                删除
              </el-button>
            </div>
          </template>
          <template slot="cardNoSearch" slot-scope="{ disabled, size }">
            <el-input
              type="password"
              style="width:300px"
              :autofocus="true"
              placeholder="请输入 电子结算卡号码 如A123456789"
              @input="changeInput"
              class="pad-input"
              ref="inputRef"
              @keyup.enter.native="getPage"
              @compositionstart.native="onCompositionStart"
              @compositionend.native="onCompositionEnd"
              v-model.trim="search.cardNo"
            >
            </el-input>
            <div
              id="show"
              style="
                position: absolute;
                left: 16px;
                top: 50%;
                transform: translate(0, -50%);
                border: none;
                height: 30px;
                pointer-events: none;
                background: #fff;
                width: 268px;
              "
              disabled
            >
              <span>{{ search.cardNo }}</span>
            </div>
          </template>
          <template slot="todayDistance" slot-scope="{ row }">
            <span
              :style="{ color: row.todayDistance < 30 ? 'red' : '#606266' }"
              >{{ row.todayDistance }}</span
            >
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { validatedCardReset, resetCardList } from "@/api/chain/dcReuseManage";

export default {
  props: {
    //弹窗状态
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [], //升序字段
        descs: [], //降序字段
      },
      form: {},
      tableData: [],
      tableLoading: false,
      search: {},
      tableOption: {
        dialogDrag: false,
        border: true,
        stripe: true,
        align: "center",
        labelWidth: 120,
        searchLabelWidth: 60,
        searchSpan: 12,
        excelBtn: false,
        printBtn: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: true,
        searchMenuSpan: 12,
        index: false,
        refreshBtn: false,
        selection: true,
        menuWidth: 120,
        // defaultSort: {
        //   prop: "operateDate",
        //   order: "ascending",
        // },
        column: [
          {
            label: "卡号",
            prop: "cardNo",
            search: true,
          },
          {
            label: "结算状态",
            prop: "settleStatusName",
          },
          {
            label: "结算时间",
            prop: "latestSettleDatetime",
          },
          {
            label: "距今(天)",
            prop: "todayDistance",
          },
        ],
      },
      selectList: [],
      lock: false,
    };
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.$refs.inputRef.focus();
    });
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    cancelModal() {
      this.$emit("update:visible", false);
      this.$emit("refreshChange");
    },
    getPage(params, done) {
      done ? done() : "";
      if (!this.search.cardNo) {
        return false;
      }
      let isAnySame =
        this.tableData &&
        this.tableData.length > 0 &&
        this.tableData.some((item) => item.cardNo == this.search.cardNo);
      if (isAnySame) {
        this.$message.error("该结算卡已存在列表，请勿重复添加");
        return false;
      }
      this.tableLoading = true;
      validatedCardReset({ cardNo: this.search.cardNo })
        .then((response) => {
          this.tableData.push(response.data.data);
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    delSelect() {
      let indexs = this.selectList
        .map((item) => item.$index)
        .sort((a, b) => b - a);
      indexs.forEach((item) => {
        this.tableData.splice(item, 1);
      });
    },
    selectionChange(e) {
      this.selectList = e;
    },
    //中文输入
    onCompositionStart(e) {
      this.lock = true;
    },
    //中文输入
    onCompositionEnd(e) {
      console.log(e);
      // this.search.cardNo = this.getParam(e.data,'id')
      // var reg = new RegExp("(^|\\?|&)" + 'id' + "=([^&]*)(\\s|&|$)", "i");

      // if(reg.test(e.data)){
      //   this.search.cardNo =unescape(RegExp.$2.replace(/\+/g, " "))
      //   this.getPage()
      // }else{
      //   this.search.cardNo = e.data
      // }
      this.lock = false;
    },
    changeInput(value) {
      console.log(value);
      if (!this.lock) {
        let reg = /^[A-Z]\d{0,14}$/;
        if (reg.test(value)) {
          this.search.cardNo = this.getParam(value, "id");
        } else {
          this.search.cardNo = "";
        }
      } else {
      }
    },
    getParam(path, name) {
      var reg = new RegExp("(^|\\?|&)" + name + "=([^&]*)(\\s|&|$)", "i");
      if (reg.test(path)) return unescape(RegExp.$2.replace(/\+/g, " "));
      return path;
    },
    confirm() {
      //存在距今小于30天电子结算的选中条数
      let lengths = this.selectList
        .map((row) => (row.todayDistance < 30 ? 1 : 0))
        .reduce((acc, cur) => parseFloat(cur) + acc, 0);
      this.$confirm(
        lengths > 0
          ? `存在${lengths}张结算时间距今小于30天的电子结算卡，是否确认重用？`
          : `已选择${this.selectList.length}张电子结算卡，是否确认重用？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          //返回所有indexs,降序
          this.tableLoading = true;
          resetCardList(this.selectList)
            .then((res) => {
              let indexs = this.selectList
                .map((item) => item.$index)
                .sort((a, b) => b - a);
              console.log(indexs);
              indexs.forEach((item) => {
                this.tableData.splice(item, 1);
              });
              this.tableLoading = false;
              this.$message.success("操作成功");
            })
            .catch((err) => {
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 10px;
}

/deep/ .drawerHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  li {
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-weight: 700;
    margin-right: 30px;
  }
}

.demo-block-control {
  border-top: 1px solid #eaeefb;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;
  background-color: #fff;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  text-align: center;
  margin-top: -1px;
  color: #d3dce6;
  cursor: pointer;
  position: relative;

  &:hover {
    color: #409eff;
    background-color: #f9fafc;
  }
}

#show:after {
  content: "";
  display: inline-block;
  height: 18px;
  position: relative;
  border-right:1px solid  #666;
  top: 4px;
  left: 0px;
  opacity: 0;
}
#show{
  span{
    line-height: 30px;
    border-right: 1px solid #000;
  }
}
.pad-input:focus + #show:after {
  animation: mymove 1.2s infinite;
}

@keyframes mymove {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 0;
  }
  75% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
