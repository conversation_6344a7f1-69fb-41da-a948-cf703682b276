export const tableOption = {
  dialogDrag: true,
  border: true,
  indexLabel: "序号",
  // stripe: true,
  menuAlign: "center",
  align: "center",
  menuType: "text",
  index:true,
  searchShow: true, // 显示搜索字段
  excelBtn: false,
  // printBtn: true,
  labelWidth: 150,
  addBtn: false,
  editBtn: false,
  delBtn:false,
  defaultSort: {
    prop: "inDatetime",
    order: "descending",
  },
  viewBtn: true,
  searchMenuSpan: 6,
  searchLabelWidth:100,
  selection:true,
  menuWidth:310,
  useVirtual:true,
  height:"auto",
  calcHeight:137,
  searchIndex:4,
  searchCustom:2,
  routerName:"companywaybill3",
  column: [
    {
      label: "ID",
      prop: "id",
      sortable: true,
      hide: true, //列表页字段隐藏
      disabled: true, //弹窗表单字段不允许输入
      display: false, //弹窗表单字段隐藏
      rules: [
        {
          required: true,
          message: "请输入ID",
          trigger: "blur",
        },
        {
          max: 36,
          message: "长度在不能超过36个字符",
        },
      ],
      minWidth:120,
      overHidden:true,
    },
    {
      label: "运单号",
      prop: "no",
      sortable: 'custom',
      search: true,
      searchOrder:1,
      minWidth:160,
      overHidden:true,
    },
    {
      label: "项目名称",
      prop: "projectInfoId",
      search: true,
      type: "select",
      searchOrder:2,
      multiple:true,
      props: {
        label: 'projectName',
        value: 'id'
      },
      dicUrl: '/chain/projectinfo/list',
      searchFilterable: true,  //是否可以搜索
      minWidth:180,
      overHidden:true,
    },
    // {
    //   label: "项目合作方",
    //   prop: "agentInfoId",
    //   search: false,
    //   type: "select",
    //   searchOrder:3,
    //   props: {
    //     label: 'agent_name',
    //     value: 'agent_info_id'
    //   },
    //   dicUrl: '/chain/companywaybill/getAgentList',
    //   filterable: true,  //是否可以搜索
    //   minWidth:84,
    //   overHidden:true,
    // },
    {
      label: "项目负责人",
      prop: "leadingNames",
      minWidth:82,
      searchOrder:8,
      search: false,
      width:120,
      overHidden:true,
    },
    {
      label: "挖机签单员",
      prop: "inStaffName",
      searchOrder:6,
      search: true,
      minWidth:106,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "挖机型号",
      prop: "machineCode",
      search:true,
      minWidth:94,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "挖机车主",
      prop: "ownerName",
      search:true,
      minWidth:94,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "挖机车主手机",
      prop: "ownerMobile",
      search:true,
      minWidth:120,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "出场签单员",
      prop: "goStaffName",
      searchOrder:7,
      search: false,
      minWidth:106,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "挖机的土质",
      prop: "inSoilType",
      search:true,
      minWidth:84,
      overHidden:true,
    },
    {
      label: "出场签单土质",
      prop: "goSoilType",
      search: true,
      minWidth:96,
      editDisabled:true,
      // type: 'select',   // 下拉选择
      // props: {
      //   label: "itemName",
      //   value: "itemValue",
      // },
      // // search:true,
      // dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=soil_type',
      minWidth:94,
      overHidden:true,
    },
    {
      label: "挖机GPS",
      prop: "inGps",
      hide: true, //列表页字段隐藏
      minWidth:80,
      overHidden:true,
    },
    {
      label: "出场签单GPS",
      prop: "goGps",
      hide: true, //列表页字段隐藏
      minWidth:80,
      overHidden:true,
    },
    {
      label: "挖机GPS地址",
      prop: "inAddr",
      hide: true, //列表页字段隐藏
      minWidth:120,
      overHidden:true,
    },
    {
      label: "出场签单地址",
      prop: "goAddr",
      hide: true, //列表页字段隐藏\
      minWidth:120,
      overHidden:true,
    },
    {
      label: "确认卸土GPS",
      prop: "completeGps",
      hide: true, //列表页字段隐藏
      minWidth:100,
      overHidden:true,
    },
    {
      label: "卸土地址",
      prop: "completeAddr",
      hide: true, //列表页字段隐藏
      minWidth:120,
      overHidden:true,
    },
    {
      label: "运输类型",
      prop: "tpMode",
      type: "select", // 下拉选择
      search: false,
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=tp_mode',
      minWidth:94,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "泥尾",
      prop: "garbageId",
      // sortable: true,
      type: "select", // 下拉选择
      // search:true,
      props: {
        label: "names",
        value: "id",
      },
      // hide:true,
      dicUrl: "/chain/garbage/list",
      filterable: true, //是否可以搜索
      minWidth:120,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "泥尾",
      prop: "garbageName",
      search:false,
      hide:true,
      display:false,
      showColumn:false,
    },
    {
      label: "是否有泥尾票",
      prop: "isTicket",
      search:false,
      type:'select',
      dicData: [
        {
          label: "否",
          value: "0",
        },
        {
          label: "是",
          value: "1",
        },
      ],
      minWidth:120,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "泥尾票土质",
      prop: "goTicketSoilType",
      search:false,
      minWidth:106,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "拍照泥尾票",
      prop: "ticketImg",
      type:'upload',
      hide:true,
      // listType: 'picture-img',
    },
    {
      label: "司机联系方式",
      prop: "mobile",
      hide: true,   //列表页字段隐藏
      minWidth:110,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "挖机班次",
      prop: "inShiftType",
      minWidth:110,
      overHidden:true,
      sortable: 'custom',
      search:true,
      type:"select",
      dicUrl:"/chain/projectinfo/getShiftOfProject",
      dicFormatter: (res) => {
        return res.data.map((item)=>{
          return {
            label:item,
            value:item,
          }
        })
      },
      searchFilterable:true,
      formatter:(val)=>{
        return val.inShiftTypeName
      }
    },
    {
      label: "出场班次",
      prop: "goShiftType",
      minWidth:110,
      overHidden:true,
      sortable: 'custom',
      search:true,
      type:"select",
      dicUrl:"/chain/projectinfo/getShiftOfProject",
      dicFormatter: (res) => {
        return res.data.map((item)=>{
          return {
            label:item,
            value:item,
          }
        })
      },
      searchFilterable:true,
      formatter:(val)=>{
        return val.goShiftTypeName
      }
    },
    {
      label: "数量",
      prop: "weightTons",
      display:false,
      minWidth:80,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "单位",
      prop: "weightUnit",
      type: "select", // 下拉选择
      search: true,
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=weight_unit',
      display:false,
      minWidth:80,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "进量(吨)",
      prop: "load",
      // display:false,
      minWidth:94,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "扣量(吨)",
      prop: "cut",
      minWidth:94,
      overHidden:true,
      sortable: 'custom',
      // display:false,
    },
    {
      label: "单价(元/吨)",
      prop: "resourcePrice",
      minWidth:110,
      overHidden:true,
      sortable: 'custom',
      // display:false,
    },
    {
      label: "磅单图片",
      prop: "imgUrl",
      type:'upload',
      hide:true,
      showColumn:false,
      // display:false,
    },
    {
      label: "车牌号",
      prop: "goTruckCode",
      search: false,
      searchOrder:11,
      minWidth:100,
      overHidden:true,
      sortable: 'custom',
      // type: "select",
      // props: {
      //   label: 'truckCode',
      //   value: 'id'
      // },
      // dicUrl: '/chain/drivertruckinfo/list',
      // filterable: true,  //是否可以搜索
    },
    {
      label: "任务车队长",
      prop: "fleetCaptainName",
      search:false,
      searchOrder:10,
      minWidth:90,
      overHidden:true,
    },
    {
      label: "车队长手机号",
      prop: "captainMobile",
      minWidth:120,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "司机",
      prop: "fleetName",
      searchOrder:9,
      search: false,
      minWidth:80,
      overHidden:true,
      sortable: 'custom',
    },

    {
      label: "入场签单图片",
      prop: "entrancePicture",
      hide:true
    },
    {
      label: "挖机签单图片",
      prop: "inPicture",
      hide:true,
    },
    {
      label: "挖机称重仪器照片",
      prop: "inWeighInstrumentPicture",
      hide:true,
      showColumn:false,
      dataType: "string",
      listType: "picture-card",
      type: "upload",
    },
    {
      label: "出场签单图片",
      prop: "goPicture",
      hide:true
    },
    {
      label: "凭证类型",
      prop: "inWeightUnit",
      hide:true,
      showColumn:false,
    },
    {
      label: "司机结算",
      prop: "driverStatus",
      type: "select", // 下拉选择
      hide:true,
      dicData: [
        {
          label: "未结算",
          value: "1",
        },
        {
          label: "已结算",
          value: "2",
        },
      ],
      minWidth:80,
      overHidden:true,
      sortable: 'custom',
    },

    // {
    //   label: "代理商结算",
    //   prop: "agentStatus",
    //   type: "select", // 下拉选择
    //   hide:true,
    //   dicData: [
    //     {
    //       label: "未结算",
    //       value: "1",
    //     },
    //     {
    //       label: "已结算",
    //       value: "2",
    //     },
    //   ],
    // },
    {
      label: "预设价",
      prop: "payeePrice",
      minWidth:84,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "结算价",
      prop: "settlePrice",
      minWidth:84,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "税费",
      prop: "taxFee",
      minWidth:70,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "税率",
      prop: "taxPoint",
      formatter: (val) => {
        return val.taxPoint && val.taxPoint + "%";
      },
      minWidth:80,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "承运人",
      prop: "carrierName",
      search:false,
      minWidth:84,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "运单状态",
      prop: "status",
      type: "select", // 下拉选择
      search: true,
      searchMultiple:true,
      searchOrder:4,
      dataType:"string",
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl:"/chain/systemdictionaryitem/listDictionaryItem?dictionary=waybill_status",
      minWidth:94,
      overHidden:true,
      sortable: 'custom',
      searchSpan:12,
      formatter:(val)=>{
        return val.statusName
      }
    },
    {
      label: "结算状态",
      prop: "agentStatus",
      type: "select", // 下拉选择
      search: false,
      searchOrder:5,
      dicData: [
        {
          label: '未结算',
          value: '1'
        },
        {
          label: '结算中',
          value: '2'
        },
        {
          label: '已结算',
          value: '3'
        },
      ],
      minWidth:94,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "是否异常申请",
      prop: "isException",
      type: "select", // 下拉选择
      dicData: [
        {
          label: '否',
          value: '0'
        },
        {
          label: '是',
          value: '1'
        },
      ],
       minWidth:120,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "是否申领运单",
      prop: "isClaim",
      type: "select", // 下拉选择
      search:false,
      dicData: [
        {
          label: '否',
          value: '0'
        },
        {
          label: '是',
          value: '1'
        },
        {
          label: '司机和车队长未认领',
          value: '2'
        },
        {
          label: '司机未认领和车队长已认领',
          value: '3'
        },
        {
          label: '司机已认领和车队长未认领',
          value: '4'
        },
        {
          label: '司机和车队长已认领',
          value: '5'
        },
     ],
      minWidth:120,
      overHidden:true,
      sortable: 'custom',
    },

    {
      label: "挖机备注",
      prop: "inRemark",
      hide:true,
      minWidth:120,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "出场备注",
      prop: "goRemark",
      hide:true,
      minWidth:120,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "运单结算类型",
      prop: "settleType",
      type: "select",
      search:false,
      props: {
        label: "itemName",
        value: "itemValue",
      },
      dicUrl: '/chain/systemdictionaryitem/listDictionaryItem?dictionary=settle_type',
      minWidth:120,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "挖机签单时间",
      prop: "inDatetime",
      type:'datetime',
      sortable: 'custom',
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:140,
      overHidden:true,
    },
    {
      label: "出场签单时间",
      prop: "goDatetime",
      type:'datetime',
      sortable: 'custom',
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:140,
      overHidden:true,
    },
    {
      label: "完成时间",
      prop: "completeDatetime",
      sortable: 'custom',
      type:'datetime',
      searchRange:true,
      search: false,
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      minWidth:140,
      overHidden:true,
    },
    {
      label: "电子结算卡",
      prop: "settleCardNo",
      searchSpan:24,
      searchSlot:false,
      // hide:true,
      search: false,
      minWidth:160,
      overHidden:true,
      sortable: 'custom',
    },
    {
      label: "创建时间",
      prop: "createDatetime",
      hide: true,   //列表页字段隐藏
      minWidth:140,
      overHidden:true,
    },
    {
      label: "修改时间",
      prop: "updateDatetime",
      hide: true, //列表页字段隐藏
      minWidth:140,
      overHidden:true,
    },
    {
      label: "PC后台备注",
      prop: "inPcRemark",
      width:120,
      overHidden:true,
      sortable: 'custom',
      search: true
    },
    {
      label: "挖机班次日期",
      prop: "inShiftTime",
      type:'date',
      sortable: 'custom',
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd',
      minWidth:140,
      overHidden:true,
      hide:true,
      showColumn:false,
    },
    {
      label: "出场班次日期",
      prop: "goShiftTime",
      type:'date',
      sortable: 'custom',
      searchRange:true,
      search: true,
      valueFormat: 'yyyy-MM-dd',
      minWidth:140,
      overHidden:true,
      hide:true,
      showColumn:false,
    },
    {
      label: "装车价",
      prop: "isSetInPreparePrice",
      type: "select", // 下拉选择
      search:true,
      dicData: [
        {
          label: '未设置',
          value: '0'
        },
        {
          label: '已设置',
          value: '1'
        },
     ],
      minWidth:96,
      overHidden:true,
      sortable: 'custom',
      formatter:(val)=>{
        return val.inPreparePrice
      }
    },
  ],
};
