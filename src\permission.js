/**
 * 全站权限配置
 *
 */
import router from "./router/router";
import store from "@/store";
import { validatenull } from "@/util/validate";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
NProgress.configure({ showSpinner: false });
const lockPage = store.getters.website.lockPage; // 锁屏页
router.beforeEach((to, from, next) => {
  // 临时解决三级菜单tab需要缓存问题
  if(to.matched && to.matched.length > 2) to.matched.splice(1,to.matched.length-2)

  // 缓冲设置
  // if (
  //   to.meta.keepAlive === true &&
  //   store.state.tags.tagList.some((ele) => {
  //     return ele.value === to.fullPath;
  //   })
  // ) {
  //   to.meta.$keepAlive = true;
  // } else {
  //   if (to.meta.keepAlive === true && validatenull(to.meta.$keepAlive)) {
  //     to.meta.$keepAlive = true;
  //   } else {
  //     to.meta.$keepAlive = false;
  //   }
  // }
  NProgress.start();
  // to.meta.$keepAlive = true;
  const meta = to.meta || {};
  if (store.getters.access_token) {
    if (store.getters.isLock && to.path !== lockPage) {
      next({ path: lockPage });
    } else if (to.path === "/login") {
      next({ path: "/" });
    } else {
      if (store.getters.roles.length === 0) {
        store
          .dispatch("GetUserInfo")
          .then((res) => {
            // console.log("res.userType", res.userType);
            if (res.userType == null) {
              store.dispatch("SetUserType", 1);
              next({ ...to, replace: true });
              return;
            } else {
              if (res.userType == 1) {
                store.dispatch("SetUserType", 1);
                next({ ...to, replace: true });
                return;
              } else if(res.userType == 2){
                store.dispatch("SetUserType", 2);
                next({ ...to, replace: true });
                return;
                // if (res.isAuth == 1) {
                //   next({ ...to, replace: true });
                // } else {
                //   // next()
                //   next({ path: "/companyAuth",replace: true});
                // }
              }else{
                store.dispatch("SetUserType", res.userType);
                next({ ...to, replace: true });
                return;
              }
            }
          })
          .catch(() => {
            store.dispatch("FedLogOut").then(() => {
              next({ path: "/login" });
            });
          });
      } else {
        const value = to.query.src || to.fullPath;
        const label = to.meta.label||to.query.name;
        const name = to.query.name || to.name;
        if (
          meta.isTab !== false &&
          !validatenull(value) &&
          !validatenull(label)
        ) {
          store.commit("ADD_TAG", {
            label: label,
            name:name,
            value: value,
            params: to.params,
            query: to.query,
            group: router.$avueRouter.group || [],
          });
        }

        //单独处理企业认证后退问题
        if(from.name=='企业认证页'&&to.name=='首页'){
          store.dispatch("FedLogOut").then(() => {
            next({ path: "/login" });
          });
        }else{
          next();
        }
      }
    }
  } else {
    if (meta.isAuth === false) {
      next();
    } else {
      next("/login");
    }
  }
});

router.afterEach(() => {
  NProgress.done();
  const title = store.getters.tag.label;
  router.$avueRouter.setTitle(title);
});


